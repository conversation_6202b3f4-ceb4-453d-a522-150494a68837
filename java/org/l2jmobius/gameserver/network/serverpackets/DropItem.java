/*
 * This file is part of the L2J Mobius project.
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.model.item.instance.Item;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;

public class DropItem extends ServerPacket
{
	private final Item _item;
	private final int _objectId;
	
	/**
	 * Constructor of the DropItem server packet
	 * @param item : Item designating the item
	 * @param playerObjId : int designating the player ID who dropped the item
	 */
	public DropItem(Item item, int playerObjId)
	{
		_item = item;
		_objectId = playerObjId;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.DROP_ITEM.writeId(this, buffer);
		buffer.writeInt(_objectId);
		buffer.writeInt(_item.getObjectId());
		buffer.writeInt(_item.getDisplayId());
		buffer.writeInt(_item.getX());
		buffer.writeInt(_item.getY());
		buffer.writeInt(_item.getZ());
		// only show item count if it is a stackable item
		buffer.writeByte(_item.isStackable());
		buffer.writeLong(_item.getCount());
		buffer.writeByte(0);
		// buffer.writeInt(1); if above C == true (1) then packet.readInt()
		buffer.writeByte(_item.getEnchantLevel()); // Grand Crusade
		buffer.writeByte(_item.getAugmentation() != null); // Grand Crusade
		buffer.writeByte(_item.getSpecialAbilities().size()); // Grand Crusade
	}
}
