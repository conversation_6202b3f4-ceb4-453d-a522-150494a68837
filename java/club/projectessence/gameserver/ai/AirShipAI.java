/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.ai;

import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.AirShipInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.serverpackets.ExMoveToLocationAirShip;
import club.projectessence.gameserver.network.serverpackets.ExStopMoveAirShip;

/**
 * <AUTHOR>
 */
public class AirShipAI extends VehicleAI {
	public AirShipAI(AirShipInstance airShip) {
		super(airShip);
	}

	@Override
	protected void moveTo(int x, int y, int z, boolean geoFindPath) {
		if (!_actor.isMovementDisabled()) {
			_clientMoving = true;
			_actor.moveToLocation(x, y, z, 0, geoFindPath);
			_actor.broadcastPacket(new ExMoveToLocationAirShip(getActor()));
		}
	}

	@Override
	public void clientStopMoving(Location loc) {
		if (_actor.isMoving()) {
			_actor.stopMove(loc);
		}

		if (_clientMoving || (loc != null)) {
			_clientMoving = false;
			_actor.broadcastPacket(new ExStopMoveAirShip(getActor()));
		}
	}

	@Override
	public void describeStateToPlayer(PlayerInstance player) {
		if (_clientMoving) {
			player.sendPacket(new ExMoveToLocationAirShip(getActor()));
		}
	}

	@Override
	public AirShipInstance getActor() {
		return (AirShipInstance) _actor;
	}
}