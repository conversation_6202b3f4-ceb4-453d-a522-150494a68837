/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.data;

import static club.projectessence.gameserver.model.itemcontainer.Inventory.ADENA_ID;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.file.filter.XMLFilter;
import club.projectessence.gameserver.data.xml.EnchantItemHPBonusData;
import club.projectessence.gameserver.enums.ItemLocation;
import club.projectessence.gameserver.instancemanager.IdManager;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Attackable;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.EventMonsterInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.item.OnItemCreate;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Armor;
import club.projectessence.gameserver.model.items.EtcItem;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.Weapon;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.util.DocumentItem;
import club.projectessence.gameserver.util.GMAudit;

/**
 * This class serves as a container for all item templates in the game.
 */
public class ItemTable
{
	public static final Map<String, Long>	SLOTS				= new HashMap<>();
	private static final Logger				LOGGER				= Logger.getLogger(ItemTable.class.getName());
	private static Logger					LOGGER_ITEMS		= Logger.getLogger("item");
	private static Logger					LOGGER_ITEMS_LCOIN	= Logger.getLogger("itemlcoin");
	static
	{
		SLOTS.put("shirt", Item.BodyPart.SLOT_UNDERWEAR.getSlot());
		SLOTS.put("lbracelet", Item.BodyPart.SLOT_L_BRACELET.getSlot());
		SLOTS.put("rbracelet", Item.BodyPart.SLOT_R_BRACELET.getSlot());
		SLOTS.put("talisman", Item.BodyPart.SLOT_DECO.getSlot());
		SLOTS.put("chest", Item.BodyPart.SLOT_CHEST.getSlot());
		SLOTS.put("fullarmor", Item.BodyPart.SLOT_FULL_ARMOR.getSlot());
		SLOTS.put("head", Item.BodyPart.SLOT_HEAD.getSlot());
		SLOTS.put("hair", Item.BodyPart.SLOT_HAIR.getSlot());
		SLOTS.put("hairall", Item.BodyPart.SLOT_HAIRALL.getSlot());
		SLOTS.put("underwear", Item.BodyPart.SLOT_UNDERWEAR.getSlot());
		SLOTS.put("back", Item.BodyPart.SLOT_BACK.getSlot());
		SLOTS.put("neck", Item.BodyPart.SLOT_NECK.getSlot());
		SLOTS.put("legs", Item.BodyPart.SLOT_LEGS.getSlot());
		SLOTS.put("feet", Item.BodyPart.SLOT_FEET.getSlot());
		SLOTS.put("gloves", Item.BodyPart.SLOT_GLOVES.getSlot());
		SLOTS.put("chest,legs", Item.BodyPart.SLOT_CHEST.getSlot() | Item.BodyPart.SLOT_LEGS.getSlot());
		SLOTS.put("belt", Item.BodyPart.SLOT_BELT.getSlot());
		SLOTS.put("rhand", Item.BodyPart.SLOT_R_HAND.getSlot());
		SLOTS.put("lhand", Item.BodyPart.SLOT_L_HAND.getSlot());
		SLOTS.put("lrhand", Item.BodyPart.SLOT_LR_HAND.getSlot());
		SLOTS.put("rear;lear", Item.BodyPart.SLOT_R_EAR.getSlot() | Item.BodyPart.SLOT_L_EAR.getSlot());
		SLOTS.put("rfinger;lfinger", Item.BodyPart.SLOT_R_FINGER.getSlot() | Item.BodyPart.SLOT_L_FINGER.getSlot());
		SLOTS.put("wolf", Item.BodyPart.SLOT_WOLF.getSlot());
		SLOTS.put("greatwolf", Item.BodyPart.SLOT_GREATWOLF.getSlot());
		SLOTS.put("hatchling", Item.BodyPart.SLOT_HATCHLING.getSlot());
		SLOTS.put("strider", Item.BodyPart.SLOT_STRIDER.getSlot());
		SLOTS.put("babypet", Item.BodyPart.SLOT_BABYPET.getSlot());
		SLOTS.put("brooch", Item.BodyPart.SLOT_BROOCH.getSlot());
		SLOTS.put("brooch_jewel", Item.BodyPart.SLOT_BROOCH_JEWEL.getSlot());
		SLOTS.put("agathion", Item.BodyPart.SLOT_AGATHION.getSlot());
		SLOTS.put("artifactbook", Item.BodyPart.SLOT_ARTIFACT_BOOK.getSlot());
		SLOTS.put("artifact", Item.BodyPart.SLOT_ARTIFACT.getSlot());
		SLOTS.put("none", Item.BodyPart.SLOT_NONE.getSlot());
		// retail compatibility
		SLOTS.put("onepiece", Item.BodyPart.SLOT_FULL_ARMOR.getSlot());
		SLOTS.put("hair2", Item.BodyPart.SLOT_HAIR2.getSlot());
		SLOTS.put("dhair", Item.BodyPart.SLOT_HAIRALL.getSlot());
		SLOTS.put("alldress", Item.BodyPart.SLOT_ALLDRESS.getSlot());
		SLOTS.put("deco1", Item.BodyPart.SLOT_DECO.getSlot());
		SLOTS.put("waist", Item.BodyPart.SLOT_BELT.getSlot());
	}
	private final Map<Integer, EtcItem>	_etcItems	= new HashMap<>();
	private final Map<Integer, Armor>	_armors		= new HashMap<>();
	private final Map<Integer, Weapon>	_weapons	= new HashMap<>();
	private final List<File>			_itemFiles	= new ArrayList<>();
	private Item[]						_allTemplates;
	
	protected ItemTable()
	{
		processDirectory("data/stats/items", _itemFiles);
		if (Config.CUSTOM_ITEMS_LOAD)
		{
			processDirectory("data/stats/items/custom", _itemFiles);
		}
		load();
	}
	
	public static ItemTable getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private void processDirectory(String dirName, List<File> list)
	{
		final File dir = new File(Config.DATAPACK_ROOT, dirName);
		if (!dir.exists())
		{
			LOGGER.warning("Dir " + dir.getAbsolutePath() + " does not exist.");
			return;
		}
		final File[] files = dir.listFiles(new XMLFilter());
		for (File file : files)
		{
			list.add(file);
		}
	}
	
	private Collection<Item> loadItems()
	{
		final Collection<Item> list = ConcurrentHashMap.newKeySet();
		if (Config.THREADS_FOR_LOADING)
		{
			final Collection<ScheduledFuture<?>> jobs = ConcurrentHashMap.newKeySet();
			for (File file : _itemFiles)
			{
				jobs.add(ThreadPool.get().schedule(() ->
				{
					final DocumentItem document = new DocumentItem(file);
					document.parse();
					list.addAll(document.getItemList());
				}, 0));
			}
			while (!jobs.isEmpty())
			{
				for (ScheduledFuture<?> job : jobs)
				{
					if ((job == null) || job.isDone() || job.isCancelled())
					{
						jobs.remove(job);
					}
				}
			}
		}
		else
		{
			for (File file : _itemFiles)
			{
				final DocumentItem document = new DocumentItem(file);
				document.parse();
				list.addAll(document.getItemList());
			}
		}
		return list;
	}
	
	private void load()
	{
		int highest = 0;
		_armors.clear();
		_etcItems.clear();
		_weapons.clear();
		for (Item item : loadItems())
		{
			if (highest < item.getId())
			{
				highest = item.getId();
			}
			if (item instanceof EtcItem)
			{
				_etcItems.put(item.getId(), (EtcItem) item);
			}
			else if (item instanceof Armor)
			{
				_armors.put(item.getId(), (Armor) item);
			}
			else
			{
				_weapons.put(item.getId(), (Weapon) item);
			}
		}
		buildFastLookupTable(highest);
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _etcItems.size() + " Etc Items");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _armors.size() + " Armor Items");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _weapons.size() + " Weapon Items");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + (_etcItems.size() + _armors.size() + _weapons.size()) + " Items in total.");
	}
	
	/**
	 * Builds a variable in which all items are putting in in function of their ID.
	 *
	 * @param size
	 */
	private void buildFastLookupTable(int size)
	{
		// Create a FastLookUp Table called _allTemplates of size : value of the highest item ID
		LOGGER.info(getClass().getSimpleName() + ": Highest item id used: " + size);
		_allTemplates = new Item[size + 1];
		// Insert armor item in Fast Look Up Table
		for (Armor item : _armors.values())
		{
			_allTemplates[item.getId()] = item;
		}
		// Insert weapon item in Fast Look Up Table
		for (Weapon item : _weapons.values())
		{
			_allTemplates[item.getId()] = item;
		}
		// Insert etcItem item in Fast Look Up Table
		for (EtcItem item : _etcItems.values())
		{
			_allTemplates[item.getId()] = item;
		}
	}
	
	/**
	 * Returns the item corresponding to the item ID
	 *
	 * @param id
	 *            : int designating the item
	 * @return Item
	 */
	public Item getTemplate(int id)
	{
		if ((id >= _allTemplates.length) || (id < 0))
		{
			return null;
		}
		return _allTemplates[id];
	}
	
	/**
	 * Create the ItemInstance corresponding to the Item Identifier and quantitiy add logs the activity. <b><u>Actions</u>:</b>
	 * <li>Create and Init the ItemInstance corresponding to the Item Identifier and quantity</li>
	 * <li>Add the ItemInstance object to _allObjects of L2world</li>
	 * <li>Logs Item creation according to log settings</li><br>
	 *
	 * @param process
	 *            : String Identifier of process triggering this action
	 * @param itemId
	 *            : int Item Identifier of the item to be created
	 * @param count
	 *            : int Quantity of items to be created for stackable items
	 * @param actor
	 *            : Creature requesting the item creation
	 * @param reference
	 *            : Object Object referencing current action like NPC selling item or previous item in transformation
	 * @return ItemInstance corresponding to the new item
	 */
	public ItemInstance createItem(String process, int itemId, long count, Creature actor, Object reference)
	{
		// Create and Init the ItemInstance corresponding to the Item Identifier
		final ItemInstance item = new ItemInstance(IdManager.getInstance().getNextId(), itemId);
		if (process.equalsIgnoreCase("loot") && !Config.AUTO_LOOT_ITEM_IDS.contains(itemId))
		{
			ScheduledFuture<?> itemLootShedule;
			boolean isRaid = (reference instanceof Attackable) && ((Attackable) reference).isRaid();
			// if (isRaid && Config.RAID_CC_DROP) // loot privilege for raids
			// {
			// final Attackable raid = (Attackable) reference;
			// // if in CommandChannel and was killing a World/RaidBoss
			// if ((raid.getFirstCommandChannelAttacked() != null) && !Config.AUTO_LOOT_RAIDS)
			// {
			// item.setOwnerId(raid.getFirstCommandChannelAttacked().getLeaderObjectId());
			// itemLootShedule = ThreadPool.get().schedule(new ResetOwner(item), Config.LOOT_RAIDS_PRIVILEGE_INTERVAL);
			// item.setItemLootShedule(itemLootShedule);
			// }
			// }
			// else
			if (!Config.AUTO_LOOT || ((reference instanceof EventMonsterInstance) && ((EventMonsterInstance) reference).eventDropOnGround()))
			{
				item.setOwnerId(actor.getObjectId());
				itemLootShedule = ThreadPool.get().schedule(new ResetOwner(item), isRaid ? 60000 : 15000);
				item.setItemLootShedule(itemLootShedule);
			}
		}
		// Add the ItemInstance object to _allObjects of L2world
		World.getInstance().addObject(item);
		// Set Item parameters
		if (item.isStackable() && (count > 1))
		{
			item.setCount(count);
		}
		if (Config.LOG_ITEMS && !process.equals("Reset") && ((actor != null) && (actor.getActingPlayer() != null) && !actor.isFakePlayer()) && !Config.LIST_CHARNAME_IGNORE.contains(actor.getName()))
		{
			if (!Config.LOG_ITEMS_SMALL_LOG || (Config.LOG_ITEMS_SMALL_LOG && (item.isEquipable() || (item.getId() == 93629 && count < 200) || ((item.getId() == ADENA_ID) && (item.getCount() >= 5_000_000)) || ((item.getId() == 400002) && (item.getCount() == 5)))))
			{
				if (item.getEnchantLevel() > 0)
				{
					LOGGER_ITEMS.info("CREATE:" + String.valueOf(process) // in case of null
					+ ", item " + item.getObjectId() //
					+ ":+" + item.getEnchantLevel() //
					+ " " + item.getItem().getName() + (item.getItem().getAdditionalName() == null ? "" : " " + item.getItem().getAdditionalName()) //
					+ "[" + item.getId() + "]" //
					+ "(" + item.getCount() //
					+ "), " + String.valueOf(actor) // in case of null
					+ ", " + String.valueOf(reference)); // in case of null
				}
				else
				{
					if ((item.getId() == 93629) || (item.getId() == Inventory.ADENA_ID) || item.getId() == 400002)
					{ // L-Coins (8000 pcs)
						LOGGER_ITEMS_LCOIN.info("CREATE:" + String.valueOf(process) // in case of null
						+ ", item " + item.getObjectId() //
						+ ":" + item.getItem().getName() + (item.getItem().getAdditionalName() == null ? "" : " " + item.getItem().getAdditionalName()) //
						+ "[" + item.getId() + "]" //
						+ "(" + item.getCount() //
						+ "), " + String.valueOf(actor) // in case of null
						+ ", " + String.valueOf(reference)); // in case of null
					}
					else
					{
						LOGGER_ITEMS.info("CREATE:" + String.valueOf(process) // in case of null
						+ ", item " + item.getObjectId() //
						+ ":" + item.getItem().getName() + (item.getItem().getAdditionalName() == null ? "" : " " + item.getItem().getAdditionalName()) //
						+ "[" + item.getId() + "]" //
						+ "(" + item.getCount() //
						+ "), " + String.valueOf(actor) // in case of null
						+ ", " + String.valueOf(reference)); // in case of null
					}
				}
			}
			if (item.isVIPReward())
			{
				LOGGER_ITEMS.info("CREATE:" + String.valueOf(process) // in case of null
				+ ", item " + item.getObjectId() //
				+ ":" + item.getItem().getName() + (item.getItem().getAdditionalName() == null ? "" : " " + item.getItem().getAdditionalName()) //
				+ "[" + item.getId() + "]" //
				+ "(" + item.getCount() //
				+ "), " + String.valueOf(actor) // in case of null
				+ ", " + String.valueOf(reference)); // in case of null
			}
		}
		if ((actor != null) && actor.isGM())
		{
			String referenceName = "no-reference";
			if (reference instanceof WorldObject)
			{
				referenceName = (((WorldObject) reference).getName() != null ? ((WorldObject) reference).getName() : "no-name");
			}
			else if (reference instanceof String)
			{
				referenceName = (String) reference;
			}
			final String targetName = (actor.getTarget() != null ? actor.getTarget().getName() : "no-target");
			if (Config.GMAUDIT)
			{
				GMAudit.auditGMAction(actor.getName() + " [" + actor.getObjectId() + "]" //
				, String.valueOf(process) // in case of null
				+ "(id: " + itemId //
				+ " count: " + count //
				+ " name: " + item.getItemName() //
				+ " objId: " + item.getObjectId() + ")" //
				, targetName //
				, "Object referencing this action is: " + referenceName);
			}
		}
		// Notify to scripts
		EventDispatcher.getInstance().notifyEventAsync(new OnItemCreate(process, item, actor, reference), item.getItem());
		return item;
	}
	
	public ItemInstance createItem(String process, int itemId, int count, PlayerInstance actor)
	{
		return createItem(process, itemId, count, actor, null);
	}
	
	/**
	 * Destroys the ItemInstance.<br>
	 * <br>
	 * <b><u>Actions</u>:</b>
	 * <ul>
	 * <li>Sets ItemInstance parameters to be unusable</li>
	 * <li>Removes the ItemInstance object to _allObjects of L2world</li>
	 * <li>Logs Item deletion according to log settings</li>
	 * </ul>
	 *
	 * @param process
	 *            a string identifier of process triggering this action.
	 * @param item
	 *            the item instance to be destroyed.
	 * @param actor
	 *            the player requesting the item destroy.
	 * @param reference
	 *            the object referencing current action like NPC selling item or previous item in transformation.
	 */
	public void destroyItem(String process, ItemInstance item, PlayerInstance actor, Object reference)
	{
		synchronized (item)
		{
			long old = item.getCount();
			item.setCount(0);
			item.setOwnerId(0);
			item.setItemLocation(ItemLocation.VOID);
			item.setLastChange(ItemInstance.REMOVED);
			World.getInstance().removeObject(item);
			IdManager.getInstance().releaseId(item.getObjectId());
			if (Config.LOG_ITEMS)
			{
				if (!Config.LOG_ITEMS_SMALL_LOG || (Config.LOG_ITEMS_SMALL_LOG && (item.isEquipable() || (item.getId() == 93629 && item.getCount() < 200) || ((item.getId() == ADENA_ID) && (item.getCount() >= 5_000_000)) || ((item.getId() == 400002) && (item.getCount() >= 5)))))
				{
					if (item.getEnchantLevel() > 0)
					{
						LOGGER_ITEMS.info("DELETE:" + String.valueOf(process) // in case of null
						+ ", item " + item.getObjectId() //
						+ ":+" + item.getEnchantLevel() //
						+ " " + item.getItem().getName() + (item.getItem().getAdditionalName() == null ? "" : " " + item.getItem().getAdditionalName()) //
						+ "[" + item.getId() + "]" //
						+ "(" + item.getCount() //
						+ "), PrevCount(" + old //
						+ "), " + String.valueOf(actor) // in case of null
						+ ", " + String.valueOf(reference)); // in case of null
					}
					else
					{
						if ((item.getId() == 93629) || (item.getId() == Inventory.ADENA_ID))
						{ // L-Coins (8000 pcs)
							if ((item.getId() == 93629 && item.getCount() < 200))
							{
								// old = 0;
								return;
							}
							LOGGER_ITEMS_LCOIN.info("DELETE:" + String.valueOf(process) // in case of null
							+ ", item " + item.getObjectId() //
							+ ":" + item.getItem().getName() + (item.getItem().getAdditionalName() == null ? "" : " " + item.getItem().getAdditionalName()) //
							+ "[" + item.getId() + "]" //
							+ "(" + item.getCount() //
							+ "), PrevCount(" + old //
							+ "), " + String.valueOf(actor) // in case of null
							+ ", " + String.valueOf(reference)); // in case of null
						}
						else
						{
							LOGGER_ITEMS.info("DELETE:" + String.valueOf(process) // in case of null
							+ ", item " + item.getObjectId() //
							+ ":" + item.getItem().getName() + (item.getItem().getAdditionalName() == null ? "" : " " + item.getItem().getAdditionalName()) //
							+ "[" + item.getId() + "]" //
							+ "(" + item.getCount() //
							+ "), PrevCount(" + old //
							+ "), " + String.valueOf(actor) // in case of null
							+ ", " + String.valueOf(reference)); // in case of null
						}
					}
				}
			}
			if ((actor != null) && actor.isGM())
			{
				String referenceName = "no-reference";
				if (reference instanceof WorldObject)
				{
					referenceName = (((WorldObject) reference).getName() != null ? ((WorldObject) reference).getName() : "no-name");
				}
				else if (reference instanceof String)
				{
					referenceName = (String) reference;
				}
				final String targetName = (actor.getTarget() != null ? actor.getTarget().getName() : "no-target");
				if (Config.GMAUDIT)
				{
					GMAudit.auditGMAction(actor.getName() + " [" + actor.getObjectId() + "]" //
					, String.valueOf(process) // in case of null
					+ "(id: " + item.getId() //
					+ " count: " + item.getCount() //
					+ " itemObjId: " //
					+ item.getObjectId() + ")" //
					, targetName //
					, "Object referencing this action is: " + referenceName);
				}
			}
			// if it's a pet control item, delete the pet as well
			if (item.getItem().isPetItem())
			{
				try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("DELETE FROM pets WHERE item_obj_id=?"))
				{
					// Delete the pet in db
					statement.setInt(1, item.getObjectId());
					statement.execute();
				}
				catch (Exception e)
				{
					LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Could not delete pet objectid:", e);
				}
				try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("DELETE FROM pet_skills WHERE item_obj_id=?"))
				{
					// Delete the pet in db
					statement.setInt(1, item.getObjectId());
					statement.execute();
				}
				catch (Exception e)
				{
					LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Could not delete pet_skills objectid:", e);
				}
			}
		}
	}
	
	public void reload()
	{
		load();
		EnchantItemHPBonusData.getInstance().load();
	}
	
	public Set<Integer> getAllArmorsId()
	{
		return _armors.keySet();
	}
	
	public Collection<Armor> getAllArmors()
	{
		return _armors.values();
	}
	
	public Set<Integer> getAllWeaponsId()
	{
		return _weapons.keySet();
	}
	
	public Collection<Weapon> getAllWeapons()
	{
		return _weapons.values();
	}
	
	public Set<Integer> getAllEtcItemsId()
	{
		return _etcItems.keySet();
	}
	
	public Collection<EtcItem> getAllEtcItems()
	{
		return _etcItems.values();
	}
	
	public Item[] getAllItems()
	{
		return _allTemplates;
	}
	
	public int getArraySize()
	{
		return _allTemplates.length;
	}
	
	protected static class ResetOwner implements Runnable
	{
		ItemInstance _item;
		
		public ResetOwner(ItemInstance item)
		{
			_item = item;
		}
		
		@Override
		public void run()
		{
			_item.setOwnerId(0);
			_item.setItemLootShedule(null);
		}
	}
	
	private static class SingletonHolder
	{
		protected static final ItemTable INSTANCE = new ItemTable();
	}
}
