/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.data.xml;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.enums.ItemGrade;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.itemBlessing.ItemBlessing;
import club.projectessence.gameserver.model.items.Armor;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class BlessItemData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(BlessItemData.class.getName());

	private static List<ItemBlessing> _itemBlessings = new ArrayList<>();
	private static Map<Integer, Integer> _itemGroups = new ConcurrentHashMap<>();

	protected BlessItemData() {
		load();
	}

	public static BlessItemData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public synchronized void load() {
		_itemBlessings.clear();
		_itemGroups.clear();
		parseDatapackFile("data/BlessItemData.xml");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _itemBlessings.size() + " item blessings.");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _itemGroups.size() + " weapons with blessing group id.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		forEach(doc, "list", listNode -> forEach(listNode, "group", groupNode ->
		{
			final int groupId = parseInteger(groupNode.getAttributes(), "id");
			forEach(groupNode, "bless", blessNode ->
			{
				NamedNodeMap blessAttrs = blessNode.getAttributes();
				ItemGrade grade = ItemGrade.getByString(parseString(blessAttrs, "grade"));
				String type = parseString(blessAttrs, "type");
				int enchantLevel = parseInteger(blessAttrs, "enchant");
				final ItemBlessing itemBlessing = new ItemBlessing(groupId, grade, type, enchantLevel);

				forEach(blessNode, "passive_skill", passiveSkillNode ->
				{
					NamedNodeMap skillAttrs = passiveSkillNode.getAttributes();
					final int skillId = parseInteger(skillAttrs, "id");
					final int skillLevel = parseInteger(skillAttrs, "level");
					final Skill skill = SkillData.getInstance().getSkill(skillId, skillLevel);
					if (skill != null) {
						itemBlessing.addPassiveSkill(new SkillHolder(skill));
					} else {
						LOGGER.warning(getClass().getSimpleName() + ": null skill id: " + skillId + " level: " + skillLevel + " groupId: " + groupId);
					}
				});

				_itemBlessings.add(itemBlessing);
			});

			forEach(groupNode, "item", itemNode ->
			{
				int itemId = parseInteger(itemNode.getAttributes(), "id");
				_itemGroups.put(itemId, groupId);
			});
		}));
	}

	public List<ItemBlessing> getItemBlessings(ItemInstance item) {
		if ((item == null) || !item.isBlessed()) {
			return null;
		}
		final int itemId = item.getId();
		final int enchantLevel = item.getEnchantLevel();
		final ItemGrade itemGrade = item.getItem().getItemGrade();
		List<ItemBlessing> itemBlessings = new ArrayList<>();
		if (item.isWeapon()) {
			String clientWeaponType = ClientWeaponTypesData.getInstance().getClientWeaponType(itemId);
			if (clientWeaponType == null) {
				return null;
			}
			for (ItemBlessing itemBlessing : _itemBlessings) {
				if (clientWeaponType.equals(itemBlessing.getClientWeaponType()) && (itemGrade == itemBlessing.getItemGrade())) {
					Integer itemGroupId = _itemGroups.get(itemId);
					if ((itemGroupId == null) || (itemGroupId != itemBlessing.getGroupId())) {
						continue;
					}
					if ((itemBlessing.getEnchantLevel() < 0) || (enchantLevel >= itemBlessing.getEnchantLevel())) {
						itemBlessings.add(itemBlessing);
					}
				}
			}
		} else { // Armor
			for (ItemBlessing itemBlessing : _itemBlessings) {
				final Armor armor = item.getArmorItem();
				switch (itemBlessing.getClientWeaponType()) {
					case "helmet" -> {
						if (armor.getBodyPart() != Item.BodyPart.SLOT_HEAD.getSlot()) {
							continue;
						}
					}
					case "armor" -> {
						if (armor.getBodyPart() != Item.BodyPart.SLOT_CHEST.getSlot()) {
							continue;
						}
					}
					case "leggings" -> {
						if (armor.getBodyPart() != Item.BodyPart.SLOT_LEGS.getSlot()) {
							continue;
						}
					}
					case "full_armor" -> {
						if (armor.getBodyPart() != Item.BodyPart.SLOT_FULL_ARMOR.getSlot()) {
							continue;
						}
					}
					case "gloves" -> {
						if (armor.getBodyPart() != Item.BodyPart.SLOT_GLOVES.getSlot()) {
							continue;
						}
					}
					case "boots" -> {
						if (armor.getBodyPart() != Item.BodyPart.SLOT_FEET.getSlot()) {
							continue;
						}
					}
				}
				if (itemGrade == itemBlessing.getItemGrade()) {
					Integer itemGroupId = _itemGroups.get(itemId);
					if ((itemGroupId == null) || (itemGroupId != itemBlessing.getGroupId())) {
						continue;
					}
					if ((itemBlessing.getEnchantLevel() < 0) || (enchantLevel >= itemBlessing.getEnchantLevel())) {
						itemBlessings.add(itemBlessing);
					}
				}
			}
		}
		return itemBlessings;
	}

	public boolean canBeBlessed(ItemInstance item) {
		return !item.isBlessed() && _itemGroups.containsKey(item.getId());
	}

	private static class SingletonHolder {
		protected static final BlessItemData INSTANCE = new BlessItemData();
	}
}
