/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.data.xml;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.enums.SpecialHuntingZoneResetType;
import club.projectessence.gameserver.enums.SpecialHuntingZoneType;
import club.projectessence.gameserver.enums.TeleportWhereType;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.holders.SpecialHuntingZoneHolder;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.instancezone.InstanceTemplate;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.type.SpecialHuntingZone;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.File;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class SpecialHuntingZoneData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(SpecialHuntingZoneData.class.getName());

	private static final Map<Integer, SpecialHuntingZoneHolder> _specialHuntingZones = new HashMap<>();
	private static final Map<Integer, Instance> _globalInstances = new HashMap<>();

	private static final Map<Integer, Long> _deathDispell = new ConcurrentHashMap<>();
	private static final int TASK_DELAY = 60000;
	private static ScheduledFuture<?> _task = null;
	private static boolean _initialized = false;

	protected SpecialHuntingZoneData() {
		load();
	}

	public static SpecialHuntingZoneData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public synchronized void load() {
		_specialHuntingZones.clear();
		parseDatapackFile("data/SpecialHuntingZones.xml");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _specialHuntingZones.size() + " special hunting zones.");
		if (_task != null) {
			_task.cancel(true);
			_task = null;
		}
		_task = ThreadPool.get().scheduleAtFixedRate(() ->
		{
			int lastPlayerId = -1;
			try {
				for (SpecialHuntingZoneHolder zoneHolder : getSpecialHuntingZones()) {
					if (zoneHolder.getType() != SpecialHuntingZoneType.PUBLIC) {
						continue;
					}
					int id = zoneHolder.getId();
					for (int zoneId : zoneHolder.getZoneIds()) {
						final SpecialHuntingZone zone = ZoneManager.getInstance().getZoneById(zoneId, SpecialHuntingZone.class);
						for (PlayerInstance player : zone.getAllPlayersInside()) {
							if (zoneHolder.isGlobalInstance()) {
								continue;
							}
							lastPlayerId = player.getObjectId();
							long remainingTime = player.getVariables().getLong("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + id, zoneHolder.getDefaultTime() * 1000);
							remainingTime = remainingTime == -1 ? zoneHolder.getDefaultTime() * 1000 : remainingTime;
							remainingTime -= TASK_DELAY;
							if ((remainingTime <= 0) || !zoneHolder.checkTimeLimit(player, true)) {
								zone.removeCharacter(player);
								player.teleToLocation(TeleportWhereType.TOWN, null);
								zone.removeCharacter(player);
							}
							player.getVariables().set("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + id, remainingTime);
						}
					}
				}
				for (SpecialHuntingZoneHolder zoneHolder : getSpecialHuntingZones()) {
					if (!zoneHolder.isGlobalInstance()) {
						continue;
					}
					int id = zoneHolder.getId();
					final Instance instance = _globalInstances.get(id);
					if (instance == null) {
						continue;
					}
					for (PlayerInstance player : instance.getPlayers()) {
						long remainingTime = player.getVariables().getLong("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + id, zoneHolder.getDefaultTime() * 1000);
						remainingTime = remainingTime == -1 ? zoneHolder.getDefaultTime() * 1000 : remainingTime;
						remainingTime -= TASK_DELAY;
						if ((remainingTime <= 0) || !zoneHolder.checkTimeLimit(player, true)) {
							instance.ejectPlayer(player);
						}
						player.getVariables().set("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + id, remainingTime);
					}
				}
			} catch (Exception e) {
				LOGGER.warning(getClass().getSimpleName() + " exception at SpecialHuntingZone [Last Player: " + lastPlayerId + "]");
				e.printStackTrace();
			}
		}, TASK_DELAY, TASK_DELAY);
		ThreadPool.get().scheduleAtFixedRate(() ->
		{
			final long current = System.currentTimeMillis();
			final Iterator<Entry<Integer, Long>> iterator = _deathDispell.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<Integer, Long> entry = iterator.next();
				if ((current - entry.getValue().longValue()) > 300_000) {
					final PlayerInstance player = World.getInstance().getPlayer(entry.getKey());
					if ((player != null) && player.isDead() && player.isInSpecialHuntingZone()) {
						player.teleToLocation(146903, 26786, -2204);
					}
					iterator.remove();
				}
			}
		}, 30_000, 30_000);

		if (!_initialized) {
			for (Entry<Integer, SpecialHuntingZoneHolder> entry : _specialHuntingZones.entrySet()) {
				final SpecialHuntingZoneHolder zone = entry.getValue();
				if (zone.isGlobalInstance()) {
					final InstanceManager instanceManager = InstanceManager.getInstance();
					final InstanceTemplate template = instanceManager.getInstanceTemplate(zone.getInstanceId());
					final Instance instance = instanceManager.createInstance(template, null);
					if (instance != null) {
						instance.setDuration(-1);
						_globalInstances.put(entry.getKey(), instance);
					}
					for (Npc npc : instance.getNpcs()) {
						npc.getSpawn().updateSpawnDelayByConfig();
					}
				}
			}
		}
	}

	public void addDeathTask(int objectId) {
		_deathDispell.put(objectId, System.currentTimeMillis());
	}

	public void removeDeathTask(int objectId) {
		_deathDispell.remove(objectId);
	}

	@Override
	public void parseDocument(Document doc, File f) {
		forEach(doc, "list", listNode -> forEach(listNode, "zone", zoneNode ->
		{
			final int id = parseInteger(zoneNode.getAttributes(), "id");
			final String name = parseString(zoneNode.getAttributes(), "name");
			final List<Integer> zoneIds = new ArrayList<>();
			for (String z : parseString(zoneNode.getAttributes(), "zoneId").split(";")) {
				zoneIds.add(Integer.parseInt(z));
			}
			final SpecialHuntingZoneResetType reset = parseEnum(zoneNode.getAttributes(), SpecialHuntingZoneResetType.class, "reset");
			final SpecialHuntingZoneType type = parseEnum(zoneNode.getAttributes(), SpecialHuntingZoneType.class, "type", SpecialHuntingZoneType.PUBLIC);
			final boolean isWorld = parseBoolean(zoneNode.getAttributes(), "isWorld", false);
			final boolean isEnabled = parseBoolean(zoneNode.getAttributes(), "isEnabled", true);
			List<ItemHolder> fees = new ArrayList<>();
			int minLevel = 0;
			int maxLevel = 999;
			int defaultTime = 0;
			int maxTime = 0;
			int extensionTime = 0;
			int reenterTimeout = 0;
			Location teleportLocation = null;
			int instanceId = -1;
			boolean isGlobalInstance = false;
			forEach(zoneNode, "fee", feeNode ->
			{
				forEach(feeNode, "item", itemNode ->
				{
					NamedNodeMap itemAttrs = itemNode.getAttributes();
					fees.add(new ItemHolder(parseInteger(itemAttrs, "id"), parseLong(itemAttrs, "count")));
				});
			});
			StatSet timeLimit = new StatSet();
			forEach(zoneNode, "timeLimit", timeLimitnode ->
			{
				forEach(timeLimitnode, "days", daysNode ->
				{
					Set<Integer> days = new HashSet<>();
					for (String d : daysNode.getTextContent().split(";")) {
						days.add(Integer.parseInt(d));
					}
					timeLimit.set("days", days);
				});
				forEach(timeLimitnode, "start", startNode ->
				{
					NamedNodeMap attrs = startNode.getAttributes();
					timeLimit.set("startHour", parseInteger(attrs, "hour"));
					timeLimit.set("startMinute", parseInteger(attrs, "minute"));
				});
				forEach(timeLimitnode, "end", endNode ->
				{
					NamedNodeMap attrs = endNode.getAttributes();
					timeLimit.set("endHour", parseInteger(attrs, "hour"));
					timeLimit.set("endMinute", parseInteger(attrs, "minute"));
				});
			});

			final NodeList list = zoneNode.getChildNodes();
			for (int i = 0; i < list.getLength(); i++) {
				final Node levelNode = list.item(i);
				if (levelNode.getNodeName().equals("level")) {
					minLevel = parseInteger(levelNode.getAttributes(), "min");
					maxLevel = parseInteger(levelNode.getAttributes(), "max");
				}
			}

			for (int i = 0; i < list.getLength(); i++) {
				final Node timeNode = list.item(i);
				if (timeNode.getNodeName().equals("time")) {
					defaultTime = parseInteger(timeNode.getAttributes(), "default");
					maxTime = parseInteger(timeNode.getAttributes(), "max");
					extensionTime = parseInteger(timeNode.getAttributes(), "extension");
					reenterTimeout = parseInteger(timeNode.getAttributes(), "reenterTimeout", 0) * 1000;
				}
			}

			for (int i = 0; i < list.getLength(); i++) {
				final Node locationNode = list.item(i);
				if (locationNode.getNodeName().equals("location")) {
					int x = parseInteger(locationNode.getAttributes(), "x", 0);
					int y = parseInteger(locationNode.getAttributes(), "y", 0);
					int z = parseInteger(locationNode.getAttributes(), "z", 0);
					if (!((x == 0) && (y == 0) && (z == 0))) {
						teleportLocation = new Location(x, y, z);
					}
				}
			}

			for (int i = 0; i < list.getLength(); i++) {
				final Node instanceNode = list.item(i);
				if (instanceNode.getNodeName().equals("instance")) {
					instanceId = parseInteger(instanceNode.getAttributes(), "id", -1);
					isGlobalInstance = parseBoolean(instanceNode.getAttributes(), "isGlobal", false);
				}
			}
			SpecialHuntingZoneHolder zoneHolder = new SpecialHuntingZoneHolder(id, name, zoneIds, reset, type, isWorld, isEnabled, instanceId, isGlobalInstance, fees, minLevel, maxLevel, defaultTime, maxTime, extensionTime, reenterTimeout, teleportLocation, timeLimit);
			_specialHuntingZones.put(id, zoneHolder);
		}));
	}

	public boolean isInsideSpecialHuntingzone(int x, int y, int z, boolean includeTranscendentZones) {
		return getSpecialHuntingZoneByLoc(x, y, z, includeTranscendentZones) != null;
	}

	public SpecialHuntingZoneHolder getSpecialHuntingZoneByLoc(int x, int y, int z, boolean includeTranscendentZones) {
		for (SpecialHuntingZoneHolder zoneHolder : getSpecialHuntingZones()) {
			if (!includeTranscendentZones && (zoneHolder.getType() == SpecialHuntingZoneType.TRANSCENDENT)) {
				continue;
			}
			for (int zoneId : zoneHolder.getZoneIds()) {
				final SpecialHuntingZone zone = ZoneManager.getInstance().getZoneById(zoneId, SpecialHuntingZone.class);
				if (zone.isInsideZone(x, y, z)) {
					return zoneHolder;
				}
			}
		}
		return null;
	}

	public SpecialHuntingZoneHolder getSpecialHuntingZoneById(int id) {
		return _specialHuntingZones.get(id);
	}

	public SpecialHuntingZoneHolder getSpecialHuntingZoneByZoneId(int zoneId) {
		for (SpecialHuntingZoneHolder zoneHolder : getSpecialHuntingZones()) {
			if (zoneHolder.getZoneIds().contains(zoneId)) {
				return zoneHolder;
			}
		}
		return null;
	}

	public Collection<SpecialHuntingZoneHolder> getSpecialHuntingZones() {
		return _specialHuntingZones.values();
	}

	public Instance getGlobalInstance(int id) {
		return _globalInstances.get(id);
	}

	private static class SingletonHolder {
		protected static final SpecialHuntingZoneData INSTANCE = new SpecialHuntingZoneData();
	}
}
