/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.data.xml;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.StatSet;
import org.w3c.dom.Document;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class RaidTeleportData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(RaidTeleportData.class.getName());
	private final Map<Integer, Location> _raids = new HashMap<>();

	protected RaidTeleportData() {
		load();
	}

	public static RaidTeleportData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public void load() {
		_raids.clear();
		parseDatapackFile("data/RaidTeleportData.xml");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _raids.size() + " raid teleport data.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		forEach(doc, "list", listNode -> forEach(listNode, "raid", rewardNode ->
		{
			final StatSet set = new StatSet(parseAttributes(rewardNode));
			final int id = set.getInt("id");
			final int x = set.getInt("x");
			final int y = set.getInt("y");
			final int z = set.getInt("z");
			final boolean enabled = set.getBoolean("enabled", true);
			if (enabled) {
				_raids.put(id, new Location(x, y, z));
			}
		}));
	}

	public Location getTeleportLocation(int raidId) {
		return _raids.get(raidId);
	}

	private static class SingletonHolder {
		protected static final RaidTeleportData INSTANCE = new RaidTeleportData();
	}
}
