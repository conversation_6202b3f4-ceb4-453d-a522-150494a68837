/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.data.xml;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.model.holders.ItemHolder;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;

import java.io.File;
import java.util.Calendar;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class MissionLevelData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(MissionLevelData.class.getName());
	private static int _seasonEndTimestamp = -1;
	private static int _currentSeasonClientDate = -1;
	private final Map<Integer, MissionLevelSeasonInfo> _seasons = new HashMap<>();

	protected MissionLevelData() {
		load();
	}

	public static MissionLevelData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public MissionLevelSeasonInfo getSeasonInfo() {
		return _seasons.get(_currentSeasonClientDate);
	}

	public int getSeasonEndDate() {
		return _seasonEndTimestamp;
	}

	public void updateSeason() {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(System.currentTimeMillis());
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 6);
		cal.set(Calendar.MINUTE, 30);
		cal.set(Calendar.SECOND, 0);
		if (cal.getTimeInMillis() < System.currentTimeMillis()) {
			cal.add(Calendar.MONTH, 1);
		}
		_seasonEndTimestamp = (int) (cal.getTimeInMillis() / 1000);
		final int year = cal.get(Calendar.YEAR);
		final int month = cal.get(Calendar.MONTH);
		_currentSeasonClientDate = Integer.valueOf(year + "" + (month < 10 ? ("0" + month) : month));
	}

	@Override
	public void load() {
		_seasons.clear();
		parseDatapackFile("data/MissionLevel.xml");

		updateSeason();

		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _seasons.size() + " seasons.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		forEach(doc, "list", listNode -> forEach(listNode, "season", seasonNode ->
		{
			NamedNodeMap seasonAttrs = seasonNode.getAttributes();
			final int clientDate = parseInteger(seasonAttrs, "clientDate");
			final int limitLv = parseInteger(seasonAttrs, "limitLv");

			final MissionLevelSeasonInfo season = new MissionLevelSeasonInfo(clientDate, limitLv);
			_seasons.put(clientDate, season);
			Map<Integer, MissionLevelStepInfo> rewards = new LinkedHashMap<>();

			forEach(seasonNode, "baseReward", baseReward ->
			{
				final NamedNodeMap attrs = baseReward.getAttributes();

				final int level = parseInteger(attrs, "level");
				final int itemId = parseInteger(attrs, "itemId");
				final long itemCount = parseLong(attrs, "itemCount");

				final MissionLevelStepInfo rewardStep = new MissionLevelStepInfo(level, new ItemHolder(itemId, itemCount), null);
				rewards.put(level, rewardStep);
			});
			forEach(seasonNode, "keyReward", keyReward ->
			{
				final NamedNodeMap attrs = keyReward.getAttributes();

				final int level = parseInteger(attrs, "level");
				final int itemId = parseInteger(attrs, "itemId");
				final long itemCount = parseLong(attrs, "itemCount");

				MissionLevelStepInfo rewardStep = rewards.get(level);
				if (rewardStep == null) {
					rewardStep = new MissionLevelStepInfo(level, null, new ItemHolder(itemId, itemCount));
				} else {
					rewardStep = new MissionLevelStepInfo(level, rewardStep.getBaseRewardItem(), new ItemHolder(itemId, itemCount));
				}
				rewards.put(level, rewardStep);
			});
			forEach(seasonNode, "specialReward", specialReward ->
			{
				final NamedNodeMap attrs = specialReward.getAttributes();
				season.setSpecialRewardItem(new ItemHolder(parseInteger(attrs, "itemId"), parseLong(attrs, "itemCount")));
			});
			forEach(seasonNode, "extraReward", extraReward ->
			{
				final NamedNodeMap attrs = extraReward.getAttributes();
				season.setExtraRewardItem(new ItemHolder(parseInteger(attrs, "itemId"), parseLong(attrs, "itemCount")));
			});
			season.setSeasonRewards(rewards);
		}));
	}

	public enum MissionLevelRewardState {
		UNAVAILABLE,
		AVAILABLE,
		ALREADY_RECEIVED
	}

	public enum MissionLevelRewardType {
		REWARD_NONE,
		REWARD_BASE,
		REWARD_KEY,
		REWARD_SPECIAL,
		REWARD_EXTRA;

		public static MissionLevelRewardType getById(int id) {
			for (MissionLevelRewardType type : values()) {
				if (type.ordinal() == id) {
					return type;
				}
			}
			return null;
		}
	}

	private static class SingletonHolder {
		protected static final MissionLevelData INSTANCE = new MissionLevelData();
	}

	public class MissionLevelSeasonInfo {
		private final int _seasonYear;
		private final int _seasonMonth;
		private final int _limitLv;
		private Map<Integer, MissionLevelStepInfo> _seasonRewards;
		private ItemHolder _specialRewardItem = null;
		private ItemHolder _extraRewardItem = null;

		private int _rewardCount = 0;

		public MissionLevelSeasonInfo(int clientDate, int limitLv) {
			_seasonYear = Integer.parseInt(String.valueOf(clientDate).substring(0, 4));
			_seasonMonth = Integer.parseInt(String.valueOf(clientDate).substring(4));
			_limitLv = limitLv;
		}

		public int getSeasonYear() {
			return _seasonYear;
		}

		public int getSeasonMonth() {
			return _seasonMonth;
		}

		public int getLimitLv() {
			return _limitLv;
		}

		public int getRewardCount() {
			return _rewardCount;
		}

		public Map<Integer, MissionLevelStepInfo> getSeasonRewards() {
			return _seasonRewards;
		}

		public void setSeasonRewards(Map<Integer, MissionLevelStepInfo> seasonRewards) {
			_seasonRewards = seasonRewards;

			for (MissionLevelStepInfo step : seasonRewards.values()) {
				if (step.getBaseRewardItem() != null) {
					_rewardCount++;
				}
				if (step.getKeyRewardItem() != null) {
					_rewardCount++;
				}
			}
		}

		public ItemHolder getSpecialRewardItem() {
			return _specialRewardItem;
		}

		public void setSpecialRewardItem(ItemHolder specialRewardItem) {
			_specialRewardItem = specialRewardItem;
		}

		public ItemHolder getExtraRewardItem() {
			return _extraRewardItem;
		}

		public void setExtraRewardItem(ItemHolder extraRewardItem) {
			_extraRewardItem = extraRewardItem;
		}
	}

	public class MissionLevelStepInfo {
		private final int _level;
		private final ItemHolder _baseRewardItem;
		private final ItemHolder _keyRewardItem;

		public MissionLevelStepInfo(int level, ItemHolder baseRewardItem, ItemHolder keyRewardItem) {
			_level = level;
			_baseRewardItem = baseRewardItem;
			_keyRewardItem = keyRewardItem;
		}

		public int getLevel() {
			return _level;
		}

		public ItemHolder getBaseRewardItem() {
			return _baseRewardItem;
		}

		public ItemHolder getKeyRewardItem() {
			return _keyRewardItem;
		}
	}
}
