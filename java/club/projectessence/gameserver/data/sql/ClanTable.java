/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.data.sql;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.communitybbs.Manager.ForumsBBSManager;
import club.projectessence.gameserver.data.xml.ClanHallData;
import club.projectessence.gameserver.enums.ClanWarState;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.UserInfoType;
import club.projectessence.gameserver.instancemanager.ClanEntryManager;
import club.projectessence.gameserver.instancemanager.IdManager;
import club.projectessence.gameserver.instancemanager.SiegeManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.model.clan.ClanPrivilege;
import club.projectessence.gameserver.model.clan.ClanWar;
import club.projectessence.gameserver.model.clan.dkp.DkpStarItemFactor;
import club.projectessence.gameserver.model.clan.entry.PledgeRecruitInfo;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.clan.OnClanWarFinish;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerClanCreate;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerClanDestroy;
import club.projectessence.gameserver.model.residences.ClanHall;
import club.projectessence.gameserver.model.siege.Siege;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.PledgeShowInfoUpdate;
import club.projectessence.gameserver.network.serverpackets.PledgeShowMemberListAll;
import club.projectessence.gameserver.network.serverpackets.PledgeShowMemberListUpdate;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.util.EnumIntBitmask;
import club.projectessence.gameserver.util.Util;

/**
 * This class loads the clan related data.
 */
public class ClanTable
{
	private static final Logger			LOGGER	= Logger.getLogger(ClanTable.class.getName());
	private final Map<Integer, Clan>	_clans	= new ConcurrentHashMap<>();
	
	protected ClanTable()
	{
		// forums has to be loaded before clan data, because of last forum id used should have also memo included
		if (Config.ENABLE_COMMUNITY_BOARD)
		{
			ForumsBBSManager.getInstance().initRoot();
		}
		// Get all clan ids.
		final List<Integer> cids = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection(); Statement s = con.createStatement(); ResultSet rs = s.executeQuery("SELECT clan_id FROM clan_data"))
		{
			while (rs.next())
			{
				cids.add(rs.getInt("clan_id"));
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Error restoring ClanTable.", e);
		}
		// Create clans.
		for (int cid : cids)
		{
			final Clan clan = new Clan(cid);
			_clans.put(cid, clan);
			if (clan.getDissolvingExpiryTime() != 0)
			{
				scheduleRemoveClan(clan.getId());
			}
		}
		LOGGER.info(getClass().getSimpleName() + ": Restored " + cids.size() + " clans from the database.");
		allianceCheck();
		restoreClanWars();
		// Apply new leaders
		for (Clan clan : getClans())
		{
			if (clan.getNewLeaderId() != 0)
			{
				final ClanMember member = clan.getClanMember(clan.getNewLeaderId());
				if (member == null)
				{
					continue;
				}
				String oldLeaderName = clan.getLeaderName();
				clan.setNewLeader(member);
				LOGGER.info(getClass().getSimpleName() + ": Changed clan leader [Clan: " + clan.getName() + "] [Old Leader: " + oldLeaderName + "] [New Leader: " + clan.getLeaderName() + "]");
			}
		}
		List<Integer> usedIds = new ArrayList<>();
		LOGGER.info("Highest " + DkpStarItemFactor.class.getSimpleName() + " id: " + DkpStarItemFactor.getHighestId());
		for (int i = 0; i < DkpStarItemFactor.CACHE.length; i++)
		{
			usedIds.add(DkpStarItemFactor.CACHE[i].getId());
			for (int j = 0; j < DkpStarItemFactor.CACHE.length; j++)
			{
				if (i == j)
				{
					continue;
				}
				if (DkpStarItemFactor.CACHE[i].getId() == DkpStarItemFactor.CACHE[j].getId())
				{
					LOGGER.warning(getClass().getSimpleName() + ": Two identical ids " + DkpStarItemFactor.CACHE[i].getId() + " used in " + DkpStarItemFactor.class.getSimpleName());
					System.exit(0);
				}
			}
		}
		Collections.sort(usedIds);
		List<Integer> missingIds = new ArrayList<>();
		for (int i = 1; i < usedIds.size(); i++)
		{
			final int id1 = usedIds.get(i - 1);
			final int id2 = usedIds.get(i);
			for (int j = 1; j < (id2 - id1); j++)
			{
				missingIds.add(id1 + j);
			}
		}
		if (!missingIds.isEmpty())
		{
			String log = getClass().getSimpleName() + ": Missing ids = {";
			for (int id : missingIds)
			{
				log += id + ";";
			}
			log = log.substring(0, log.length() - 1) + "}";
			LOGGER.warning(log);
		}
		ThreadPool.get().scheduleAtFixedRate(this::saveClansDkp, 60_000, 60_000);
	}
	
	public static ClanTable getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private void saveClansDkp()
	{
		for (Clan clan : getClans())
		{
			if (clan == null)
			{
				continue;
			}
			clan.getDkp().save();
		}
	}
	
	/**
	 * Gets the clans.
	 *
	 * @return the clans
	 */
	public Collection<Clan> getClans()
	{
		return _clans.values();
	}
	
	/**
	 * Gets the clan count.
	 *
	 * @return the clan count
	 */
	public int getClanCount()
	{
		return _clans.size();
	}
	
	/**
	 * @param clanId
	 * @return
	 */
	public Clan getClan(int clanId)
	{
		return _clans.get(clanId);
	}
	
	public Clan getClanByName(String clanName)
	{
		for (Clan clan : _clans.values())
		{
			if (clan.getName().equalsIgnoreCase(clanName))
			{
				return clan;
			}
		}
		return null;
	}
	
	/**
	 * Creates a new clan and store clan info to database
	 *
	 * @param player
	 * @param clanName
	 * @return NULL if clan with same name already exists
	 */
	public Clan createClan(PlayerInstance player, String clanName)
	{
		if (player == null)
		{
			return null;
		}
		if (player.getLevel() < 10)
		{
			player.sendPacket(SystemMessageId.YOU_DO_NOT_MEET_THE_CRITERIA_IN_ORDER_TO_CREATE_A_CLAN);
			return null;
		}
		if (player.getClanId() != 0)
		{
			player.sendPacket(SystemMessageId.FAILED_TO_CREATE_A_CLAN);
			return null;
		}
		if (System.currentTimeMillis() < player.getClanCreateExpiryTime())
		{
			player.sendPacket(SystemMessageId.YOU_MUST_WAIT_10_DAYS_BEFORE_CREATING_A_NEW_CLAN);
			return null;
		}
		if (!Util.isAlphaNumeric(clanName) || (clanName.length() < 2))
		{
			player.sendPacket(SystemMessageId.CLAN_NAME_IS_INVALID);
			return null;
		}
		if (clanName.length() > 16)
		{
			player.sendPacket(SystemMessageId.CLAN_NAME_S_LENGTH_IS_INCORRECT);
			return null;
		}
		if (getClanByName(clanName) != null)
		{
			// clan name is already taken
			final SystemMessage sm = new SystemMessage(SystemMessageId.S1_ALREADY_EXISTS);
			sm.addString(clanName);
			player.sendPacket(sm);
			return null;
		}
		// Thêm điều kiện: Người tạo phải có faction khác NONE
		if (player.getFaction() == Faction.NONE)
		{
			player.sendPacket(SystemMessageId.FAILED_TO_CREATE_A_CLAN);
			return null;
		}
		final Clan clan = new Clan(IdManager.getInstance().getNextId(), clanName);
		clan.setFaction(player.getFaction()); // Set clan faction based on leader's faction
		final ClanMember leader = new ClanMember(clan, player);
		clan.setLeader(leader);
		leader.setPlayerInstance(player);
		clan.store();
		player.setClan(clan);
		player.setPledgeClass(ClanMember.calculatePledgeClass(player));
		player.setClanPrivileges(new EnumIntBitmask<>(ClanPrivilege.class, true));
		_clans.put(clan.getId(), clan);
		// Add to Clan Entry
		ClanEntryManager.getInstance().addToClanList(clan.getId(), new PledgeRecruitInfo(clan.getId(), 3, "", "", 0, 0));
		// should be update packet only
		player.sendPacket(new PledgeShowInfoUpdate(clan));
		PledgeShowMemberListAll.sendAllTo(player);
		player.sendPacket(new PledgeShowMemberListUpdate(player));
		player.sendPacket(SystemMessageId.YOUR_CLAN_HAS_BEEN_CREATED);
		player.broadcastUserInfo(UserInfoType.RELATION, UserInfoType.CLAN);
		player.getVariables().set(PlayerVariables.CLAN_JOINED_TODAY, true);
		player.getVariables().set(PlayerVariables.CLAN_DONATIONS_MADE, Clan.MAX_DAILY_DONATIONS);
		clan.restoreDkpFromDb();
		// Notify to scripts
		EventDispatcher.getInstance().notifyEventAsync(new OnPlayerClanCreate(player, clan));
		return clan;
	}
	
	public synchronized void destroyClan(int clanId)
	{
		final Clan clan = getClan(clanId);
		if (clan == null)
		{
			return;
		}
		clan.broadcastToOnlineMembers(new SystemMessage(SystemMessageId.CLAN_HAS_DISPERSED));
		ClanEntryManager.getInstance().removeFromClanList(clan.getId());
		final int castleId = clan.getCastleId();
		if (castleId == 0)
		{
			for (Siege siege : SiegeManager.getInstance().getSieges())
			{
				siege.removeSiegeClan(clan);
			}
		}
		// final int fortId = clan.getFortId();
		// if (fortId == 0)
		// {
		// for (FortSiege siege : FortSiegeManager.getInstance().getSieges())
		// {
		// siege.removeAttacker(clan);
		// }
		// }
		final ClanHall hall = ClanHallData.getInstance().getClanHallByClan(clan);
		if (hall != null)
		{
			hall.setOwner(null);
		}
		final ClanMember leaderMember = clan.getLeader();
		if (leaderMember == null)
		{
			clan.getWarehouse().destroyAllItems("ClanRemove", null, null);
			clan.getDkpShopWarehouse().destroyAllItems("ClanRemove", null, null);
			clan.getDkpAuctionWarehouse().destroyAllItems("ClanRemove", null, null);
		}
		else
		{
			clan.getWarehouse().destroyAllItems("ClanRemove", clan.getLeader().getPlayerInstance(), null);
			clan.getDkpShopWarehouse().destroyAllItems("ClanRemove", clan.getLeader().getPlayerInstance(), null);
			clan.getDkpAuctionWarehouse().destroyAllItems("ClanRemove", clan.getLeader().getPlayerInstance(), null);
		}
		for (ClanMember member : clan.getMembers())
		{
			clan.removeClanMember(member.getObjectId(), 0);
		}
		_clans.remove(clanId);
		IdManager.getInstance().releaseId(clanId);
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_data WHERE clan_id=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_privs WHERE clan_id=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_skills WHERE clan_id=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_subpledges WHERE clan_id=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_wars WHERE clan1=? OR clan2=?"))
			{
				ps.setInt(1, clanId);
				ps.setInt(2, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_notices WHERE clan_id=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_data WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_item_price WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_player_data WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_player_history WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_shop_history WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_auction_history WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_items WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_skills WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_classes WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_chars WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_dkp_auction_settings_presets WHERE clanId=?"))
			{
				ps.setInt(1, clanId);
				ps.execute();
			}
			// if (fortId != 0)
			// {
			// final Fort fort = FortManager.getInstance().getFortById(fortId);
			// if (fort != null)
			// {
			// final Clan owner = fort.getOwnerClan();
			// if (clan == owner)
			// {
			// fort.removeOwner(true);
			// }
			// }
			// }
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, getClass().getSimpleName() + ": Error removing clan from DB.", e);
		}
		// Notify to scripts
		EventDispatcher.getInstance().notifyEventAsync(new OnPlayerClanDestroy(leaderMember, clan));
	}
	
	public void scheduleRemoveClan(int clanId)
	{
		ThreadPool.get().schedule(() ->
		{
			if (getClan(clanId) == null)
			{
				return;
			}
			if (getClan(clanId).getDissolvingExpiryTime() != 0)
			{
				destroyClan(clanId);
			}
		}, Math.max(getClan(clanId).getDissolvingExpiryTime() - System.currentTimeMillis(), 300000));
	}
	
	public boolean isAllyExists(String allyName)
	{
		for (Clan clan : _clans.values())
		{
			if ((clan.getAllyName() != null) && clan.getAllyName().equalsIgnoreCase(allyName))
			{
				return true;
			}
		}
		return false;
	}
	
	public void storeClanWars(ClanWar war)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("REPLACE INTO clan_wars (clan1, clan2, clan1Kill, clan2Kill, winnerClan, startTime, endTime, state) VALUES(?,?,?,?,?,?,?,?)"))
		{
			ps.setInt(1, war.getAttackerClanId());
			ps.setInt(2, war.getAttackedClanId());
			ps.setInt(3, war.getAttackerKillCount());
			ps.setInt(4, war.getAttackedKillCount());
			ps.setInt(5, war.getWinnerClanId());
			ps.setLong(6, war.getStartTime());
			ps.setLong(7, war.getEndTime());
			ps.setInt(8, war.getState().ordinal());
			ps.execute();
		}
		catch (Exception e)
		{
			LOGGER.severe("Error storing clan wars data: " + e);
		}
	}
	
	public void deleteClanWars(int clanId1, int clanId2)
	{
		final Clan clan1 = getInstance().getClan(clanId1);
		final Clan clan2 = getInstance().getClan(clanId2);
		EventDispatcher.getInstance().notifyEventAsync(new OnClanWarFinish(clan1, clan2));
		clan1.deleteWar(clan2.getId());
		clan2.deleteWar(clan1.getId());
		clan1.broadcastClanStatus();
		clan2.broadcastClanStatus();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_wars WHERE clan1=? AND clan2=?"))
			{
				ps.setInt(1, clanId2);
				ps.setInt(2, clanId1);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_wars WHERE clan1=? AND clan2=?"))
			{
				ps.setInt(1, clanId1);
				ps.setInt(2, clanId2);
				ps.execute();
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, getClass().getSimpleName() + ": Error removing clan wars data.", e);
		}
	}
	
	private void restoreClanWars()
	{
		try (Connection con = DatabaseFactory.getConnection(); Statement statement = con.createStatement(); ResultSet rset = statement.executeQuery("SELECT clan1, clan2, clan1Kill, clan2Kill, winnerClan, startTime, endTime, state FROM clan_wars"))
		{
			while (rset.next())
			{
				final Clan attacker = getClan(rset.getInt("clan1"));
				final Clan attacked = getClan(rset.getInt("clan2"));
				if ((attacker != null) && (attacked != null))
				{
					final ClanWarState state = ClanWarState.values()[rset.getInt("state")];
					final ClanWar clanWar = new ClanWar(attacker, attacked, rset.getInt("clan1Kill"), rset.getInt("clan2Kill"), rset.getInt("winnerClan"), rset.getLong("startTime"), rset.getLong("endTime"), state);
					attacker.addWar(attacked.getId(), clanWar);
					attacked.addWar(attacker.getId(), clanWar);
				}
				else
				{
					LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Restorewars one of clans is null attacker:" + attacker + " attacked:" + attacked);
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, getClass().getSimpleName() + ": Error restoring clan wars data.", e);
		}
	}
	
	/**
	 * Check for nonexistent alliances
	 */
	private void allianceCheck()
	{
		for (Clan clan : _clans.values())
		{
			final int allyId = clan.getAllyId();
			if ((allyId != 0) && (clan.getId() != allyId) && !_clans.containsKey(allyId))
			{
				clan.setAllyId(0);
				clan.setAllyName(null);
				clan.changeAllyCrest(0, true);
				clan.updateClanInDB();
				LOGGER.info(getClass().getSimpleName() + ": Removed alliance from clan: " + clan);
			}
		}
	}
	
	public List<Clan> getClanAllies(int allianceId)
	{
		final List<Clan> clanAllies = new ArrayList<>();
		if (allianceId != 0)
		{
			for (Clan clan : _clans.values())
			{
				if ((clan != null) && (clan.getAllyId() == allianceId))
				{
					clanAllies.add(clan);
				}
			}
		}
		return clanAllies;
	}
	
	public void shutdown()
	{
		for (Clan clan : _clans.values())
		{
			clan.updateInDB();
			for (ClanWar war : clan.getWarList().values())
			{
				storeClanWars(war);
			}
			clan.getDkp().save();
		}
	}
	
	public List<ClanWar> getClanWars()
	{
		List<ClanWar> wars = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection(); Statement statement = con.createStatement(); ResultSet rset = statement.executeQuery("SELECT clan1, clan2, clan1Kill, clan2Kill, winnerClan, startTime, endTime, state FROM clan_wars"))
		{
			while (rset.next())
			{
				final Clan attacker = getClan(rset.getInt("clan1"));
				final Clan attacked = getClan(rset.getInt("clan2"));
				if (attacker != null && attacked != null)
				{
					final ClanWarState state = ClanWarState.values()[rset.getInt("state")];
					final ClanWar clanWar = new ClanWar(attacker, attacked, rset.getInt("clan1Kill"), rset.getInt("clan2Kill"), rset.getInt("winnerClan"), rset.getLong("startTime"), rset.getLong("endTime"), state);
					wars.add(clanWar);
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, getClass().getSimpleName() + ": Error retrieving clan wars data.", e);
		}
		return wars;
	}
	
	private static class SingletonHolder
	{
		protected static final ClanTable INSTANCE = new ClanTable();
	}
}
