/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.handler;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class UserCommandHandler implements IHandler<IUserCommandHandler, Integer> {
	private final Map<Integer, IUserCommandHandler> _datatable;

	protected UserCommandHandler() {
		_datatable = new HashMap<>();
	}

	public static UserCommandHandler getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public void registerHandler(IUserCommandHandler handler) {
		for (int id : handler.getUserCommandList()) {
			_datatable.put(id, handler);
		}
	}

	@Override
	public synchronized void removeHandler(IUserCommandHandler handler) {
		for (int id : handler.getUserCommandList()) {
			_datatable.remove(id);
		}
	}

	@Override
	public IUserCommandHandler getHandler(Integer userCommand) {
		return _datatable.get(userCommand);
	}

	@Override
	public int size() {
		return _datatable.size();
	}

	private static class SingletonHolder {
		protected static final UserCommandHandler INSTANCE = new UserCommandHandler();
	}
}
