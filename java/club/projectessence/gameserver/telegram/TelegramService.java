package club.projectessence.gameserver.telegram;

import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Telegram Service for managing bot lifecycle
 * Handles initialization and shutdown of Telegram bot components
 * 
 * <AUTHOR> Team
 */
public class TelegramService
{
    private static final Logger LOGGER = Logger.getLogger(TelegramService.class.getName());
    private static boolean _initialized = false;
    private static boolean _running = false;
    
    /**
     * Initialize Telegram service
     */
    public static void initialize()
    {
        if (_initialized) {
            LOGGER.warning("TelegramService already initialized");
            return;
        }
        
        try {
            LOGGER.info("Initializing Telegram Service...");
            
            // Validate configuration
            if (!TelegramConfig.validateConfig()) {
                LOGGER.severe("Telegram configuration validation failed");
                return;
            }
            
            // Log configuration summary
            LOGGER.info(TelegramConfig.getConfigSummary());
            
            if (!TelegramConfig.TELEGRAM_BOT_ENABLED) {
                LOGGER.info("Telegram bot is disabled in configuration");
                _initialized = true;
                return;
            }
            
            if (!TelegramConfig.isProperlyConfigured()) {
                LOGGER.warning("Telegram bot is enabled but not properly configured");
                _initialized = true;
                return;
            }
            
            // Initialize bot manager
            TelegramBotManager.getInstance();
            
            _initialized = true;
            LOGGER.info("Telegram Service initialized successfully");
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Failed to initialize Telegram Service", e);
        }
    }
    
    /**
     * Start Telegram service
     */
    public static void start()
    {
        if (!_initialized) {
            LOGGER.warning("TelegramService not initialized, cannot start");
            return;
        }
        
        if (_running) {
            LOGGER.warning("TelegramService already running");
            return;
        }
        
        if (!TelegramConfig.TELEGRAM_BOT_ENABLED || !TelegramConfig.isProperlyConfigured()) {
            LOGGER.info("Telegram bot not enabled or configured, skipping start");
            return;
        }
        
        try {
            LOGGER.info("Starting Telegram Service...");
            
            if (TelegramConfig.USE_WEBHOOK) {
                // Start webhook server
                TelegramWebhookHandler.startWebhookServer();
                LOGGER.info("Telegram webhook server started on port " + TelegramConfig.WEBHOOK_PORT);
            } else {
                // TODO: Implement polling mode if needed
                LOGGER.info("Polling mode not implemented, use webhook mode");
            }
            
            _running = true;
            LOGGER.info("Telegram Service started successfully");
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Failed to start Telegram Service", e);
        }
    }
    
    /**
     * Stop Telegram service
     */
    public static void stop()
    {
        if (!_running) {
            LOGGER.info("TelegramService not running");
            return;
        }
        
        try {
            LOGGER.info("Stopping Telegram Service...");
            
            // Stop webhook server
            TelegramWebhookHandler.stopWebhookServer();
            
            _running = false;
            LOGGER.info("Telegram Service stopped successfully");
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Failed to stop Telegram Service", e);
        }
    }
    
    /**
     * Shutdown Telegram service
     */
    public static void shutdown()
    {
        LOGGER.info("Shutting down Telegram Service...");
        
        if (_running) {
            stop();
        }
        
        _initialized = false;
        LOGGER.info("Telegram Service shutdown complete");
    }
    
    /**
     * Restart Telegram service
     */
    public static void restart()
    {
        LOGGER.info("Restarting Telegram Service...");
        
        if (_running) {
            stop();
        }
        
        // Reload configuration
        TelegramConfig.reloadConfig();
        
        // Start again
        start();
    }
    
    /**
     * Check if service is initialized
     */
    public static boolean isInitialized()
    {
        return _initialized;
    }
    
    /**
     * Check if service is running
     */
    public static boolean isRunning()
    {
        return _running;
    }
    
    /**
     * Get service status
     */
    public static String getStatus()
    {
        if (!_initialized) {
            return "Not Initialized";
        } else if (!TelegramConfig.TELEGRAM_BOT_ENABLED) {
            return "Disabled";
        } else if (!TelegramConfig.isProperlyConfigured()) {
            return "Not Configured";
        } else if (_running) {
            return "Running";
        } else {
            return "Stopped";
        }
    }
}
