package club.projectessence.gameserver.telegram;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executors;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Telegram Webhook Handler for receiving bot messages
 * Handles incoming webhook requests from Telegram Bot API
 * 
 * <AUTHOR> Team
 */
public class TelegramWebhookHandler implements HttpHandler
{
    private static final Logger LOGGER = Logger.getLogger(TelegramWebhookHandler.class.getName());
    private static HttpServer server;
    private static final int WEBHOOK_PORT = 8443; // <PERSON><PERSON><PERSON> hình port
    private static final String WEBHOOK_PATH = "/telegram-webhook"; // Cấu hình path
    
    /**
     * Start webhook server
     */
    public static void startWebhookServer()
    {
        try {
            server = HttpServer.create(new InetSocketAddress(WEBHOOK_PORT), 0);
            server.createContext(WEBHOOK_PATH, new TelegramWebhookHandler());
            server.setExecutor(Executors.newFixedThreadPool(4));
            server.start();
            
            LOGGER.info("Telegram webhook server started on port " + WEBHOOK_PORT + " with path " + WEBHOOK_PATH);
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "Failed to start Telegram webhook server", e);
        }
    }
    
    /**
     * Stop webhook server
     */
    public static void stopWebhookServer()
    {
        if (server != null) {
            server.stop(0);
            LOGGER.info("Telegram webhook server stopped");
        }
    }
    
    @Override
    public void handle(HttpExchange exchange) throws IOException
    {
        try {
            if ("POST".equals(exchange.getRequestMethod())) {
                handleWebhookRequest(exchange);
            } else {
                // Return 405 Method Not Allowed for non-POST requests
                sendResponse(exchange, 405, "Method Not Allowed");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error handling webhook request", e);
            sendResponse(exchange, 500, "Internal Server Error");
        }
    }
    
    /**
     * Handle incoming webhook request
     */
    private void handleWebhookRequest(HttpExchange exchange) throws IOException
    {
        // Read request body
        StringBuilder requestBody = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
        }
        
        try {
            // Parse JSON
            JSONObject update = new JSONObject(requestBody.toString());
            
            // Check if it's a message update
            if (update.has("message")) {
                JSONObject message = update.getJSONObject("message");
                
                // Extract message data
                long chatId = message.getJSONObject("chat").getLong("id");
                long userId = message.getJSONObject("from").getLong("id");
                String username = message.getJSONObject("from").optString("username", "unknown");
                String text = message.optString("text", "");
                
                // Log incoming message
                LOGGER.info("Received Telegram message from " + username + " (ID: " + userId + "): " + text);
                
                // Process message through TelegramBotManager
                TelegramBotManager.processMessage(chatId, userId, text, username);
                
                // Send 200 OK response
                sendResponse(exchange, 200, "OK");
            } else {
                // Not a message update, ignore
                sendResponse(exchange, 200, "OK");
            }
            
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Error parsing webhook JSON: " + requestBody.toString(), e);
            sendResponse(exchange, 400, "Bad Request");
        }
    }
    
    /**
     * Send HTTP response
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException
    {
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }
}
