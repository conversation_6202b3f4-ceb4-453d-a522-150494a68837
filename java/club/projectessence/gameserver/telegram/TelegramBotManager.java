package club.projectessence.gameserver.telegram;

import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.serverpackets.primeshop.ExBRGamePoint;
import club.projectessence.gameserver.util.BuilderUtil;
import club.projectessence.gameserver.data.sql.CharNameTable;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.Executors;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import club.projectessence.commons.database.DatabaseFactory;

/**
 * Telegram Bot Manager for Prime Points Management
 * Handles remote Prime Points addition via Telegram bot commands
 * 
 * <AUTHOR> Team
 */
public class TelegramBotManager
{
    private static final Logger LOGGER = Logger.getLogger(TelegramBotManager.class.getName());
    
    // Security Configuration
    private static final ConcurrentHashMap<Long, String> AUTHORIZED_ADMINS = new ConcurrentHashMap<>();
    private static final Pattern COMMAND_PATTERN = Pattern.compile("^/addprime\\s+(\\w+)\\s+(\\d+)$", Pattern.CASE_INSENSITIVE);
    private static final Pattern ACCOUNT_COMMAND_PATTERN = Pattern.compile("^/addprime_acc\\s+(\\w+)\\s+(\\d+)$", Pattern.CASE_INSENSITIVE);

    // Thread pool for async operations
    private static final ThreadPoolExecutor EXECUTOR = (ThreadPoolExecutor) Executors.newFixedThreadPool(3);

    // Rate limiting
    private static final ConcurrentHashMap<Long, Long> LAST_COMMAND_TIME = new ConcurrentHashMap<>();

    static {
        // Initialize authorized admins (load from config)
        initializeAuthorizedAdmins();
    }
    
    /**
     * Initialize authorized admin list from config
     */
    private static void initializeAuthorizedAdmins()
    {
        // Load admin chat IDs
        long[] adminChatIds = TelegramConfig.getAdminChatIds();
        for (long chatId : adminChatIds) {
            if (chatId != 0) {
                AUTHORIZED_ADMINS.put(chatId, "ChatID_" + chatId);
            }
        }

        // Load admin usernames
        String[] adminUsernames = TelegramConfig.getAdminUsernames();
        for (String username : adminUsernames) {
            if (!username.trim().isEmpty()) {
                AUTHORIZED_ADMINS.put(0L, username.trim()); // Use 0L as placeholder for username-based auth
            }
        }

        LOGGER.info("TelegramBotManager: Initialized " + AUTHORIZED_ADMINS.size() + " authorized admins");
    }
    
    /**
     * Process incoming Telegram message
     * @param chatId Telegram chat ID
     * @param userId Telegram user ID  
     * @param message Message content
     * @param username Telegram username
     */
    public static void processMessage(long chatId, long userId, String message, String username)
    {
        EXECUTOR.submit(() -> {
            try {
                handleMessageAsync(chatId, userId, message, username);
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error processing Telegram message", e);
                sendTelegramMessage(chatId, "❌ Đã xảy ra lỗi khi xử lý lệnh.");
            }
        });
    }
    
    /**
     * Handle message asynchronously
     */
    private static void handleMessageAsync(long chatId, long userId, String message, String username)
    {
        // Check authorization
        if (!isAuthorized(userId, username)) {
            sendTelegramMessage(chatId, "❌ Bạn không có quyền sử dụng bot này.");
            LOGGER.warning("Unauthorized access attempt from user: " + username + " (ID: " + userId + ")");
            return;
        }
        
        // Check rate limiting
        if (!checkRateLimit(userId)) {
            sendTelegramMessage(chatId, "⏰ Vui lòng đợi " + (TelegramConfig.COMMAND_COOLDOWN/1000) + " giây trước khi sử dụng lệnh tiếp theo.");
            return;
        }
        
        // Process commands
        if (message.startsWith("/addprime ")) {
            handleAddPrimeCommand(chatId, message, username);
        } else if (message.startsWith("/addprime_acc ")) {
            handleAddPrimeAccountCommand(chatId, message, username);
        } else if (message.equals("/help")) {
            sendHelpMessage(chatId);
        } else {
            sendTelegramMessage(chatId, "❓ Lệnh không hợp lệ. Sử dụng /help để xem danh sách lệnh.");
        }
    }
    
    /**
     * Handle /addprime command (by character name)
     */
    private static void handleAddPrimeCommand(long chatId, String message, String adminName)
    {
        Matcher matcher = COMMAND_PATTERN.matcher(message.trim());
        if (!matcher.matches()) {
            sendTelegramMessage(chatId, "❌ Cú pháp sai. Sử dụng: /addprime <tên_nhân_vật> <số_prime_points>");
            return;
        }
        
        String characterName = matcher.group(1);
        int primePoints;
        
        try {
            primePoints = Integer.parseInt(matcher.group(2));
            if (primePoints <= 0 || primePoints > TelegramConfig.MAX_PRIME_POINTS_PER_COMMAND) {
                sendTelegramMessage(chatId, "❌ Số Prime Points phải từ 1 đến " + TelegramConfig.MAX_PRIME_POINTS_PER_COMMAND + ".");
                return;
            }
        } catch (NumberFormatException e) {
            sendTelegramMessage(chatId, "❌ Số Prime Points không hợp lệ.");
            return;
        }
        
        // Find player online
        PlayerInstance player = World.getInstance().getPlayer(characterName);
        if (player != null && player.isOnline()) {
            // Player is online - direct update
            addPrimePointsToOnlinePlayer(chatId, player, primePoints, adminName);
        } else {
            // Player is offline - update via database
            addPrimePointsToOfflinePlayer(chatId, characterName, primePoints, adminName);
        }
    }
    
    /**
     * Handle /addprime_acc command (by account name)
     */
    private static void handleAddPrimeAccountCommand(long chatId, String message, String adminName)
    {
        Matcher matcher = ACCOUNT_COMMAND_PATTERN.matcher(message.trim());
        if (!matcher.matches()) {
            sendTelegramMessage(chatId, "❌ Cú pháp sai. Sử dụng: /addprime_acc <tên_account> <số_prime_points>");
            return;
        }
        
        String accountName = matcher.group(1);
        int primePoints;
        
        try {
            primePoints = Integer.parseInt(matcher.group(2));
            if (primePoints <= 0 || primePoints > TelegramConfig.MAX_PRIME_POINTS_PER_COMMAND) {
                sendTelegramMessage(chatId, "❌ Số Prime Points phải từ 1 đến " + TelegramConfig.MAX_PRIME_POINTS_PER_COMMAND + ".");
                return;
            }
        } catch (NumberFormatException e) {
            sendTelegramMessage(chatId, "❌ Số Prime Points không hợp lệ.");
            return;
        }
        
        addPrimePointsToAccount(chatId, accountName, primePoints, adminName);
    }
    
    /**
     * Add Prime Points to online player
     */
    private static void addPrimePointsToOnlinePlayer(long chatId, PlayerInstance player, int primePoints, String adminName)
    {
        try {
            int currentPoints = player.getPrimePoints();
            int newPoints = Math.min(currentPoints + primePoints, Integer.MAX_VALUE);
            
            player.setPrimePoints(newPoints);
            
            // Send real-time update packet to player
            player.sendPacket(new ExBRGamePoint(player));
            
            // Send notification to player
            player.sendMessage("🎁 Bạn đã nhận được " + primePoints + " Prime Points từ Admin qua Telegram Bot!");
            
            // Log the action
            LOGGER.info("TelegramBot: Admin " + adminName + " added " + primePoints + " Prime Points to online player " + player.getName());
            
            // Send success message to Telegram
            sendTelegramMessage(chatId, 
                "✅ Đã thêm " + primePoints + " Prime Points cho nhân vật " + player.getName() + " (đang online)\n" +
                "📊 Prime Points hiện tại: " + newPoints);
                
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error adding Prime Points to online player", e);
            sendTelegramMessage(chatId, "❌ Lỗi khi thêm Prime Points cho nhân vật đang online.");
        }
    }
    
    /**
     * Add Prime Points to offline player (by character name)
     */
    private static void addPrimePointsToOfflinePlayer(long chatId, String characterName, int primePoints, String adminName)
    {
        try {
            // Get account name from character name
            String accountName = getAccountNameByCharacter(characterName);
            if (accountName == null) {
                sendTelegramMessage(chatId, "❌ Không tìm thấy nhân vật: " + characterName);
                return;
            }
            
            // Update Prime Points in database
            boolean success = updateOfflinePlayerPrimePoints(accountName, primePoints, true);
            if (success) {
                LOGGER.info("TelegramBot: Admin " + adminName + " added " + primePoints + " Prime Points to offline player " + characterName + " (account: " + accountName + ")");
                sendTelegramMessage(chatId, 
                    "✅ Đã thêm " + primePoints + " Prime Points cho nhân vật " + characterName + " (offline)\n" +
                    "ℹ️ Người chơi sẽ thấy Prime Points khi đăng nhập lại.");
            } else {
                sendTelegramMessage(chatId, "❌ Lỗi khi cập nhật Prime Points trong database.");
            }
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error adding Prime Points to offline player", e);
            sendTelegramMessage(chatId, "❌ Lỗi khi thêm Prime Points cho nhân vật offline.");
        }
    }
    
    /**
     * Add Prime Points directly to account
     */
    private static void addPrimePointsToAccount(long chatId, String accountName, int primePoints, String adminName)
    {
        try {
            // Check if any character from this account is online
            PlayerInstance onlinePlayer = findOnlinePlayerByAccount(accountName);
            
            if (onlinePlayer != null) {
                // Account has online player - direct update
                addPrimePointsToOnlinePlayer(chatId, onlinePlayer, primePoints, adminName);
                return;
            }
            
            // Account is offline - update via database
            boolean success = updateOfflinePlayerPrimePoints(accountName, primePoints, true);
            if (success) {
                LOGGER.info("TelegramBot: Admin " + adminName + " added " + primePoints + " Prime Points to account " + accountName);
                sendTelegramMessage(chatId, 
                    "✅ Đã thêm " + primePoints + " Prime Points cho account " + accountName + "\n" +
                    "ℹ️ Người chơi sẽ thấy Prime Points khi đăng nhập lại.");
            } else {
                sendTelegramMessage(chatId, "❌ Lỗi khi cập nhật Prime Points trong database.");
            }
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error adding Prime Points to account", e);
            sendTelegramMessage(chatId, "❌ Lỗi khi thêm Prime Points cho account.");
        }
    }
    
    /**
     * Get account name by character name
     */
    private static String getAccountNameByCharacter(String characterName)
    {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement("SELECT account_name FROM characters WHERE char_name = ?")) {
            
            ps.setString(1, characterName);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("account_name");
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting account name for character: " + characterName, e);
        }
        return null;
    }
    
    /**
     * Find online player by account name
     */
    private static PlayerInstance findOnlinePlayerByAccount(String accountName)
    {
        for (PlayerInstance player : World.getInstance().getPlayers()) {
            if (player != null && player.isOnline() && accountName.equals(player.getAccountName())) {
                return player;
            }
        }
        return null;
    }
    
    /**
     * Update Prime Points for offline player (reuse existing method from AdminPrimePoints)
     */
    private static boolean updateOfflinePlayerPrimePoints(String accountName, int points, boolean isIncrease)
    {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement checkExistence = con.prepareStatement("SELECT COUNT(*) FROM account_gsdata WHERE var = 'PRIME_POINTS' AND account_name = ?");
             PreparedStatement updatePrimePoints = con.prepareStatement("UPDATE account_gsdata SET value = GREATEST(value + ?, 0) WHERE var = 'PRIME_POINTS' AND account_name = ?");
             PreparedStatement insertPrimePoints = con.prepareStatement("INSERT INTO account_gsdata (account_name, var, value) VALUES (?, 'PRIME_POINTS', ?)")) {
            
            // Check if record exists
            checkExistence.setString(1, accountName);
            try (ResultSet rs = checkExistence.executeQuery()) {
                if (rs.next() && rs.getInt(1) == 0) {
                    // Record doesn't exist, insert new one
                    insertPrimePoints.setString(1, accountName);
                    insertPrimePoints.setInt(2, isIncrease ? points : Math.max(points, 0));
                    insertPrimePoints.executeUpdate();
                    return true;
                }
            }
            
            // Record exists, update it
            updatePrimePoints.setInt(1, isIncrease ? points : points);
            updatePrimePoints.setString(2, accountName);
            int rowsUpdated = updatePrimePoints.executeUpdate();
            return rowsUpdated > 0;
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating offline player Prime Points", e);
            return false;
        }
    }
    
    /**
     * Check if user is authorized
     */
    private static boolean isAuthorized(long userId, String username)
    {
        return AUTHORIZED_ADMINS.containsKey(userId) || 
               AUTHORIZED_ADMINS.containsValue(username);
    }
    
    /**
     * Check rate limiting
     */
    private static boolean checkRateLimit(long userId)
    {
        long currentTime = System.currentTimeMillis();
        Long lastTime = LAST_COMMAND_TIME.get(userId);
        
        if (lastTime != null && (currentTime - lastTime) < TelegramConfig.COMMAND_COOLDOWN) {
            return false;
        }
        
        LAST_COMMAND_TIME.put(userId, currentTime);
        return true;
    }
    
    /**
     * Send help message
     */
    private static void sendHelpMessage(long chatId)
    {
        String helpText = 
            "🤖 **GVE Prime Points Bot**\n\n" +
            "📋 **Danh sách lệnh:**\n" +
            "• `/addprime <tên_nhân_vật> <số_points>` - Thêm Prime Points cho nhân vật\n" +
            "• `/addprime_acc <tên_account> <số_points>` - Thêm Prime Points cho account\n" +
            "• `/help` - Hiển thị trợ giúp\n\n" +
            "⚠️ **Lưu ý:**\n" +
            "• Số Prime Points: 1-" + TelegramConfig.MAX_PRIME_POINTS_PER_COMMAND + "\n" +
            "• Cooldown: " + (TelegramConfig.COMMAND_COOLDOWN/1000) + " giây giữa các lệnh\n" +
            "• Chỉ Admin được phép sử dụng\n\n" +
            "✅ **Ví dụ:**\n" +
            "`/addprime PlayerName 1000`\n" +
            "`/addprime_acc account123 5000`";
            
        sendTelegramMessage(chatId, helpText);
    }
    
    /**
     * Send message to Telegram
     */
    private static void sendTelegramMessage(long chatId, String message)
    {
        if (!TelegramConfig.isProperlyConfigured()) {
            LOGGER.warning("Telegram bot not properly configured, cannot send message");
            return;
        }

        try {
            String telegramApiUrl = "https://api.telegram.org/bot" + TelegramConfig.BOT_TOKEN + "/";
            URL url = new URL(telegramApiUrl + "sendMessage");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setDoOutput(true);

            String jsonPayload = String.format(
                "{\"chat_id\":%d,\"text\":\"%s\",\"parse_mode\":\"Markdown\"}",
                chatId, message.replace("\"", "\\\"")
            );

            try (OutputStream os = conn.getOutputStream()) {
                os.write(jsonPayload.getBytes(StandardCharsets.UTF_8));
            }

            int responseCode = conn.getResponseCode();
            if (responseCode != 200) {
                LOGGER.warning("Failed to send Telegram message. Response code: " + responseCode);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error sending Telegram message", e);
        }
    }
    
    /**
     * Add authorized admin
     */
    public static void addAuthorizedAdmin(long userId, String username)
    {
        AUTHORIZED_ADMINS.put(userId, username);
        LOGGER.info("Added authorized admin: " + username + " (ID: " + userId + ")");
    }
    
    /**
     * Remove authorized admin
     */
    public static void removeAuthorizedAdmin(long userId)
    {
        String username = AUTHORIZED_ADMINS.remove(userId);
        if (username != null) {
            LOGGER.info("Removed authorized admin: " + username + " (ID: " + userId + ")");
        }
    }
    
    /**
     * Get singleton instance
     */
    public static TelegramBotManager getInstance()
    {
        return SingletonHolder.INSTANCE;
    }
    
    private static class SingletonHolder
    {
        protected static final TelegramBotManager INSTANCE = new TelegramBotManager();
    }
}
