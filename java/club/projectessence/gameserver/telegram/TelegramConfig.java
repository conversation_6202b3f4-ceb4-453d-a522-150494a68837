package club.projectessence.gameserver.telegram;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Telegram Bot Configuration Manager
 * Handles loading and managing Telegram bot settings
 * 
 * <AUTHOR> Team
 */
public class TelegramConfig
{
    private static final Logger LOGGER = Logger.getLogger(TelegramConfig.class.getName());
    private static final String CONFIG_FILE = "config/telegram.properties";
    
    // Configuration properties
    public static boolean TELEGRAM_BOT_ENABLED = false;
    public static String BOT_TOKEN = "";
    public static String WEBHOOK_URL = "";
    public static int WEBHOOK_PORT = 8443;
    public static String WEBHOOK_PATH = "/telegram-webhook";
    public static boolean USE_WEBHOOK = true;
    public static long COMMAND_COOLDOWN = 5000; // 5 seconds
    public static int MAX_PRIME_POINTS_PER_COMMAND = 100000;
    public static boolean LOG_ALL_COMMANDS = true;
    public static boolean REQUIRE_USERNAME = true;
    
    // Security settings
    public static String ADMIN_CHAT_IDS = ""; // Comma-separated list
    public static String ADMIN_USERNAMES = ""; // Comma-separated list
    public static boolean STRICT_AUTHORIZATION = true;
    
    static {
        loadConfig();
    }
    
    /**
     * Load configuration from file
     */
    private static void loadConfig()
    {
        Properties props = new Properties();
        
        try (FileInputStream fis = new FileInputStream(CONFIG_FILE)) {
            props.load(fis);
            
            // Load basic settings
            TELEGRAM_BOT_ENABLED = Boolean.parseBoolean(props.getProperty("TelegramBotEnabled", "false"));
            BOT_TOKEN = props.getProperty("BotToken", "");
            WEBHOOK_URL = props.getProperty("WebhookUrl", "");
            WEBHOOK_PORT = Integer.parseInt(props.getProperty("WebhookPort", "8443"));
            WEBHOOK_PATH = props.getProperty("WebhookPath", "/telegram-webhook");
            USE_WEBHOOK = Boolean.parseBoolean(props.getProperty("UseWebhook", "true"));
            
            // Load limits and cooldowns
            COMMAND_COOLDOWN = Long.parseLong(props.getProperty("CommandCooldown", "5000"));
            MAX_PRIME_POINTS_PER_COMMAND = Integer.parseInt(props.getProperty("MaxPrimePointsPerCommand", "100000"));
            
            // Load logging settings
            LOG_ALL_COMMANDS = Boolean.parseBoolean(props.getProperty("LogAllCommands", "true"));
            REQUIRE_USERNAME = Boolean.parseBoolean(props.getProperty("RequireUsername", "true"));
            
            // Load security settings
            ADMIN_CHAT_IDS = props.getProperty("AdminChatIds", "");
            ADMIN_USERNAMES = props.getProperty("AdminUsernames", "");
            STRICT_AUTHORIZATION = Boolean.parseBoolean(props.getProperty("StrictAuthorization", "true"));
            
            LOGGER.info("Telegram configuration loaded successfully");
            
            if (TELEGRAM_BOT_ENABLED && BOT_TOKEN.isEmpty()) {
                LOGGER.warning("Telegram bot is enabled but BOT_TOKEN is empty!");
            }
            
        } catch (IOException e) {
            LOGGER.log(Level.WARNING, "Could not load Telegram config file: " + CONFIG_FILE + ". Using default values.", e);
            createDefaultConfig();
        } catch (NumberFormatException e) {
            LOGGER.log(Level.WARNING, "Invalid number format in Telegram config", e);
        }
    }
    
    /**
     * Create default configuration file
     */
    private static void createDefaultConfig()
    {
        LOGGER.info("Creating default Telegram configuration...");
        
        // Set default values
        TELEGRAM_BOT_ENABLED = false;
        BOT_TOKEN = "YOUR_BOT_TOKEN_HERE";
        WEBHOOK_URL = "https://yourdomain.com/telegram-webhook";
        WEBHOOK_PORT = 8443;
        WEBHOOK_PATH = "/telegram-webhook";
        USE_WEBHOOK = true;
        COMMAND_COOLDOWN = 5000;
        MAX_PRIME_POINTS_PER_COMMAND = 100000;
        LOG_ALL_COMMANDS = true;
        REQUIRE_USERNAME = true;
        ADMIN_CHAT_IDS = "";
        ADMIN_USERNAMES = "";
        STRICT_AUTHORIZATION = true;
        
        // TODO: Write default config file
        // This would create a template config file for users to fill in
    }
    
    /**
     * Reload configuration
     */
    public static void reloadConfig()
    {
        LOGGER.info("Reloading Telegram configuration...");
        loadConfig();
    }
    
    /**
     * Check if Telegram bot is properly configured
     */
    public static boolean isProperlyConfigured()
    {
        return TELEGRAM_BOT_ENABLED && 
               !BOT_TOKEN.isEmpty() && 
               !BOT_TOKEN.equals("YOUR_BOT_TOKEN_HERE");
    }
    
    /**
     * Get admin chat IDs as array
     */
    public static long[] getAdminChatIds()
    {
        if (ADMIN_CHAT_IDS.isEmpty()) {
            return new long[0];
        }
        
        String[] parts = ADMIN_CHAT_IDS.split(",");
        long[] chatIds = new long[parts.length];
        
        for (int i = 0; i < parts.length; i++) {
            try {
                chatIds[i] = Long.parseLong(parts[i].trim());
            } catch (NumberFormatException e) {
                LOGGER.warning("Invalid chat ID in config: " + parts[i]);
                chatIds[i] = 0;
            }
        }
        
        return chatIds;
    }
    
    /**
     * Get admin usernames as array
     */
    public static String[] getAdminUsernames()
    {
        if (ADMIN_USERNAMES.isEmpty()) {
            return new String[0];
        }
        
        return ADMIN_USERNAMES.split(",");
    }
    
    /**
     * Validate configuration
     */
    public static boolean validateConfig()
    {
        if (!TELEGRAM_BOT_ENABLED) {
            return true; // Valid to be disabled
        }
        
        if (BOT_TOKEN.isEmpty() || BOT_TOKEN.equals("YOUR_BOT_TOKEN_HERE")) {
            LOGGER.severe("Invalid BOT_TOKEN in Telegram configuration");
            return false;
        }
        
        if (USE_WEBHOOK && WEBHOOK_URL.isEmpty()) {
            LOGGER.severe("Webhook is enabled but WEBHOOK_URL is empty");
            return false;
        }
        
        if (WEBHOOK_PORT < 1 || WEBHOOK_PORT > 65535) {
            LOGGER.severe("Invalid WEBHOOK_PORT: " + WEBHOOK_PORT);
            return false;
        }
        
        if (COMMAND_COOLDOWN < 0) {
            LOGGER.severe("Invalid COMMAND_COOLDOWN: " + COMMAND_COOLDOWN);
            return false;
        }
        
        if (MAX_PRIME_POINTS_PER_COMMAND < 1) {
            LOGGER.severe("Invalid MAX_PRIME_POINTS_PER_COMMAND: " + MAX_PRIME_POINTS_PER_COMMAND);
            return false;
        }
        
        return true;
    }
    
    /**
     * Get configuration summary for logging
     */
    public static String getConfigSummary()
    {
        return String.format(
            "Telegram Bot Config: Enabled=%s, UseWebhook=%s, Port=%d, Cooldown=%dms, MaxPoints=%d",
            TELEGRAM_BOT_ENABLED, USE_WEBHOOK, WEBHOOK_PORT, COMMAND_COOLDOWN, MAX_PRIME_POINTS_PER_COMMAND
        );
    }
}
