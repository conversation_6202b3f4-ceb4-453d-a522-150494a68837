package club.projectessence.gameserver.telegram;

import java.util.logging.Logger;

/**
 * Integration class for Telegram Bot with L2J Server
 * This class should be called from GameServer startup/shutdown
 * 
 * <AUTHOR> Team
 */
public class TelegramIntegration
{
    private static final Logger LOGGER = Logger.getLogger(TelegramIntegration.class.getName());
    
    /**
     * Initialize Telegram integration during server startup
     * Call this method from GameServer.main() or similar startup method
     */
    public static void onServerStartup()
    {
        LOGGER.info("Initializing Telegram Bot integration...");
        
        try {
            // Initialize Telegram service
            TelegramService.initialize();
            
            // Start the service if properly configured
            TelegramService.start();
            
            LOGGER.info("Telegram Bot integration initialized successfully");
            
        } catch (Exception e) {
            LOGGER.severe("Failed to initialize Telegram Bot integration: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Cleanup Telegram integration during server shutdown
     * Call this method from GameServer shutdown hook or similar
     */
    public static void onServerShutdown()
    {
        LOGGER.info("Shutting down Telegram Bot integration...");
        
        try {
            TelegramService.shutdown();
            LOGGER.info("Telegram Bot integration shutdown completed");
            
        } catch (Exception e) {
            LOGGER.severe("Error during Telegram Bot shutdown: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Handle server restart
     */
    public static void onServerRestart()
    {
        LOGGER.info("Restarting Telegram Bot integration...");
        
        try {
            TelegramService.restart();
            LOGGER.info("Telegram Bot integration restarted successfully");
            
        } catch (Exception e) {
            LOGGER.severe("Error during Telegram Bot restart: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
