/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.pledge;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExPledgeDonationInfo extends ServerPacket {
	private final PlayerInstance _player;

	public ExPledgeDonationInfo(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PLEDGE_DONATION_INFO, buffer);

		Clan clan = _player.getClan();
		if (clan == null) {
			return;
		}

		buffer.writeInt(Clan.MAX_DAILY_DONATIONS - _player.getVariables().getInt(PlayerVariables.CLAN_DONATIONS_MADE, 0));
		buffer.writeByte(_player.getVariables().getBoolean(PlayerVariables.CLAN_JOINED_TODAY, false) ? 0x01 : 0x00);
	}
}
