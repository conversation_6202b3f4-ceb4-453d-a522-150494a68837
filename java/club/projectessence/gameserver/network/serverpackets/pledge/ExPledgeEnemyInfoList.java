/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.pledge;

import java.util.Collection;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanWar;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExPledgeEnemyInfoList extends ServerPacket
{
	private final PlayerInstance _player;
	
	public ExPledgeEnemyInfoList(PlayerInstance player)
	{
		_player = player;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.EX_PLEDGE_ENEMY_INFO_LIST, buffer);
		Clan clan = _player.getClan();
		if (clan == null)
		{
			return;
		}
		Collection<ClanWar> clanList = clan.getWarList().values();
		buffer.writeInt(clanList.size());
		for (ClanWar clanWar : clanList)
		{
			final Clan enemy = clanWar.getOpposingClan(clan);
			if (enemy == null)
			{
				continue;
			}
			buffer.writeInt(Config.SERVER_ID);
			buffer.writeInt(enemy.getId());
			buffer.writeSizedString(enemy.getName());
			buffer.writeSizedString(enemy.getLeaderName());
		}
	}
}
