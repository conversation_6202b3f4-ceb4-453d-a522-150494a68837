/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.newhenna;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExNewHennaPotenEnchant extends ServerPacket {
	private final int _slotId;
	private final int _enchantStep;
	private final int _enchantExp;
	private final int _dailyStep;
	private final int _dailyCount;
	private final int _activeStep;
	private final boolean _success;
	private final int _localStep;
	private final int _localCount;

	public ExNewHennaPotenEnchant(int slotId, int enchantStep, int enchantExp, int dailyStep, int dailyCount, int activeStep, boolean success, int localStep, int localCount) {
		_slotId = slotId;
		_enchantStep = enchantStep;
		_enchantExp = enchantExp;
		_dailyStep = dailyStep;
		_dailyCount = dailyCount;
		_activeStep = activeStep;
		_success = success;
		_localStep = localStep;
		_localCount = localCount;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_NEW_HENNA_POTEN_ENCHANT, buffer);

		buffer.writeByte(_slotId); // cSlotID;
		buffer.writeShort(_enchantStep); // nEnchantStep;
		buffer.writeInt(_enchantExp); // nEnchantExp;
		buffer.writeShort(_dailyStep); // nDailyStep;
		buffer.writeShort(_dailyCount); // nDailyCount;
		buffer.writeShort(_activeStep); // nActiveStep;
		buffer.writeByte(_success); // cSuccess;
		buffer.writeShort(_localStep); // nSlotDailyStep (388)
		buffer.writeShort(_localCount); // nSlotDailyCount (388)
	}
}
