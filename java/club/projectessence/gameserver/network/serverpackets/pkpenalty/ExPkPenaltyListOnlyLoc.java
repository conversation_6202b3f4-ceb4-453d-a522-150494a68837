/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.pkpenalty;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Comparator;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ExPkPenaltyListOnlyLoc extends ServerPacket {
	private final int _lastPkTime;
	private final Set<PlayerInstance> _players;
	public ExPkPenaltyListOnlyLoc() {
		_lastPkTime = World.getInstance().getLastPkTime();
		_players = World.getInstance().getPkPlayers().stream().filter(p -> p.getEinhasadOverseeingLevel() >= 5).sorted(Comparator.comparing(Creature::getReputation)).limit(30).collect(Collectors.toSet());
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PK_PENALTY_LIST_ONLY_LOC, buffer);

		buffer.writeInt(_lastPkTime);
		buffer.writeInt(_players.size());
		for (PlayerInstance player : _players) {
			buffer.writeInt(player.getObjectId());
			buffer.writeInt(player.getX());
			buffer.writeInt(player.getY());
			buffer.writeInt(player.getZ());
		}
	}
}
