/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.friend;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.LinkedList;
import java.util.List;

/**
 * Support for "Chat with Friends" dialog. <br />
 * This packet is sent only at login.
 *
 * <AUTHOR>
 */
public class L2FriendList extends ServerPacket {
	private final List<FriendInfo> _info = new LinkedList<>();

	public L2FriendList(PlayerInstance player) {
		for (int objId : player.getFriendList()) {
			final String name = CharNameTable.getInstance().getNameById(objId);
			final PlayerInstance player1 = World.getInstance().getPlayer(objId);
			boolean online = false;
			int level = 0;
			int classId = 0;
			if (player1 != null) {
				online = true;
				level = player1.getLevel();
				classId = player1.getClassId().getId();
			} else {
				level = CharNameTable.getInstance().getLevelById(objId);
				classId = CharNameTable.getInstance().getClassIdById(objId);
			}
			_info.add(new FriendInfo(objId, name, online, level, classId));
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.L2_FRIEND_LIST, buffer);

		buffer.writeInt(_info.size());
		for (FriendInfo info : _info) {
			buffer.writeInt(info._objId); // character id
			buffer.writeString(info._name);
			buffer.writeInt(info._online ? 0x01 : 0x00); // online
			buffer.writeInt(info._online ? info._objId : 0x00); // object id if online
			buffer.writeInt(info._level);
			buffer.writeInt(info._classId);
			buffer.writeShort(0x00);
		}
	}

	private static class FriendInfo {
		int _objId;
		String _name;
		int _level;
		int _classId;
		boolean _online;

		public FriendInfo(int objId, String name, boolean online, int level, int classId) {
			_objId = objId;
			_name = name;
			_online = online;
			_level = level;
			_classId = classId;
		}
	}
}