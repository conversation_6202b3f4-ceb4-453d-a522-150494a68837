/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.steadybox;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExTimer<PERSON>heck extends ServerPacket {
	private final PlayerInstance _player;
	private final int _type;
	private final int _index;

	public ExTimerCheck(PlayerInstance player, int type, int index) {
		_player = player;
		_type = type;
		_index = index;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_TIMER_CHECK, buffer);
		buffer.writeInt(_type);
		buffer.writeInt(_index);
		buffer.writeByte(_player.getSteadyBox().getBoxOpenTime() == 0 ? 0x01 : 0x00); // bFinished
		buffer.writeInt((int) (System.currentTimeMillis() / 1000));
		buffer.writeInt((int) (_player.getSteadyBox().getBoxOpenTime() / 1000));
	}
}