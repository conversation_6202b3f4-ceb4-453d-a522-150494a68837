/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.ceremonyofchaos;

import club.projectessence.gameserver.enums.CeremonyOfChaosState;
import club.projectessence.gameserver.instancemanager.CeremonyOfChaosManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

/**
 * <AUTHOR>
 */
public class RequestCuriousHouseHtml extends ClientPacket {
	@Override
	public void readImpl() {
		// Nothing to read
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if (CeremonyOfChaosManager.getInstance().getState() != CeremonyOfChaosState.REGISTRATION) {
			return;
		} else if (CeremonyOfChaosManager.getInstance().isRegistered(player)) {
			player.sendPacket(SystemMessageId.YOU_ARE_ON_THE_WAITING_LIST_FOR_THE_CEREMONY_OF_CHAOS);
			return;
		}

		if (CeremonyOfChaosManager.getInstance().canRegister(player, true)) {
			final NpcHtmlMessage message = new NpcHtmlMessage(0);
			message.setFile(player, "data/html/CeremonyOfChaos/invite.htm");
			player.sendPacket(message);
		}
	}
}
