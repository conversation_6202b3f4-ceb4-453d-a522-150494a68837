package club.projectessence.gameserver.network.clientpackets.pet;

import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.data.xml.PetDataTable;
import club.projectessence.gameserver.data.xml.PetExtractData;
import club.projectessence.gameserver.model.PetData;
import club.projectessence.gameserver.model.actor.instance.PetInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.holders.PetExtractionHolder;
import club.projectessence.gameserver.model.itemcontainer.PetInventory;
import club.projectessence.gameserver.model.itemcontainer.PlayerInventory;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.pet.ExResultPetExtractSystem;

public class ExTryPetExtractSystem extends ClientPacket
{
	private int _itemObjId;
	
	@Override
	public void readImpl()
	{
		_itemObjId = readInt();
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		if (player.isAccountLockedDown())
		{
			player.sendMessage("Your account is in lockdown");
			return;
		}
		final ItemInstance petItem = player.getInventory().getItemByObjectId(_itemObjId);
		if ((petItem == null) || ((player.getPet() != null) && (player.getPet().getControlItem() == petItem)))
		{
			player.sendPacket(new ExResultPetExtractSystem(false));
			return;
		}
		final PetData petData = PetDataTable.getInstance().getPetDataByItemId(petItem.getId());
		final NpcTemplate npcTemplate = NpcData.getInstance().getTemplate(petData.getNpcId());
		final PetInstance pet = new PetInstance(npcTemplate, player, petItem, true);
		final PetInventory petInventory = pet.getInventory();
		final PlayerInventory playerInventory = player.getInventory();
		if ((petInventory == null) || (playerInventory == null))
		{
			player.sendPacket(new ExResultPetExtractSystem(false));
			return;
		}
		if (!playerInventory.validateWeight(petInventory.getTotalWeight()) || !playerInventory.validateCapacity(petInventory.getSize()))
		{
			player.sendPacket(SystemMessageId.THERE_ARE_ITEMS_IN_THE_PET_S_INVENTORY_TAKE_THEM_OUT_FIRST);
			player.sendPacket(new ExResultPetExtractSystem(false));
			return;
		}
		petInventory.transferItemsToOwner();
		int petId = PetDataTable.getInstance().getPetDataByItemId(petItem.getItem().getId()).getType();
		int petLevel = petItem.getEnchantLevel();
		long petExp = PetDataTable.getInstance().getPetExp(petItem.getObjectId());
		PetExtractionHolder holder = PetExtractData.getInstance().getExtraction(petId, petLevel);
		if (holder != null)
		{
			int extractItemId = holder.getExtractItem();
			int extractItemCount = (int) (petExp / holder.getExtractExp());
			final int extractCostId = holder.getExtractCost().getId();
			final long extractCostCount = holder.getExtractCost().getCount() * extractItemCount;
			final int defaultCostId = holder.getDefaultCost().getId();
			final long defaultCostCount = holder.getDefaultCost().getCount();
			if ((player.getInventory().getInventoryItemCount(extractCostId, -1) >= extractCostCount) //
			&& (player.getInventory().getInventoryItemCount(defaultCostId, -1) >= defaultCostCount))
			{
				if (player.destroyItemByItemId("Pet Extraction", extractCostId, extractCostCount, player, true) //
				&& player.destroyItemByItemId("Pet Extraction", defaultCostId, defaultCostCount, player, true) //
				&& player.destroyItem("Pet Extraction", petItem, player, true))
				{
					player.addItem("Pet Extraction", extractItemId, extractItemCount, player, true);
					player.sendPacket(new ExResultPetExtractSystem(true));
				}
			}
			else
			{
				player.sendPacket(SystemMessageId.INCORRECT_ITEM_COUNT);
				player.sendPacket(new ExResultPetExtractSystem(false));
			}
			return;
		}
		player.sendPacket(new ExResultPetExtractSystem(false));
	}
}
