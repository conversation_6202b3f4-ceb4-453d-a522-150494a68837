/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.model.TradeList;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.ExPrivateStoreSetWholeMsg;
import club.projectessence.gameserver.network.serverpackets.PrivateStoreManageListSell;
import club.projectessence.gameserver.network.serverpackets.PrivateStoreMsgSell;
import club.projectessence.gameserver.taskmanager.AttackStanceTaskManager;
import club.projectessence.gameserver.util.Util;

import static club.projectessence.gameserver.model.itemcontainer.Inventory.MAX_ADENA;

/**
 * @version $Revision: 1.2.2.1.2.5 $ $Date: 2005/03/27 15:29:30 $
 */
public class SetPrivateStoreListSell extends ClientPacket {
	private static final int BATCH_LENGTH = 20; // length of one item without name

	private boolean _packageSale;
	private Item[] _items = null;

	@Override
	public void readImpl() {
		_packageSale = (readInt() == 1);
		final int count = readInt();
		if ((count < 1) || (count > Config.MAX_ITEM_IN_PACKET) || ((count * BATCH_LENGTH) > available())) {
			return;
		}

		_items = new Item[count];
		for (int i = 0; i < count; i++) {
			final int itemId = readInt();
			final long cnt = readLong();
			final long price = readLong();
			readString(); // 338 Item Name
			if ((itemId < 1) || (cnt < 1) || (price < 0)) {
				_items = null;
				return;
			}
			_items[i] = new Item(itemId, cnt, price);
		}
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if (player.getLevel() < 35) {
			player.sendPacket(ActionFailed.STATIC_PACKET);
			player.sendMessage("Minimum level to open private store is 35.");
			return;
		}

		if (_items == null) {
			player.sendPacket(SystemMessageId.INCORRECT_ITEM_COUNT);
			player.setPrivateStoreType(PrivateStoreType.NONE);
			player.broadcastUserInfo();
			return;
		}

		if (!player.getAccessLevel().allowTransaction()) {
			player.sendPacket(SystemMessageId.YOU_ARE_NOT_AUTHORIZED_TO_DO_THAT);
			return;
		}

		if (AttackStanceTaskManager.getInstance().hasAttackStanceTask(player) || player.isInDuel()) {
			player.sendPacket(SystemMessageId.WHILE_YOU_ARE_ENGAGED_IN_COMBAT_YOU_CANNOT_OPERATE_A_PRIVATE_STORE_OR_PRIVATE_WORKSHOP);
			player.sendPacket(new PrivateStoreManageListSell(1, player, _packageSale));
			player.sendPacket(new PrivateStoreManageListSell(2, player, _packageSale));
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		if (player.isInsideZone(ZoneId.NO_STORE)) {
			player.sendPacket(new PrivateStoreManageListSell(1, player, _packageSale));
			player.sendPacket(new PrivateStoreManageListSell(2, player, _packageSale));
			player.sendPacket(SystemMessageId.YOU_CANNOT_OPEN_A_PRIVATE_STORE_HERE);
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		// Check maximum number of allowed slots for pvt shops
		if (_items.length > player.getPrivateSellStoreLimit()) {
			player.sendPacket(new PrivateStoreManageListSell(1, player, _packageSale));
			player.sendPacket(new PrivateStoreManageListSell(2, player, _packageSale));
			player.sendPacket(SystemMessageId.YOU_HAVE_EXCEEDED_THE_QUANTITY_THAT_CAN_BE_INPUTTED);
			return;
		}

		final TradeList tradeList = player.getSellList();
		tradeList.clear();
		tradeList.setPackaged(_packageSale);

		long totalCost = player.getAdena();
		for (Item i : _items) {
			if (!i.addToTradeList(tradeList)) {
				Util.handleIllegalPlayerAction(player, "Warning!! Character " + player.getName() + " of account " + player.getAccountName() + " tried to set price more than " + MAX_ADENA + " adena in Private Store - Sell.", Config.DEFAULT_PUNISH);
				return;
			}

			final ItemInstance item = player.getInventory().getItemByObjectId(i._objectId);
			if ((item == null) || (item.getCount() < i._count)) {
				player.sendPacket(SystemMessageId.INCORRECT_ITEM_COUNT);
				player.setPrivateStoreType(PrivateStoreType.NONE);
				return;
			}

			totalCost += i.getPrice();
			if (totalCost > MAX_ADENA) {
				Util.handleIllegalPlayerAction(player, "Warning!! Character " + player.getName() + " of account " + player.getAccountName() + " tried to set total price more than " + MAX_ADENA + " adena in Private Store - Sell.", Config.DEFAULT_PUNISH);
				return;
			}
		}

		player.sitDown();
		if (_packageSale) {
			player.setPrivateStoreType(PrivateStoreType.PACKAGE_SELL);
		} else {
			player.setPrivateStoreType(PrivateStoreType.SELL);
		}

		player.broadcastUserInfo();

		if (_packageSale) {
			player.broadcastPacket(new ExPrivateStoreSetWholeMsg(player));
		} else {
			player.broadcastPacket(new PrivateStoreMsgSell(player));
		}
	}

	private static class Item {
		private final int _objectId;
		private final long _count;
		private final long _price;

		public Item(int objectId, long count, long price) {
			_objectId = objectId;
			_count = count;
			_price = price;
		}

		public boolean addToTradeList(TradeList list) {
			if ((MAX_ADENA / _count) < _price) {
				return false;
			}

			list.addItem(_objectId, _count, _price);
			return true;
		}

		public long getPrice() {
			return _count * _price;
		}
	}
}
