/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.enums.AutoPlaySkillType;
import club.projectessence.gameserver.enums.AutoUseItemType;
import club.projectessence.gameserver.enums.ShortcutType;
import club.projectessence.gameserver.model.Shortcut;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.serverpackets.ShortCutRegister;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;
import club.projectessence.gameserver.taskmanager.autoplay.AutoUseTaskManager;
import club.projectessence.gameserver.taskmanager.autoplay.AutoUseTaskManager.AutoUsePotionType;

/**
 * <AUTHOR>
 */
public class RequestShortCutReg extends ClientPacket
{
	private ShortcutType	_type;
	private int				_id;
	private int				_slotId;
	private int				_slot;
	private int				_page;
	private int				_level;
	private int				_subLevel;
	private int				_characterType;	// 1 - player, 2 - pet
	
	@Override
	public void readImpl()
	{
		final int typeId = readInt();
		_type = ShortcutType.values()[(typeId < 1) || (typeId > 6) ? 0 : typeId];
		_slotId = readInt();
		_slot = _slotId % 12;
		_page = _slotId / 12;
		readByte(); // 228
		_id = readInt();
		_level = readShort();
		_subLevel = readShort(); // Sublevel
		_characterType = readInt();
	}
	
	@Override
	public void runImpl()
	{
		PlayerInstance player = client.getPlayer();
		if ((player == null) || (_page > 23) || (_page < 0))
		{
			return;
		}
		AutoUsePotionType potionType = null;
		switch (_slotId)
		{
			case AutoUseTaskManager.SHORTCUT_CP:
				potionType = AutoUsePotionType.CP;
				break;
			case AutoUseTaskManager.SHORTCUT_HP:
				potionType = AutoUsePotionType.HP;
				break;
			case AutoUseTaskManager.SHORTCUT_HP2:
				potionType = AutoUsePotionType.HP2;
				break;
			case AutoUseTaskManager.SHORTCUT_MP:
				potionType = AutoUsePotionType.MP;
				break;
		}
		if ((potionType != null) || (_slotId == AutoUseTaskManager.SHORTCUT_PET_HP))
		{
			final ItemInstance potion = player.getInventory().getItemByObjectId(_id);
			if ((potion == null) || !potion.isPotion() || (potion.getItem().getShortcutToggleType() != AutoUseItemType.POTION))
			{
				return;
			}
			if (potionType != null)
			{
				switch (potionType)
				{
					case CP:
					{
						if ((potion.getId() != 91912) && (potion.getId() != 93708) && (potion.getId() != 5591) && (potion.getId() != 5592) && (potion.getId() != 49663) && (potion.getId() != 91690))
						{
							return;
						}
						break;
					}
					case HP:
					{
						if (potion.getId() != 91912 && (potion.getId() != 93708))
						{
							return;
						}
						break;
					}
					case HP2:
					{
						if ((potion.getId() != 49663) && (potion.getId() != 91690) && (potion.getId() != 93708))
						{
							return;
						}
						break;
					}
					case MP:
					{
						if ((potion.getId() != 49854) && (potion.getId() != 91691))
						{
							return;
						}
						break;
					}
				}
				AutoUseTaskManager.getInstance().addAutoPotionItem(player, potionType, potion.getId());
			}
			else if (potion.getId() == 93967)
			{
				AutoUseTaskManager.getInstance().addPetAutoPotionItem(player, potion.getId());
			}
		}
		final Shortcut esc = player.getShortCut(_slot, _page);
		if (esc != null)
		{
			switch (esc.getType())
			{
				case SKILL:
				{
					AutoPlayTaskManager.getInstance().removeAutoSkill(player, esc.getId());
					AutoUseTaskManager.getInstance().removeAutoSkill(player, esc.getId());
					break;
				}
				case ITEM:
				{
					ItemInstance item = player.getInventory().getItemByObjectId(esc.getId());
					if (item != null)
					{
						AutoUseTaskManager.getInstance().removeAutoPotionItem(player, _id);
						AutoUseTaskManager.getInstance().removeAutoSupplyItem(player, item.getId());
					}
					break;
				}
				case ACTION:
				{
					AutoPlayTaskManager.getInstance().removeAutoAction(player, esc.getId());
					break;
				}
			}
		}
		final Shortcut sc = new Shortcut(_slot, _page, _type, _id, _level, _subLevel, _characterType);
		player.registerShortCut(sc);
		client.sendPacket(new ShortCutRegister(sc));
		if (sc.getType() == ShortcutType.SKILL)
		{
			final Skill skill = player.getKnownSkill(sc.getId());
			if (skill != null)
			{
				// if (skill.getShortcutToggleType() == AutoPlaySkillType.BUFF)
				// {
				// if (player.getAutoUseSettings().getAutoSkills().contains(skill.getId()))
				// {
				// AutoUseTaskManager.getInstance().addAutoSkill(player, skill.getId());
				// }
				// }
				// else
				if (skill.getShortcutToggleType() == AutoPlaySkillType.ATTACK)
				{
					if (player.getAutoPlaySettings().getAutoSkills().contains(skill.getId()))
					{
						AutoPlayTaskManager.getInstance().addAutoSkill(player, skill.getId());
					}
				}
			}
		}
		player.sendToggledShortcuts();
	}
}
