/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.pledge;

import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.model.clan.ClanPrivilege;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.pledge.ExPledgeEnemyInfoList;
import club.projectessence.gameserver.taskmanager.AttackStanceTaskManager;

/**
 * <AUTHOR>
 */
public class RequestExPledgeEnemyDelete extends ClientPacket {
	private int _clanId;

	@Override
	public void readImpl() {
		_clanId = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}
		final Clan playerClan = player.getClan();
		if (playerClan == null) {
			return;
		}

		final Clan clan = ClanTable.getInstance().getClan(_clanId);
		if (clan == null) {
			player.sendMessage("No such clan.");
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		if (!playerClan.isAtWarWith(clan.getId())) {
			player.sendMessage("You aren't at war with this clan.");
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		// Check if player who does the request has the correct rights to do it
		if (!player.hasClanPrivilege(ClanPrivilege.CL_PLEDGE_WAR)) {
			player.sendPacket(SystemMessageId.YOU_ARE_NOT_AUTHORIZED_TO_DO_THAT);
			return;
		}

		for (ClanMember member : playerClan.getMembers()) {
			if ((member == null) || (member.getPlayerInstance() == null)) {
				continue;
			}
			if (AttackStanceTaskManager.getInstance().hasAttackStanceTask(member.getPlayerInstance())) {
				player.sendPacket(SystemMessageId.THE_CLAN_WAR_CANNOT_BE_STOPPED_BECAUSE_SOMEONE_FROM_YOUR_CLAN_IS_STILL_ENGAGED_IN_BATTLE);
				return;
			}
		}

		ClanTable.getInstance().deleteClanWars(playerClan.getId(), clan.getId());
		for (PlayerInstance member : playerClan.getOnlineMembers(0)) {
			member.broadcastUserInfo();
		}

		for (PlayerInstance member : clan.getOnlineMembers(0)) {
			member.broadcastUserInfo();
		}
		player.sendPacket(new ExPledgeEnemyInfoList(player));
	}
}
