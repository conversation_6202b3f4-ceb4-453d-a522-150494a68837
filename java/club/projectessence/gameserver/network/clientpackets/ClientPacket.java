/*
 * Copyright © 2019-2021 L2JOrg
 *
 * This file is part of the L2JOrg project.
 *
 * L2JOrg is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * L2JOrg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.InvalidDataPacketException;
import io.github.joealisson.mmocore.ReadablePacket;

import java.util.logging.Logger;

/**
 * Packets received by the game server from clients
 *
 * <AUTHOR>
 */
public abstract class ClientPacket extends ReadablePacket<GameClient> {

	protected static final Logger LOGGER = Logger.getLogger(ClientPacket.class.getSimpleName());

	@Override
	protected boolean read() {
		try {
			readImpl();
			return true;
		} catch (InvalidDataPacketException e) {
			LOGGER.warning("Invalid data packet " + this + " from client " + client);
			e.printStackTrace();
		} catch (Exception e) {

			LOGGER.warning("Invalid data packet " + this + " from client " + client);
			e.printStackTrace();
		}
		if (!Config.ASYNC_MMOCORE_AUTO_READ) {
			client.readNextPacket();
		}
		return false;
	}

	@Override
	public void run() {
		try {
			runImpl();
		} catch (Exception e) {

			LOGGER.warning("Invalid data packet " + this + " from client " + client);
			e.printStackTrace();
		} finally {
			if (!Config.ASYNC_MMOCORE_AUTO_READ) {
				client.readNextPacket();
			}
		}
	}

	protected abstract void runImpl() throws Exception;

	protected abstract void readImpl() throws Exception;
}
