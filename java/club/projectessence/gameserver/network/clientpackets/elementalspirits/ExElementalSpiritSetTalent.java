/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.elementalspirits;

import club.projectessence.gameserver.enums.ElementalType;
import club.projectessence.gameserver.enums.UserInfoType;
import club.projectessence.gameserver.model.ElementalSpirit;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.UserInfo;
import club.projectessence.gameserver.network.serverpackets.elementalspirits.ElementalSpiritSetTalent;

/**
 * <AUTHOR>
 */
public class ExElementalSpiritSetTalent extends ClientPacket
{
	private byte	_type;
	private byte	_attackPoints;
	private byte	_defensePoints;
	private byte	_critRate;
	private byte	_critDamage;
	
	@Override
	public void readImpl()
	{
		_type = readByte();
		readByte(); // Characteristics for now always 4
		readByte(); // attack id
		_attackPoints = readByte();
		readByte(); // defense id
		_defensePoints = readByte();
		readByte(); // crit rate id
		_critRate = readByte();
		readByte(); // crit damage id
		_critDamage = readByte();
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		if (player.isAccountLockedDown())
		{
			player.sendMessage("Your account is in lockdown");
			return;
		}
		final ElementalSpirit spirit = player.getElementalSpirit(ElementalType.of(_type));
		boolean result = false;
		if (spirit != null)
		{
			if ((_attackPoints > 0) && (spirit.getAvailableCharacteristicsPoints() >= _attackPoints))
			{
				spirit.addAttackPoints(_attackPoints);
				result = true;
			}
			if ((_defensePoints > 0) && (spirit.getAvailableCharacteristicsPoints() >= _defensePoints))
			{
				spirit.addDefensePoints(_defensePoints);
				result = true;
			}
			if ((_critRate > 0) && (spirit.getAvailableCharacteristicsPoints() >= _critRate))
			{
				spirit.addCritRatePoints(_critRate);
				result = true;
			}
			if ((_critDamage > 0) && (spirit.getAvailableCharacteristicsPoints() >= _critDamage))
			{
				spirit.addCritDamage(_critDamage);
				result = true;
			}
		}
		if (result)
		{
			final UserInfo userInfo = new UserInfo(player);
			userInfo.addComponentType(UserInfoType.ATT_SPIRITS);
			client.sendPacket(userInfo);
			client.sendPacket(new SystemMessage(SystemMessageId.CHARACTERISTICS_WERE_APPLIED_SUCCESSFULLY));
		}
		client.sendPacket(new ElementalSpiritSetTalent(player, _type, result));
	}
}
