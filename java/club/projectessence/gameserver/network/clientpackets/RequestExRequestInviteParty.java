/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.handler.ChatHandler;
import club.projectessence.gameserver.handler.IChatHandler;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.ceremonyofchaos.CeremonyOfChaosEvent;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.olympiad.OlympiadManager;
import club.projectessence.gameserver.network.SystemMessageId;

import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class RequestExRequestInviteParty extends ClientPacket {
	private static Logger LOGGER_CHAT = Logger.getLogger("chat");

	private byte _reqType;
	private byte _sayType;

	@Override
	public void readImpl() {
		_reqType = readByte(); // cReqType 0 - LFP | 1 - CC
		_sayType = readByte(); // cSayType
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if ((player.getLastPartyRequestTimestamp() + 10_000) > System.currentTimeMillis()) {
			return;
		}

		ChatType chatType = ChatType.findByClientId(_sayType);
		if (chatType == null) {
			return;
		}

		if (chatType == ChatType.PETITION_PLAYER) {
			return;
		}

		if (player.isCursedWeaponEquipped() && ((chatType == ChatType.TRADE) || (chatType == ChatType.SHOUT))) {
			player.sendPacket(SystemMessageId.SHOUT_AND_TRADE_CHATTING_CANNOT_BE_USED_WHILE_POSSESSING_A_CURSED_WEAPON);
			return;
		}

		if (player.isChatBanned()) {
			if (player.isAffected(EffectFlag.CHAT_BLOCK)) {
				player.sendPacket(SystemMessageId.YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_CHATTING_IS_NOT_ALLOWED);
			} else if (Config.BAN_CHAT_CHANNELS.contains(chatType)) {
				player.sendPacket(SystemMessageId.CHATTING_IS_CURRENTLY_PROHIBITED_IF_YOU_TRY_TO_CHAT_BEFORE_THE_PROHIBITION_IS_REMOVED_THE_PROHIBITION_TIME_WILL_INCREASE_EVEN_FURTHER);
			}
			return;
		}

		if (player.isInOlympiadMode() || OlympiadManager.getInstance().isRegistered(player)) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_CHAT_WHILE_PARTICIPATING_IN_THE_OLYMPIAD);
			return;
		}

		if (player.isOnEvent(CeremonyOfChaosEvent.class)) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_CHAT_IN_THE_CEREMONY_OF_CHAOS);
			return;
		}

		if (player.isJailed() && Config.JAIL_DISABLE_CHAT && ((chatType == ChatType.WHISPER) || (chatType == ChatType.SHOUT) || (chatType == ChatType.TRADE) || (chatType == ChatType.HERO_VOICE))) {
			player.sendMessage("You can not chat with players outside of the jail.");
			return;
		}

		if (Config.LOG_CHAT) {
			if (chatType == ChatType.WHISPER) {
				LOGGER_CHAT.info(chatType.name() + " [" + player + "] " + getClass().getSimpleName());
			} else {
				LOGGER_CHAT.info(chatType.name() + " [" + player + "] " + getClass().getSimpleName());
			}
		}

		if (player.getCommandChannel() != null) {
			return;
		} else if (player.getParty() != null) {
			_reqType = 1; // need cc
		} else {
			_reqType = 0; // need pt
		}

		final IChatHandler handler = ChatHandler.getInstance().getHandler(chatType);
		if (handler != null) {
			player.setLastPartyRequestTimeStamp(System.currentTimeMillis());
			handler.handleRequestInvitePartyPacket(chatType, player, _reqType);
		} else {
			LOGGER.info("No handler registered for ChatType: " + _sayType + " Player: " + client);
		}
	}
}
