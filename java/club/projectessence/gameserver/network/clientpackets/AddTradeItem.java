/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.model.TradeItem;
import club.projectessence.gameserver.model.TradeList;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.TradeOtherAdd;
import club.projectessence.gameserver.network.serverpackets.TradeOwnAdd;
import club.projectessence.gameserver.network.serverpackets.TradeUpdate;

/**
 * @version $Revision: 1.5.2.2.2.5 $ $Date: 2005/03/27 15:29:29 $
 */
public class AddTradeItem extends ClientPacket {
	private int _tradeId;
	private int _objectId;
	private long _count;

	@Override
	public void readImpl() {
		_tradeId = readInt();
		_objectId = readInt();
		_count = readLong();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		final TradeList trade = player.getActiveTradeList();
		if (trade == null) {
			LOGGER.warning("Character: " + player.getName() + " requested item:" + _objectId + " add without active tradelist:" + _tradeId);
			return;
		}

		final PlayerInstance partner = trade.getPartner();
		if ((partner == null) || (World.getInstance().getPlayer(partner.getObjectId()) == null) || (partner.getActiveTradeList() == null)) {
			// Trade partner not found, cancel trade
			if (partner != null) {
				LOGGER.warning("Character:" + player.getName() + " requested invalid trade object: " + _objectId);
			}
			player.sendPacket(SystemMessageId.THAT_PLAYER_IS_NOT_ONLINE);
			player.cancelActiveTrade();
			return;
		}

		if (trade.isConfirmed() || partner.getActiveTradeList().isConfirmed()) {
			player.sendPacket(SystemMessageId.YOU_MAY_NO_LONGER_ADJUST_ITEMS_IN_THE_TRADE_BECAUSE_THE_TRADE_HAS_BEEN_CONFIRMED);
			return;
		}

		if (!player.getAccessLevel().allowTransaction()) {
			player.sendMessage("Transactions are disabled for your Access Level.");
			player.cancelActiveTrade();
			return;
		}

		if (!player.validateItemManipulation(_objectId, "trade")) {
			player.sendPacket(SystemMessageId.NOTHING_HAPPENED);
			return;
		}

		final ItemInstance item1 = player.getInventory().getItemByObjectId(_objectId);
		final TradeItem item2 = trade.addItem(_objectId, _count);
		if (item2 != null) {
			player.sendPacket(new TradeOwnAdd(1, item2));
			player.sendPacket(new TradeOwnAdd(2, item2));
			player.sendPacket(new TradeUpdate(1, null, null, 0));
			player.sendPacket(new TradeUpdate(2, player, item2, item1.getCount() - item2.getCount()));
			partner.sendPacket(new TradeOtherAdd(1, item2));
			partner.sendPacket(new TradeOtherAdd(2, item2));
		}
	}
}
