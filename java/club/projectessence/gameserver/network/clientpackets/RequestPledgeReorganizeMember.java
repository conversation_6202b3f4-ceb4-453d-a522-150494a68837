/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.model.clan.ClanPrivilege;

/**
 * Format: (ch) dSdS
 *
 * <AUTHOR>
 */
public class RequestPledgeReorganizeMember extends ClientPacket {
	private int _isMemberSelected;
	private String _memberName;
	private int _newPledgeType;
	private String _selectedMember;

	@Override
	public void readImpl() {
		_isMemberSelected = readInt();
		_memberName = readString();
		_newPledgeType = readInt();
		_selectedMember = readString();
	}

	@Override
	public void runImpl() {
		if (_isMemberSelected == 0) {
			return;
		}

		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		final Clan clan = player.getClan();
		if (clan == null) {
			return;
		}

		if (!player.hasClanPrivilege(ClanPrivilege.CL_MANAGE_RANKS)) {
			return;
		}

		final ClanMember member1 = clan.getClanMember(_memberName);
		if ((member1 == null) || (member1.getObjectId() == clan.getLeaderId())) {
			return;
		}

		final ClanMember member2 = clan.getClanMember(_selectedMember);
		if ((member2 == null) || (member2.getObjectId() == clan.getLeaderId())) {
			return;
		}

		final int oldPledgeType = member1.getPledgeType();
		if (oldPledgeType == _newPledgeType) {
			return;
		}

		member1.setPledgeType(_newPledgeType);
		member2.setPledgeType(oldPledgeType);
		clan.broadcastClanStatus();
	}
}
