/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.newhenna;

import club.projectessence.gameserver.data.xml.DyePotentialData;
import club.projectessence.gameserver.data.xml.DyePotentialData.DyePotential;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.henna.HennaPoten;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.newhenna.ExNewHennaPotenSelect;

/**
 * <AUTHOR>
 */
public class ExRequestNewHennaPotenSelect extends ClientPacket {
	private int _slotId;
	private int _potenId;

	@Override
	public void readImpl() {
		_slotId = readByte(); // cSlotID
		_potenId = readInt(); // nPotenID
	}

	@Override
	public void runImpl() {
		PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if ((_slotId < 1) || (_slotId > player.getHennaPotenList().length)) {
			return;
		}

		final DyePotential potential = DyePotentialData.getInstance().getPotential(_potenId);
		final HennaPoten hennaPoten = player.getHennaPoten(_slotId);
		if ((potential == null) || (potential.getSlotId() != _slotId)) {
			player.sendPacket(new ExNewHennaPotenSelect(_slotId, _potenId, hennaPoten.getActiveStep(), false));
			return;
		}

		hennaPoten.setPotenId(_potenId);
		player.sendPacket(new ExNewHennaPotenSelect(_slotId, _potenId, hennaPoten.getActiveStep(), true));
		player.applyDyePotenSkills();
	}
}
