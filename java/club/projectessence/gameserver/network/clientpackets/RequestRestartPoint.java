package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.data.xml.ClanHallData;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData.FeeHolder;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData.ResurrectionFeeItem;
import club.projectessence.gameserver.enums.TeleportWhereType;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.FactionZoneManager;
import club.projectessence.gameserver.instancemanager.FortManager;
import club.projectessence.gameserver.instancemanager.GveRewardManager; // Thêm import
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.SiegeClan;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.listeners.AbstractEventListener;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.quest.Event;
import club.projectessence.gameserver.model.residences.ClanHall;
import club.projectessence.gameserver.model.residences.ResidenceFunctionType;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.model.siege.Castle.CastleFunction;
import club.projectessence.gameserver.model.siege.Fort;
import club.projectessence.gameserver.model.siege.Fort.FortFunction;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.type.FactionZone;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import gabriel.eventEngine.interf.GabrielEvents;

/**
 * @version $Revision: 1.7.2.3.2.6 $ $Date: 2005/03/27 15:29:30 $
 */
public class RequestRestartPoint extends ClientPacket
{
	private int	_requestedPointType;
	private int	_itemId;
	@SuppressWarnings("unused")
	private int	_itemCount;
	
	@Override
	public void readImpl()
	{
		_requestedPointType = readInt();
		if (_requestedPointType == 9)
		{
			_itemId = readInt();
			_itemCount = readInt();
		}
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		if (!player.canRevive())
		{
			return;
		}
		if (GabrielEvents.isInEvent(player))
		{
			return;
		}
		if (player.isFakeDeath())
		{
			player.stopFakeDeath(true);
			return;
		}
		else if (!player.isDead())
		{
			return;
		}
		// Custom event resurrection management.
		if (player.isOnCustomEvent())
		{
			for (AbstractEventListener listener : player.getListeners(EventType.ON_CREATURE_DEATH))
			{
				if (listener.getOwner() instanceof Event)
				{
					((Event) listener.getOwner()).notifyEvent("ResurrectPlayer", null, player);
					return;
				}
			}
		}
		final Castle castle = CastleManager.getInstance().getCastle(player.getX(), player.getY(), player.getZ());
		if ((castle != null) && castle.getSiege().isInProgress() && (player.getClan() != null) && castle.getSiege().checkIsAttacker(player.getClan()))
		{
			// Schedule respawn delay for attacker
			ThreadPool.schedule(new DeathTask(player), castle.getSiege().getAttackerRespawnDelay());
			if (castle.getSiege().getAttackerRespawnDelay() > 0)
			{
				player.sendMessage("You will be re-spawned in " + (castle.getSiege().getAttackerRespawnDelay() / 1000) + " seconds");
			}
			return;
		}
		portPlayer(player);
	}
	
	protected final void portPlayer(PlayerInstance player)
	{
		Location loc = null;
		boolean setPendingRevive = true;
		Instance instance = null;
		// force jail
		if (player.isJailed())
		{
			_requestedPointType = 27;
		}
		switch (_requestedPointType)
		{
			case 1: // to clanhall
			{
				if ((player.getClan() == null) || (player.getClan().getHideoutId() == 0))
				{
					LOGGER.warning("Player [" + player.getName() + "] called RestartPointPacket - To Clanhall and he doesn't have Clanhall!");
					return;
				}
				loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.CLANHALL);
				final ClanHall residense = ClanHallData.getInstance().getClanHallByClan(player.getClan());
				if ((residense != null) && (residense.hasFunction(ResidenceFunctionType.EXP_RESTORE)))
				{
					player.restoreExp(residense.getFunction(ResidenceFunctionType.EXP_RESTORE).getValue(), false);
				}
				break;
			}
			case 2: // to castle
			{
				final Clan clan = player.getClan();
				Castle castle = CastleManager.getInstance().getCastle(player);
				if ((castle != null) && castle.getSiege().isInProgress())
				{
					// Siege in progress
					if (castle.getSiege().checkIsDefender(clan))
					{
						loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.CASTLE);
					}
					else if (castle.getSiege().checkIsAttacker(clan))
					{
						loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN);
					}
					else
					{
						LOGGER.warning("Player [" + player.getName() + "] called RestartPointPacket - To Castle and he doesn't have Castle!");
						return;
					}
				}
				else
				{
					if ((clan == null) || (clan.getCastleId() == 0))
					{
						return;
					}
					loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.CASTLE);
				}
				if (clan != null)
				{
					castle = CastleManager.getInstance().getCastleByOwner(clan);
					if (castle != null)
					{
						final CastleFunction castleFunction = castle.getCastleFunction(Castle.FUNC_RESTORE_EXP);
						if (castleFunction != null)
						{
							player.restoreExp(castleFunction.getLvl(), false);
						}
					}
				}
				break;
			}
			case 3: // to fortress
			{
				final Clan clan = player.getClan();
				if ((clan == null) || (clan.getFortId() == 0))
				{
					LOGGER.warning("Player [" + player.getName() + "] called RestartPointPacket - To Fortress and he doesn't have Fortress!");
					return;
				}
				loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.FORTRESS);
				final Fort fort = FortManager.getInstance().getFortByOwner(clan);
				if (fort != null)
				{
					final FortFunction fortFunction = fort.getFortFunction(Fort.FUNC_RESTORE_EXP);
					if (fortFunction != null)
					{
						player.restoreExp(fortFunction.getLvl(), false);
					}
				}
				break;
			}
			case 4: // to siege HQ
			{
				SiegeClan siegeClan = null;
				final Castle castle = CastleManager.getInstance().getCastle(player);
				final Fort fort = FortManager.getInstance().getFort(player);
				if ((castle != null) && castle.getSiege().isInProgress())
				{
					siegeClan = castle.getSiege().getAttackerClan(player.getClan());
				}
				else if ((fort != null) && fort.getSiege().isInProgress())
				{
					siegeClan = fort.getSiege().getAttackerClan(player.getClan());
				}
				if (((siegeClan == null) || siegeClan.getFlag().isEmpty()))
				{
					LOGGER.warning("Player [" + player.getName() + "] called RestartPointPacket - To Siege HQ and he doesn't have Siege HQ!");
					return;
				}
				loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.SIEGEFLAG);
				break;
			}
			case 5: // Fixed or Player is a festival participant
			{
				if (!player.isGM() && !player.getInventory().haveItemForSelfResurrection())
				{
					LOGGER.warning("Player [" + player.getName() + "] called RestartPointPacket - Fixed and he isn't festival participant!");
					return;
				}
				if (player.isGM() || player.destroyItemByItemId("Feather", 10649, 1, player, false) || player.destroyItemByItemId("Feather", 13300, 1, player, false) || player.destroyItemByItemId("Feather", 13128, 1, player, false))
				{
					player.doRevive(100.00);
					setPendingRevive = false;
					GveRewardManager.getInstance().manageRevivePenalty(player, false); // Xóa penalty ngay sau khi hồi sinh
				}
				else
				{
					instance = player.getInstanceWorld();
					loc = new Location(player);
				}
				break;
			}
			case 6: // TODO: Agathion resurrection
			{
				break;
			}
			case 7: // TODO: Adventurer's Song
			{
				break;
			}
			case 9: // Paid resurrection
			{
				if (player.getExpBeforeDeath() <= 0)
				{
					player.sendPacket(SystemMessageId.NO_XP_TO_RECOVER);
					return;
				}
				int freeResurrectionLeft = player.getVariables().getInt(PlayerVariables.RESURRECTION_COUNT_FREE, 2);
				int power = 100;
				if (freeResurrectionLeft > 0)
				{
					player.getVariables().set(PlayerVariables.RESURRECTION_COUNT_FREE, freeResurrectionLeft - 1);
				}
				else
				{
					boolean isAdena = _itemId == Inventory.ADENA_ID;
					FeeHolder fee = isAdena ? ResurrectionFeesData.getInstance().getResurrectionFee(player, ResurrectionFeeItem.ADENA) : ResurrectionFeesData.getInstance().getResurrectionFee(player, ResurrectionFeeItem.LCOIN);
					power = fee.getRecovery();
					double premiumMod = PremiumManager.getInstance().hasAnyPremiumBenefits(player) ? PremiumManager.getInstance().getEffectivePremiumRate(player, "RESURRECTION_COST") : 1.0;
					if (isAdena)
					{
						if (!player.reduceAdena("Resurrection", (long) (fee.getCost() * Math.max(0, player.getStat().getValue(Stat.RESURRECTION_FEE_MOD, 1)) * premiumMod), player, true))
						{
							player.sendPacket(new SystemMessage(SystemMessageId.INCORRECT_ITEM_COUNT));
							return;
						}
						player.getVariables().increaseInt(PlayerVariables.RESURRECTION_COUNT_ADENA, 2, 1);
					}
					else
					{
						if (!player.destroyItemByItemId("Resurrection", Inventory.LCOIN_ID, (long) (fee.getCost() * Math.max(0, player.getStat().getValue(Stat.RESURRECTION_FEE_MOD, 1)) * premiumMod), player, true))
						{
							player.sendPacket(new SystemMessage(SystemMessageId.INCORRECT_ITEM_COUNT));
							return;
						}
						player.getVariables().increaseInt(PlayerVariables.RESURRECTION_COUNT_LCOIN, 2, 1);
					}
				}
				if (player.isInInstance() && ZoneManager.getInstance().getZoneByName("toi_hz").isCharacterInZone(player))
				{
					instance = player.getInstanceWorld();
				}
				loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN);
				FactionZone factionZone = ZoneManager.getInstance().getZone(player, FactionZone.class);
				boolean isControllingFaction = factionZone != null && factionZone.getStatus() == FactionZoneManager.ZoneStatus.ACTIVATED && player.getFaction() == factionZone.getControllingFaction();
				if (isControllingFaction)
				{
					// Kiểm tra trạng thái Outpost trước khi chọn điểm hồi sinh
					if (factionZone.getOutpostStatus() != 0) // Outpost bị phá hủy
					{
						loc = getFactionBaseLocation(player);
						player.sendMessage("Outpost has been destroyed. Teleporting to your faction's base.");
					}
					else
					{
						loc = factionZone.getRandomRespawnPoint(player);
						if (loc == null)
						{
							loc = getFactionBaseLocation(player);
							player.sendMessage("No valid respawn points found near the Outpost. Teleporting to your faction's base.");
							LOGGER.warning("No valid respawn points found for zone " + factionZone.getName() + ". Teleporting player " + player.getName() + " to faction base.");
						}
					}
				}
				else
				{
					loc = getFactionBaseLocation(player);
				}
				player.doRevive(power, true);
				setPendingRevive = false;
				GveRewardManager.getInstance().manageRevivePenalty(player, false); // Xóa penalty ngay sau khi hồi sinh
				break;
			}
			case 27: // to jail
			{
				if (!player.isJailed())
				{
					return;
				}
				loc = new Location(-114356, -249645, -2984);
				break;
			}
			default:
			{
				// Các trường hợp khác
				if (player.isInInstance() && ZoneManager.getInstance().getZoneByName("toi_hz").isCharacterInZone(player))
				{
					instance = player.getInstanceWorld();
				}
				loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN);
				FactionZone factionZone = ZoneManager.getInstance().getZone(player, FactionZone.class);
				boolean isControllingFaction = factionZone != null && factionZone.getStatus() == FactionZoneManager.ZoneStatus.ACTIVATED && player.getFaction() == factionZone.getControllingFaction();
				if (isControllingFaction)
				{
					// Kiểm tra trạng thái Outpost trước khi chọn điểm hồi sinh
					if (factionZone.getOutpostStatus() != 0) // Outpost bị phá hủy
					{
						loc = getFactionBaseLocation(player);
						player.sendMessage("Outpost has been destroyed. Teleporting to your faction's base.");
					}
					else
					{
						loc = factionZone.getRandomRespawnPoint(player);
						if (loc == null)
						{
							loc = getFactionBaseLocation(player);
							player.sendMessage("No valid respawn points found near the Outpost. Teleporting to your faction's base.");
							LOGGER.warning("No valid respawn points found for zone " + factionZone.getName() + ". Teleporting player " + player.getName() + " to faction base.");
						}
					}
				}
				else
				{
					loc = getFactionBaseLocation(player);
				}
				break;
			}
		}
		// Teleport and revive
		if (loc != null)
		{
			if (setPendingRevive)
			{
				player.setIsPendingRevive(true);
				GveRewardManager.getInstance().manageRevivePenalty(player, true);
			}
			player.teleToLocation(loc, true, instance);
			GveRewardManager.getInstance().manageRevivePenalty(player, false);
		}
	}
	
	private Location getFactionBaseLocation(PlayerInstance player)
	{
		// if (player.getFaction() == Faction.FIRE)
		// {
		// player.sendMessage("Teleporting to Fire Faction base.");
		// return Config.FACTION_FIRE_BASE_LOCATION;
		// }
		// else if (player.getFaction() == Faction.WATER)
		// {
		// player.sendMessage("Teleporting to Water Faction base.");
		// return Config.FACTION_WATER_BASE_LOCATION;
		// }
		// else
		// {
		// player.sendMessage("Teleporting to default location.");
		// return new Location(83000, 148000, -3400);
		// }
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("Teleporting player " + player.getName() + " to nearest town for faction " + player.getFaction());
		}
		return new Location(83000, 148000, -3400); // Giran
		// return MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN);
	}
	
	class DeathTask implements Runnable
	{
		final PlayerInstance _player;
		
		DeathTask(PlayerInstance player)
		{
			_player = player;
		}
		
		@Override
		public void run()
		{
			portPlayer(_player);
		}
	}
}