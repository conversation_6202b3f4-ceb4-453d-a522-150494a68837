/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.stats;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.UserInfo;

/**
 * <AUTHOR>
 */
public class ExSetStatusBonus extends ClientPacket {
	private int _str;
	private int _dex;
	private int _con;
	private int _int;
	private int _wit;
	private int _men;

	@Override
	public void readImpl() {
		readShort(); // luc?
		readShort(); // cha?
		_str = readShort();
		_dex = readShort();
		_con = readShort();
		_int = readShort();
		_wit = readShort();
		_men = readShort();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}
		if ((_str < 0) || (_dex < 0) || (_con < 0) || (_int < 0) || (_wit < 0) || (_men < 0)) {
			return;
		}

		final PlayerVariables vars = player.getVariables();
		final int usedPoints = vars.getInt(PlayerVariables.STAT_STR, 0) //
				+ vars.getInt(PlayerVariables.STAT_DEX, 0) //
				+ vars.getInt(PlayerVariables.STAT_CON, 0) //
				+ vars.getInt(PlayerVariables.STAT_INT, 0) //
				+ vars.getInt(PlayerVariables.STAT_WIT, 0) //
				+ vars.getInt(PlayerVariables.STAT_MEN, 0);
		final int pointsToAdd = _str + _dex + _con + _int + _wit + _men;
		int elixirAvailable = vars.getInt(PlayerVariables.STAT_ELIXIR, 0);
		int possiblePoints = player.getLevel() < 76 ? 0 : ((player.getLevel() - 75) + elixirAvailable) - usedPoints;
		if ((possiblePoints <= 0) || (pointsToAdd > possiblePoints)) {
			LOGGER.warning(player + " tried to use more stat points than he can. Tried to use: " + pointsToAdd + " Have: " + possiblePoints);
			return;
		}

		if (_str > 0) {
			vars.set(PlayerVariables.STAT_STR, vars.getInt(PlayerVariables.STAT_STR, 0) + _str);
			player.getStat().mergeAdd(Stat.STAT_STR, _str);
		}
		if (_dex > 0) {
			vars.set(PlayerVariables.STAT_DEX, vars.getInt(PlayerVariables.STAT_DEX, 0) + _dex);
			player.getStat().mergeAdd(Stat.STAT_DEX, _dex);
		}
		if (_con > 0) {
			vars.set(PlayerVariables.STAT_CON, vars.getInt(PlayerVariables.STAT_CON, 0) + _con);
			player.getStat().mergeAdd(Stat.STAT_CON, _con);
		}
		if (_int > 0) {
			vars.set(PlayerVariables.STAT_INT, vars.getInt(PlayerVariables.STAT_INT, 0) + _int);
			player.getStat().mergeAdd(Stat.STAT_INT, _int);
		}
		if (_wit > 0) {
			vars.set(PlayerVariables.STAT_WIT, vars.getInt(PlayerVariables.STAT_WIT, 0) + _wit);
			player.getStat().mergeAdd(Stat.STAT_WIT, _wit);
		}
		if (_men > 0) {
			vars.set(PlayerVariables.STAT_MEN, vars.getInt(PlayerVariables.STAT_MEN, 0) + _men);
			player.getStat().mergeAdd(Stat.STAT_MEN, _men);
		}

		player.getStat().recalculateStats(true);
		player.updateStatBonusPassives();
		player.sendPacket(new UserInfo(player));
	}
}
