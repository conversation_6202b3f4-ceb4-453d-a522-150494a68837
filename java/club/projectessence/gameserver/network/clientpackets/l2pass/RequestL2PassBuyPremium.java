/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.l2pass;

import java.util.Calendar;

import club.projectessence.gameserver.data.xml.L2PassData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.l2pass.ExL2PassInfo;
import club.projectessence.gameserver.network.serverpackets.l2pass.ExSayhasSupportInfo;

/**
 * <AUTHOR>
 */
public class RequestL2PassBuyPremium extends ClientPacket
{
	private int cPassType;
	
	@Override
	public void readImpl()
	{
		cPassType = readByte();
	}
	
	@Override
	public void runImpl()
	{
		PlayerInstance player = client.getPlayer();
		if (player == null || !L2PassData.isEnabled())
		{
			return;
		}
		Calendar c = Calendar.getInstance();
		if ((c.get(Calendar.DAY_OF_MONTH) == 1) && (((c.get(Calendar.HOUR_OF_DAY) * 100) + c.get(Calendar.MINUTE)) < 630))
		{
			player.sendPacket(new SystemMessage(SystemMessageId.INELIGIBLE_FOR_PURCHASE_YOU_CAN_PURCHASE_ADDITIONAL_REWARDS_OF_THE_SEASON_PASS_ONLY_TILL_6_30_AM_OF_THE_DAY_WHEN_THE_SEASON_ENDS));
			return;
		}
		// if (!player.destroyItemByItemId("L2Pass Premium", Inventory.LCOIN_ID, 4000, player, true)) {
		// player.sendPacket(new SystemMessage(SystemMessageId.NOT_ENOUGH_MONEY_TO_USE_THE_FUNCTION));
		// return;
		// }
		if (!player.destroyItemByItemId("L2Pass Premium", 72290, 1, player, true))
		{
			player.sendPacket(new SystemMessage(SystemMessageId.NOT_ENOUGH_MONEY_TO_USE_THE_FUNCTION));
			return;
		}
		player.getL2Pass().setPremium(true);
		player.sendPacket(new ExSayhasSupportInfo(player));
		player.sendPacket(new ExL2PassInfo(player, cPassType));
	}
}