/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.l2pass;

import club.projectessence.gameserver.data.xml.L2PassData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.l2pass.ExL2PassInfo;
import club.projectessence.gameserver.network.serverpackets.l2pass.ExSayhasSupportInfo;

/**
 * <AUTHOR>
 */
public class RequestL2PassRequestReward extends ClientPacket {
	public static int DUMMY_L2PASS_ITEM = 72286;

	private int cPassType;
	private int bPremium;

	public static void rewardPlayer(PlayerInstance player, int itemID, int count) {
		if (itemID == DUMMY_L2PASS_ITEM) {
			final SystemMessage sm = new SystemMessage(SystemMessageId.YOU_RECEIVED_S1_SAYHAS_GRACE_SUSTENTION_POINTS);
			sm.addInt(count);
			player.sendPacket(sm);
			player.getL2Pass().addSayhasSustentionTimeEarned(count * 60);
		} else {
			player.addItem("l2pass", itemID, count, player, true);
		}
	}

	@Override
	public void readImpl() {
		cPassType = readByte();
		bPremium = readByte();
	}

	@Override
	public void runImpl() {
		PlayerInstance player = client.getPlayer();
		if (player == null || !L2PassData.isEnabled()) {
			return;
		}

		if (!player.getL2Pass().hasUnclaimedRewards(bPremium == 0x01)) {
			return;
		}

		if (!player.isInventoryUnder80(false)) {
			// player.sendPacket(SystemMessageId.UNABLE_TO_PROCESS_THIS_REQUEST_UNTIL_YOUR_INVENTORY_S_WEIGHT_AND_SLOT_COUNT_ARE_LESS_THAN_80_PERCENT_OF_CAPACITY);
			player.sendPacket(SystemMessageId.YOUR_INVENTORY_WEIGHT_LIMIT_HAS_BEEN_EXCEEDED_SO_YOU_CANT_RECEIVE_THE_REWARD_PLEASE_FREE_UP_SOME_SPACE_AND_TRY_AGAIN);
			return;
		}

		if (bPremium == 0x01) {
			if (player.getL2Pass().isPremium()) {
				player.getL2Pass().addPremiumRewardStep(1);
				L2PassData.L2PassRewardStep l2PassRewardStep = L2PassData.getInstance().getPassTypesList().get(cPassType).get(player.getL2Pass().getPremiumRewardStep());
				rewardPlayer(player, l2PassRewardStep.getPremiumItemId(), l2PassRewardStep.getPremiumItemCount());
			} else {
				return;
			}
		} else {
			player.getL2Pass().addRewardStep(1);
			L2PassData.L2PassRewardStep l2PassRewardStep = L2PassData.getInstance().getPassTypesList().get(cPassType).get(player.getL2Pass().getRewardStep());
			rewardPlayer(player, l2PassRewardStep.getStandardItemId(), l2PassRewardStep.getStandardItemCount());
		}

		player.sendPacket(new ExL2PassInfo(player, cPassType));
		player.sendPacket(new ExSayhasSupportInfo(player));
	}
}