package club.projectessence.gameserver.network.clientpackets.festivalbm;

import club.projectessence.gameserver.instancemanager.events.GoldFestival.GoldFestivalEvent;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;

/**
 * <AUTHOR>
 */
public class ExRequestFestivalBmGame extends ClientPacket {
	@SuppressWarnings("unused")
	private byte _nTicketCount;

	@Override
	public void readImpl() {
		_nTicketCount = readByte();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}
		GoldFestivalEvent.getInstance().exchange(player);
	}
}