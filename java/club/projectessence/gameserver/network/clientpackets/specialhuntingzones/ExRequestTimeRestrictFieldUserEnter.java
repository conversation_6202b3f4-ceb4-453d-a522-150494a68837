/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.specialhuntingzones;

import club.projectessence.gameserver.data.xml.SpecialHuntingZoneData;
import club.projectessence.gameserver.enums.InstanceReenterType;
import club.projectessence.gameserver.enums.SpecialHuntingZoneType;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.holders.SpecialHuntingZoneHolder;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.instancezone.InstanceTemplate;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.olympiad.OlympiadManager;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class ExRequestTimeRestrictFieldUserEnter extends ClientPacket {
	private static final Map<Integer, Long> _timestamps = new ConcurrentHashMap<>();
	private static final Map<Integer, Map<Integer, Long>> _reenterTimeouts = new ConcurrentHashMap<>();

	private int _zoneId;

	@Override
	public void readImpl() {
		_zoneId = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if (player.isInCombat()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_IN_COMBAT);
			return;
		}
		if (player.isInDuel()) {
			player.sendPacket(SystemMessageId.CANNOT_BE_USED_DURING_A_DUEL);
			return;
		}
		if (player.isInOlympiadMode() || OlympiadManager.getInstance().isRegistered(player)) {
			player.sendPacket(SystemMessageId.SPECIAL_INSTANCE_ZONES_CANNOT_BE_USED_WHILE_WAITING_FOR_THE_OLYMPIAD);
			return;
		}
		if (player.isOnEvent() || (player.getBlockCheckerArena() > -1)) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_ENTER_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS);
			return;
		}
		if (player.isInInstance()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_IN_THE_INSTANCE_ZONE);
			return;
		}

		if (player.isInsideZone(ZoneId.UNDERGROUND_LABYRINTH)) {
			player.sendPacket(SystemMessageId.THHE_FUNCTION_IS_NOT_AVAILABLE_IN_THE_UNDERGROUND_LABYRINTH);
			return;
		}

		SpecialHuntingZoneHolder zoneHolder = SpecialHuntingZoneData.getInstance().getSpecialHuntingZoneById(_zoneId);
		if (zoneHolder == null) {
			LOGGER.warning(player + " requested unexisting special hunting zone ID: " + _zoneId);
			return;
		}

		if ((player.getLevel() < zoneHolder.getMinLevel()) || (player.getLevel() > zoneHolder.getMaxLevel())) {
			return;
		}

		if (!zoneHolder.isEnabled()) {
			LOGGER.warning(player + " requested disabled special hunting zone ID: " + _zoneId);
			return;
		}

		if (_timestamps.getOrDefault(player.getObjectId(), 0L) + 5000 > System.currentTimeMillis()) {
			return;
		}
		_timestamps.put(player.getObjectId(), System.currentTimeMillis());

		if (zoneHolder.getReenterTimeout() > 0 && _reenterTimeouts.computeIfAbsent(player.getObjectId(), k -> new HashMap<>()).getOrDefault(_zoneId, 0L) > System.currentTimeMillis()) {
			player.sendMessage("You can not re-enter for " + zoneHolder.getReenterTimeout() + " seconds.");
			return;
		}

		if (zoneHolder.getType() == SpecialHuntingZoneType.PUBLIC) {
			final long remainingTime = player.getVariables().getLong("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + _zoneId, zoneHolder.getDefaultTime() * 1000);
			final long remainingTimeReal = remainingTime == -1 ? zoneHolder.getDefaultTime() * 1000 : remainingTime;
			if (!zoneHolder.checkTimeLimit(player, true)) {
				return;
			}
			if (remainingTimeReal > 0) {
				if (!checkFees(player, zoneHolder)) {
					return;
				}

				if (zoneHolder.isGlobalInstance()) {
					player.teleToLocation(zoneHolder.getTeleportLocation(), SpecialHuntingZoneData.getInstance().getGlobalInstance(zoneHolder.getId()));
				} else {
					player.teleToLocation(zoneHolder.getTeleportLocation());
				}
			}
		} else {
			final InstanceManager manager = InstanceManager.getInstance();
			Instance instance = manager.getPlayerInstance(player, false);
			int templateId = zoneHolder.getInstanceId();
			if (instance != null) // Player has already any instance active
			{
				if (instance.getTemplateId() != templateId) {
					player.sendPacket(new SystemMessage(SystemMessageId.SINCE_C1_ENTERED_ANOTHER_INSTANCE_ZONE_THEREFORE_YOU_CANNOT_ENTER_THIS_DUNGEON).addString(player.getName()));
					return;
				}
				player.teleToLocation(instance.getEnterLocation(), instance);
			} else {
				if ((zoneHolder.getType() == SpecialHuntingZoneType.TRANSCENDENT) && player.getVariables().getBoolean(PlayerVariables.TRANSCENDENT_ZONE_USED, false)) {
					return;
				} else if ((zoneHolder.getType() == SpecialHuntingZoneType.TRAINING) && player.getVariables().getBoolean(PlayerVariables.TRAINING_ZONE_USED, false)) {
					return;
				}

				final InstanceTemplate template = manager.getInstanceTemplate(templateId);
				if (template == null) {
					LOGGER.warning("Player " + player.getName() + " (" + player.getObjectId() + ") wants to create instance with unknown template id " + templateId + "!");
					return;
				}

				// Check if maximum world count limit is exceeded
				if ((template.getMaxWorlds() != -1) && (manager.getWorldCount(templateId) >= template.getMaxWorlds())) {
					player.sendPacket(SystemMessageId.THE_NUMBER_OF_INSTANCE_ZONES_THAT_CAN_BE_CREATED_HAS_BEEN_EXCEEDED_PLEASE_TRY_AGAIN_LATER);
					return;
				}

				if (!checkFees(player, zoneHolder)) {
					return;
				}

				// Create new instance for enter player group
				instance = manager.createInstance(template, player);

				// Move player to instance
				instance.addAllowed(player);
				player.teleToLocation(instance.getEnterLocation(), instance);

				// Set re-enter for instances with re-enter on start
				if (instance.getReenterType() == InstanceReenterType.ON_ENTER) {
					instance.setReenterTime();
				}

				if (zoneHolder.getType() == SpecialHuntingZoneType.TRANSCENDENT) {
					player.getVariables().set(PlayerVariables.TRANSCENDENT_ZONE_USED, true);
				} else if (zoneHolder.getType() == SpecialHuntingZoneType.TRAINING) {
					player.getVariables().set(PlayerVariables.TRAINING_ZONE_USED, true);
				}
			}
		}

		if (zoneHolder.getReenterTimeout() > 0) {
			_reenterTimeouts.computeIfAbsent(player.getObjectId(), k -> new HashMap<>()).put(_zoneId, System.currentTimeMillis() + zoneHolder.getReenterTimeout());
		}
	}

	public boolean checkFees(PlayerInstance player, SpecialHuntingZoneHolder zoneHolder) {
		List<ItemHolder> fees = zoneHolder.getFees();
		for (ItemHolder fee : fees) {
			ItemInstance item = player.getInventory().getItemByItemId(fee.getId());
			if ((item == null) || (item.getCount() < fee.getCount())) {
				player.sendPacket(SystemMessageId.INCORRECT_ITEM_COUNT);
				return false;
			}
		}

		for (ItemHolder fee : fees) {
			player.destroyItemByItemId("Special Hunting Zone Enter", fee.getId(), fee.getCount(), player, true);
		}
		return true;
	}
}
