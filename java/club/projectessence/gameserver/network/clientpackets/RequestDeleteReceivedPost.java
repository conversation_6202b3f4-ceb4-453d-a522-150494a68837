/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.gameserver.instancemanager.MailManager;
import club.projectessence.gameserver.model.Message;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ExChangePostState;
import club.projectessence.gameserver.util.Util;

/**
 * <AUTHOR> DS
 */
public class RequestDeleteReceivedPost extends ClientPacket {
	private static final int BATCH_LENGTH = 4; // length of the one item

	int[] _msgIds = null;

	@Override
	public void readImpl() {
		final int count = readInt();
		if ((count <= 0) || (count > Config.MAX_ITEM_IN_PACKET) || ((count * BATCH_LENGTH) != available())) {
			return;
		}

		_msgIds = new int[count];
		for (int i = 0; i < count; i++) {
			_msgIds[i] = readInt();
		}
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if ((player == null) || (_msgIds == null) || !Config.ALLOW_MAIL) {
			return;
		}

		if (player.isDead()) {
			player.sendPacket(SystemMessageId.PLEASE_TRY_AGAIN_LATER);
			return;
		}

		for (int msgId : _msgIds) {
			final Message msg = MailManager.getInstance().getMessage(msgId);
			if (msg == null) {
				continue;
			}
			if (msg.getReceiverId() != player.getObjectId()) {
				Util.handleIllegalPlayerAction(player, "Player " + player.getName() + " tried to delete not own post!", Config.DEFAULT_PUNISH);
				return;
			}

			if (msg.hasAttachments() || msg.isDeletedByReceiver()) {
				return;
			}

			msg.setDeletedByReceiver();
		}
		client.sendPacket(new ExChangePostState(true, _msgIds, Message.DELETED));
	}
}
