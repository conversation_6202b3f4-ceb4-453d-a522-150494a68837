/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.friend;

import club.projectessence.Config;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.L2FriendSay;

import java.util.logging.Logger;

/**
 * Recieve Private (Friend) Message - 0xCC Format: c SS S: Message S: Receiving Player
 *
 * <AUTHOR>
 */
public class RequestSendFriendMsg extends ClientPacket {
	private static Logger LOGGER_CHAT = Logger.getLogger("chat");

	private String _message;
	private String _reciever;

	@Override
	public void readImpl() {
		_message = readString();
		_reciever = readString();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if ((_message == null) || _message.isEmpty() || (_message.length() > 300)) {
			return;
		}

		final PlayerInstance targetPlayer = World.getInstance().getPlayer(_reciever);
		if ((targetPlayer == null) || !targetPlayer.getFriendList().contains(player.getObjectId())) {
			player.sendPacket(SystemMessageId.THAT_PLAYER_IS_NOT_ONLINE);
			return;
		}

		if (Config.LOG_CHAT) {
			LOGGER_CHAT.info("PRIV_MSG [" + player + " to " + targetPlayer + "] " + _message);
		}

		targetPlayer.sendPacket(new L2FriendSay(player.getName(), _reciever, _message));
	}
}
