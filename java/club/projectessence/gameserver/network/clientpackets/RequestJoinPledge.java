/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.AskJoinPledge;

/**
 * @version $Revision: 1.3.4.4 $ $Date: 2005/03/27 15:29:30 $
 */
public class RequestJoin<PERSON>ledge extends ClientPacket {
	private int _target;
	private int _pledgeType;

	@Override
	public void readImpl() {
		_target = readInt();
		_pledgeType = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		final Clan clan = player.getClan();
		if (clan == null) {
			return;
		}

		final PlayerInstance target = World.getInstance().getPlayer(_target);
		if (target == null) {
			player.sendPacket(SystemMessageId.THE_TARGET_CANNOT_BE_INVITED);
			return;
		}

		if (!clan.checkClanJoinCondition(player, target, _pledgeType)) {
			return;
		}

		// Faction restriction: Cannot invite different faction to clan
		if (!player.canInteractWithFaction(target)) {
			player.sendMessage("You cannot invite players from enemies factions to your clan.");
			return;
		}

		if (!player.getRequest().setRequest(target, this)) {
			return;
		}

		final String pledgeName = player.getClan().getName();
		target.sendPacket(new AskJoinPledge(player, _pledgeType, pledgeName));
	}

	public int getPledgeType() {
		return _pledgeType;
	}
}
