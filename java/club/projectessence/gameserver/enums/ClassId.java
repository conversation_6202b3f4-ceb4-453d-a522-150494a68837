/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.enums;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import club.projectessence.gameserver.model.interfaces.IIdentifiable;
import gabriel.eventEngine.l2j.ClassType;

/**
 * This class defines all classes (ex : human fighter, darkFighter...) that a player can chose.<br>
 * Data:
 * <ul>
 * <li>id : The Identifier of the class</li>
 * <li>isMage : True if the class is a mage class</li>
 * <li>race : The race of this class</li>
 * <li>parent : The parent ClassId or null if this class is the root</li>
 * </ul>
 *
 * @version $Revision: 1.4.4.4 $ $Date: 2005/03/27 15:29:33 $
 */
public enum ClassId implements IIdentifiable
{
	FIGHTER(0, false, Race.HUMAN, null, ClassType.Fighter),
	WARRIOR(1, false, Race.HUMAN, FIGHTER, ClassType.Fighter),
	GLADIATOR(2, false, Race.HUMAN, WARRIOR, ClassType.Fighter),
	WARLORD(3, false, Race.HUMAN, WARRIOR, ClassType.Fighter),
	KNIGHT(4, false, Race.HUMAN, FIGHTER, ClassType.Fighter),
	PALADIN(5, false, Race.HUMAN, KNIGHT, ClassType.Fighter),
	DARK_AVENGER(6, false, Race.HUMAN, KNIGHT, ClassType.Fighter),
	ROGUE(7, false, Race.HUMAN, FIGHTER, ClassType.Fighter),
	TREASURE_HUNTER(8, false, Race.HUMAN, ROGUE, ClassType.Fighter),
	HAWKEYE(9, false, Race.HUMAN, ROGUE, ClassType.Fighter),
	MAGE(10, true, Race.HUMAN, null, ClassType.Mystic),
	WIZARD(11, true, Race.HUMAN, MAGE, ClassType.Mystic),
	SORCERER(12, true, Race.HUMAN, WIZARD, ClassType.Mystic),
	NECROMANCER(13, true, Race.HUMAN, WIZARD, ClassType.Mystic),
	WARLOCK(14, true, true, Race.HUMAN, WIZARD, ClassType.Mystic),
	CLERIC(15, true, Race.HUMAN, MAGE, ClassType.Priest),
	BISHOP(16, true, Race.HUMAN, CLERIC, ClassType.Priest),
	PROPHET(17, true, Race.HUMAN, CLERIC, ClassType.Priest),
	ELVEN_FIGHTER(18, false, Race.ELF, null, ClassType.Fighter),
	ELVEN_KNIGHT(19, false, Race.ELF, ELVEN_FIGHTER, ClassType.Fighter),
	TEMPLE_KNIGHT(20, false, Race.ELF, ELVEN_KNIGHT, ClassType.Fighter),
	SWORDSINGER(21, false, Race.ELF, ELVEN_KNIGHT, ClassType.Fighter),
	ELVEN_SCOUT(22, false, Race.ELF, ELVEN_FIGHTER, ClassType.Fighter),
	PLAINS_WALKER(23, false, Race.ELF, ELVEN_SCOUT, ClassType.Fighter),
	SILVER_RANGER(24, false, Race.ELF, ELVEN_SCOUT, ClassType.Fighter),
	ELVEN_MAGE(25, true, Race.ELF, null, ClassType.Mystic),
	ELVEN_WIZARD(26, true, Race.ELF, ELVEN_MAGE, ClassType.Mystic),
	SPELLSINGER(27, true, Race.ELF, ELVEN_WIZARD, ClassType.Mystic),
	ELEMENTAL_SUMMONER(28, true, true, Race.ELF, ELVEN_WIZARD, ClassType.Mystic),
	ORACLE(29, true, Race.ELF, ELVEN_MAGE, ClassType.Priest),
	ELDER(30, true, Race.ELF, ORACLE, ClassType.Priest),
	DARK_FIGHTER(31, false, Race.DARK_ELF, null, ClassType.Fighter),
	PALUS_KNIGHT(32, false, Race.DARK_ELF, DARK_FIGHTER, ClassType.Fighter),
	SHILLIEN_KNIGHT(33, false, Race.DARK_ELF, PALUS_KNIGHT, ClassType.Fighter),
	BLADEDANCER(34, false, Race.DARK_ELF, PALUS_KNIGHT, ClassType.Fighter),
	ASSASSIN(35, false, Race.DARK_ELF, DARK_FIGHTER, ClassType.Fighter),
	ABYSS_WALKER(36, false, Race.DARK_ELF, ASSASSIN, ClassType.Fighter),
	PHANTOM_RANGER(37, false, Race.DARK_ELF, ASSASSIN, ClassType.Fighter),
	DARK_MAGE(38, true, Race.DARK_ELF, null, ClassType.Mystic),
	DARK_WIZARD(39, true, Race.DARK_ELF, DARK_MAGE, ClassType.Mystic),
	SPELLHOWLER(40, true, Race.DARK_ELF, DARK_WIZARD, ClassType.Mystic),
	PHANTOM_SUMMONER(41, true, true, Race.DARK_ELF, DARK_WIZARD, ClassType.Mystic),
	SHILLIEN_ORACLE(42, true, Race.DARK_ELF, DARK_MAGE, ClassType.Priest),
	SHILLIEN_ELDER(43, true, Race.DARK_ELF, SHILLIEN_ORACLE, ClassType.Priest),
	ORC_FIGHTER(44, false, Race.ORC, null, ClassType.Fighter),
	ORC_RAIDER(45, false, Race.ORC, ORC_FIGHTER, ClassType.Fighter),
	DESTROYER(46, false, Race.ORC, ORC_RAIDER, ClassType.Fighter),
	ORC_MONK(47, false, Race.ORC, ORC_FIGHTER, ClassType.Fighter),
	TYRANT(48, false, Race.ORC, ORC_MONK, ClassType.Fighter),
	ORC_MAGE(49, true, Race.ORC, null, ClassType.Mystic),
	ORC_SHAMAN(50, true, Race.ORC, ORC_MAGE, ClassType.Mystic),
	OVERLORD(51, true, Race.ORC, ORC_SHAMAN, ClassType.Priest),
	WARCRYER(52, true, Race.ORC, ORC_SHAMAN, ClassType.Priest),
	DWARVEN_FIGHTER(53, false, Race.DWARF, null, ClassType.Fighter),
	SCAVENGER(54, false, Race.DWARF, DWARVEN_FIGHTER, ClassType.Fighter),
	BOUNTY_HUNTER(55, false, Race.DWARF, SCAVENGER, ClassType.Fighter),
	ARTISAN(56, false, Race.DWARF, DWARVEN_FIGHTER, ClassType.Fighter),
	WARSMITH(57, false, Race.DWARF, ARTISAN, ClassType.Fighter),
	DUELIST(88, false, Race.HUMAN, GLADIATOR, ClassType.Fighter),
	DREADNOUGHT(89, false, Race.HUMAN, WARLORD, ClassType.Fighter),
	PHOENIX_KNIGHT(90, false, Race.HUMAN, PALADIN, ClassType.Fighter),
	HELL_KNIGHT(91, false, Race.HUMAN, DARK_AVENGER, ClassType.Fighter),
	SAGITTARIUS(92, false, Race.HUMAN, HAWKEYE, ClassType.Fighter),
	ADVENTURER(93, false, Race.HUMAN, TREASURE_HUNTER, ClassType.Fighter),
	ARCHMAGE(94, true, Race.HUMAN, SORCERER, ClassType.Mystic),
	SOULTAKER(95, true, Race.HUMAN, NECROMANCER, ClassType.Mystic),
	ARCANA_LORD(96, true, true, Race.HUMAN, WARLOCK, ClassType.Mystic),
	CARDINAL(97, true, Race.HUMAN, BISHOP, ClassType.Priest),
	HIEROPHANT(98, true, Race.HUMAN, PROPHET, ClassType.Priest),
	EVA_TEMPLAR(99, false, Race.ELF, TEMPLE_KNIGHT, ClassType.Fighter),
	SWORD_MUSE(100, false, Race.ELF, SWORDSINGER, ClassType.Fighter),
	WIND_RIDER(101, false, Race.ELF, PLAINS_WALKER, ClassType.Fighter),
	MOONLIGHT_SENTINEL(102, false, Race.ELF, SILVER_RANGER, ClassType.Fighter),
	MYSTIC_MUSE(103, true, Race.ELF, SPELLSINGER, ClassType.Mystic),
	ELEMENTAL_MASTER(104, true, true, Race.ELF, ELEMENTAL_SUMMONER, ClassType.Mystic),
	EVA_SAINT(105, true, Race.ELF, ELDER, ClassType.Priest),
	SHILLIEN_TEMPLAR(106, false, Race.DARK_ELF, SHILLIEN_KNIGHT, ClassType.Fighter),
	SPECTRAL_DANCER(107, false, Race.DARK_ELF, BLADEDANCER, ClassType.Fighter),
	GHOST_HUNTER(108, false, Race.DARK_ELF, ABYSS_WALKER, ClassType.Fighter),
	GHOST_SENTINEL(109, false, Race.DARK_ELF, PHANTOM_RANGER, ClassType.Fighter),
	STORM_SCREAMER(110, true, Race.DARK_ELF, SPELLHOWLER, ClassType.Mystic),
	SPECTRAL_MASTER(111, true, true, Race.DARK_ELF, PHANTOM_SUMMONER, ClassType.Mystic),
	SHILLIEN_SAINT(112, true, Race.DARK_ELF, SHILLIEN_ELDER, ClassType.Mystic),
	TITAN(113, false, Race.ORC, DESTROYER, ClassType.Fighter),
	GRAND_KHAVATARI(114, false, Race.ORC, TYRANT, ClassType.Fighter),
	DOMINATOR(115, true, Race.ORC, OVERLORD, ClassType.Mystic),
	DOOMCRYER(116, true, Race.ORC, WARCRYER, ClassType.Mystic),
	FORTUNE_SEEKER(117, false, Race.DWARF, BOUNTY_HUNTER, ClassType.Fighter),
	MAESTRO(118, false, Race.DWARF, WARSMITH, ClassType.Fighter),
	KAMAEL_SOLDIER(192, false, Race.KAMAEL, null, ClassType.Fighter),
	TROOPER(125, false, Race.KAMAEL, KAMAEL_SOLDIER, ClassType.Fighter),
	SOUL_FINDER(193, false, Race.KAMAEL, KAMAEL_SOLDIER, ClassType.Fighter),
	WARDER(126, false, Race.KAMAEL, KAMAEL_SOLDIER, ClassType.Fighter),
	BERSERKER(127, false, Race.KAMAEL, TROOPER, ClassType.Fighter),
	SOUL_BREAKER(194, false, Race.KAMAEL, SOUL_FINDER, ClassType.Fighter),
	SOUL_RANGER(130, false, Race.KAMAEL, WARDER, ClassType.Fighter),
	DOOMBRINGER(131, false, Race.KAMAEL, BERSERKER, ClassType.Fighter),
	SOUL_HOUND(195, false, Race.KAMAEL, SOUL_BREAKER, ClassType.Fighter),
	TRICKSTER(134, false, Race.KAMAEL, SOUL_RANGER, ClassType.Fighter),
	DEATH_PILGRIM_HUMAN(196, false, Race.HUMAN, null, ClassType.Fighter),
	DEATH_BLADE_HUMAN(197, false, Race.HUMAN, DEATH_PILGRIM_HUMAN, ClassType.Fighter),
	DEATH_MESSENGER_HUMAN(198, false, Race.HUMAN, DEATH_BLADE_HUMAN, ClassType.Fighter),
	DEATH_KIGHT_HUMAN(199, false, Race.HUMAN, DEATH_MESSENGER_HUMAN, ClassType.Fighter),
	DEATH_PILGRIM_ELF(200, false, Race.ELF, null, ClassType.Fighter),
	DEATH_BLADE_ELF(201, false, Race.ELF, DEATH_PILGRIM_ELF, ClassType.Fighter),
	DEATH_MESSENGER_ELF(202, false, Race.ELF, DEATH_BLADE_ELF, ClassType.Fighter),
	DEATH_KIGHT_ELF(203, false, Race.ELF, DEATH_MESSENGER_ELF, ClassType.Fighter),
	DEATH_PILGRIM_DARK_ELF(204, false, Race.DARK_ELF, null, ClassType.Fighter),
	DEATH_BLADE_DARK_ELF(205, false, Race.DARK_ELF, DEATH_PILGRIM_DARK_ELF, ClassType.Fighter),
	DEATH_MESSENGER_DARK_ELF(206, false, Race.DARK_ELF, DEATH_BLADE_DARK_ELF, ClassType.Fighter),
	DEATH_KIGHT_DARK_ELF(207, false, Race.DARK_ELF, DEATH_MESSENGER_DARK_ELF, ClassType.Fighter),
	SYLPH_GUNNER(208, false, Race.SYLPH, null, ClassType.Fighter),
	SHARPSHOOTER(209, false, Race.SYLPH, SYLPH_GUNNER, ClassType.Fighter),
	WIND_SNIPER(210, false, Race.SYLPH, SHARPSHOOTER, ClassType.Fighter),
	STORM_BLASTER(211, false, Race.SYLPH, WIND_SNIPER, ClassType.Fighter),
	ORC_LANCER(217, false, Race.ORC, null, ClassType.Fighter),
	RIDER(218, false, Race.ORC, ORC_LANCER, ClassType.Fighter),
	DRAGOON(219, false, Race.ORC, RIDER, ClassType.Fighter),
	VANGUARD_RIDER(220, false, Race.ORC, DRAGOON, ClassType.Fighter);
	
	private static Map<Integer, ClassId> _classIdMap = new HashMap<>(ClassId.values().length);
	static
	{
		for (ClassId classId : ClassId.values())
		{
			_classIdMap.put(classId.getId(), classId);
		}
	}
	/**
	 * The Identifier of the Class
	 */
	private final int			_id;
	/**
	 * True if the class is a mage class
	 */
	private final boolean		_isMage;
	/**
	 * True if the class is a summoner class
	 */
	private final boolean		_isSummoner;
	/**
	 * The Race object of the class
	 */
	private final Race			_race;
	/**
	 * The parent ClassId or null if this class is a root
	 */
	private final ClassId		_parent;
	/**
	 * List of available Class for next transfer
	 **/
	private final Set<ClassId>	_nextClassIds	= new HashSet<>(1);
	private ClassType			_type			= ClassType.Fighter;
	
	private ClassId(int pId, boolean pIsMage, Race race, ClassId pParent, ClassType classType)
	{
		_id = pId;
		_isMage = pIsMage;
		_isSummoner = false;
		_race = race;
		_parent = pParent;
		if (_parent != null)
		{
			_parent.addNextClassId(this);
		}
		_type = classType;
	}
	
	/**
	 * Class constructor.
	 *
	 * @param pId
	 *            the class Id.
	 * @param pIsMage
	 *            {code true} if the class is mage class.
	 * @param race
	 *            the race related to the class.
	 * @param pParent
	 *            the parent class Id.
	 */
	private ClassId(int pId, boolean pIsMage, Race race, ClassId pParent)
	{
		_id = pId;
		_isMage = pIsMage;
		_isSummoner = false;
		_race = race;
		_parent = pParent;
		if (_parent != null)
		{
			_parent.addNextClassId(this);
		}
	}
	
	/**
	 * Class constructor.
	 *
	 * @param pId
	 *            the class Id.
	 * @param pIsMage
	 *            {code true} if the class is mage class.
	 * @param pIsSummoner
	 *            {code true} if the class is summoner class.
	 * @param race
	 *            the race related to the class.
	 * @param pParent
	 *            the parent class Id.
	 */
	private ClassId(int pId, boolean pIsMage, boolean pIsSummoner, Race race, ClassId pParent)
	{
		_id = pId;
		_isMage = pIsMage;
		_isSummoner = pIsSummoner;
		_race = race;
		_parent = pParent;
		if (_parent != null)
		{
			_parent.addNextClassId(this);
		}
	}
	
	public static ClassId getClassId(int cId)
	{
		return _classIdMap.get(cId);
	}
	
	private ClassId(int pId, boolean pIsMage, boolean pIsSummoner, Race race, ClassId pParent, ClassType classType)
	{
		_id = pId;
		_isMage = pIsMage;
		_isSummoner = pIsSummoner;
		_race = race;
		_parent = pParent;
		if (_parent != null)
		{
			_parent.addNextClassId(this);
		}
		_type = classType;
	}
	
	/**
	 * Gets the ID of the class.
	 *
	 * @return the ID of the class
	 */
	@Override
	public int getId()
	{
		return _id;
	}
	
	/**
	 * @return {code true} if the class is a mage class.
	 */
	public boolean isMage()
	{
		return _isMage;
	}
	
	public boolean isDeathKnight()
	{
		return (_id >= 196) && (_id <= 207);
	}
	
	public boolean isVanguard()
	{
		return (_id >= 217) && (_id <= 220);
	}
	
	public boolean isSyplh()
	{
		return (_id >= 208) && (_id <= 211);
	}
	
	public int getVanguardBeastId()
	{
		switch (this)
		{
			case ORC_LANCER:
				return 1;
			case RIDER:
				return 1;
			case DRAGOON:
				return 2;
			case VANGUARD_RIDER:
				return 3;
			default:
				return 0;
		}
	}
	
	public boolean isArcher()
	{
		switch (this)
		{
			case ROGUE:
			case HAWKEYE:
			case SAGITTARIUS:
			case ELVEN_SCOUT:
			case SILVER_RANGER:
			case MOONLIGHT_SENTINEL:
			case ASSASSIN:
			case PHANTOM_RANGER:
			case GHOST_SENTINEL:
			case WARDER:
			case SOUL_RANGER:
			case TRICKSTER:
			{
				return true;
			}
			default:
			{
				return false;
			}
		}
	}
	
	public boolean isKamael()
	{
		switch (this)
		{
			case KAMAEL_SOLDIER:
			case TROOPER:
			case SOUL_FINDER:
			case WARDER:
			case BERSERKER:
			case SOUL_BREAKER:
			case SOUL_RANGER:
			case DOOMBRINGER:
			case SOUL_HOUND:
			case TRICKSTER:
			{
				return true;
			}
			default:
			{
				return false;
			}
		}
	}
	
	/**
	 * @return {code true} if the class is a summoner class.
	 */
	public boolean isSummoner()
	{
		return _isSummoner;
	}
	
	/**
	 * @return the Race object of the class.
	 */
	public Race getRace()
	{
		return _race;
	}
	
	/**
	 * @param cid
	 *            the parent ClassId to check.
	 * @return {code true} if this Class is a child of the selected ClassId.
	 */
	public boolean childOf(ClassId cid)
	{
		if (_parent == null)
		{
			return false;
		}
		if (_parent == cid)
		{
			return true;
		}
		return _parent.childOf(cid);
	}
	
	/**
	 * @param cid
	 *            the parent ClassId to check.
	 * @return {code true} if this Class is equal to the selected ClassId or a child of the selected ClassId.
	 */
	public boolean equalsOrChildOf(ClassId cid)
	{
		return (this == cid) || childOf(cid);
	}
	
	/**
	 * @return the child level of this Class (0=root, 1=child leve 1...)
	 */
	public int level()
	{
		if (_parent == null)
		{
			return 0;
		}
		return 1 + _parent.level();
	}
	
	/**
	 * @return its parent Class Id
	 */
	public ClassId getParent()
	{
		return _parent;
	}
	
	public ClassId getRootClassId()
	{
		if (_parent != null)
		{
			return _parent.getRootClassId();
		}
		return this;
	}
	
	public final boolean isOfType(ClassType pType)
	{
		return _type == pType;
	}
	
	/**
	 * @return list of possible class transfer for this class
	 */
	public Set<ClassId> getNextClassIds()
	{
		return _nextClassIds;
	}
	
	private final void addNextClassId(ClassId cId)
	{
		_nextClassIds.add(cId);
	}
}
