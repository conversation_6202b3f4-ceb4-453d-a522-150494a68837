package club.projectessence.gameserver.enums;

/**
 * Enum representing different leadership achievements and their rewards.
 */
public enum LeadershipAchievement
{
	// Combat Achievements
	FIRST_BLOOD("First Blood", "Kill your first enemy as faction leader", AchievementCategory.COMBAT, 50, 1),
	WARMONGER("Warmonger", "Win 10 faction wars", AchievementCategory.COMBAT, 500, 10),
	DEFENDER("Defender", "Successfully defend faction zone 50 times", AchievementCategory.COMBAT, 300, 50),
	<PERSON><PERSON>Y<PERSON>("Slayer", "Kill 100 enemy faction members", AchievementCategory.COMBAT, 200, 100),
	UNTOUCHABLE("Untouchable", "Survive 24 hours without dying as leader", AchievementCategory.COMBAT, 150, 1),
	ZONE_MASTER("Zone Master", "Control faction zone for 100 hours total", AchievementCategory.COMBAT, 250, 100),
	
	// Leadership Achievements  
	RECRUITER("Recruiter", "Recruit 50 faction members", AchievementCategory.LEADERSHIP, 250, 50),
	MENTOR("Mentor", "Help 20 members reach max level", AchievementCategory.LEADERSHIP, 400, 20),
	DIP<PERSON><PERSON><PERSON>("Diplomat", "Establish 5 successful alliances", AchievementCategory.LEADERSHIP, 300, 5),
	ECONOMIST("Economist", "Grow faction treasury to 1,000,000 adena", AchievementCategory.LEADERSHIP, 350, 1000000),
	STRATEGIST("Strategist", "Capture 10 outposts", AchievementCategory.LEADERSHIP, 400, 10),
	LONG_TERM("Long Term Leader", "Serve as leader for 30 days total", AchievementCategory.LEADERSHIP, 500, 30),
	
	// Social Achievements
	POPULAR("Popular Leader", "Maintain 90%+ faction member satisfaction", AchievementCategory.SOCIAL, 200, 90),
	UNIFIER("Unifier", "Unite 3 different factions in alliance", AchievementCategory.SOCIAL, 600, 3),
	PEACEMAKER("Peacemaker", "End 5 faction wars through diplomacy", AchievementCategory.SOCIAL, 400, 5),
	ORGANIZER("Event Organizer", "Successfully organize 10 faction events", AchievementCategory.SOCIAL, 300, 10),
	
	// Economic Achievements
	TRADER("Master Trader", "Complete 100 trade agreements", AchievementCategory.ECONOMIC, 250, 100),
	WEALTHY("Wealthy Leader", "Accumulate 10,000,000 adena in treasury", AchievementCategory.ECONOMIC, 800, 10000000),
	EFFICIENT("Efficiency Expert", "Maintain faction efficiency above 80% for 7 days", AchievementCategory.ECONOMIC, 300, 7),
	
	// Prestige Achievements
	LEGEND("Living Legend", "Reach Leadership Level 8", AchievementCategory.PRESTIGE, 1000, 8),
	MYTHIC("Mythic Leader", "Reach Leadership Level 9", AchievementCategory.PRESTIGE, 1500, 9),
	TRANSCENDENT("Transcendent", "Reach Leadership Level 10", AchievementCategory.PRESTIGE, 2000, 10),
	ETERNAL("Eternal Leader", "Complete 10 full terms as leader", AchievementCategory.PRESTIGE, 2500, 10);
	
	public enum AchievementCategory
	{
		COMBAT("Combat", "⚔️"),
		LEADERSHIP("Leadership", "👑"), 
		SOCIAL("Social", "🤝"),
		ECONOMIC("Economic", "💰"),
		PRESTIGE("Prestige", "🌟");
		
		private final String name;
		private final String icon;
		
		AchievementCategory(String name, String icon)
		{
			this.name = name;
			this.icon = icon;
		}
		
		public String getName() { return name; }
		public String getIcon() { return icon; }
	}
	
	private final String name;
	private final String description;
	private final AchievementCategory category;
	private final int lpReward;
	private final int requirement;
	
	LeadershipAchievement(String name, String description, AchievementCategory category, int lpReward, int requirement)
	{
		this.name = name;
		this.description = description;
		this.category = category;
		this.lpReward = lpReward;
		this.requirement = requirement;
	}
	
	public String getName() { return name; }
	public String getDescription() { return description; }
	public AchievementCategory getCategory() { return category; }
	public int getLpReward() { return lpReward; }
	public int getRequirement() { return requirement; }
	
	/**
	 * Get achievements by category
	 */
	public static LeadershipAchievement[] getByCategory(AchievementCategory category)
	{
		return java.util.Arrays.stream(values())
			.filter(achievement -> achievement.getCategory() == category)
			.toArray(LeadershipAchievement[]::new);
	}
}
