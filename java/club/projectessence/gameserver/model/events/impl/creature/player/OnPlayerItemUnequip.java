/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.events.impl.creature.player;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.IBaseEvent;
import club.projectessence.gameserver.model.items.instance.ItemInstance;

/**
 * <AUTHOR>
 */
public class OnPlayerItemUnequip implements IBaseEvent {
	private final PlayerInstance _player;
	private final ItemInstance _item;

	public OnPlayerItemUnequip(PlayerInstance player, ItemInstance item) {
		_player = player;
		_item = item;
	}

	public PlayerInstance getPlayer() {
		return _player;
	}

	public ItemInstance getItem() {
		return _item;
	}

	@Override
	public EventType getType() {
		return EventType.ON_PLAYER_ITEM_UNEQUIP;
	}
}