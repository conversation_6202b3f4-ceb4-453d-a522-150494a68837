/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.events.impl.creature;

import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.IBaseEvent;

/**
 * <AUTHOR>
 */
public class OnCreatureMaxHpChange implements IBaseEvent {
	private final Creature _creature;
	private final int _newMaxHp;
	private final int _oldMaxHp;

	public OnCreatureMaxHpChange(Creature creature, int oldMaxHp, int newMaxHp) {
		_creature = creature;
		_oldMaxHp = oldMaxHp;
		_newMaxHp = newMaxHp;
	}

	public Creature getCreature() {
		return _creature;
	}

	public int getOldMaxHp() {
		return _oldMaxHp;
	}

	public int getNewMaxHp() {
		return _newMaxHp;
	}

	@Override
	public EventType getType() {
		return EventType.ON_CREATURE_MAX_HP_CHANGE;
	}
}
