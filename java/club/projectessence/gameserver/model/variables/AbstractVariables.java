/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.variables;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.interfaces.IDeletable;
import club.projectessence.gameserver.model.interfaces.IRestorable;
import club.projectessence.gameserver.model.interfaces.IStorable;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
public abstract class AbstractVariables extends StatSet implements IRestorable, IStorable, IDeletable {
	private final AtomicBoolean _hasChanges = new AtomicBoolean(false);

	/**
	 * Overriding following methods to prevent from doing useless database operations if there is no changes since player's login.
	 */

	@Override
	public void set(String name, double value) {
		_hasChanges.compareAndSet(false, true);
		super.set(name, value);
	}

	@Override
	public void set(String name, Enum<?> value) {
		_hasChanges.compareAndSet(false, true);
		super.set(name, value);
	}

	@Override
	public void set(String name, int value) {
		_hasChanges.compareAndSet(false, true);
		super.set(name, value);
	}

	@Override
	public void set(String name, long value) {
		_hasChanges.compareAndSet(false, true);
		super.set(name, value);
	}

	@Override
	public void set(String name, String value) {
		_hasChanges.compareAndSet(false, true);
		super.set(name, value);
	}

	/**
	 * Put's entry to the variables and marks as changed if required (<i>Useful when restoring to do not save them again</i>).
	 *
	 * @param name
	 * @param value
	 * @param markAsChanged
	 */
	public void set(String name, String value, boolean markAsChanged) {
		if (markAsChanged) {
			_hasChanges.compareAndSet(false, true);
		}
		super.set(name, value);
	}

	/**
	 * Return true if there exists a record for the variable name.
	 *
	 * @param name
	 * @return
	 */
	public boolean hasVariable(String name) {
		return getSet().keySet().contains(name);
	}

	public void setHasChanges(boolean val) {
		_hasChanges.set(val);
	}

	/**
	 * @return {@code true} if changes are made since last load/save.
	 */
	public boolean hasChanges() {
		return _hasChanges.get();
	}

	/**
	 * Atomically sets the value to the given updated value if the current value {@code ==} the expected value.
	 *
	 * @param expect
	 * @param update
	 * @return {@code true} if successful. {@code false} return indicates that the actual value was not equal to the expected value.
	 */
	public boolean compareAndSetChanges(boolean expect, boolean update) {
		return _hasChanges.compareAndSet(expect, update);
	}

	/**
	 * Removes variable
	 *
	 * @param name
	 */
	@Override
	public void remove(String name) {
		_hasChanges.compareAndSet(false, true);
		getSet().remove(name);
	}
}
