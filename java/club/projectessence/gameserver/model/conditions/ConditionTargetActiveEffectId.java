/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.conditions;

import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.skills.BuffInfo;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * The Class ConditionTargetActiveEffectId.
 */
public class ConditionTargetActiveEffectId extends Condition {
	private final int _effectId;
	private final int _effectLvl;

	/**
	 * Instantiates a new condition target active effect id.
	 *
	 * @param effectId the effect id
	 */
	public ConditionTargetActiveEffectId(int effectId) {
		_effectId = effectId;
		_effectLvl = -1;
	}

	/**
	 * Instantiates a new condition target active effect id.
	 *
	 * @param effectId    the effect id
	 * @param effectLevel the effect level
	 */
	public ConditionTargetActiveEffectId(int effectId, int effectLevel) {
		_effectId = effectId;
		_effectLvl = effectLevel;
	}

	@Override
	public boolean testImpl(Creature effector, Creature effected, Skill skill, Item item) {
		final BuffInfo info = effected.getEffectList().getBuffInfoBySkillId(_effectId);
		return (info != null) && ((_effectLvl == -1) || (_effectLvl <= info.getSkill().getLevel()));
	}
}
