/*
 * Copyright (C) 2004-2020 L2J Server
 * 
 * This file is part of L2J Server.
 * 
 * L2J Server is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * L2J Server is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.stats.finalizers;

import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.stats.IStatFunction;
import club.projectessence.gameserver.model.stats.Stat;

import java.util.OptionalDouble;

/**
 * Max Magic Critical Rate Finalizer - Handles enchant balance for max magic critical rate stats
 * <AUTHOR>
 */
public class MaxMagicCriticalRateFinalizer implements IStatFunction
{
    @Override
    public double calc(Creature creature, OptionalDouble base, Stat stat)
    {
        throwIfPresent(base);
        
        // Calculate enchant bonuses with balance system applied
        double baseValue = calcEnchantedItemBonus(creature, stat);
        
        return Stat.defaultValue(creature, stat, baseValue);
    }
}
