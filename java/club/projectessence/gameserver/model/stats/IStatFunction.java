/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.stats;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.BalanceItemData;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PetInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.transform.TransformType;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.CrystalType;
import club.projectessence.gameserver.model.items.type.WeaponType;

import java.util.OptionalDouble;

/**
 * <AUTHOR>
 */
@FunctionalInterface
public interface IStatFunction {
	/**
	 * @param item
	 * @param blessedBonus
	 * @param enchant
	 * @return
	 */
	static double calcEnchantmDefBonus(ItemInstance item, double blessedBonus, int enchant) {
		switch (item.getItem().getCrystalTypePlus()) {
			case A: {
				return ((3 * enchant) + (4 * Math.max(0, enchant - 3)));
			}
			default: {
				return enchant + (2 * Math.max(0, enchant - 3));
			}
		}
	}

	static double calcEnchantDefBonus(ItemInstance item, double blessedBonus, int enchant) {
		switch (item.getItem().getCrystalTypePlus()) {
			case R: {
				return ((2 * blessedBonus * enchant) + (6 * blessedBonus * Math.max(0, enchant - 3)));
			}
			case A: {
				return ((4 * enchant) + (5 * Math.max(0, enchant - 3)));
			}
			default: {
				return enchant + (3 * Math.max(0, enchant - 3));
			}
		}
	}

	/**
	 * @param item
	 * @param blessedBonus
	 * @param enchant
	 * @return
	 */
	static double calcEnchantMatkBonus(ItemInstance item, double blessedBonus, int enchant) {
		if (item.getId() == 93864) // Death Knight's Flame Sword
		{
			if ((item.getWeaponItem().getBodyPart() == Item.BodyPart.SLOT_LR_HAND.getSlot())) {
				return (6 * enchant) + (12 * Math.max(0, enchant - 3));
			}
			return 0;
		}
		switch (item.getItem().getCrystalTypePlus()) {
			case R: {
				return ((5 * blessedBonus * enchant) + (10 * blessedBonus * Math.max(0, enchant - 3)));
			}
			case S: {
				return (10 * enchant) + (33 * Math.max(0, enchant - 3));
			}
			case A: {
				return (6 * enchant) + (12 * Math.max(0, enchant - 3));
			}
			case B:
			case C:
			case D: {
				return (3 * enchant) + (3 * Math.max(0, enchant - 3));
			}
			default: {
				return (2 * enchant) + (2 * Math.max(0, enchant - 3));
			}
		}
	}

	/**
	 * @param item
	 * @param blessedBonus
	 * @param enchant
	 * @return
	 */
	static double calcEnchantedPAtkBonus(ItemInstance item, double blessedBonus, int enchant) {
		if (item.getId() == 93864) // Death Knight's Flame Sword
		{
			if ((item.getWeaponItem().getBodyPart() == Item.BodyPart.SLOT_LR_HAND.getSlot())) {
				return (10 * enchant) + (20 * Math.max(0, enchant - 3));
			}
			return 0;
		}
		switch (item.getItem().getCrystalTypePlus()) {
			case R: {
				if ((item.getWeaponItem().getBodyPart() == Item.BodyPart.SLOT_LR_HAND.getSlot()) && (item.getWeaponItem().getItemType() != WeaponType.POLE)) {
					if (item.getWeaponItem().getItemType().isRanged()) {
						return (12 * blessedBonus * enchant) + (24 * blessedBonus * Math.max(0, enchant - 3));
					}
					return (7 * blessedBonus * enchant) + (14 * blessedBonus * Math.max(0, enchant - 3));
				}
				return (6 * blessedBonus * enchant) + (12 * blessedBonus * Math.max(0, enchant - 3));
			}
			case S: {
				if ((item.getWeaponItem().getBodyPart() == Item.BodyPart.SLOT_LR_HAND.getSlot()) && (item.getWeaponItem().getItemType() != WeaponType.POLE)) {
					if (item.getWeaponItem().getItemType().isRanged()) {
						// bows.
						return (31 * enchant) + (95 * Math.max(0, enchant - 3));
					}
					// two-handed swords, two-handed blunts, dualswords, and two-handed combat weapons.
					return (19 * enchant) + (58 * Math.max(0, enchant - 3));
				}
				// one-handed swords, one-handed blunts, daggers, spears, and other weapons.
				return (15 * enchant) + (28 * Math.max(0, enchant - 3));
			}
			case A: {
				if ((item.getWeaponItem().getBodyPart() == Item.BodyPart.SLOT_LR_HAND.getSlot()) && (item.getWeaponItem().getItemType() != WeaponType.POLE)) {
					if (item.getWeaponItem().getItemType().isRanged()) {
						// bows.
						return (16 * enchant) + (32 * Math.max(0, enchant - 3));
					}
					// two-handed swords, two-handed blunts, dualswords, and two-handed combat weapons
					return (12 * enchant) + (24 * Math.max(0, enchant - 3));
				}
				// one-handed swords, one-handed blunts, daggers, spears, and other weapons.
				return (10 * enchant) + (20 * Math.max(0, enchant - 3));
			}
			case B:
			case C:
			case D: {
				if ((item.getWeaponItem().getBodyPart() == Item.BodyPart.SLOT_LR_HAND.getSlot()) && (item.getWeaponItem().getItemType() != WeaponType.POLE)) {
					if (item.getWeaponItem().getItemType().isRanged()) {
						// bows.
						return (8 * enchant) + (8 * Math.max(0, enchant - 3));
					}
					// two-handed swords, two-handed blunts, dualswords, and two-handed combat weapons.
					return (5 * enchant) + (5 * Math.max(0, enchant - 3));
				}
				// one-handed swords, one-handed blunts, daggers, spears, and other weapons.
				return (4 * enchant) + (4 * Math.max(0, enchant - 3));
			}
			default: {
				if (item.getWeaponItem().getItemType().isRanged()) {
					// Bows.
					return (8 * enchant) + (8 * Math.max(0, enchant - 3));
				}
				// all weapons with the exception of bows.
				return (4 * enchant) + (4 * Math.max(0, enchant - 3));
			}
		}
	}

	default void throwIfPresent(OptionalDouble base) {
		if (base.isPresent()) {
			throw new IllegalArgumentException("base should not be set for " + getClass().getSimpleName());
		}
	}

	default double calcEnchantBodyPart(Creature creature, long... slots) {
		double value = 0;
		for (long slot : slots) {
			final ItemInstance item = creature.getInventory().getPaperdollItemByItemId(slot);
			if ((item != null) && (item.getEnchantLevel() >= 4) && (item.getItem().getCrystalTypePlus() == CrystalType.R)) {
				int enchantLevel = item.getEnchantLevel();

				// Apply balance system scaling for enchant calculations
				if (BalanceItemData.getInstance().isBalancedItem(item.getId())) {
					double actualPower = BalanceItemData.getInstance().getActualEnchantPower(enchantLevel);
					enchantLevel = (int) Math.floor(actualPower);
				}

				value += calcEnchantBodyPartBonus(enchantLevel, item.getItem().isBlessed());
			}
		}
		return value;
	}

	default double calcEnchantBodyPartBonus(int enchantLevel, boolean isBlessed) {
		return 0;
	}

	default double calcWeaponBaseValue(Creature creature, Stat stat) {
		final double baseTemplateValue = creature.getTemplate().getBaseValue(stat, 0);
		double baseValue = creature.getTransformation().map(transform -> transform.getStats(creature, stat, baseTemplateValue)).orElse(baseTemplateValue);
		ItemInstance weapon = null;
		if (creature.isPet()) {
			final PetInstance pet = (PetInstance) creature;
			weapon = pet.getActiveWeaponInstance();
			final double baseVal = stat == Stat.PHYSICAL_ATTACK ? pet.getPetLevelData().getPetPAtk() : stat == Stat.MAGIC_ATTACK ? pet.getPetLevelData().getPetMAtk() : baseTemplateValue;
			baseValue = baseVal + (weapon != null ? weapon.getItem().getStats(stat, baseVal) : 0);
		} else if (creature.isPlayer() && (!creature.isTransformed() || (creature.getTransformation().get().getType() == TransformType.COMBAT) || (creature.getTransformation().get().getType() == TransformType.MODE_CHANGE))) {
			weapon = creature.getActiveWeaponInstance();
			baseValue = (weapon != null ? weapon.getItem().getStats(stat, baseTemplateValue) : baseTemplateValue);
		}

		double levelBonus = 0;
		int lv = creature.getLevel();
		if (weapon != null) {
			switch (stat) {
				case PHYSICAL_ATTACK: {
					switch (weapon.getId()) {
						// @formatter:off
						case 93028 -> levelBonus = lv >= 75 ? 101 : lv >= 70 ? 84 : lv >= 65 ? 68 : lv >= 60 ? 51 : lv >= 50 ? 33 : lv >= 40 ? 17 : 0; // Aden Sword
						case 93029 -> levelBonus = lv >= 75 ? 91 : lv >= 70 ? 76 : lv >= 65 ? 61 : lv >= 60 ? 46 : lv >= 50 ? 30 : lv >= 40 ? 15 : 0; // Aden Dagger
						case 93030 -> levelBonus = lv >= 75 ? 192 : lv >= 70 ? 159 : lv >= 65 ? 129 : lv >= 60 ? 96 : lv >= 50 ? 63 : lv >= 40 ? 33 : 0; // Aden Bow
						case 93031 -> levelBonus = lv >= 75 ? 101 : lv >= 70 ? 84 : lv >= 65 ? 68 : lv >= 60 ? 51 : lv >= 50 ? 33 : lv >= 40 ? 17 : 0; // Aden Club
						case 93032 -> levelBonus = lv >= 75 ? 124 : lv >= 70 ? 103 : lv >= 65 ? 83 : lv >= 60 ? 62 : lv >= 50 ? 41 : lv >= 40 ? 21 : 0; // Two-handed Sword of Aden
						case 93033 -> levelBonus = lv >= 75 ? 105 : lv >= 70 ? 87 : lv >= 65 ? 70 : lv >= 60 ? 53 : lv >= 50 ? 35 : lv >= 40 ? 18 : 0; // Two-handed Blunt Weapon of Aden
						case 93034 -> levelBonus = lv >= 75 ? 101 : lv >= 70 ? 84 : lv >= 65 ? 68 : lv >= 60 ? 51 : lv >= 50 ? 33 : lv >= 40 ? 17 : 0; // Aden Spear
						case 93035 -> levelBonus = lv >= 75 ? 124 : lv >= 70 ? 103 : lv >= 65 ? 83 : lv >= 60 ? 62 : lv >= 50 ? 41 : lv >= 40 ? 21 : 0; // Aden Fist
						case 93036 -> levelBonus = lv >= 75 ? 97 : lv >= 70 ? 81 : lv >= 65 ? 65 : lv >= 60 ? 49 : lv >= 50 ? 32 : lv >= 40 ? 16 : 0; // Aden Rapier
						case 93037 -> levelBonus = lv >= 75 ? 115 : lv >= 70 ? 95 : lv >= 65 ? 77 : lv >= 60 ? 58 : lv >= 50 ? 38 : lv >= 40 ? 20 : 0; // Aden Ancient Sword
						case 94897 -> levelBonus = lv >= 75 ? 192 : lv >= 70 ? 159 : lv >= 65 ? 129 : lv >= 60 ? 96 : lv >= 50 ? 63 : lv >= 40 ? 33 : 0; // Aden Pistols
						case 95691 -> levelBonus = lv >= 75 ? 124 : lv >= 70 ? 103 : lv >= 65 ? 83 : lv >= 60 ? 62 : lv >= 50 ? 41 : lv >= 40 ? 21 : 0; // Aden Dual Swords
						case 92408 ->
								levelBonus = lv >= 99 ? 115 : lv >= 98 ? 108 : lv >= 97 ? 101 : lv >= 96 ? 94 : lv >= 95 ? 87 : lv >= 94 ? 80 : lv >= 93 ? 74 : lv >= 92 ? 68 : lv >= 91 ? 62 : lv >= 90 ? 56 : lv >= 89 ? 50 : lv >= 88 ? 45 : lv >= 87 ? 40 : lv >= 86 ? 35 : lv >= 85 ? 30 : lv >= 84 ? 25 : lv >= 83 ? 22 : lv >= 82 ? 19 : lv >= 81 ? 16 : lv >= 80 ? 13 : lv >= 70 ? 8 : lv >= 65 ? 5 : lv >= 60 ? 3 : 0; // Baium's Thunder Breaker
						case 93294 ->
								levelBonus = lv >= 99 ? 115 : lv >= 98 ? 108 : lv >= 97 ? 101 : lv >= 96 ? 94 : lv >= 95 ? 87 : lv >= 94 ? 80 : lv >= 93 ? 74 : lv >= 92 ? 68 : lv >= 91 ? 62 : lv >= 90 ? 56 : lv >= 89 ? 50 : lv >= 88 ? 45 : lv >= 87 ? 40 : lv >= 86 ? 35 : lv >= 85 ? 30 : lv >= 84 ? 25 : lv >= 83 ? 22 : lv >= 82 ? 19 : lv >= 81 ? 16 : lv >= 80 ? 13 : lv >= 70 ? 8 : lv >= 65 ? 5 : lv >= 60 ? 3 : 0; // Baium's Thunder Breaker (Sealed)
						case 96268 ->
								levelBonus = lv >= 99 ? 26 : lv >= 98 ? 25 : lv >= 97 ? 24 : lv >= 96 ? 23 : lv >= 95 ? 22 : lv >= 94 ? 20 : lv >= 93 ? 19 : lv >= 92 ? 18 : lv >= 91 ? 17 : lv >= 90 ? 16 : lv >= 89 ? 14 : lv >= 88 ? 13 : lv >= 87 ? 12 : lv >= 86 ? 11 : lv >= 85 ? 10 : lv >= 84 ? 8 : lv >= 83 ? 7 : lv >= 82 ? 6 : lv >= 81 ? 5 : lv >= 80 ? 4 : lv >= 70 ? 2 : lv >= 60 ? 1 : 0; // Beleth's Soul Eater
						case 96269 ->
								levelBonus = lv >= 99 ? 26 : lv >= 98 ? 25 : lv >= 97 ? 24 : lv >= 96 ? 23 : lv >= 95 ? 22 : lv >= 94 ? 20 : lv >= 93 ? 19 : lv >= 92 ? 18 : lv >= 91 ? 17 : lv >= 90 ? 16 : lv >= 89 ? 14 : lv >= 88 ? 13 : lv >= 87 ? 12 : lv >= 86 ? 11 : lv >= 85 ? 10 : lv >= 84 ? 8 : lv >= 83 ? 7 : lv >= 82 ? 6 : lv >= 81 ? 5 : lv >= 80 ? 4 : lv >= 70 ? 2 : lv >= 60 ? 1 : 0; // Beleth's Soul Eater (Sealed)
						// @formatter:on
					}
					break;
				}
				case MAGIC_ATTACK: {
					switch (weapon.getId()) {
						// @formatter:off
						case 93028 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Aden Sword
						case 93029 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Aden Dagger
						case 93030 -> levelBonus = lv >= 75 ? 57 : lv >= 70 ? 47 : lv >= 65 ? 38 : lv >= 60 ? 29 : lv >= 50 ? 19 : lv >= 40 ? 10 : 0; // Aden Bow
						case 93031 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Aden Club
						case 93032 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Two-handed Sword of Aden
						case 93033 -> levelBonus = lv >= 75 ? 82 : lv >= 70 ? 68 : lv >= 65 ? 55 : lv >= 60 ? 41 : lv >= 50 ? 27 : lv >= 40 ? 14 : 0; // Two-handed Blunt Weapon of Aden
						case 93034 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Aden Spear
						case 93035 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Aden Fist
						case 93036 -> levelBonus = lv >= 75 ? 80 : lv >= 70 ? 66 : lv >= 65 ? 54 : lv >= 60 ? 40 : lv >= 50 ? 26 : lv >= 40 ? 14 : 0; // Aden Rapier
						case 93037 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Aden Ancient Sword
						case 94897 -> levelBonus = lv >= 75 ? 57 : lv >= 70 ? 47 : lv >= 65 ? 38 : lv >= 60 ? 29 : lv >= 50 ? 19 : lv >= 40 ? 10 : 0; // Aden Pistols
						case 95691 -> levelBonus = lv >= 75 ? 61 : lv >= 70 ? 51 : lv >= 65 ? 41 : lv >= 60 ? 31 : lv >= 50 ? 20 : lv >= 40 ? 10 : 0; // Aden Dual Swords
						case 92408 ->
								levelBonus = lv >= 99 ? 66 : lv >= 98 ? 62 : lv >= 97 ? 58 : lv >= 96 ? 54 : lv >= 95 ? 50 : lv >= 94 ? 46 : lv >= 93 ? 43 : lv >= 92 ? 40 : lv >= 91 ? 37 : lv >= 90 ? 34 : lv >= 89 ? 31 : lv >= 88 ? 28 : lv >= 87 ? 25 : lv >= 86 ? 22 : lv >= 85 ? 19 : lv >= 84 ? 16 : lv >= 83 ? 14 : lv >= 82 ? 12 : lv >= 81 ? 10 : lv >= 80 ? 8 : lv >= 70 ? 5 : lv >= 65 ? 3 : lv >= 60 ? 2 : 0; // Baium's Thunder Breaker
						case 93294 ->
								levelBonus = lv >= 99 ? 66 : lv >= 98 ? 62 : lv >= 97 ? 58 : lv >= 96 ? 54 : lv >= 95 ? 50 : lv >= 94 ? 46 : lv >= 93 ? 43 : lv >= 92 ? 40 : lv >= 91 ? 37 : lv >= 90 ? 34 : lv >= 89 ? 31 : lv >= 88 ? 28 : lv >= 87 ? 25 : lv >= 86 ? 22 : lv >= 85 ? 19 : lv >= 84 ? 16 : lv >= 83 ? 14 : lv >= 82 ? 12 : lv >= 81 ? 10 : lv >= 80 ? 8 : lv >= 70 ? 5 : lv >= 65 ? 3 : lv >= 60 ? 2 : 0; // Baium's Thunder Breaker (Sealed)
						case 96268 ->
								levelBonus = lv >= 99 ? 100 : lv >= 98 ? 96 : lv >= 97 ? 92 : lv >= 96 ? 88 : lv >= 95 ? 82 : lv >= 94 ? 77 : lv >= 93 ? 72 : lv >= 92 ? 67 : lv >= 91 ? 62 : lv >= 90 ? 57 : lv >= 89 ? 49 : lv >= 88 ? 45 : lv >= 87 ? 41 : lv >= 86 ? 37 : lv >= 85 ? 33 : lv >= 84 ? 28 : lv >= 83 ? 26 : lv >= 82 ? 24 : lv >= 81 ? 22 : lv >= 80 ? 20 : lv >= 70 ? 10 : lv >= 60 ? 4 : 0; // Beleth's Soul Eater
						case 96269 ->
								levelBonus = lv >= 99 ? 100 : lv >= 98 ? 96 : lv >= 97 ? 92 : lv >= 96 ? 88 : lv >= 95 ? 82 : lv >= 94 ? 77 : lv >= 93 ? 72 : lv >= 92 ? 67 : lv >= 91 ? 62 : lv >= 90 ? 57 : lv >= 89 ? 49 : lv >= 88 ? 45 : lv >= 87 ? 41 : lv >= 86 ? 37 : lv >= 85 ? 33 : lv >= 84 ? 28 : lv >= 83 ? 26 : lv >= 82 ? 24 : lv >= 81 ? 22 : lv >= 80 ? 20 : lv >= 70 ? 10 : lv >= 60 ? 4 : 0; // Beleth's Soul Eater
						// @formatter:on
					}
					break;
				}
			}
		}
		int buffBonus = 0;
		if (creature.isAffected(EffectFlag.CRUSADERS_RED_ENERGY)) {
			buffBonus += 1;
		}
		if (creature.isAffected(EffectFlag.GLAKIAS_DOLL_SORCERY_1)) {
			buffBonus += 5;
		} else if (creature.isAffected(EffectFlag.GLAKIAS_DOLL_SORCERY_2)) {
			buffBonus += 10;
		} else if (creature.isAffected(EffectFlag.GLAKIAS_DOLL_SORCERY_3)) {
			buffBonus += 15;
		} else if (creature.isAffected(EffectFlag.GLAKIAS_DOLL_SORCERY_4)) {
			buffBonus += 20;
		} else if (creature.isAffected(EffectFlag.GLAKIAS_DOLL_SORCERY_5)) {
			buffBonus += 35;
		}
		return baseValue + levelBonus + buffBonus;
	}

	default double calcWeaponPlusBaseValue(Creature creature, Stat stat) {
		final double baseTemplateValue = creature.getTemplate().getBaseValue(stat, 0);
		double baseValue = creature.getTransformation().filter(transform -> !transform.isStance()).map(transform -> transform.getStats(creature, stat, baseTemplateValue)).orElse(baseTemplateValue);

		if (creature.isPlayable()) {
			final Inventory inv = creature.getInventory();
			if (inv != null) {
				baseValue += inv.getPaperdollCache().getStats(stat);
			}
		}

		return baseValue;
	}

	default double calcEnchantedItemBonus(Creature creature, Stat stat) {
		if (!creature.isPlayer() && !creature.isPet()) {
			return 0;
		}

		double value = 0;
		for (ItemInstance equippedItem : creature.getInventory().getPaperdollItems(ItemInstance::isEnchanted)) {
			final Item item = equippedItem.getItem();
			final long bodypart = item.getBodyPart();
			if ((bodypart == Item.BodyPart.SLOT_HAIR.getSlot()) || //
					(bodypart == Item.BodyPart.SLOT_HAIR2.getSlot()) || //
					(bodypart == Item.BodyPart.SLOT_HAIRALL.getSlot())) {
				// TODO: Item after enchant shows pDef, but scroll says mDef increase.
				if ((stat != Stat.PHYSICAL_DEFENCE) && (stat != Stat.MAGICAL_DEFENCE)) {
					continue;
				}
			} else if (item.getStats(stat, 0) <= 0) {
				if (!((bodypart == Item.BodyPart.SLOT_AGATHION.getSlot()) && (stat == Stat.MAGICAL_DEFENCE))) {
					continue;
				}
			}

			final double blessedBonus = item.isBlessed() ? 1.5 : 1;
			int enchant = equippedItem.getEnchantLevel();

			// Apply balance system scaling for enchant calculations
			if (BalanceItemData.getInstance().isBalancedItem(equippedItem.getId())) {
				double actualPower = BalanceItemData.getInstance().getActualEnchantPower(enchant);
				enchant = (int) Math.floor(actualPower);
			}

			if (creature.getActingPlayer().isInOlympiadMode()) {
				if (item.isWeapon()) {
					if ((Config.OLY_WEAPON_ENCHANT_LIMIT >= 0) && (enchant > Config.OLY_WEAPON_ENCHANT_LIMIT)) {
						enchant = Config.OLY_WEAPON_ENCHANT_LIMIT;
					}
				} else {
					if ((Config.OLY_ARMOR_ENCHANT_LIMIT >= 0) && (enchant > Config.OLY_ARMOR_ENCHANT_LIMIT)) {
						enchant = Config.OLY_ARMOR_ENCHANT_LIMIT;
					}
				}
			}

			if (stat == Stat.MAGICAL_DEFENCE)
			{
				if (creature instanceof PlayerInstance && enchant >= 5)
				{
					switch (equippedItem.getItem().getId())
					{
						case 95663:
						case 95664:
						case 95665:
						case 95666:
							value += 0;
							break;
					}
				}
				value += calcEnchantmDefBonus(equippedItem, blessedBonus, enchant);
			}
			else if (stat == Stat.PHYSICAL_DEFENCE)
			{
				if (creature instanceof PlayerInstance && enchant >= 5)
				{
					switch (equippedItem.getItem().getId())
					{
						case 95663:
						case 95664:
						case 95665:
						case 95666:
							value += 0;
							break;
					}
				}

				value += calcEnchantDefBonus(equippedItem, blessedBonus, enchant);
			} else if (stat == Stat.MAGIC_ATTACK) {
				value += calcEnchantMatkBonus(equippedItem, blessedBonus, enchant);
			} else if ((stat == Stat.PHYSICAL_ATTACK) && equippedItem.isWeapon()) {
				value += calcEnchantedPAtkBonus(equippedItem, blessedBonus, enchant);
			}
		}
		return value;
	}

	default double validateValue(Creature creature, double value, double minValue, double maxValue) {
		if ((value > maxValue) && !creature.canOverrideCond(PlayerCondOverride.MAX_STATS_VALUE)) {
			return maxValue;
		}

		return Math.max(minValue, value);
	}

	double calc(Creature creature, OptionalDouble base, Stat stat);
}
