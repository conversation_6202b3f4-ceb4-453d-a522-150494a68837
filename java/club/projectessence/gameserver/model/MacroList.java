/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.enums.MacroType;
import club.projectessence.gameserver.enums.MacroUpdateType;
import club.projectessence.gameserver.enums.ShortcutType;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.interfaces.IRestorable;
import club.projectessence.gameserver.network.serverpackets.SendMacroList;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MacroList implements IRestorable {
	private static final Logger LOGGER = Logger.getLogger(MacroList.class.getName());

	private final PlayerInstance _owner;
	private final Map<Integer, Macro> _macroses = Collections.synchronizedMap(new LinkedHashMap<>());
	private int _macroId;

	public MacroList(PlayerInstance owner) {
		_owner = owner;
		_macroId = 1000;
	}

	public Map<Integer, Macro> getAllMacroses() {
		return _macroses;
	}

	public void registerMacro(Macro macro) {
		MacroUpdateType updateType = MacroUpdateType.ADD;
		if (macro.getId() == 0) {
			macro.setId(_macroId++);
			while (_macroses.containsKey(macro.getId())) {
				macro.setId(_macroId++);
			}
			_macroses.put(macro.getId(), macro);
			registerMacroInDb(macro);
		} else {
			updateType = MacroUpdateType.MODIFY;
			final Macro old = _macroses.put(macro.getId(), macro);
			if (old != null) {
				deleteMacroFromDb(old);
			}
			registerMacroInDb(macro);
		}
		_owner.sendPacket(new SendMacroList(1, macro, updateType));
	}

	public void deleteMacro(int id) {
		final Macro removed = _macroses.remove(id);
		if (removed != null) {
			deleteMacroFromDb(removed);
		}

		final Shortcut[] allShortCuts = _owner.getAllShortCuts();
		for (Shortcut sc : allShortCuts) {
			if ((sc.getId() == id) && (sc.getType() == ShortcutType.MACRO)) {
				_owner.deleteShortCut(sc.getSlot(), sc.getPage());
			}
		}
		_owner.sendPacket(new SendMacroList(0, removed, MacroUpdateType.DELETE));
	}

	public void sendAllMacros() {
		final Collection<Macro> allMacros = _macroses.values();
		final int count = allMacros.size();

		synchronized (_macroses) {
			if (allMacros.isEmpty()) {
				_owner.sendPacket(new SendMacroList(0, null, MacroUpdateType.LIST));
			} else {
				for (Macro m : allMacros) {
					_owner.sendPacket(new SendMacroList(count, m, MacroUpdateType.LIST));
				}
			}
		}
	}

	private void registerMacroInDb(Macro macro) {
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement ps_macro = con.prepareStatement("INSERT INTO character_macroses (charId,id,icon,name,descr,acronym) values(?,?,?,?,?,?)");
		     PreparedStatement ps_commands = con.prepareStatement("INSERT INTO character_macroses_commands (charId,macro_id,command_index,type,d1,d2,command) values(?,?,?,?,?,?,?)")) {
			ps_macro.setInt(1, _owner.getObjectId());
			ps_macro.setInt(2, macro.getId());
			ps_macro.setInt(3, macro.getIcon());
			ps_macro.setString(4, macro.getName());
			ps_macro.setString(5, macro.getDescr());
			ps_macro.setString(6, macro.getAcronym());
			ps_macro.execute();

			int index = 0;
			for (MacroCmd cmd : macro.getCommands()) {
				ps_commands.setInt(1, _owner.getObjectId());
				ps_commands.setInt(2, macro.getId());
				ps_commands.setInt(3, index++);
				ps_commands.setInt(4, cmd.getType().ordinal());
				ps_commands.setInt(5, cmd.getD1());
				ps_commands.setInt(6, cmd.getD2());
				ps_commands.setString(7, cmd.getCmd());
				ps_commands.execute();
			}
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "could not store macro:", e);
		}
	}

	private void deleteMacroFromDb(Macro macro) {
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement ps = con.prepareStatement("DELETE FROM character_macroses WHERE charId=? AND id=?")) {
			ps.setInt(1, _owner.getObjectId());
			ps.setInt(2, macro.getId());
			ps.execute();
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "could not delete macro:", e);
		}
	}

	@Override
	public boolean restoreMe() {
		_macroses.clear();
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement ps = con.prepareStatement("SELECT character_macroses.charId as charId, character_macroses.id as id, icon, name, descr, acronym, command_index, type, d1, d2, command FROM character_macroses INNER JOIN character_macroses_commands ON character_macroses.charId = character_macroses_commands.charId AND character_macroses.id = character_macroses_commands.macro_id WHERE character_macroses.charId=?")) {
			ps.setInt(1, _owner.getObjectId());
			try (ResultSet rset = ps.executeQuery()) {
				Map<Integer, MacroResultSet> macroResultSetMap = new HashMap<>();
				while (rset.next()) {
					final int id = rset.getInt("id");
					final int icon = rset.getInt("icon");
					final String name = rset.getString("name");
					final String descr = rset.getString("descr");
					final String acronym = rset.getString("acronym");
					final int commandIndex = rset.getInt("command_index");
					final MacroType type = MacroType.values()[rset.getInt("type")];
					final int d1 = rset.getInt("d1");
					final int d2 = rset.getInt("d2");
					final String command = rset.getString("command");

					if (macroResultSetMap.get(id) == null) {
						macroResultSetMap.put(id, new MacroResultSet(id, icon, name, descr, acronym));
					}

					macroResultSetMap.get(id).addCommand(new MacroCmd(commandIndex, type, d1, d2, command));
				}

				for (Map.Entry<Integer, MacroResultSet> entry : macroResultSetMap.entrySet()) {
					final MacroResultSet macro = entry.getValue();
					_macroses.put(macro.getMacroId(), new Macro(macro.getMacroId(), macro.getIcon(), macro.getName(), macro.getDescr(), macro.getAcronym(), macro.getCommands()));
				}
			}
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "could not store shortcuts:", e);
			return false;
		}
		return true;
	}

	private class MacroResultSet {
		private final int _macro_id;
		private final int _icon;
		private final String _name;
		private final String _descr;
		private final String _acronym;
		private final List<MacroCmd> _commands = new ArrayList<>();

		public MacroResultSet(int id, int icon, String name, String descr, String acronym) {
			_macro_id = id;
			_icon = icon;
			_name = name;
			_descr = descr;
			_acronym = acronym;
		}

		public void addCommand(MacroCmd command) {
			_commands.add(command);
		}

		public int getMacroId() {
			return _macro_id;
		}

		public int getIcon() {
			return _icon;
		}

		public String getName() {
			return _name;
		}

		public String getDescr() {
			return _descr;
		}

		public String getAcronym() {
			return _acronym;
		}

		public List<MacroCmd> getCommands() {
			return _commands;
		}
	}
}
