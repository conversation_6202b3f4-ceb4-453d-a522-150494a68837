/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.holders;

import club.projectessence.gameserver.taskmanager.PromoCodeManager.PromoCodeType;

/**
 * <AUTHOR>
 */
public class PromoCodeHolder {
	private final String _code;
	private final PromoCodeType _type;
	private final String _params;
	private String _usedBy;

	public PromoCodeHolder(String code, PromoCodeType type, String params, String usedBy) {
		_code = code;
		_type = type;
		_params = params;
		_usedBy = usedBy;
	}

	public String getCode() {
		return _code;
	}

	public PromoCodeType getType() {
		return _type;
	}

	public String getParams() {
		return _params;
	}

	public void setUser(String charName) {
		_usedBy = charName;
	}

	public boolean isUsed() {
		if (_type == PromoCodeType.LAUNCH_PACK) {
			return !_usedBy.equals("PAID") && !_usedBy.equals("SENT");
		}
		return _usedBy != null;
	}
}