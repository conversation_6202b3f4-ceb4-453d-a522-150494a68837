package club.projectessence.gameserver.model.holders;

public class ExtendDropHolder
{
	private final int	_bossId;
	private final int	_itemId;
	private final int	_minAmount;
	private final int	_maxAmount;
	private final int	_radius;
	
	public ExtendDropHolder(int bossId, int itemId, int minAmount, int maxAmount, int radius)
	{
		_bossId = bossId;
		_itemId = itemId;
		_minAmount = minAmount;
		_maxAmount = maxAmount;
		_radius = radius;
	}
	
	public int getBossId()
	{
		return _bossId;
	}
	
	public int getItemId()
	{
		return _itemId;
	}
	
	public int getMinAmount()
	{
		return _minAmount;
	}
	
	public int getMaxAmount()
	{
		return _maxAmount;
	}
	
	public int getRadius()
	{
		return _radius;
	}
}
