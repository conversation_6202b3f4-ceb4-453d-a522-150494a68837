package club.projectessence.gameserver.model.holders;

import club.projectessence.gameserver.enums.ShortcutType;
import club.projectessence.gameserver.model.StatSet;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FakePlayerSkillListHolder {
	private final int _minLevel;
	private final int _maxLevel;
	private final List<StatSet> _skills = new ArrayList<>();

	public FakePlayerSkillListHolder(int minLevel, int maxLevel) {
		_minLevel = minLevel;
		_maxLevel = maxLevel;
	}

	public int getMinLevel() {
		return _minLevel;
	}

	public int getMaxLevel() {
		return _maxLevel;
	}

	public void add(ShortcutType type, int id) {
		StatSet set = new StatSet();
		set.set("type", type);
		set.set("id", id);
		_skills.add(set);
	}

	public List<StatSet> getSkills() {
		return _skills;
	}
}
