/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.itemcontainer;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.holders.SteadyBoxHolder;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.steadybox.ExSteadyAllBoxUpdate;
import club.projectessence.gameserver.network.serverpackets.steadybox.ExSteadyBoxReward;

/**
 * <AUTHOR>
 */
public class PlayerSteadyBox
{
	public static final int				MAX_MONSTER_HUNTING_POINTS	= 1000;
	public static final int				MAX_PLAYER_HUNTING_POINTS	= 5;
	public static final int				MAX_STEADY_BOXES			= 4;
	// @formatter:off
	public static final int				BOX_OPEN_PRICE[]			=
	{
		500, 1000, 1500
	};
	public static final int				TIME_AMOUNT[]				=
	{
		0, 60, 180, 360, 540
	};
	public static final int				TIME_PRICE[]				=
	{
		100, 500, 1000, 1500, 2000
	};
	private static final Logger			LOGGER						= Logger.getLogger(PlayerSteadyBox.class.getName());
	private final PlayerInstance		_owner;
	private final List<SteadyBoxHolder>	_steadyBoxes;
	// @formatter:on
	private int							_steadyBoxesOwned;
	private int							_monsterHuntingPoints;
	private int							_playerHuntingPoints;
	private int							_pendingBoxSlotId;
	private long						_boxOpenTime;
	private ScheduledFuture<?>			_boxOpenTask;
	
	public PlayerSteadyBox(PlayerInstance owner)
	{
		_owner = owner;
		_steadyBoxes = new ArrayList<>();
		_steadyBoxesOwned = 1;
		_monsterHuntingPoints = 0;
		_playerHuntingPoints = 0;
		_pendingBoxSlotId = 0;
		_boxOpenTime = 0;
		_boxOpenTask = null;
	}
	
	public PlayerInstance getOwner()
	{
		return _owner;
	}
	
	public int getOwnerId()
	{
		return _owner.getObjectId();
	}
	
	public void restore()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT * FROM steady_box WHERE charId=?"))
		{
			ps.setInt(1, getOwnerId());
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					try
					{
						_steadyBoxesOwned = rs.getInt("steady_boxes_owned");
						_monsterHuntingPoints = rs.getInt("monster_hunting_point");
						_playerHuntingPoints = rs.getInt("player_hunting_point");
						_pendingBoxSlotId = rs.getInt("pending_box_slot_id");
						_boxOpenTime = rs.getLong("box_open_time");
						for (int i = 1; i <= MAX_STEADY_BOXES; i++)
						{
							int state = rs.getInt("box_" + i + "_state");
							int type = rs.getInt("box_" + i + "_type");
							if ((i == 1) && (state == 0))
							{
								state = 1;
							}
							final SteadyBoxHolder holder = new SteadyBoxHolder(i, state, type);
							_steadyBoxes.add(i - 1, holder);
						}
					}
					catch (Exception e)
					{
						LOGGER.warning("Could not restore steady box for " + getOwner());
					}
				}
				else
				{
					storeNew();
					_steadyBoxes.add(0, new SteadyBoxHolder(1, 1, 0));
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Could not restore steady box for " + getOwner(), e);
		}
	}
	
	public void store()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE steady_box SET steady_boxes_owned=?,monster_hunting_point=?,player_hunting_point=?,pending_box_slot_id=?,box_open_time=?,box_1_state=?,box_1_type=?,box_2_state=?,box_2_type=?,box_3_state=?,box_3_type=?,box_4_state=?,box_4_type=? WHERE charId=?"))
		{
			ps.setInt(1, getSteadyBoxesOwned());
			ps.setInt(2, getMonsterHuntingPoints());
			ps.setInt(3, getPlayerHuntingPoints());
			ps.setInt(4, getPendingBoxSlotId());
			ps.setLong(5, getBoxOpenTime());
			for (int i = 0; i < 4; i++)
			{
				if (_steadyBoxes.size() >= (i + 1))
				{
					SteadyBoxHolder holder = _steadyBoxes.get(i);
					ps.setInt(6 + (i * 2), holder == null ? 0 : holder.getState().ordinal());
					ps.setInt(7 + (i * 2), holder == null ? 0 : holder.getType().ordinal());
				}
				else
				{
					ps.setInt(6 + (i * 2), 0);
					ps.setInt(7 + (i * 2), 0);
				}
			}
			ps.setInt(14, getOwnerId());
			ps.execute();
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Could not store Steady Box for: " + getOwner(), e);
		}
	}
	
	public void storeNew()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO steady_box VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"))
		{
			ps.setInt(1, getOwnerId());
			ps.setInt(2, _steadyBoxesOwned);
			ps.setInt(3, _monsterHuntingPoints);
			ps.setInt(4, _playerHuntingPoints);
			ps.setInt(5, _pendingBoxSlotId);
			ps.setLong(6, _boxOpenTime);
			for (int i = 0; i < 4; i++)
			{
				ps.setInt(7 + (i * 2), 0);
				ps.setInt(8 + (i * 2), 0);
			}
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			LOGGER.warning("Could not store new Steady Box for: " + getOwner());
			e.printStackTrace();
		}
	}
	
	public List<SteadyBoxHolder> getSteadyBoxes()
	{
		return _steadyBoxes;
	}
	
	public void addMonsterHuntingPoints(int value)
	{
		int newPoints = Math.min(MAX_MONSTER_HUNTING_POINTS, _monsterHuntingPoints + value);
		if (newPoints >= MAX_MONSTER_HUNTING_POINTS)
		{
			if (addNewBox(false))
			{
				_monsterHuntingPoints = 0;
			}
			else
			{
				_monsterHuntingPoints = MAX_MONSTER_HUNTING_POINTS;
			}
			return;
		}
		_monsterHuntingPoints = newPoints;
	}
	
	public void addPlayerHuntingPoints(int value)
	{
		int newPoints = Math.min(MAX_PLAYER_HUNTING_POINTS, _playerHuntingPoints + value);
		if (newPoints >= MAX_PLAYER_HUNTING_POINTS)
		{
			if (addNewBox(true))
			{
				_playerHuntingPoints = 0;
			}
			else
			{
				_playerHuntingPoints = MAX_PLAYER_HUNTING_POINTS;
			}
			return;
		}
		_playerHuntingPoints = newPoints;
	}
	
	public boolean addNewBox(boolean isPlayerhunting)
	{
		SteadyBoxHolder freeHolder = null;
		int id = -1;
		for (int i = 1; i <= getSteadyBoxesOwned(); i++)
		{
			final SteadyBoxHolder holder = getSteadyBoxes().get(i - 1);
			if (holder.getState() == SteadyBoxState.AVAILABLE)
			{
				freeHolder = holder;
				id = i;
				break;
			}
		}
		if (freeHolder != null)
		{
			int rnd = Rnd.get(0, 100);
			freeHolder.setType(rnd < 10 ? SteadyBoxType.BOX_12H : rnd < 35 ? SteadyBoxType.BOX_6H : SteadyBoxType.BOX_2H);
			// if (getBoxOpenTime() == 0)
			// {
			switch (freeHolder.getType())
			{
				case BOX_2H:
				{
					freeHolder.setState(SteadyBoxState.OPEN);
					getSteadyBoxes().remove(id - 1);
					getSteadyBoxes().add(id - 1, freeHolder);
					sendBoxUpdate();
					break;
				}
				case BOX_6H:
				{
					freeHolder.setState(SteadyBoxState.OPEN);
					getSteadyBoxes().remove(id - 1);
					getSteadyBoxes().add(id - 1, freeHolder);
					sendBoxUpdate();
					break;
				}
				case BOX_12H:
				{
					freeHolder.setState(SteadyBoxState.OPEN);
					getSteadyBoxes().remove(id - 1);
					getSteadyBoxes().add(id - 1, freeHolder);
					sendBoxUpdate();
					break;
				}
			}
			return true;
			// }
		}
		return false;
	}
	
	public void openBox(int slotId)
	{
		if (slotId > getSteadyBoxesOwned())
		{
			return;
		}
		final SteadyBoxHolder holder = getSteadyBoxes().get(slotId - 1);
		{
			if ((holder != null) && (_boxOpenTime == 0))
			{
				_pendingBoxSlotId = slotId;
				switch (holder.getType())
				{
					case BOX_2H:
					{
						if (setBoxOpenTime(2 * 3_600_000))
						{
							holder.setState(SteadyBoxState.UNLOCK_IN_PROGRESS);
							getSteadyBoxes().remove(slotId - 1);
							getSteadyBoxes().add(slotId - 1, holder);
							sendBoxUpdate();
						}
						break;
					}
					case BOX_6H:
					{
						if (setBoxOpenTime(6 * 3_600_000))
						{
							holder.setState(SteadyBoxState.UNLOCK_IN_PROGRESS);
							getSteadyBoxes().remove(slotId - 1);
							getSteadyBoxes().add(slotId - 1, holder);
							sendBoxUpdate();
						}
						break;
					}
					case BOX_12H:
					{
						if (setBoxOpenTime(12 * 3_600_000))
						{
							holder.setState(SteadyBoxState.UNLOCK_IN_PROGRESS);
							getSteadyBoxes().remove(slotId - 1);
							getSteadyBoxes().add(slotId - 1, holder);
							sendBoxUpdate();
						}
						break;
					}
				}
			}
		}
	}
	
	public void skipTime(int slotId, long clientFee)
	{
		if (slotId > getSteadyBoxesOwned())
		{
			return;
		}
		final SteadyBoxHolder holder = getSteadyBoxes().get(slotId - 1);
		if (holder != null)
		{
			long serverFee = 0;
			long milisRemaining = 0;
			if (_pendingBoxSlotId == slotId)
			{
				milisRemaining = _boxOpenTime - System.currentTimeMillis();
			}
			else
			{
				switch (holder.getType())
				{
					case BOX_2H -> milisRemaining = 2 * 3_600_000;
					case BOX_6H -> milisRemaining = 6 * 3_600_000;
					case BOX_12H -> milisRemaining = 12 * 3_600_000;
				}
			}
			if (milisRemaining <= (1 * 3_600_000))
			{
				serverFee = 100;
			}
			else if (milisRemaining <= (3 * 3_600_000))
			{
				serverFee = 500;
			}
			else if (milisRemaining <= (6 * 3_600_000))
			{
				serverFee = 1000;
			}
			else if (milisRemaining <= (9 * 3_600_000))
			{
				serverFee = 1500;
			}
			else
			{
				serverFee = 2000;
			}
			if (serverFee != clientFee)
			{
				getOwner().sendPacket(SystemMessageId.SYSTEM_ERROR_PLEASE_TRY_AGAIN);
				return;
			}
			if (getOwner().destroyItemByItemId("Skip Steady Box Time", Inventory.LCOIN_ID, serverFee, getOwner(), true))
			{
				if (_pendingBoxSlotId == slotId)
				{
					cancelOpenTask();
				}
				finishUnlockChest(slotId);
			}
		}
	}
	
	public int getSteadyBoxesOwned()
	{
		return _steadyBoxesOwned;
	}
	
	public int getMonsterHuntingPoints()
	{
		return _monsterHuntingPoints;
	}
	
	public int getPlayerHuntingPoints()
	{
		return _playerHuntingPoints;
	}
	
	public int getPendingBoxSlotId()
	{
		return _pendingBoxSlotId;
	}
	
	public long getBoxOpenTime()
	{
		return _boxOpenTime;
	}
	
	public boolean setBoxOpenTime(long time)
	{
		if ((_boxOpenTask != null) && !(_boxOpenTask.isDone() || _boxOpenTask.isCancelled()))
		{
			return false;
		}
		_boxOpenTime = System.currentTimeMillis() + time;
		return true;
	}
	
	public void tryFinishBox()
	{
		if ((_boxOpenTime != 0) && (_boxOpenTime < System.currentTimeMillis()))
		{
			if ((getOwner() == null) || !getOwner().isOnline())
			{
				return;
			}
			SteadyBoxHolder holder = getSteadyBoxes().get(_pendingBoxSlotId - 1);
			if (holder != null)
			{
				finishUnlockChest(_pendingBoxSlotId);
			}
		}
	}
	
	public void finishUnlockChest(int id)
	{
		if (id > getSteadyBoxesOwned())
		{
			return;
		}
		if (_pendingBoxSlotId == id)
		{
			_boxOpenTime = 0;
			_pendingBoxSlotId = 0;
		}
		getSteadyBoxes().get(id - 1).setState(SteadyBoxState.RECEIVE_REWARD);
		sendBoxUpdate();
	}
	
	public void sendBoxUpdate()
	{
		getOwner().sendPacket(new ExSteadyAllBoxUpdate(getOwner()));
	}
	
	public void cancelOpenTask()
	{
		if (_boxOpenTask != null)
		{
			_boxOpenTask.cancel(false);
			_boxOpenTask = null;
		}
	}
	
	public void unlockSlot(int slotId)
	{
		if (((slotId - 1) == getSteadyBoxesOwned()) && (slotId <= MAX_STEADY_BOXES))
		{
			boolean paid = false;
			switch (slotId)
			{
				case 2:
				{
					if (getOwner().reduceAdena("Unlock Steady Box Slot " + slotId, 100_000_000, getOwner(), true))
					{
						paid = true;
					}
					break;
				}
				case 3:
				{
					if (getOwner().destroyItemByItemId("Unlock Steady Box Slot " + slotId, Inventory.LCOIN_ID, 2000, getOwner(), true))
					{
						paid = true;
					}
					break;
				}
				case 4:
				{
					if (getOwner().destroyItemByItemId("Unlock Steady Box Slot " + slotId, Inventory.LCOIN_ID, 8000, getOwner(), true))
					{
						paid = true;
					}
					break;
				}
			}
			if (paid)
			{
				_steadyBoxesOwned = slotId;
				final SteadyBoxHolder holder = new SteadyBoxHolder(slotId, 1, 0);
				holder.setState(SteadyBoxState.AVAILABLE);
				holder.setType(SteadyBoxType.LOCKED);
				getSteadyBoxes().add(slotId - 1, holder);
				sendBoxUpdate();
			}
		}
	}
	
	public void getReward(int slotId)
	{
		final SteadyBoxHolder holder = getSteadyBoxes().get(slotId - 1);
		if (holder.getState() != SteadyBoxState.RECEIVE_REWARD)
		{
			return;
		}
		ItemHolder reward = null;
		switch (holder.getType())
		{
			case BOX_2H:
			{
				int rnd = Rnd.get(0, 100);
				// if (rnd < 1) {
				// reward = new ItemHolder(Rnd.get(72084, 72102), 1);
				// }
				// else {
				if (rnd < 33)
				{
					reward = new ItemHolder(93274, 5); // Sayha's Cookie
				}
				else if (rnd < 66)
				{
					reward = new ItemHolder(90907, 250); // Soulshot Ticket
				}
				else
				{
					reward = new ItemHolder(3031, 50); // Spirit Ore
				}
				// }
				break;
			}
			case BOX_6H:
			{
				int rnd = Rnd.get(0, 100);
				// if (rnd < 3) {
				// reward = new ItemHolder(Rnd.get(72084, 72102), 1);
				// }
				// else {
				if (rnd < 33)
				{
					reward = new ItemHolder(93274, 10); // Sayha's Cookie
				}
				else if (rnd < 66)
				{
					reward = new ItemHolder(90907, 500); // Soulshot Ticket
				}
				else
				{
					reward = new ItemHolder(3031, 100); // Spirit Ore
				}
				// }
				break;
			}
			case BOX_12H:
			{
				int rnd = Rnd.get(0, 100);
				// if (rnd < 5) {
				// reward = new ItemHolder(Rnd.get(72084, 72102), 1);
				// }
				// else {
				if (rnd < 33)
				{
					reward = new ItemHolder(93274, 20); // Sayha's Cookie
				}
				else if (rnd < 66)
				{
					reward = new ItemHolder(90907, 1000); // Soulshot Ticket
				}
				else
				{
					reward = new ItemHolder(3031, 200); // Spirit Ore
				}
				// }
				break;
			}
		}
		holder.setState(SteadyBoxState.AVAILABLE);
		holder.setType(SteadyBoxType.LOCKED);
		sendBoxUpdate();
		if (reward != null)
		{
			getOwner().addItem("Steady Chest unlock", reward, getOwner(), true);
			getOwner().sendPacket(new ExSteadyBoxReward(slotId, reward.getId(), reward.getCount(), 0));
		}
	}
	
	public void printDetails()
	{
		LOGGER.info("----- " + getOwner() + " -----");
		for (int i = 0; i < getSteadyBoxesOwned(); i++)
		{
			SteadyBoxHolder box = getSteadyBoxes().get(i);
			LOGGER.info("[ID: " + box.getSlotId() + "] [Type: " + box.getType() + "] [State: " + box.getState() + "]");
		}
		LOGGER.info("------------");
	}
	
	public enum SteadyBoxState
	{
		LOCKED,
		AVAILABLE,
		OPEN,
		UNLOCK_IN_PROGRESS,
		RECEIVE_REWARD
	}
	
	public enum SteadyBoxType
	{
		LOCKED,
		BOX_2H,
		BOX_6H,
		BOX_12H
	}
}
