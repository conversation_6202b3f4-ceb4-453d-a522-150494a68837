package club.projectessence.gameserver.model.premium;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PremiumAccount
{
	private final int					_id;
	private final int					_time;
	private final String				_name;
	private final String				_icon;
	private final List<PremiumGift>		_gifts;
	private final int					_priceId;
	private final long					_priceCount;
	private double						_exp					= 1.0;
	private double						_sp						= 1.0;
	private double						_adena					= 1.0; // Legacy field, keep for compatibility
	private double						_adenaChance			= 1.0;
	private double						_adenaAmount			= 1.0;
	private double						_dropChance				= 1.0;
	private double						_dropAmount				= 1.0;
	private double						_spoilChance			= 1.0;
	private double						_spoilAmount			= 1.0;
	private double						_sayhaGraceXp			= 1.0;
	private double						_limitedSayhaGraceXp	= 1.0;
	private double						_resurrectionCost		= 1.0;
	private double						_randomCraftHerb		= 1.0;
	private double						_lcoinDropAmountAdd		= 0.0;
	private boolean						_onlyFishing			= false;
	private final Map<Integer, Float>	_dropChanceById			= new HashMap<>();
	private final Map<Integer, Float>	_dropAmountById			= new HashMap<>();
	
	public PremiumAccount(int id, int time, String name, String icon, List<PremiumGift> gifts, int priceId, long priceCount)
	{
		_id = id;
		_time = time;
		_name = name;
		_icon = icon;
		_gifts = gifts;
		_priceId = priceId;
		_priceCount = priceCount;
	}
	
	public int getId()
	{
		return _id;
	}
	
	public int getTime()
	{
		return _time;
	}
	
	public String getName()
	{
		return _name;
	}
	
	public String getIcon()
	{
		return _icon;
	}
	
	public int getPriceId()
	{
		return _priceId;
	}
	
	public long getPriceCount()
	{
		return _priceCount;
	}
	
	public double getExp()
	{
		return _exp;
	}
	
	public double getSp()
	{
		return _sp;
	}
	
	public double getAdena()
	{
		return _adena;
	}

	public double getAdenaChance()
	{
		return _adenaChance;
	}

	public double getAdenaAmount()
	{
		return _adenaAmount;
	}
	
	public double getDropChance()
	{
		return _dropChance;
	}
	
	public double getDropAmount()
	{
		return _dropAmount;
	}
	
	public double getSpoilChance()
	{
		return _spoilChance;
	}
	
	public double getSpoilAmount()
	{
		return _spoilAmount;
	}
	
	public double getSayhaGraceXp()
	{
		return _sayhaGraceXp;
	}
	
	public double getLimitedSayhaGraceXp()
	{
		return _limitedSayhaGraceXp;
	}
	
	public double getResurrectionCost()
	{
		return _resurrectionCost;
	}
	
	public double getRandomCraftHerb()
	{
		return _randomCraftHerb;
	}
	
	public double getLCoinDropAmountAdd()
	{
		return _lcoinDropAmountAdd;
	}
	
	public boolean getOnlyFishing()
	{
		return _onlyFishing;
	}
	
	public float getDropChanceById(int itemId)
	{
		return _dropChanceById.getOrDefault(itemId, 1.0f);
	}
	
	public float getDropAmountById(int itemId)
	{
		return _dropAmountById.getOrDefault(itemId, 1.0f);
	}
	
	// Thêm getter để trả về toàn bộ Map
	public Map<Integer, Float> getDropChanceByIdMap()
	{
		return _dropChanceById;
	}
	
	public Map<Integer, Float> getDropAmountByIdMap()
	{
		return _dropAmountById;
	}
	
	public void setRate(PremiumKeys key, String value)
	{
		switch (key)
		{
			case EXP:
				_exp = Double.parseDouble(value);
				break;
			case SP:
				_sp = Double.parseDouble(value);
				break;
			case ADENA:
				_adena = Double.parseDouble(value);
				break;
			case ADENA_CHANCE:
				_adenaChance = Double.parseDouble(value);
				break;
			case ADENA_AMOUNT:
				_adenaAmount = Double.parseDouble(value);
				break;
			case DROP_CHANCE:
				_dropChance = Double.parseDouble(value);
				break;
			case DROP_AMOUNT:
				_dropAmount = Double.parseDouble(value);
				break;
			case SPOIL_CHANCE:
				_spoilChance = Double.parseDouble(value);
				break;
			case SPOIL_AMOUNT:
				_spoilAmount = Double.parseDouble(value);
				break;
			case SAYHA_GRACE_XP:
				_sayhaGraceXp = Double.parseDouble(value);
				break;
			case LIMITED_SAYHA_GRACE_XP:
				_limitedSayhaGraceXp = Double.parseDouble(value);
				break;
			case RESURRECTION_COST:
				_resurrectionCost = Double.parseDouble(value);
				break;
			case RANDOM_CRAFT_HERB:
				_randomCraftHerb = Double.parseDouble(value);
				break;
			case LCOIN_DROP_AMOUNT_ADD:
				_lcoinDropAmountAdd = Double.parseDouble(value);
				break;
			case ONLY_FISHING:
				_onlyFishing = Boolean.parseBoolean(value);
				break;
			default:
				// Handle DROP_CHANCE_BY_ID and DROP_AMOUNT_BY_ID
				if (key.name().startsWith("DROP_CHANCE_BY_ID_"))
				{
					int itemId = Integer.parseInt(key.name().substring("DROP_CHANCE_BY_ID_".length()));
					_dropChanceById.put(itemId, Float.parseFloat(value));
				}
				else if (key.name().startsWith("DROP_AMOUNT_BY_ID_"))
				{
					int itemId = Integer.parseInt(key.name().substring("DROP_AMOUNT_BY_ID_".length()));
					_dropAmountById.put(itemId, Float.parseFloat(value));
				}
				break;
		}
	}
	
	public List<PremiumGift> getGifts()
	{
		return _gifts;
	}
}