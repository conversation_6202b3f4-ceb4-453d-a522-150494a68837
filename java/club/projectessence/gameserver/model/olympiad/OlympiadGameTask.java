/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.olympiad;

import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.olympiad.ExOlympiadMatchResultInfo;

/**
 * <AUTHOR>
 */
public class OlympiadGameTask implements Runnable
{
	protected static final Logger	LOGGER						= Logger.getLogger(OlympiadGameTask.class.getName());
	private static final int[]		TELEPORT_TO_ARENA_TIMES		=
	{
		120,
		60,
		30, // All rounds get 30 seconds buffer time
		20,
		15,
		10, // Port players and clean round timing
		5,
		4,
		3,
		2,
		1,
		0
	};
	private static final int[]		BATTLE_START_TIME_FIRST		=
	{
		// 60,
		// 55,
		// 50,
		// 40,
		30,
		20,
		10,
		0
	};
	private static final int[]		BATTLE_START_TIME_SECOND	=
	{
		10,
		5,
		4,
		3,
		2,
		1,
		0
	};
	private static final int[]		BATTLE_END_TIME_SECOND		=
	{
		120,
		60,
		30,
		10,
		5
	};
	private static final int[]		TELEPORT_TO_TOWN_TIMES		=
	{
		// 40,
		// 30,
		// 20,
		10,
		5,
		4,
		3,
		2,
		1,
		0
	};
	private final OlympiadStadium	_stadium;
	private AbstractOlympiadGame	_game;
	private GameState				_state						= GameState.IDLE;
	private boolean					_needAnnounce				= false;
	private int						_countDown					= 0;
	
	public OlympiadGameTask(OlympiadStadium stadium)
	{
		_stadium = stadium;
		_stadium.registerTask(this);
	}
	
	public boolean isRunning()
	{
		return _state != GameState.IDLE;
	}
	
	public boolean isGameStarted()
	{
		return (_state.ordinal() >= GameState.GAME_STARTED.ordinal()) && (_state.ordinal() <= GameState.CLEANUP.ordinal());
	}
	
	public boolean isBattleStarted()
	{
		return ((_state == GameState.BATTLE_FIRST_ROUND) || (_state == GameState.BATTLE_SECOND_ROUND) || (_state == GameState.BATTLE_THIRD_ROUND) || (_state == GameState.PREPARE_SECOND_ROUND) || (_state == GameState.PREPARE_THIRD_ROUND));
	}
	
	public boolean isBattleFinished()
	{
		return _state == GameState.TELEPORT_TO_TOWN;
	}
	
	public boolean needAnnounce()
	{
		if (_needAnnounce)
		{
			_needAnnounce = false;
			return true;
		}
		return false;
	}
	
	public OlympiadStadium getStadium()
	{
		return _stadium;
	}
	
	public AbstractOlympiadGame getGame()
	{
		return _game;
	}
	
	public void attachGame(AbstractOlympiadGame game)
	{
		if ((game != null) && (_state != GameState.IDLE))
		{
			LOGGER.warning("Attempt to overwrite non-finished game in state " + _state);
			return;
		}
		_game = game;
		_state = GameState.BEGIN;
		_needAnnounce = false;
		ThreadPool.get().execute(this);
	}
	
	@Override
	public void run()
	{
		try
		{
			int delay = 1; // schedule next call after 1s
			switch (_state)
			{
				// Game created
				case BEGIN:
				{
					_state = GameState.TELEPORT_TO_ARENA;
					_countDown = Config.OLY_WAIT_TIME;
					break;
				}
				// Teleport to arena countdown
				case TELEPORT_TO_ARENA:
				{
					if (_countDown > 0)
					{
						final SystemMessage sm = new SystemMessage(SystemMessageId.YOU_WILL_BE_TAKEN_TO_THE_OLYMPIC_STADIUM_IN_S1_SEC);
						sm.addInt(_countDown);
						_game.broadcastPacket(sm);
					}
					if (_countDown == 1)
					{
						_game.untransformPlayers();
					}
					delay = getDelay(TELEPORT_TO_ARENA_TIMES);
					if (_countDown <= 0)
					{
						_state = GameState.GAME_STARTED;
					}
					break;
				}
				// Game start, port players to arena
				case GAME_STARTED:
				{
					if (!startGame())
					{
						_state = GameState.GAME_STOPPED;
						break;
					}
					_state = GameState.BATTLE_COUNTDOWN_FIRST;
					_countDown = BATTLE_START_TIME_FIRST[0];
					_stadium.updateZoneInfoForObservers(); // TODO lion temp hack for remove old info from client about prevoius match
					_stadium.spawnBuffers();
					delay = 5;
					break;
				}
				// Battle start countdown, first part (60-10)
				case BATTLE_COUNTDOWN_FIRST:
				{
					if (_countDown > 0)
					{
						if (_countDown == 55) // 55sec
						{
							_game.healPlayers();
						}
						else
						{
							final SystemMessage sm = new SystemMessage(SystemMessageId.THE_MATCH_BEGINS_IN_S1_SEC);
							sm.addInt(_countDown);
							_stadium.broadcastPacket(sm);
						}
					}
					delay = getDelay(BATTLE_START_TIME_FIRST);
					if (_countDown <= 0)
					{
						_game.makePlayersInvul();
						_game.resetDamage();
						_stadium.openDoors();
						_state = GameState.BATTLE_COUNTDOWN_SECOND;
						_countDown = BATTLE_START_TIME_SECOND[0];
						delay = getDelay(BATTLE_START_TIME_SECOND);
					}
					break;
				}
				// Battle start countdown, second part (10-0)
				case BATTLE_COUNTDOWN_SECOND:
				{
					if (_countDown > 0)
					{
						final SystemMessage sm = new SystemMessage(SystemMessageId.THE_MATCH_BEGINS_IN_S1_SEC);
						sm.addInt(_countDown);
						_stadium.broadcastPacket(sm);
					}
					delay = getDelay(BATTLE_START_TIME_SECOND);
					if (_countDown <= 0)
					{
						_state = GameState.BATTLE_STARTED;
						_game.removePlayersInvul();
					}
					break;
				}
				// Beginning of the battle
				case BATTLE_STARTED:
				{
					_stadium.deleteBuffers();
					_countDown = 0;
					_state = GameState.BATTLE_FIRST_ROUND; // set state first, used in zone update
					if (!startBattle())
					{
						_state = GameState.GAME_STOPPED;
					}
					final int remaining = (int) ((Config.OLY_BATTLE - _countDown) / 1000);
					_stadium.broadcastPacket(new ExOlympiadMatchResultInfo(_game.getPlayerOne(), _game.getPlayerTwo(), 1, _game.getRoundResult(), remaining));
					SystemMessage sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_ROUND_1);
					_stadium.broadcastPacket(sm);
					sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_START_OLYMPIAD);
					_stadium.broadcastPacket(sm);
					break;
				}
				case BATTLE_FIRST_ROUND:
				{
					_countDown += 1000;
					if (checkBattle(1) || (_countDown > Config.OLY_BATTLE))
					{
						if (!checkBattle(1))
						{
							SystemMessage sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_TIME_OVER);
							_stadium.broadcastPacket(sm);
							sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_TIE);
							_stadium.broadcastPacket(sm);
						}
						_stadium.broadcastPacket(new ExOlympiadMatchResultInfo(_game.getPlayerOne(), _game.getPlayerTwo(), 1, _game.getRoundResult(), 0));
						_countDown = 30; // Same as round 1 - 30 seconds buffer time
						_game.makePlayersInvul();
						_state = GameState.PREPARE_SECOND_ROUND;
					}
					break;
				}
				case PREPARE_SECOND_ROUND:
				{
					_stadium.spawnBuffers();
					if (_countDown > 0)
					{
						final SystemMessage sm = new SystemMessage(SystemMessageId.THE_MATCH_BEGINS_IN_S1_SEC);
						sm.addInt(_countDown);
						_game.broadcastPacket(sm);
					}
					if (_countDown == 10) // Port players and clean round at 10 seconds remaining
					{
						if (!_game.portPlayersToArena(_stadium.getZone().getSpawns(), _stadium.getInstance(), false))
						{
							_state = GameState.GAME_STOPPED;
						}
						_game.cleanNextRound(2);
					}
					delay = getDelay(TELEPORT_TO_ARENA_TIMES);
					if (_countDown <= 0)
					{
						_countDown = 0;
						if (!startBattle())
						{
							_state = GameState.GAME_STOPPED;
						}
						final int remaining = (int) ((Config.OLY_BATTLE - _countDown) / 1000);
						_stadium.broadcastPacket(new ExOlympiadMatchResultInfo(_game.getPlayerOne(), _game.getPlayerTwo(), 2, _game.getRoundResult(), remaining));
						SystemMessage sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_ROUND_2);
						_stadium.broadcastPacket(sm);
						sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_START_OLYMPIAD);
						_stadium.broadcastPacket(sm);
						_game.removePlayersInvul();
						_state = GameState.BATTLE_SECOND_ROUND;
					}
					break;
				}
				case BATTLE_SECOND_ROUND:
				{
					_stadium.deleteBuffers();
					_countDown += 1000;
					if (checkBattle(2) || (_countDown > Config.OLY_BATTLE))
					{
						if (!checkBattle(2))
						{
							SystemMessage sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_TIME_OVER);
							_stadium.broadcastPacket(sm);
							sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_TIE);
							_stadium.broadcastPacket(sm);
						}
						_stadium.broadcastPacket(new ExOlympiadMatchResultInfo(_game.getPlayerOne(), _game.getPlayerTwo(), 2, _game.getRoundResult(), 0));
						// Check round results with null safety
						final Map<Integer, Integer> roundResults = _game.getRoundResult();
						if (roundResults != null)
						{
							long player1Wins = roundResults.values().stream()
								.filter(Objects::nonNull)
								.filter(e -> e.intValue() == 1)
								.count();
							long player2Wins = roundResults.values().stream()
								.filter(Objects::nonNull)
								.filter(e -> e.intValue() == 2)
								.count();

							if (player1Wins >= 2 || player2Wins >= 2)
							{
								_state = GameState.GAME_STOPPED;
							}
							else
							{
								_countDown = 30; // Same as round 1 - 30 seconds buffer time
								_game.makePlayersInvul();
								_state = GameState.PREPARE_THIRD_ROUND;
							}
						}
						else
						{
							// Fallback if round results are null
							_state = GameState.GAME_STOPPED;
						}
					}
					break;
				}
				case PREPARE_THIRD_ROUND:
				{
					_stadium.spawnBuffers();
					if (_countDown > 0)
					{
						final SystemMessage sm = new SystemMessage(SystemMessageId.THE_MATCH_BEGINS_IN_S1_SEC);
						sm.addInt(_countDown);
						_game.broadcastPacket(sm);
					}
					if (_countDown == 10) // Port players and clean round at 10 seconds remaining
					{
						if (!_game.portPlayersToArena(_stadium.getZone().getSpawns(), _stadium.getInstance(), false))
						{
							_state = GameState.GAME_STOPPED;
						}
						_game.cleanNextRound(3);
					}
					delay = getDelay(TELEPORT_TO_ARENA_TIMES);
					if (_countDown <= 0)
					{
						_countDown = 0;
						if (!startBattle())
						{
							_state = GameState.GAME_STOPPED;
						}
						final int remaining = (int) ((Config.OLY_BATTLE - _countDown) / 1000);
						_stadium.broadcastPacket(new ExOlympiadMatchResultInfo(_game.getPlayerOne(), _game.getPlayerTwo(), 3, _game.getRoundResult(), remaining));
						SystemMessage sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_ROUND_3);
						_stadium.broadcastPacket(sm);
						sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_START_OLYMPIAD);
						_stadium.broadcastPacket(sm);
						_game.removePlayersInvul();
						_state = GameState.BATTLE_THIRD_ROUND;
					}
					break;
				}
				case BATTLE_THIRD_ROUND:
				{
					_stadium.deleteBuffers();
					_countDown += 1000;
					if (checkBattle(3) || (_countDown > Config.OLY_BATTLE))
					{
						if (!checkBattle(3))
						{
							SystemMessage sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_TIME_OVER);
							_stadium.broadcastPacket(sm);
							sm = new SystemMessage(SystemMessageId.HIDDEN_MSG_OLYMPIAD_TIE);
							_stadium.broadcastPacket(sm);
						}
						_stadium.broadcastPacket(new ExOlympiadMatchResultInfo(_game.getPlayerOne(), _game.getPlayerTwo(), 3, _game.getRoundResult(), 0));
						_state = GameState.GAME_STOPPED;
					}
					break;
				}
				// End of the battle
				case GAME_STOPPED:
				{
					_state = GameState.TELEPORT_TO_TOWN;
					_countDown = TELEPORT_TO_TOWN_TIMES[0];
					stopGame();
					delay = getDelay(TELEPORT_TO_TOWN_TIMES);
					break;
				}
				// Teleport to town countdown
				case TELEPORT_TO_TOWN:
				{
					if (_countDown > 0)
					{
						final SystemMessage sm = new SystemMessage(SystemMessageId.YOU_WILL_BE_MOVED_BACK_TO_TOWN_IN_S1_SECOND_S);
						sm.addInt(_countDown);
						_game.broadcastPacket(sm);
					}
					delay = getDelay(TELEPORT_TO_TOWN_TIMES);
					if (_countDown <= 0)
					{
						_state = GameState.CLEANUP;
					}
					break;
				}
				// Removals
				case CLEANUP:
				{
					cleanupGame();
					_state = GameState.IDLE;
					_game = null;
					return;
				}
			}
			ThreadPool.get().schedule(this, delay * 1000);
		}
		catch (Exception e)
		{
			switch (_state)
			{
				case GAME_STOPPED:
				case TELEPORT_TO_TOWN:
				case CLEANUP:
				case IDLE:
				{
					LOGGER.warning("Unable to return players back in town, exception: " + e.getMessage());
					// Force cleanup before going idle
					try
					{
						cleanupGame();
					}
					catch (Exception cleanupEx)
					{
						LOGGER.log(Level.SEVERE, "Failed to cleanup game after exception: " + cleanupEx.getMessage(), cleanupEx);
					}
					_state = GameState.IDLE;
					_game = null;
					return;
				}
			}
			LOGGER.log(Level.WARNING, "Exception in " + _state + ", trying to port players back: " + e.getMessage(), e);
			// Try to cleanup and stop the game properly
			try
			{
				if (_game != null)
				{
					_game.validateWinner(_stadium);
				}
			}
			catch (Exception validationEx)
			{
				LOGGER.log(Level.WARNING, "Failed to validate winner after exception: " + validationEx.getMessage(), validationEx);
			}
			_state = GameState.GAME_STOPPED;
			ThreadPool.get().schedule(this, 1000);
		}
	}
	
	private int getDelay(int[] times)
	{
		int time;
		for (int i = 0; i < (times.length - 1); i++)
		{
			time = times[i];
			if (time >= _countDown)
			{
				continue;
			}
			final int delay = _countDown - time;
			_countDown = time;
			return delay;
		}
		// should not happens
		_countDown = -1;
		return 1;
	}

	/**
	 * Second stage: check for defaulted, port players to arena, announce game.
	 * 
	 * @return true if no participants defaulted.
	 */
	private boolean startGame()
	{
		try
		{
			// Checking for opponents and teleporting to arena
			if (_game.checkDefaulted())
			{
				return false;
			}
			_stadium.closeDoors();
			if (!_game.portPlayersToArena(_stadium.getZone().getSpawns(), _stadium.getInstance(), true))
			{
				return false;
			}
			_game.removals();
			_needAnnounce = true;
			OlympiadGameManager.getInstance().startBattle(); // inform manager
			return true;
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		return false;
	}
	
	/**
	 * Fourth stage: last checks, remove buffers, start competition itself.
	 * 
	 * @return true if all participants online and ready on the stadium.
	 */
	private boolean startBattle()
	{
		try
		{
			if (_game.needBuffers())
			{
				_stadium.deleteBuffers();
			}
			if (_game.checkBattleStatus() && _game.makeCompetitionStart())
			{
				// game successfully started
				_game.broadcastOlympiadInfo(_stadium);
				_stadium.broadcastPacket(new SystemMessage(SystemMessageId.THE_MATCH_HAS_BEGUN_FIGHT));
				_stadium.updateZoneStatusForCharactersInside();
				return true;
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		return false;
	}
	
	/**
	 * Fifth stage: battle is running, returns true if winner found.
	 * 
	 * @return
	 */
	private boolean checkBattle(int round)
	{
		try
		{
			return _game.haveWinner(round);
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		return true;
	}
	
	/**
	 * Sixth stage: winner's validations
	 */
	private void stopGame()
	{
		try
		{
			_game.validateWinner(_stadium);
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		// try
		// {
		// _game.cleanEffects();
		// }
		// catch (Exception e)
		// {
		// LOGGER.log(Level.WARNING, e.getMessage(), e);
		// }
		try
		{
			_game.makePlayersInvul();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		try
		{
			_stadium.updateZoneStatusForCharactersInside();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
	}
	
	/**
	 * Seventh stage: game cleanup (port players back, closing doors, etc)
	 */
	private void cleanupGame()
	{
		try
		{
			_game.removePlayersInvul();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		try
		{
			_game.playersStatusBack();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		try
		{
			_game.portPlayersBack();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		try
		{
			_game.clearPlayers();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		try
		{
			_stadium.closeDoors();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, e.getMessage(), e);
		}
		try
		{
			// Cleanup any remaining buffers
			_stadium.deleteBuffers();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Failed to cleanup buffers: " + e.getMessage(), e);
		}
		try
		{
			// Reset stadium task reference
			_stadium.registerTask(null);
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Failed to reset stadium task: " + e.getMessage(), e);
		}
	}

	private enum GameState
	{
		BEGIN,
		TELEPORT_TO_ARENA,
		GAME_STARTED,
		BATTLE_COUNTDOWN_FIRST,
		BATTLE_COUNTDOWN_SECOND,
		BATTLE_STARTED,
		// BATTLE_IN_PROGRESS,
		BATTLE_FIRST_ROUND,
		PREPARE_SECOND_ROUND,
		BATTLE_SECOND_ROUND,
		PREPARE_THIRD_ROUND,
		BATTLE_THIRD_ROUND,
		GAME_STOPPED,
		TELEPORT_TO_TOWN,
		CLEANUP,
		IDLE
	}
}