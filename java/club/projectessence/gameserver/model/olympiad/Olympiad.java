/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.olympiad;

import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.xml.CategoryData;
import club.projectessence.gameserver.data.xml.ClassListData;
import club.projectessence.gameserver.enums.CategoryType;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.instancemanager.AntiFeedManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.olympiad.ExOlympiadInfo;
import club.projectessence.gameserver.util.Broadcast;

/**
 * <AUTHOR>
 */
public class Olympiad extends ListenersContainer
{
	private final Logger				LOGGER									= Logger.getLogger(Olympiad.class.getName());
	private final Logger				LOGGER_OLYMPIAD							= Logger.getLogger("olympiad");
	private final Map<Integer, StatSet>	NOBLES									= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>	NOBLES_RANK								= new HashMap<>();
	public static final String			OLYMPIAD_HTML_PATH						= "data/html/olympiad/";
	private final String				OLYMPIAD_LOAD_DATA						= "SELECT current_cycle, olympiad_end FROM olympiad_data WHERE id = 0";
	private final String				OLYMPIAD_SAVE_DATA						= "INSERT INTO olympiad_data (id, current_cycle, olympiad_end, olympiad_end_date) VALUES (0,?,?,?) ON DUPLICATE KEY UPDATE current_cycle=?, olympiad_end=?, olympiad_end_date=?";
	private final String				OLYMPIAD_LOAD_NOBLES					= "SELECT olympiad_nobles.charId, olympiad_nobles.class_id, characters.char_name, olympiad_nobles.olympiad_points, olympiad_nobles.competitions_done, olympiad_nobles.competitions_won, olympiad_nobles.competitions_lost, olympiad_nobles.competitions_drawn, olympiad_nobles.competitions_done_day FROM olympiad_nobles, characters WHERE characters.charId = olympiad_nobles.charId";
	private final String				OLYMPIAD_SAVE_NOBLES					= "INSERT INTO olympiad_nobles (charId,class_id,olympiad_points,competitions_done,competitions_won,competitions_lost,competitions_drawn,competitions_done_day) VALUES (?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE olympiad_points=?,competitions_done=?,competitions_won=?,competitions_lost=?,competitions_drawn=?,competitions_done_day=?";
	private final String				OLYMPIAD_GET_HEROS						= "SELECT olympiad_nobles.charId, characters.char_name FROM olympiad_nobles, characters WHERE characters.charId = olympiad_nobles.charId AND olympiad_nobles.class_id in (?, ?) AND olympiad_nobles.competitions_done >= " + Config.OLY_MIN_MATCHES + " AND olympiad_nobles.competitions_won > 0 ORDER BY olympiad_nobles.olympiad_points DESC, olympiad_nobles.competitions_done DESC, olympiad_nobles.competitions_won DESC";
	private final String				OLYMPIAD_GET_LEGEND						= "SELECT olympiad_nobles.charId FROM olympiad_nobles WHERE olympiad_nobles.competitions_done >=" + Config.OLY_MIN_MATCHES + " ORDER BY olympiad_nobles.olympiad_points DESC LIMIT 1";
	private final String				GET_ALL_CLASSIFIED_NOBLESS				= "SELECT charId from olympiad_nobles_prev WHERE competitions_done >= " + Config.OLY_MIN_MATCHES + " ORDER BY olympiad_points DESC, competitions_done DESC, competitions_won DESC";
	private final String				GET_EACH_CLASS_LEADER					= "SELECT characters.char_name from olympiad_nobles_prev, characters WHERE characters.charId = olympiad_nobles_prev.charId AND olympiad_nobles_prev.class_id = ? AND olympiad_nobles_prev.competitions_done >= " + Config.OLY_MIN_MATCHES + " ORDER BY olympiad_nobles_prev.olympiad_points DESC, olympiad_nobles_prev.competitions_done DESC, olympiad_nobles_prev.competitions_won DESC LIMIT 10";
	private final String				GET_EACH_CLASS_LEADER_CURRENT			= "SELECT characters.char_name from olympiad_nobles, characters WHERE characters.charId = olympiad_nobles.charId AND olympiad_nobles.class_id = ? AND olympiad_nobles.competitions_done >= " + Config.OLY_MIN_MATCHES + " ORDER BY olympiad_nobles.olympiad_points DESC, olympiad_nobles.competitions_done DESC, olympiad_nobles.competitions_won DESC LIMIT 10";
	private final String				GET_EACH_CLASS_LEADER_SOULHOUND			= "SELECT characters.char_name from olympiad_nobles_prev, characters WHERE characters.charId = olympiad_nobles_prev.charId AND (olympiad_nobles_prev.class_id = ? OR olympiad_nobles_prev.class_id = 133) AND olympiad_nobles_prev.competitions_done >= " + Config.OLY_MIN_MATCHES + " ORDER BY olympiad_nobles_prev.olympiad_points DESC, olympiad_nobles_prev.competitions_done DESC, olympiad_nobles_prev.competitions_won DESC LIMIT 10";
	private final String				GET_EACH_CLASS_LEADER_CURRENT_SOULHOUND	= "SELECT characters.char_name from olympiad_nobles, characters WHERE characters.charId = olympiad_nobles.charId AND (olympiad_nobles.class_id = ? OR olympiad_nobles.class_id = 133) AND olympiad_nobles.competitions_done >= " + Config.OLY_MIN_MATCHES + " ORDER BY olympiad_nobles.olympiad_points DESC, olympiad_nobles.competitions_done DESC, olympiad_nobles.competitions_won DESC LIMIT 10";
	public final String					UNCLAIMED_OLYMPIAD_POINTS_VAR			= "UNCLAIMED_OLYMPIAD_POINTS";
	private final String				OLYMPIAD_DELETE_ALL						= "TRUNCATE olympiad_nobles";
	private final String				OLYMPIAD_MONTH_CLEAR					= "TRUNCATE olympiad_nobles_prev";
	private final String				OLYMPIAD_MONTH_CREATE					= "INSERT INTO olympiad_nobles_prev SELECT charId, class_id, olympiad_points, competitions_done, competitions_won, competitions_lost, competitions_drawn FROM olympiad_nobles";
	private final Set<Integer>			HERO_IDS								= CategoryData.getInstance().getCategoryByType(CategoryType.FOURTH_CLASS_GROUP);
	public static final String			CHAR_ID									= "charId";
	public static final String			CLASS_ID								= "class_id";
	public static final String			CHAR_NAME								= "char_name";
	public static final String			POINTS									= "olympiad_points";
	public static final String			COMP_DONE								= "competitions_done";
	public static final String			COMP_WON								= "competitions_won";
	public static final String			COMP_LOST								= "competitions_lost";
	public static final String			COMP_DRAWN								= "competitions_drawn";
	public static final String			COMP_DONE_DAY							= "competitions_done_day";
	private long						_olympiadEnd							= 0;
	private int							_currentCycle							= 0;
	private long						_compEnd;
	private Calendar					_compStart;
	private boolean						_inCompPeriod;
	protected ScheduledFuture<?>		_scheduledCompStart;
	protected ScheduledFuture<?>		_scheduledCompEnd;
	private ScheduledFuture<?>			_scheduledOlympiadEnd;
	private ScheduledFuture<?>			_gameManager							= null;
	private ScheduledFuture<?>			_gameAnnouncer							= null;
	
	protected Olympiad()
	{
		if (Config.OLY_ENABLED)
		{
			load();
			AntiFeedManager.getInstance().registerEvent(AntiFeedManager.OLYMPIAD_ID);
		}
		else
		{
			LOGGER.log(Level.INFO, "Disabled.");
		}
	}
	
	private void load()
	{
		NOBLES.clear();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement statement = con.prepareStatement(OLYMPIAD_LOAD_DATA))
			{
				try (ResultSet rset = statement.executeQuery())
				{
					while (rset.next())
					{
						_currentCycle = rset.getInt("current_cycle");
						_olympiadEnd = rset.getLong("olympiad_end");
					}
				}
			}
		}
		catch (Exception e)
		{
			_currentCycle = 0;
			_olympiadEnd = 0;
			LOGGER.log(Level.WARNING, "Olympiad System: Error loading olympiad data from database: ", e);
		}
		final long currentTime = System.currentTimeMillis();
		if (_olympiadEnd == 0)
		{
			setNewOlympiadEnd();
		}
		else if (_olympiadEnd < currentTime)
		{
			if (Config.OLY_OFFLINE_ENDING_RULE)
			{
				setNewOlympiadEnd();
			}
		}
		init();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_LOAD_NOBLES))
			{
				try (ResultSet rs = ps.executeQuery())
				{
					StatSet statData;
					while (rs.next())
					{
						statData = new StatSet();
						statData.set(CLASS_ID, rs.getInt(CLASS_ID));
						statData.set(CHAR_NAME, rs.getString(CHAR_NAME));
						statData.set(POINTS, rs.getInt(POINTS));
						statData.set(COMP_DONE, rs.getInt(COMP_DONE));
						statData.set(COMP_WON, rs.getInt(COMP_WON));
						statData.set(COMP_LOST, rs.getInt(COMP_LOST));
						statData.set(COMP_DRAWN, rs.getInt(COMP_DRAWN));
						statData.set(COMP_DONE_DAY, rs.getInt(COMP_DONE_DAY));
						addNobleStats(rs.getInt(CHAR_ID), statData);
					}
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Olympiad System: Error loading noblesse data from database: ", e);
		}
		synchronized (this)
		{
			LOGGER.info("Olympiad System: Loading....");
			long milliToEnd = getMillisToOlympiadEnd();
			final double numSecs = (milliToEnd / 1000) % 60;
			double countDown = ((milliToEnd / 1000.) - numSecs) / 60;
			final int numMins = (int) Math.floor(countDown % 60);
			countDown = (countDown - numMins) / 60;
			final int numHours = (int) Math.floor(countDown % 24);
			final int numDays = (int) Math.floor((countDown - numHours) / 24);
			LOGGER.info("Olympiad System: " + numDays + " days, " + numHours + " hours and " + numMins + " mins until period ends.");
		}
		LOGGER.info("Olympiad System: Loaded " + NOBLES.size() + " Nobles");
	}
	
	public int getOlympiadRank(PlayerInstance player)
	{
		return NOBLES_RANK.getOrDefault(player.getObjectId(), 0);
	}
	
	public void loadNoblesRank()
	{
		NOBLES_RANK.clear();
		final Map<Integer, Integer> tmpPlace = new HashMap<>();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(GET_ALL_CLASSIFIED_NOBLESS))
			{
				try (ResultSet rs = ps.executeQuery())
				{
					int place = 1;
					while (rs.next())
					{
						tmpPlace.put(rs.getInt(CHAR_ID), place++);
					}
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Olympiad System: Error loading noblesse data from database for Ranking: ", e);
		}
		int rank1 = (int) Math.round(tmpPlace.size() * 0.01);
		int rank2 = (int) Math.round(tmpPlace.size() * 0.10);
		int rank3 = (int) Math.round(tmpPlace.size() * 0.25);
		int rank4 = (int) Math.round(tmpPlace.size() * 0.50);
		if (rank1 == 0)
		{
			rank1 = 1;
			rank2++;
			rank3++;
			rank4++;
		}
		for (Entry<Integer, Integer> chr : tmpPlace.entrySet())
		{
			if (chr.getValue() <= rank1)
			{
				NOBLES_RANK.put(chr.getKey(), 1);
			}
			else if (tmpPlace.get(chr.getKey()) <= rank2)
			{
				NOBLES_RANK.put(chr.getKey(), 2);
			}
			else if (tmpPlace.get(chr.getKey()) <= rank3)
			{
				NOBLES_RANK.put(chr.getKey(), 3);
			}
			else if (tmpPlace.get(chr.getKey()) <= rank4)
			{
				NOBLES_RANK.put(chr.getKey(), 4);
			}
			else
			{
				NOBLES_RANK.put(chr.getKey(), 5);
			}
		}
	}
	
	protected void init()
	{
		_compStart = Calendar.getInstance();
		final int currentDay = _compStart.get(Calendar.DAY_OF_WEEK);
		boolean dayFound = false;
		int dayCounter = 0;
		for (int i = currentDay; i < 8; i++)
		{
			if (Config.OLY_COMPETITION_DAYS.contains(i))
			{
				dayFound = true;
				break;
			}
			dayCounter++;
		}
		if (!dayFound)
		{
			for (int i = 1; i < 8; i++)
			{
				if (Config.OLY_COMPETITION_DAYS.contains(i))
				{
					break;
				}
				dayCounter++;
			}
		}
		if (dayCounter > 0)
		{
			_compStart.add(Calendar.DAY_OF_MONTH, dayCounter);
		}
		_compStart.set(Calendar.HOUR_OF_DAY, Config.OLY_COMPETITION_TIME[0]);
		_compStart.set(Calendar.MINUTE, Config.OLY_COMPETITION_TIME[1]);
		_compStart.set(Calendar.SECOND, 0);
		_compStart.set(Calendar.MILLISECOND, 0);
		_compEnd = _compStart.getTimeInMillis() + Config.OLY_COMPETITION_PERIOD;
		if (_scheduledOlympiadEnd != null)
		{
			_scheduledOlympiadEnd.cancel(true);
		}
		_scheduledOlympiadEnd = ThreadPool.schedule(new OlympiadEndTask(), getMillisToOlympiadEnd());
		updateCompStatus();
	}
	
	public void end()
	{
		new OlympiadEndTask().run();
	}
	
	public class OlympiadEndTask implements Runnable
	{
		@Override
		public void run()
		{
			final SystemMessage sm = new SystemMessage(SystemMessageId.ROUND_S1_OF_THE_OLYMPIAD_HAS_NOW_ENDED);
			sm.addInt(_currentCycle);
			Broadcast.toAllOnlinePlayers(sm);
			final List<StatSet> heroesToBe = sortHerosToBe();
			Hero.getInstance().resetData();
			Hero.getInstance().computeNewHeroes(heroesToBe);
			saveOlympiadStatus();
			updatePrevCycleData();
			loadNoblesRank();
			deleteNobles();
			setNewOlympiadEnd();
		}
	}
	
	protected int getNobleCount()
	{
		return NOBLES.size();
	}
	
	public StatSet getNobleStats(int playerId)
	{
		return NOBLES.get(playerId);
	}
	
	public void removeNobleStats(int playerId)
	{
		NOBLES.remove(playerId);
		NOBLES_RANK.remove(playerId);
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM olympiad_nobles WHERE charId=?"))
			{
				ps.setInt(1, playerId);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM olympiad_nobles_prev WHERE charId=?"))
			{
				ps.setInt(1, playerId);
				ps.execute();
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Olympiad System: Error removing noblesse data from database: ", e);
		}
	}
	
	private void updateCompStatus()
	{
		synchronized (this)
		{
			final long milliToStart = getMillisToCompBegin();
			final double numSecs = (milliToStart / 1000) % 60;
			double countDown = ((milliToStart / 1000.) - numSecs) / 60;
			final int numMins = (int) Math.floor(countDown % 60);
			countDown = (countDown - numMins) / 60;
			final int numHours = (int) Math.floor(countDown % 24);
			final int numDays = (int) Math.floor((countDown - numHours) / 24);
			LOGGER.info("Olympiad System: Competition Period Starts in " + numDays + " days, " + numHours + " hours and " + numMins + " mins.");
			LOGGER.info("Olympiad System: Event starts/started: " + _compStart.getTime());
			_scheduledCompStart = ThreadPool.schedule(() ->
			{
				startOlympiad();
			}, milliToStart);
		}
	}
	
	public void startOlympiad()
	{
		_inCompPeriod = true;
		final long remain = getMillisToCompEnd();
		Broadcast.toAllOnlinePlayers(new SystemMessage(SystemMessageId.THE_OLYMPIAD_HAS_BEGAN));
		LOGGER.info("Olympiad System: Olympiad Games have started.");
		LOGGER_OLYMPIAD.info("Result,Player1,Player2,Player1 HP,Player2 HP,Player1 Damage,Player2 Damage,Points,Classed");
		_gameManager = ThreadPool.scheduleAtFixedRate(OlympiadGameManager.getInstance(), 30000, 30000);
		if (Config.OLY_ANNOUNCE_GAMES)
		{
			_gameAnnouncer = ThreadPool.scheduleAtFixedRate(new OlympiadAnnouncer(), 30000, 500);
		}
		_scheduledCompEnd = ThreadPool.schedule(() ->
		{
			endOlympiad();
		}, remain);
		broadcastOlympiadInfo(remain);
	}

	/**
	 * Force start Olympiad immediately for testing
	 */
	public void forceStartOlympiad()
	{
		LOGGER.info("Olympiad System: Force starting Olympiad for testing...");

		// Cancel existing schedules
		if (_scheduledCompStart != null)
		{
			_scheduledCompStart.cancel(true);
		}
		if (_scheduledCompEnd != null)
		{
			_scheduledCompEnd.cancel(true);
		}

		// Set competition time to now
		_compStart = Calendar.getInstance();
		_compStart.add(Calendar.SECOND, 10); // Start in 10 seconds
		_compEnd = _compStart.getTimeInMillis() + Config.OLY_COMPETITION_PERIOD;

		LOGGER.info("Olympiad System: Force Schedule @ " + _compStart.getTime());

		// Schedule immediate start
		_scheduledCompStart = ThreadPool.schedule(() ->
		{
			startOlympiad();
		}, 10000); // 10 seconds delay

		updateCompStatus();
	}

	public void endOlympiad()
	{
		_inCompPeriod = false;
		Broadcast.toAllOnlinePlayers(new SystemMessage(SystemMessageId.THE_OLYMPIAD_IS_OVER));
		LOGGER.info("Olympiad System: Olympiad games have ended.");
		while (OlympiadGameManager.getInstance().isBattleStarted()) // cleared in game manager
		{
			try
			{
				// wait 1 minutes for end of pending games
				Thread.sleep(60000);
			}
			catch (Exception e)
			{
				// Ignore.
			}
		}
		if (_gameManager != null)
		{
			_gameManager.cancel(false);
			_gameManager = null;
		}
		if (_gameAnnouncer != null)
		{
			_gameAnnouncer.cancel(false);
			_gameAnnouncer = null;
		}
		resetDaily();
		saveOlympiadStatus();
		init();
		broadcastOlympiadInfo(0);
	}
	
	private void resetDaily()
	{
		for (StatSet noble : NOBLES.values())
		{
			noble.set(COMP_DONE_DAY, 0);
		}
	}
	
	private void broadcastOlympiadInfo(long remain)
	{
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player == null || !player.isOnline())
			{
				continue;
			}
			player.sendPacket(new ExOlympiadInfo(remain));
		}
	}
	
	private long getMillisToOlympiadEnd()
	{
		return Math.max(0, _olympiadEnd - System.currentTimeMillis());
	}
	
	public void manualSelectHeroes()
	{
		if (_scheduledOlympiadEnd != null)
		{
			_scheduledOlympiadEnd.cancel(true);
		}
		_scheduledOlympiadEnd = ThreadPool.schedule(new OlympiadEndTask(), 0);
	}
	
	public void setNewOlympiadEnd()
	{
		_currentCycle++;
		final SystemMessage sm = new SystemMessage(SystemMessageId.ROUND_S1_OF_THE_OLYMPIAD_GAMES_HAS_STARTED);
		sm.addInt(_currentCycle);
		Broadcast.toAllOnlinePlayers(sm);
		final Calendar olympiadEnd = Calendar.getInstance();
		olympiadEnd.set(Calendar.HOUR_OF_DAY, Config.OLY_CYCLE_END[0]);
		olympiadEnd.set(Calendar.MINUTE, Config.OLY_CYCLE_END[1]);
		olympiadEnd.set(Calendar.SECOND, 0);
		olympiadEnd.set(Calendar.MILLISECOND, 0);
		switch (Config.OLY_PERIOD)
		{
			case "MINUTE":
			{
				olympiadEnd.setTimeInMillis(System.currentTimeMillis());
				olympiadEnd.add(Calendar.MINUTE, Config.OLY_PERIOD_MULTIPLIER);
				break;
			}
			case "HOUR":
			{
				olympiadEnd.setTimeInMillis(System.currentTimeMillis());
				olympiadEnd.add(Calendar.HOUR, Config.OLY_PERIOD_MULTIPLIER);
				break;
			}
			case "DAY":
			{
				olympiadEnd.add(Calendar.DATE, Config.OLY_PERIOD_MULTIPLIER);
				break;
			}
			case "WEEK":
			{
				olympiadEnd.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
				if (olympiadEnd.getTimeInMillis() < System.currentTimeMillis())
				{
					olympiadEnd.add(Calendar.WEEK_OF_MONTH, Config.OLY_PERIOD_MULTIPLIER);
				}
				break;
			}
			case "MONTH":
			{
				olympiadEnd.set(Calendar.DAY_OF_MONTH, 1);
				if (olympiadEnd.getTimeInMillis() < System.currentTimeMillis())
				{
					olympiadEnd.add(Calendar.MONTH, Config.OLY_PERIOD_MULTIPLIER);
				}
				break;
			}
		}
		_olympiadEnd = olympiadEnd.getTimeInMillis();
		saveOlympiadStatus();
	}
	
	public boolean inCompPeriod()
	{
		return _inCompPeriod;
	}
	
	private long getMillisToCompBegin()
	{
		final long currentTime = System.currentTimeMillis();
		if ((_compStart.getTimeInMillis() < currentTime) && (_compEnd > currentTime))
		{
			return 10;
		}
		if (_compStart.getTimeInMillis() > currentTime)
		{
			return _compStart.getTimeInMillis() - currentTime;
		}
		return setNewCompBegin();
	}
	
	private long setNewCompBegin()
	{
		_compStart = Calendar.getInstance();
		_compStart.set(Calendar.HOUR_OF_DAY, Config.OLY_COMPETITION_TIME[0]);
		_compStart.set(Calendar.MINUTE, Config.OLY_COMPETITION_TIME[1]);
		_compStart.set(Calendar.SECOND, 0);
		_compStart.set(Calendar.MILLISECOND, 0);
		int currentDay = _compStart.get(Calendar.DAY_OF_WEEK);
		// Today's competitions ended, start checking from next day.
		if (currentDay == _compStart.get(Calendar.DAY_OF_WEEK))
		{
			if (currentDay == Calendar.SATURDAY)
			{
				currentDay = Calendar.SUNDAY;
			}
			else
			{
				currentDay++;
			}
		}
		boolean dayFound = false;
		int dayCounter = 0;
		for (int i = currentDay; i < 8; i++)
		{
			if (Config.OLY_COMPETITION_DAYS.contains(i))
			{
				dayFound = true;
				break;
			}
			dayCounter++;
		}
		if (!dayFound)
		{
			for (int i = 1; i < 8; i++)
			{
				if (Config.OLY_COMPETITION_DAYS.contains(i))
				{
					break;
				}
				dayCounter++;
			}
		}
		if (dayCounter > 0)
		{
			_compStart.add(Calendar.DAY_OF_MONTH, dayCounter);
		}
		_compStart.add(Calendar.HOUR_OF_DAY, 24);
		_compEnd = _compStart.getTimeInMillis() + Config.OLY_COMPETITION_PERIOD;
		LOGGER.info("Olympiad System: New Schedule @ " + _compStart.getTime());
		return _compStart.getTimeInMillis() - System.currentTimeMillis();
	}
	
	public long getMillisToCompEnd()
	{
		return _compEnd - System.currentTimeMillis();
	}
	
	public int getCurrentCycle()
	{
		return _currentCycle;
	}
	
	public boolean playerInStadia(PlayerInstance player)
	{
		return ZoneManager.getInstance().getOlympiadStadium(player) != null;
	}
	
	/**
	 * Save noblesse data to database
	 */
	public synchronized void saveNobleData()
	{
		if (NOBLES.isEmpty())
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			for (Entry<Integer, StatSet> entry : NOBLES.entrySet())
			{
				final StatSet nobleInfo = entry.getValue();
				if (nobleInfo == null)
				{
					continue;
				}
				final int charId = entry.getKey();
				final int classId = nobleInfo.getInt(CLASS_ID);
				final int points = nobleInfo.getInt(POINTS);
				final int compDone = nobleInfo.getInt(COMP_DONE);
				final int compWon = nobleInfo.getInt(COMP_WON);
				final int compLost = nobleInfo.getInt(COMP_LOST);
				final int compDrawn = nobleInfo.getInt(COMP_DRAWN);
				final int compDoneDay = nobleInfo.getInt(COMP_DONE_DAY);
				try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_SAVE_NOBLES))
				{
					ps.setInt(1, charId);
					ps.setInt(2, classId);
					ps.setInt(3, points);
					ps.setInt(4, compDone);
					ps.setInt(5, compWon);
					ps.setInt(6, compLost);
					ps.setInt(7, compDrawn);
					ps.setInt(8, compDoneDay);
					ps.setInt(9, points);
					ps.setInt(10, compDone);
					ps.setInt(11, compWon);
					ps.setInt(12, compLost);
					ps.setInt(13, compDrawn);
					ps.setInt(14, compDoneDay);
					ps.execute();
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Olympiad System: Failed to save noblesse data to database: ", e);
		}
	}
	
	/**
	 * Save olympiad.properties file with current olympiad status and update noblesse table in database
	 */
	public void saveOlympiadStatus()
	{
		saveNobleData();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_SAVE_DATA))
			{
				final Date date = new Date(_olympiadEnd);
				ps.setInt(1, _currentCycle);
				ps.setLong(2, _olympiadEnd);
				ps.setDate(3, date);
				ps.setInt(4, _currentCycle);
				ps.setLong(5, _olympiadEnd);
				ps.setDate(6, date);
				ps.execute();
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Olympiad System: Failed to save olympiad data to database: ", e);
		}
	}
	
	protected void updatePrevCycleData()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_MONTH_CLEAR))
			{
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_MONTH_CREATE))
			{
				ps.execute();
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Olympiad System: Failed to update previous cycle noblese data: ", e);
		}
	}
	
	protected List<StatSet> sortHerosToBe()
	{
		LOGGER_OLYMPIAD.info("Noble,charid,classid,compDone,points");
		StatSet nobleInfo;
		for (Entry<Integer, StatSet> entry : NOBLES.entrySet())
		{
			nobleInfo = entry.getValue();
			if (nobleInfo == null)
			{
				continue;
			}
			final int charId = entry.getKey();
			final int classId = nobleInfo.getInt(CLASS_ID);
			final String charName = nobleInfo.getString(CHAR_NAME);
			final int points = nobleInfo.getInt(POINTS);
			final int compDone = nobleInfo.getInt(COMP_DONE);
			LOGGER_OLYMPIAD.info(charName + "," + charId + "," + classId + "," + compDone + "," + points);
		}
		final List<StatSet> heroesToBe = new LinkedList<>();
		int legendId = 0;
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_GET_LEGEND))
			{
				try (ResultSet rs = ps.executeQuery())
				{
					if (rs.next())
					{
						legendId = rs.getInt("charId");
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Olympiad System: Couldnt load legend from DB");
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_GET_HEROS))
			{
				StatSet hero;
				for (int element : HERO_IDS)
				{
					// Classic can have 2nd and 3rd class competitors, but only 1 hero
					final ClassId parent = ClassListData.getInstance().getClass(element).getParentClassId();
					ps.setInt(1, element);
					ps.setInt(2, parent.getId());
					try (ResultSet rs = ps.executeQuery())
					{
						if (rs.next())
						{
							hero = new StatSet();
							final int charId = rs.getInt(CHAR_ID);
							hero.set(CLASS_ID, element); // save the 3rd class title
							hero.set(CHAR_ID, charId);
							hero.set(CHAR_NAME, rs.getString(CHAR_NAME));
							hero.set("LEGEND", charId == legendId ? 1 : 0);
							LOGGER_OLYMPIAD.info("Hero " + hero.getString(CHAR_NAME) + "," + charId + "," + hero.getInt(CLASS_ID));
							heroesToBe.add(hero);
						}
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Olympiad System: Couldnt load heros from DB");
		}
		return heroesToBe;
	}
	
	public List<String> getClassLeaderBoard(int classId)
	{
		final String query;
		if (Config.OLY_SHOW_MONTHLY_WINNERS)
		{
			if (classId == 132)
			{
				query = GET_EACH_CLASS_LEADER_SOULHOUND;
			}
			else
			{
				query = GET_EACH_CLASS_LEADER;
			}
		}
		else
		{
			if (classId == 132)
			{
				query = GET_EACH_CLASS_LEADER_CURRENT_SOULHOUND;
			}
			else
			{
				query = GET_EACH_CLASS_LEADER_CURRENT;
			}
		}
		final List<String> names = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(query))
			{
				ps.setInt(1, classId);
				try (ResultSet rs = ps.executeQuery())
				{
					while (rs.next())
					{
						names.add(rs.getString(CHAR_NAME));
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Olympiad System: Couldn't load olympiad leaders from DB!");
		}
		return names;
	}
	
	public int getNoblePoints(PlayerInstance player)
	{
		if (!NOBLES.containsKey(player.getObjectId()))
		{
			final StatSet statDat = new StatSet();
			statDat.set(CLASS_ID, player.getBaseClass());
			statDat.set(CHAR_NAME, player.getName());
			statDat.set(POINTS, 0);
			statDat.set(COMP_DONE, 0);
			statDat.set(COMP_WON, 0);
			statDat.set(COMP_LOST, 0);
			statDat.set(COMP_DRAWN, 0);
			statDat.set(COMP_DONE_DAY, 0);
			addNobleStats(player.getObjectId(), statDat);
		}
		return NOBLES.get(player.getObjectId()).getInt(POINTS);
	}
	
	public int getCompetitionDone(int objId)
	{
		if (!NOBLES.containsKey(objId))
		{
			return 0;
		}
		return NOBLES.get(objId).getInt(COMP_DONE);
	}
	
	public int getCompetitionWon(int objId)
	{
		if (!NOBLES.containsKey(objId))
		{
			return 0;
		}
		return NOBLES.get(objId).getInt(COMP_WON);
	}
	
	public int getCompetitionLost(int objId)
	{
		if (!NOBLES.containsKey(objId))
		{
			return 0;
		}
		return NOBLES.get(objId).getInt(COMP_LOST);
	}
	
	public int getCompetitionDoneDay(int objId)
	{
		if (!NOBLES.containsKey(objId))
		{
			return 0;
		}
		return NOBLES.get(objId).getInt(COMP_DONE_DAY);
	}
	
	public int getRemainingDailyMatches(int objId)
	{
		return Math.max(Config.OLY_MAX_DAILY_MATCHES - getCompetitionDoneDay(objId), 0);
	}
	
	protected void deleteNobles()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(OLYMPIAD_DELETE_ALL))
			{
				ps.execute();
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Olympiad System: Couldn't delete nobles from DB!");
		}
		NOBLES.clear();
	}
	
	public StatSet addNobleStats(int charId, StatSet data)
	{
		return NOBLES.put(charId, data);
	}
	
	public static Olympiad getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final Olympiad INSTANCE = new Olympiad();
	}
}
