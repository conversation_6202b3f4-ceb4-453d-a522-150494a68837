/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.actor.instance;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.instancemanager.FortSiegeManager;
import club.projectessence.gameserver.instancemanager.SiegeManager;
import club.projectessence.gameserver.model.SiegeClan;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.status.SiegeFlagStatus;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Siegable;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

public class SiegeFlagInstance extends Npc {
	private final Clan _clan;
	private final boolean _isAdvanced;
	private Siegable _siege;
	private boolean _canTalk;

	public SiegeFlagInstance(PlayerInstance player, NpcTemplate template, boolean advanced) {
		super(template);
		setInstanceType(InstanceType.SiegeFlagInstance);

		_clan = player.getClan();
		_canTalk = true;
		_siege = SiegeManager.getInstance().getSiege(player.getX(), player.getY(), player.getZ());
		if (_siege == null) {
			_siege = FortSiegeManager.getInstance().getSiege(player.getX(), player.getY(), player.getZ());
		}
		if ((_clan == null) || (_siege == null)) {
			throw new NullPointerException(getClass().getSimpleName() + ": Initialization failed.");
		}

		final SiegeClan sc = _siege.getAttackerClan(_clan);
		if (sc == null) {
			throw new NullPointerException(getClass().getSimpleName() + ": Cannot find siege clan.");
		}

		sc.addFlag(this);
		_isAdvanced = advanced;
		getStatus();
		setInvul(false);
	}

	@Override
	public boolean canBeAttacked() {
		return !isInvul();
	}

	@Override
	public boolean isAutoAttackable(Creature attacker) {
		return !isInvul();
	}

	@Override
	public boolean doDie(Creature killer) {
		if (!super.doDie(killer)) {
			return false;
		}
		if ((_siege != null) && (_clan != null)) {
			final SiegeClan sc = _siege.getAttackerClan(_clan);
			if (sc != null) {
				sc.removeFlag(this);
			}
		}
		return true;
	}

	@Override
	public void onForcedAttack(PlayerInstance player) {
		onAction(player);
	}

	@Override
	public void onAction(PlayerInstance player, boolean interact) {
		if ((player == null) || !canTarget(player)) {
			return;
		}

		// Check if the PlayerInstance already target the NpcInstance
		if (this != player.getTarget()) {
			// Set the target of the PlayerInstance player
			player.setTarget(this);
		} else if (interact) {
			if (isAutoAttackable(player) && (Math.abs(player.getZ() - getZ()) < 100)) {
				player.getAI().setIntention(CtrlIntention.AI_INTENTION_ATTACK, this);
			} else {
				// Send a Server->Client ActionFailed to the PlayerInstance in order to avoid that the client wait another packet
				player.sendPacket(ActionFailed.STATIC_PACKET);
			}
		}
	}

	public boolean isAdvancedHeadquarter() {
		return _isAdvanced;
	}

	@Override
	public SiegeFlagStatus getStatus() {
		return (SiegeFlagStatus) super.getStatus();
	}

	@Override
	public void initCharStatus() {
		setStatus(new SiegeFlagStatus(this));
	}

	@Override
	public void reduceCurrentHp(double damage, Creature attacker, Skill skill) {
		super.reduceCurrentHp(damage, attacker, skill);
		if (canTalk() && (((getCastle() != null) && getCastle().getSiege().isInProgress()) || ((getFort() != null) && getFort().getSiege().isInProgress())) && (_clan != null)) {
			// send warning to owners of headquarters that theirs base is under attack
			_clan.broadcastToOnlineMembers(new SystemMessage(SystemMessageId.SIEGE_CAMP_IS_UNDER_ATTACK));
			setCanTalk(false);
			ThreadPool.get().schedule(new ScheduleTalkTask(), 20000);
		}
	}

	void setCanTalk(boolean value) {
		_canTalk = value;
	}

	private boolean canTalk() {
		return _canTalk;
	}

	private class ScheduleTalkTask implements Runnable {
		@Override
		public void run() {
			setCanTalk(true);
		}
	}
}
