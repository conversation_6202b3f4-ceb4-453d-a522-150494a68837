/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.actor;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.util.CommonUtil;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.ai.CreatureAI;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.ai.SummonAI;
import club.projectessence.gameserver.data.sql.CharSummonTable;
import club.projectessence.gameserver.data.xml.ExperienceData;
import club.projectessence.gameserver.data.xml.PetDataTable;
import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.enums.NpcInfoType;
import club.projectessence.gameserver.enums.Race;
import club.projectessence.gameserver.enums.Team;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.IItemHandler;
import club.projectessence.gameserver.handler.ItemHandler;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.AggroInfo;
import club.projectessence.gameserver.model.DamageList;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.instance.PetInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.stat.SummonStat;
import club.projectessence.gameserver.model.actor.status.SummonStatus;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerSummonSpawn;
import club.projectessence.gameserver.model.itemcontainer.PetInventory;
import club.projectessence.gameserver.model.items.Weapon;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.ActionType;
import club.projectessence.gameserver.model.olympiad.OlympiadGameManager;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.SkillCaster;
import club.projectessence.gameserver.model.skills.targets.TargetType;
import club.projectessence.gameserver.model.stats.Formulas;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.model.zone.ZoneRegion;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.AbstractMaskPacket;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.ExPartyPetWindowAdd;
import club.projectessence.gameserver.network.serverpackets.ExPartyPetWindowDelete;
import club.projectessence.gameserver.network.serverpackets.ExPartyPetWindowUpdate;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.RelationChanged;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import club.projectessence.gameserver.network.serverpackets.SummonInfo;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.TeleportToLocation;
import club.projectessence.gameserver.network.serverpackets.pet.ExPetInfo;
import club.projectessence.gameserver.network.serverpackets.pet.ExPetSkillList;
import club.projectessence.gameserver.network.serverpackets.pet.PetDelete;
import club.projectessence.gameserver.network.serverpackets.pet.PetInfo;
import club.projectessence.gameserver.network.serverpackets.pet.PetItemList;
import club.projectessence.gameserver.network.serverpackets.pet.PetStatusUpdate;
import club.projectessence.gameserver.taskmanager.DecayTaskManager;

public abstract class Summon extends Playable
{
	// @formatter:off
	private static final int[]						PASSIVE_SUMMONS						=
	{
		12564, 12621, 14702, 14703, 14704, 14705, 14706, 14707, 14708, 14709, 14710, 14711,
		14712, 14713, 14714, 14715, 14716, 14717, 14718, 14719, 14720, 14721, 14722, 14723,
		14724, 14725, 14726, 14727, 14728, 14729, 14730, 14731, 14732, 14733, 14734, 14735, 14736, 15955, 15977
	};
	// TODO: setter & getter
	public long										_lastSkillTimeStamp					= 0;
	protected boolean								_restoreSummon						= true;
	private PlayerInstance							_owner;
	private int										_attackRange						= 36;						// Melee range
	private boolean									_follow								= true;
	private boolean									_previousFollowStatus				= true;
	private int										_summonPoints						= 0;
	private int										_summonEvolveLevel					= 0;
	public static final int							BROADCAST_DELAY						= 250;
	private final Map<Integer, ScheduledFuture<?>>	_summonBroadcastTaskMap				= new ConcurrentHashMap<>();
	private ScheduledFuture<?>						_summonAbnormalVisualBroadcastTask	= null;
	// @formatter:on
	
	public Summon(NpcTemplate template, PlayerInstance owner)
	{
		super(template);
		setInstanceType(InstanceType.Summon);
		setInstance(owner.getInstanceWorld()); // set instance to same as owner
		setShowSummonAnimation(true);
		_owner = owner;
		getAI();
		// Make sure summon does not spawn in a wall.
		final int x = owner.getX();
		final int y = owner.getY();
		final int z = owner.getZ();
		final Location location = GeoEngine.getInstance().canMoveToTargetLoc(x, y, z, x + Rnd.get(-100, 100), y + Rnd.get(-100, 100), z, owner.getInstanceWorld());
		setXYZInvisible(location.getX(), location.getY(), location.getZ());
	}
	
	public Summon(NpcTemplate template, PlayerInstance owner, boolean invisible)
	{
		super(template);
		setInstanceType(InstanceType.Summon);
		setInstance(owner.getInstanceWorld()); // set instance to same as owner
		_owner = owner;
	}
	
	@Override
	public void onSpawn()
	{
		super.onSpawn();
		if (Config.SUMMON_STORE_SKILL_COOLTIME && !isTeleporting())
		{
			restoreEffects();
		}
		setFollowStatus(true);
		updateAndBroadcastStatus(0);
		sendPacket(new RelationChanged(this, _owner));
		World.getInstance().forEachVisibleObject(getOwner(), PlayerInstance.class, player -> player.sendPacket(new RelationChanged(this, player)));
		final Party party = _owner.getParty();
		if (party != null)
		{
			party.broadcastToPartyMembers(_owner, new ExPartyPetWindowAdd(this));
		}
		setShowSummonAnimation(false); // addVisibleObject created the info packets with summon animation
		// if someone comes into range now, the animation shouldn't show any more
		_restoreSummon = false;
		rechargeShots(true, true, false);
		// Notify to scripts
		EventDispatcher.getInstance().notifyEventAsync(new OnPlayerSummonSpawn(this), this);
	}
	
	@Override
	public SummonStat getStat()
	{
		return (SummonStat) super.getStat();
	}
	
	@Override
	public void initCharStat()
	{
		setStat(new SummonStat(this));
	}
	
	@Override
	public SummonStatus getStatus()
	{
		return (SummonStatus) super.getStatus();
	}
	
	@Override
	public void initCharStatus()
	{
		setStatus(new SummonStatus(this));
	}
	
	@Override
	protected CreatureAI initAI()
	{
		return new SummonAI(this);
	}
	
	@Override
	public NpcTemplate getTemplate()
	{
		return (NpcTemplate) super.getTemplate();
	}
	
	// this defines the action buttons, 1 for Summon, 2 for Pets
	public abstract int getSummonType();
	
	@Override
	public void stopAllEffects()
	{
		super.stopAllEffects();
		updateAndBroadcastStatus(1);
	}
	
	@Override
	public void stopAllEffectsExceptThoseThatLastThroughDeath()
	{
		super.stopAllEffectsExceptThoseThatLastThroughDeath();
		updateAndBroadcastStatus(1);
	}
	
	@Override
	public void updateAbnormalVisualEffects()
	{
		if (_summonAbnormalVisualBroadcastTask != null)
		{
			_summonAbnormalVisualBroadcastTask.cancel(true);
			_summonAbnormalVisualBroadcastTask = null;
		}
		_summonAbnormalVisualBroadcastTask = ThreadPool.get().schedule(() ->
		{
			World.getInstance().forEachVisibleObject(this, PlayerInstance.class, player ->
			{
				if (player == _owner)
				{
					player.sendPacket(new PetInfo(this, 1));
					return;
				}
				final AbstractMaskPacket<NpcInfoType> packet;
				if (isPet())
				{
					packet = new ExPetInfo(this, player, 1);
				}
				else
				{
					packet = new SummonInfo(this, player, 1);
				}
				packet.addComponentType(NpcInfoType.ABNORMALS);
				player.sendPacket(packet);
			});
		}, BROADCAST_DELAY);
	}
	
	/**
	 * @return Returns the mountable.
	 */
	public boolean isMountable()
	{
		return false;
	}
	
	public long getExpForThisLevel()
	{
		if (getLevel() >= ExperienceData.getInstance().getMaxPetLevel())
		{
			return 0;
		}
		return ExperienceData.getInstance().getExpForLevel(getLevel());
	}
	
	public long getExpForNextLevel()
	{
		if (getLevel() >= (ExperienceData.getInstance().getMaxPetLevel() - 1))
		{
			return 0;
		}
		return ExperienceData.getInstance().getExpForLevel(getLevel() + 1);
	}
	
	@Override
	public int getReputation()
	{
		return _owner != null ? _owner.getReputation() : 0;
	}
	
	@Override
	public byte getPvpFlag()
	{
		return _owner != null ? _owner.getPvpFlag() : 0;
	}
	
	@Override
	public Team getTeam()
	{
		return _owner != null ? _owner.getTeam() : Team.NONE;
	}
	
	public PlayerInstance getOwner()
	{
		return _owner;
	}
	
	public void setOwner(PlayerInstance newOwner)
	{
		_owner = newOwner;
	}
	
	/**
	 * Gets the summon ID.
	 *
	 * @return the summon ID
	 */
	@Override
	public int getId()
	{
		return getTemplate().getId();
	}
	
	public short getSoulShotsPerHit()
	{
		if (getTemplate().getSoulShot() > 0)
		{
			return (short) getTemplate().getSoulShot();
		}
		return 1;
	}
	
	public short getSpiritShotsPerHit()
	{
		if (getTemplate().getSpiritShot() > 0)
		{
			return (short) getTemplate().getSpiritShot();
		}
		return 1;
	}
	
	public void followOwner()
	{
		setFollowStatus(true);
	}
	
	@Override
	public boolean doDie(Creature killer)
	{
		if (isNoblesseBlessedAffected())
		{
			stopEffects(EffectFlag.NOBLESS_BLESSING);
			storeEffect(true);
		}
		else
		{
			storeEffect(false);
		}
		if (!super.doDie(killer))
		{
			return false;
		}
		if (_owner != null)
		{
			World.getInstance().forEachVisibleObject(this, Attackable.class, target ->
			{
				if (target.isDead())
				{
					return;
				}
				final AggroInfo info = target.getAggroList().get(this);
				if (info != null)
				{
					target.addDamageHate(_owner, info.getDamage(), info.getHate());
				}
			});
		}
		DecayTaskManager.getInstance().add(this);
		return true;
	}
	
	public boolean doDie(Creature killer, boolean decayed)
	{
		if (!super.doDie(killer))
		{
			return false;
		}
		if (!decayed)
		{
			DecayTaskManager.getInstance().add(this);
		}
		return true;
	}
	
	public void stopDecay()
	{
		DecayTaskManager.getInstance().cancel(this);
	}
	
	@Override
	public void onDecay()
	{
		if (!isPet())
		{
			super.onDecay();
		}
		else
		{
			unSummon(_owner);
		}
		deleteMe(_owner);
	}
	
	@Override
	public void broadcastStatusUpdate(Creature caster)
	{
		super.broadcastStatusUpdate(caster);
		updateAndBroadcastStatus(1);
	}
	
	public void deleteMe(PlayerInstance owner)
	{
		super.deleteMe();
		if (owner != null)
		{
			owner.sendPacket(new PetDelete(getSummonType(), getObjectId()));
			final Party party = owner.getParty();
			if (party != null)
			{
				party.broadcastToPartyMembers(owner, new ExPartyPetWindowDelete(this));
			}
			if (isPet())
			{
				owner.setPet(null);
			}
			else
			{
				owner.removeServitor(getObjectId());
			}
		}
		// pet will be deleted along with all his items
		if (getInventory() != null)
		{
			getInventory().destroyAllItems("pet deleted", _owner, this);
		}
		decayMe();
		CharSummonTable.getInstance().removeServitor(_owner, getObjectId());
	}
	
	public void unSummon(PlayerInstance owner)
	{
		if (isSpawned())
		{
			if (isDead())
			{
				stopDecay();
			}
			// Prevent adding effects while unsummoning.
			setInvul(true);
			abortAttack();
			abortCast();
			storeMe();
			storeEffect(true);
			// Stop AI tasks
			if (hasAI())
			{
				getAI().stopAITask(); // Calls stopFollow as well.
			}
			// Cancel running skill casters.
			abortAllSkillCasters();
			stopAllEffects();
			stopHpMpRegeneration();
			cancelBuffFinishTask();
			for (ScheduledFuture<?> task : _summonBroadcastTaskMap.values())
			{
				if (task != null)
				{
					task.cancel(true);
				}
			}
			_summonBroadcastTaskMap.clear();
			if (owner != null)
			{
				if (isPet())
				{
					owner.setPet(null);
				}
				else
				{
					owner.removeServitor(getObjectId());
				}
				owner.sendPacket(new PetDelete(getSummonType(), getObjectId()));
				final Party party = owner.getParty();
				if (party != null)
				{
					party.broadcastToPartyMembers(owner, new ExPartyPetWindowDelete(this));
				}
				if ((getInventory() != null) && (getInventory().getSize() > 0))
				{
					_owner.setPetInvItems(true);
					// sendPacket(SystemMessageId.THERE_ARE_ITEMS_IN_THE_PET_S_INVENTORY_TAKE_THEM_OUT_FIRST);
				}
				else
				{
					_owner.setPetInvItems(false);
				}
			}
			final ZoneRegion oldRegion = ZoneManager.getInstance().getRegion(this);
			decayMe();
			oldRegion.removeFromZones(this);
			setTarget(null);
			// if (owner != null)
			// {
			// for (int itemId : owner.getAutoSoulShot())
			// {
			// final String handler = ((EtcItem) ItemTable.getInstance().getTemplate(itemId)).getHandlerName();
			// if ((handler != null) && handler.contains("Beast"))
			// {
			// owner.disableAutoShot(itemId);
			// }
			// }
			// }
		}
	}
	
	public int getAttackRange()
	{
		return _attackRange;
	}
	
	public void setAttackRange(int range)
	{
		_attackRange = (range < 36) ? 36 : range;
	}
	
	public boolean getFollowStatus()
	{
		return _follow;
	}
	
	public void setFollowStatus(boolean value)
	{
		_follow = value;
		if (_follow)
		{
			getAI().setIntention(CtrlIntention.AI_INTENTION_FOLLOW, _owner);
		}
		else
		{
			getAI().setIntention(CtrlIntention.AI_INTENTION_IDLE);
		}
	}
	
	@Override
	public boolean isAutoAttackable(Creature attacker)
	{
		if (_owner != null)
		{
			return _owner.isAutoAttackable(attacker);
		}
		return false;
	}
	
	public int getControlObjectId()
	{
		return 0;
	}
	
	public Weapon getActiveWeapon()
	{
		return null;
	}
	
	@Override
	public PetInventory getInventory()
	{
		return null;
	}
	
	public void setRestoreSummon(boolean value)
	{}
	
	@Override
	public ItemInstance getActiveWeaponInstance()
	{
		return null;
	}
	
	@Override
	public Weapon getActiveWeaponItem()
	{
		return null;
	}
	
	@Override
	public ItemInstance getSecondaryWeaponInstance()
	{
		return null;
	}
	
	@Override
	public Weapon getSecondaryWeaponItem()
	{
		return null;
	}
	
	/**
	 * Return True if the Summon is invulnerable or if the summoner is in spawn protection.
	 */
	@Override
	public boolean isInvul()
	{
		return super.isInvul() || _owner.isSpawnProtected();
	}
	
	/**
	 * Return the Party object of its PlayerInstance owner or null.
	 */
	@Override
	public Party getParty()
	{
		if (_owner == null)
		{
			return null;
		}
		return _owner.getParty();
	}
	
	/**
	 * Return True if the Creature has a Party in progress.
	 */
	@Override
	public boolean isInParty()
	{
		return (_owner != null) && _owner.isInParty();
	}
	
	/**
	 * Check if the active Skill can be casted.<br>
	 * <br>
	 * <b><u>Actions</u>:</b>
	 * <ul>
	 * <li>Check if the target is correct</li>
	 * <li>Check if the target is in the skill cast range</li>
	 * <li>Check if the summon owns enough HP and MP to cast the skill</li>
	 * <li>Check if all skills are enabled and this skill is enabled</li>
	 * <li>Check if the skill is active</li>
	 * <li>Notify the AI with AI_INTENTION_CAST and target</li>
	 * </ul>
	 *
	 * @param skill
	 *            The Skill to use
	 * @param forceUse
	 *            used to force ATTACK on players
	 * @param dontMove
	 *            used to prevent movement, if not in range
	 */
	@Override
	public boolean useMagic(Skill skill, ItemInstance item, boolean forceUse, boolean dontMove, boolean showSystemMessage)
	{
		// Null skill, dead summon or null owner are reasons to prevent casting.
		if ((skill == null) || isDead() || (_owner == null))
		{
			return false;
		}
		// Check if the skill is active
		if (skill.isPassive())
		{
			// just ignore the passive skill request. why does the client send it anyway ??
			return false;
		}
		// If a skill is currently being used
		if (isCastingNow(SkillCaster::isAnyNormalType))
		{
			return false;
		}
		// Get the target for the skill
		final WorldObject target;
		if (skill.getTargetType() == TargetType.OWNER_PET)
		{
			target = _owner;
		}
		else
		{
			final WorldObject currentTarget = _owner.getTarget();
			if (currentTarget != null)
			{
				target = skill.getTarget(this, forceUse && (!currentTarget.isPlayable() || !currentTarget.isInsideZone(ZoneId.PEACE) || !currentTarget.isInsideZone(ZoneId.NO_PVP)), dontMove, false);
				if (skill.isDebuff() && (currentTarget == _owner) && ((skill.getTargetType() == (TargetType.TARGET) || (skill.getTargetType() == (TargetType.ENEMY)))))
				{
					return false;
				}
				if (skill.isBad() && !forceUse && (_owner.getTarget() != _owner))
				{
					if (!currentTarget.isAutoAttackable(_owner))
					{
						_owner.sendPacket(SystemMessageId.INVALID_TARGET);
						return false;
					}
				}
				if (skill.isBad() && forceUse && (_owner.getTarget() == _owner))
				{
					if (!currentTarget.isAutoAttackable(_owner))
					{
						_owner.sendPacket(SystemMessageId.INVALID_TARGET);
						return false;
					}
				}
			}
			else
			{
				target = skill.getTarget(this, forceUse, dontMove, false);
			}
		}
		// Check the validity of the target
		if (target == null)
		{
			if (showSystemMessage)
			{
				sendPacket(SystemMessageId.YOUR_TARGET_CANNOT_BE_FOUND);
			}
			return false;
		}
		// Check if this skill is enabled (e.g. reuse time)
		if (isSkillDisabled(skill))
		{
			if (showSystemMessage)
			{
				sendPacket(SystemMessageId.THAT_SERVITOR_SKILL_CANNOT_BE_USED_BECAUSE_IT_IS_RECHARGING);
			}
			return false;
		}
		// Check if the summon has enough MP
		if (getCurrentMp() < (getStat().getMpConsume(skill) + getStat().getMpInitialConsume(skill)))
		{
			// Send a System Message to the caster
			if (showSystemMessage)
			{
				sendPacket(SystemMessageId.NOT_ENOUGH_MP);
			}
			return false;
		}
		// Check if the summon has enough HP
		if (getCurrentHp() <= skill.getHpConsume())
		{
			// Send a System Message to the caster
			if (showSystemMessage)
			{
				sendPacket(SystemMessageId.NOT_ENOUGH_HP);
			}
			return false;
		}
		// Check if all casting conditions are completed
		if (!skill.checkCondition(this, target, true))
		{
			// Send a Server->Client packet ActionFailed to the PlayerInstance
			sendPacket(ActionFailed.STATIC_PACKET);
			return false;
		}
		// Check if this is bad magic skill and if PlayerInstance is in Olympiad and the match isn't already start, send a Server->Client packet ActionFailed
		if (skill.isBad() && _owner.isInOlympiadMode() && !_owner.isOlympiadStart())
		{
			sendPacket(ActionFailed.STATIC_PACKET);
			return false;
		}
		// Notify the AI with AI_INTENTION_CAST and target
		getAI().setIntention(CtrlIntention.AI_INTENTION_CAST, skill, target);
		return true;
	}
	
	@Override
	public void setImmobilized(boolean value)
	{
		super.setImmobilized(value);
		if (value)
		{
			_previousFollowStatus = _follow;
			// if immobilized temporarily disable follow mode
			if (_previousFollowStatus)
			{
				setFollowStatus(false);
			}
		}
		else
		{
			// if not more immobilized restore previous follow mode
			setFollowStatus(_previousFollowStatus);
		}
	}
	
	@Override
	public void sendDamageMessage(Creature target, Skill skill, int damage, double elementalDamage, boolean crit, boolean miss, boolean elementalCrit)
	{
		if (miss || (_owner == null))
		{
			return;
		}
		// Prevents the double spam of system messages, if the target is the owning player.
		if (target.getObjectId() != _owner.getObjectId())
		{
			if (crit)
			{
				if (isServitor())
				{
					sendPacket(SystemMessageId.SUMMONED_MONSTER_S_CRITICAL_HIT);
				}
				else
				{
					sendPacket(SystemMessageId.PET_S_CRITICAL_HIT);
				}
			}
			if (_owner.isInOlympiadMode() && target.isPlayer() && ((PlayerInstance) target).isInOlympiadMode() && (((PlayerInstance) target).getOlympiadGameId() == _owner.getOlympiadGameId()))
			{
				OlympiadGameManager.getInstance().notifyCompetitorDamage(getOwner(), damage);
			}
			final SystemMessage sm;
			if ((target.isHpBlocked() && !target.isNpc()) || (!Formulas.verifyFaceOff(this, target)))
			{
				sm = new SystemMessage(SystemMessageId.THE_ATTACK_HAS_BEEN_BLOCKED);
			}
			else
			{
				if (elementalDamage != 0)
				{
					sm = new SystemMessage(SystemMessageId.S1_HAS_DEALT_S3_DAMAGE_TO_S2_S4_ATTRIBUTE_DAMAGE);
				}
				else
				{
					sm = new SystemMessage(SystemMessageId.C1_HAS_DEALT_S3_DAMAGE_TO_C2);
				}
				sm.addNpcName(this);
				sm.addString(target.getName());
				sm.addInt(damage);
				if (elementalDamage != 0)
				{
					sm.addInt((int) elementalDamage);
				}
				sm.addPopup(target.getObjectId(), getObjectId(), (damage * -1));
			}
			sendPacket(sm);
		}
	}
	
	@Override
	public void reduceCurrentHp(double damage, Creature attacker, Skill skill)
	{
		super.reduceCurrentHp(damage, attacker, skill);
		if (!isDead() && !isHpBlocked() && (_owner != null) && (attacker != null) && (attacker != _owner) && (!Formulas.verifyFaceOff(attacker, this)))
		{
			final SystemMessage sm = new SystemMessage(SystemMessageId.C1_HAS_RECEIVED_S3_DAMAGE_FROM_C2);
			sm.addNpcName(this);
			sm.addString(attacker.getName());
			sm.addInt((int) damage);
			sm.addPopup(getObjectId(), attacker.getObjectId(), (int) -damage);
			sendPacket(sm);
		}
	}
	
	@Override
	public void doCast(Skill skill)
	{
		if ((skill.getTarget(this, false, false, false) == null) && !_owner.getAccessLevel().allowPeaceAttack())
		{
			// Send a System Message to the PlayerInstance
			_owner.sendPacket(SystemMessageId.THAT_IS_AN_INCORRECT_TARGET);
			// Send a Server->Client packet ActionFailed to the PlayerInstance
			_owner.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}
		super.doCast(skill);
	}
	
	@Override
	public boolean isInCombat()
	{
		return (_owner != null) && _owner.isInCombat();
	}
	
	@Override
	public PlayerInstance getActingPlayer()
	{
		return _owner;
	}
	
	public void updateAndBroadcastStatus(int value)
	{
		final ScheduledFuture<?> prevTask = _summonBroadcastTaskMap.get(value);
		if (prevTask != null)
		{
			prevTask.cancel(true);
		}
		_summonBroadcastTaskMap.put(value, ThreadPool.get().schedule(() -> doUpdateAndBroadcastStatus(value), BROADCAST_DELAY));
	}
	
	public void doUpdateAndBroadcastStatus(int value)
	{
		if (_owner == null || (!(this instanceof PetInstance) && !_owner.getServitors().containsKey(getObjectId())))
		{
			return;
		}
		sendPacket(new PetInfo(this, value));
		sendPacket(new PetStatusUpdate(this));
		if (isSpawned())
		{
			broadcastNpcInfo(value);
		}
		final Party party = _owner.getParty();
		if (party != null)
		{
			party.broadcastToPartyMembers(_owner, new ExPartyPetWindowUpdate(this));
		}
	}
	
	public void broadcastNpcInfo(int value)
	{
		World.getInstance().forEachVisibleObject(this, PlayerInstance.class, player ->
		{
			if ((player == _owner))
			{
				return;
			}
			player.sendPacket(new ExPetInfo(this, player, value));
		});
	}
	
	public boolean isHungry()
	{
		return false;
	}
	
	public int getWeapon()
	{
		return 0;
	}
	
	public int getArmor()
	{
		return 0;
	}
	
	@Override
	public void sendInfo(PlayerInstance player)
	{
		// Check if the PlayerInstance is the owner of the Pet
		if (player == _owner)
		{
			player.sendPacket(new PetInfo(this, isDead() ? 0 : 1));
			if (isPet())
			{
				player.sendPacket(new PetItemList(getInventory().getItems()));
				player.sendPacket(new ExPetSkillList(player.getPet().getAllSkills(), true));
			}
		}
		else
		{
			player.sendPacket(new ExPetInfo(this, player, 0));
		}
	}
	
	@Override
	public void onTeleported()
	{
		super.onTeleported();
		sendPacket(new TeleportToLocation(this, getX(), getY(), getZ(), getHeading()));
	}
	
	@Override
	public String toString()
	{
		return super.toString() + "(" + getId() + ") Owner: " + _owner;
	}
	
	@Override
	public boolean isUndead()
	{
		return getTemplate().getRace() == Race.UNDEAD;
	}
	
	/**
	 * Change the summon's state.
	 */
	public void switchMode()
	{
		// Do nothing.
	}
	
	/**
	 * Cancel the summon's action.
	 */
	public void cancelAction()
	{
		if (!isMovementDisabled())
		{
			getAI().setIntention(CtrlIntention.AI_INTENTION_ACTIVE);
		}
	}
	
	/**
	 * Performs an attack to the owner's target.
	 *
	 * @param target
	 *            the target to attack.
	 */
	public void doAttack(WorldObject target)
	{
		if ((_owner != null) && (target != null))
		{
			setTarget(target);
			getAI().setIntention(CtrlIntention.AI_INTENTION_ATTACK, target);
		}
	}
	
	/**
	 * Verify if the summon can perform an attack.
	 *
	 * @param target
	 *            the target to check if can be attacked.
	 * @param ctrlPressed
	 *            {@code true} if Ctrl key is pressed
	 * @return {@code true} if the summon can attack, {@code false} otherwise
	 */
	public boolean canAttack(WorldObject target, boolean ctrlPressed)
	{
		if (_owner == null)
		{
			return false;
		}
		if ((target == null) || (this == target) || (_owner == target))
		{
			return false;
		}
		// Sin eater, Big Boom, Wyvern can't attack with attack button.
		final int npcId = getId();
		if (CommonUtil.contains(PASSIVE_SUMMONS, npcId))
		{
			_owner.sendPacket(ActionFailed.STATIC_PACKET);
			return false;
		}
		if (isBetrayed())
		{
			sendPacket(SystemMessageId.YOUR_SERVITOR_IS_UNRESPONSIVE_AND_WILL_NOT_OBEY_ANY_ORDERS);
			sendPacket(ActionFailed.STATIC_PACKET);
			return false;
		}
		if (isPet() && ((getLevel() - _owner.getLevel()) > 20))
		{
			sendPacket(SystemMessageId.YOUR_PET_IS_TOO_HIGH_LEVEL_TO_CONTROL);
			sendPacket(ActionFailed.STATIC_PACKET);
			return false;
		}
		if (_owner.isInOlympiadMode() && !_owner.isOlympiadStart())
		{
			// If owner is in Olympiad and the match isn't already start, send a Server->Client packet ActionFailed
			_owner.sendPacket(ActionFailed.STATIC_PACKET);
			return false;
		}
		if (_owner.isSiegeFriend(target))
		{
			sendPacket(SystemMessageId.FORCE_ATTACK_IS_IMPOSSIBLE_AGAINST_A_TEMPORARY_ALLIED_MEMBER_DURING_A_SIEGE);
			sendPacket(ActionFailed.STATIC_PACKET);
			return false;
		}
		if (!_owner.getAccessLevel().allowPeaceAttack() && _owner.isInsidePeaceZone(this, target))
		{
			sendPacket(SystemMessageId.YOU_MAY_NOT_ATTACK_THIS_TARGET_IN_A_PEACEFUL_ZONE);
			return false;
		}
		if (isLockedTarget())
		{
			sendPacket(SystemMessageId.FAILED_TO_CHANGE_ENMITY);
			return false;
		}
		// Summons can attack NPCs even when the owner cannot.
		if (!target.isAutoAttackable(_owner) && !ctrlPressed && !target.isNpc())
		{
			setFollowStatus(false);
			getAI().setIntention(CtrlIntention.AI_INTENTION_FOLLOW, target);
			sendPacket(SystemMessageId.INVALID_TARGET);
			return false;
		}
		// Siege golems AI doesn't support attacking other than doors/walls at the moment.
		if (target.isDoor() && (getTemplate().getRace() != Race.SIEGE_WEAPON))
		{
			return false;
		}
		return true;
	}
	
	@Override
	public void sendPacket(ServerPacket packet)
	{
		if (_owner != null)
		{
			_owner.sendPacket(packet);
		}
	}
	
	@Override
	public void sendPacket(SystemMessageId id)
	{
		if (_owner != null)
		{
			_owner.sendPacket(id);
		}
	}
	
	@Override
	public boolean isSummon()
	{
		return true;
	}
	
	@Override
	public void rechargeShots(boolean physical, boolean magic, boolean fish)
	{
		ItemInstance item;
		IItemHandler handler;
		if ((_owner.getAutoSoulShot() == null) || _owner.getAutoSoulShot().isEmpty())
		{
			return;
		}
		for (int itemId : _owner.getAutoSoulShot())
		{
			item = _owner.getInventory().getItemByItemId(itemId);
			if (item != null)
			{
				if (magic && (item.getItem().getDefaultAction() == ActionType.SUMMON_SPIRITSHOT))
				{
					handler = ItemHandler.getInstance().getHandler(item.getEtcItem());
					if (handler != null)
					{
						handler.useItem(_owner, item, false);
					}
				}
				if (physical && (item.getItem().getDefaultAction() == ActionType.SUMMON_SOULSHOT))
				{
					handler = ItemHandler.getInstance().getHandler(item.getEtcItem());
					if (handler != null)
					{
						handler.useItem(_owner, item, false);
					}
				}
			}
			else
			{
				_owner.removeAutoSoulShot(itemId);
			}
		}
	}
	
	@Override
	public int getClanId()
	{
		return (_owner != null) ? _owner.getClanId() : 0;
	}
	
	@Override
	public int getAllyId()
	{
		return (_owner != null) ? _owner.getAllyId() : 0;
	}
	
	public int getSummonPoints()
	{
		return _summonPoints;
	}
	
	public void setSummonPoints(int summonPoints)
	{
		_summonPoints = summonPoints;
	}
	
	public void sendInventoryUpdate(InventoryUpdate iu)
	{
		final PlayerInstance owner = _owner;
		if (owner != null)
		{
			owner.sendInventoryUpdate(iu);
		}
	}
	
	public void SetEvolveLevel(int level)
	{
		_summonEvolveLevel = level;
		PetDataTable.getInstance().setPetEvolutionLevel(getControlObjectId(), level);
	}
	
	public int getEvolveLevel()
	{
		return _summonEvolveLevel;
	}
	
	@Override
	public boolean isMovementDisabled()
	{
		return super.isMovementDisabled() || !getTemplate().canMove();
	}
	
	@Override
	public boolean isTargetable()
	{
		return super.isTargetable() && getTemplate().isTargetable();
	}
	
	@Override
	public DamageList getDamageList()
	{
		return null; // Summon không cần DamageList
	}
	
	@Override
	public void addDamage(Playable attacker, long damage)
	{
		// Summon không cần lưu DamageList, nên để trống
	}
	
	@Override
	public void clearDamageAndDebuffList()
	{
		// Summon không có DamageList, nên để trống
		// Nếu có danh sách debuff, bạn có thể xóa tại đây
	}
	
	@Override
	public Map<PlayerInstance, Long> getDebuffList()
	{
		return new HashMap<>(); // Summons don't track debuffs in this context
	}
}
