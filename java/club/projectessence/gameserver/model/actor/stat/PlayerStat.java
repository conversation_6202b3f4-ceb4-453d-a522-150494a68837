/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.actor.stat;

import java.util.concurrent.atomic.AtomicInteger;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.ExperienceData;
import club.projectessence.gameserver.enums.ElementalType;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.PartySmallWindowUpdateType;
import club.projectessence.gameserver.enums.UserInfoType;
import club.projectessence.gameserver.fakeplayers.FakePlayerManager;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLevelChanged;
import club.projectessence.gameserver.model.holders.ItemSkillHolder;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.WeaponType;
import club.projectessence.gameserver.model.skills.AbnormalType;
import club.projectessence.gameserver.model.stats.Formulas;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.ConnectionState;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.AcquireSkillList;
import club.projectessence.gameserver.network.serverpackets.ExSayhaGracePointInfo;
import club.projectessence.gameserver.network.serverpackets.ExVoteSystemInfo;
import club.projectessence.gameserver.network.serverpackets.PartySmallWindowUpdate;
import club.projectessence.gameserver.network.serverpackets.PledgeShowMemberListUpdate;
import club.projectessence.gameserver.network.serverpackets.SocialAction;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.UserInfo;
import club.projectessence.gameserver.network.serverpackets.dailymission.ExOneDayReceiveRewardList;
import club.projectessence.gameserver.network.serverpackets.friend.FriendStatus;
import club.projectessence.gameserver.instancemanager.ZoneDominanceManager;

import java.util.logging.Logger;

public class PlayerStat extends PlayableStat
{
	private static final Logger	LOGGER					= Logger.getLogger(PlayerStat.class.getName());
	public static final int		MAX_SAYHA_GRACE_POINTS	= 3500000;
	public static final int		MIN_SAYHA_GRACE_POINTS	= 0;
	private static final int	FANCY_FISHING_ROD_SKILL	= 21484;
	/**
	 * Player's maximum talisman count.
	 */
	private final AtomicInteger	_talismanSlots			= new AtomicInteger();
	private boolean				_cloakSlot				= false;
	private int					_sayhaGracePoints		= 0;
	
	public PlayerStat(PlayerInstance player)
	{
		super(player);
	}
	
	@Override
	public boolean addExp(long value)
	{
		final PlayerInstance player = getActiveChar();
		// Allowed to gain exp?
		if (!player.getAccessLevel().canGainExp())
		{
			return false;
		}
		if (!super.addExp(value))
		{
			return false;
		}
		// Set new karma
		if (!player.isCursedWeaponEquipped() && (player.getReputation() < 0) && !player.isInsideZone(ZoneId.NO_PVP) && !player.isInsideZone(ZoneId.PVP))
		{
			final int karmaLost = Formulas.calculateKarmaLost(player, value);
			final int finalKarmaLost = ((karmaLost * 30) / 100);
			if (finalKarmaLost > 0)
			{
				player.setReputation(Math.min((player.getReputation() + (karmaLost + finalKarmaLost)), 0));
			}
		}
		// EXP status update currently not used in retail
		// player.sendPacket(new UserInfo(player));
		return true;
	}
	
	public void addExpAndSp(long addToExpValue, long addToSpValue, boolean useBonuses)
	{
		final PlayerInstance player = getActiveChar();
		// Allowed to gain exp/sp?
		if (!player.getAccessLevel().canGainExp())
		{
			return;
		}
		long addToExp = addToExpValue;
		long addToSp = addToSpValue;
		// Premium rates (individual or shared)
		if (useBonuses && PremiumManager.getInstance().hasAnyPremiumBenefits(player))
		{
			addToExp *= PremiumManager.getInstance().getEffectivePremiumRate(player, "EXP_RATE");
			addToSp *= PremiumManager.getInstance().getEffectivePremiumRate(player, "SP_RATE");
		}
		final long baseExp = addToExp;
		final long baseSp = addToSp;
		double bonusExp = 1.;
		double bonusSp = 1.;
		if (useBonuses)
		{
			if (player.isFishing())
			{
				// rod fishing skills
				final ItemInstance rod = player.getActiveWeaponInstance();
				if ((rod != null) && (rod.getItemType() == WeaponType.FISHINGROD) && (rod.getItem().getAllSkills() != null))
				{
					for (ItemSkillHolder s : rod.getItem().getAllSkills())
					{
						if (s.getSkill().getId() == FANCY_FISHING_ROD_SKILL)
						{
							bonusExp *= 1.5;
							bonusSp *= 1.5;
						}
					}
				}
			}
			else
			{
				bonusExp = getExpBonusMultiplier();
				bonusSp = getSpBonusMultiplier();

				// Apply Zone Dominance EXP/SP bonus
				if (Config.ENABLE_ZONE_DOMINANCE_SYSTEM && player.getFaction() != null && player.getFaction() != Faction.NONE)
				{
					int expSpBonus = ZoneDominanceManager.getInstance().getExpSpBonus(player.getFaction());
					if (expSpBonus > 0)
					{
						double dominanceBonus = 1.0 + (expSpBonus / 100.0);
						bonusExp *= dominanceBonus;
						bonusSp *= dominanceBonus;
						if (Config.LOG_FACTION_DETAILS)
						{
							LOGGER.fine("Zone Dominance EXP/SP bonus applied: +" + expSpBonus + "%");
						}
					}
				}
			}
		}
		addToExp *= bonusExp;
		addToSp *= bonusSp;
		/**
		 * Pet shouldn't earn Xp from master but by its own behaviour and stat.
		 * 
		 * @see club.projectessence.gameserver.model.actor.Attackable @line 403
		 */
		/*
		 * double ratioTakenByPlayer = 0; // if this player has a pet and it is in his range he takes from the owner's Exp, give the pet Exp now final Summon sPet = player.getPet(); if ((sPet != null) && Util.checkIfInShortRange(Config.ALT_PARTY_RANGE, player, sPet, false)) { final PetInstance pet =
		 * (PetInstance) sPet; ratioTakenByPlayer = pet.getPetLevelData().getOwnerExpTaken() / 100f; // only give exp/sp to the pet by taking from the owner if the pet has a non-zero, positive ratio // allow possible customizations that would have the pet earning more than 100% of the owner's exp/sp
		 * if (ratioTakenByPlayer > 1) { ratioTakenByPlayer = 1; } if (!pet.isDead()) { pet.addExpAndSp((long) (addToExp * (1 - ratioTakenByPlayer)), (long) (addToSp * (1 - ratioTakenByPlayer))); } // now adjust the max ratio to avoid the owner earning negative exp/sp addToExp *= ratioTakenByPlayer;
		 * addToSp *= ratioTakenByPlayer; }
		 */
		final long finalExp = addToExp;
		final long finalSp = addToSp;
		int levelBefore = getLevel();
		final boolean expAdded = addExp(finalExp);
		final boolean spAdded = addSp(finalSp);
		if (levelBefore != getLevel())
		{
			player.sendPacket(new UserInfo(player));
		}
		else
		{
			// getActiveChar().broadcastUserInfo(UserInfoType.CURRENT_HPMPCP_EXP_SP);
			final UserInfo ui = new UserInfo(player, false);
			ui.addComponentType(UserInfoType.CURRENT_HPMPCP_EXP_SP);
			player.sendPacket(ui);
		}
		SystemMessage sm = null;
		if (!expAdded && spAdded)
		{
			sm = new SystemMessage(SystemMessageId.YOU_HAVE_ACQUIRED_S1_SP);
			sm.addLong(finalSp);
		}
		else if (expAdded && !spAdded)
		{
			sm = new SystemMessage(SystemMessageId.YOU_HAVE_ACQUIRED_S1_XP);
			sm.addLong(finalExp);
			player.addMagicLampExp(finalExp);
		}
		else
		{
			sm = new SystemMessage(SystemMessageId.YOU_HAVE_ACQUIRED_S1_XP_BONUS_S2_AND_S3_SP_BONUS_S4);
			sm.addLong(finalExp);
			sm.addLong(addToExp - baseExp);
			sm.addLong(finalSp);
			sm.addLong(addToSp - baseSp);
			player.addMagicLampExp(finalExp);
		}
		player.sendPacket(sm);
	}
	
	public void addExpAndSpStatic(long addToExpValue, long addToSpValue)
	{
		super.addExp(addToExpValue);
		super.addSp(addToSpValue);
		SystemMessage sm = new SystemMessage(SystemMessageId.YOU_HAVE_ACQUIRED_S1_XP_BONUS_S2_AND_S3_SP_BONUS_S4);
		sm.addLong(addToExpValue);
		sm.addLong(0);
		sm.addLong(addToSpValue);
		sm.addLong(0);
		getActiveChar().sendPacket(sm);
		getActiveChar().broadcastUserInfo(UserInfoType.CURRENT_HPMPCP_EXP_SP);
	}
	
	public void addExpStatic(long addToExpValue)
	{
		super.addExp(addToExpValue);
		SystemMessage sm = new SystemMessage(SystemMessageId.YOU_HAVE_ACQUIRED_S1_XP);
		sm.addLong(addToExpValue);
		getActiveChar().sendPacket(sm);
		getActiveChar().broadcastUserInfo(UserInfoType.CURRENT_HPMPCP_EXP_SP);
	}
	
	public void addExpAfterPaidRes(long addToExpValue, int percent)
	{
		super.addExp(addToExpValue);
		SystemMessage sm = new SystemMessage(SystemMessageId.S1_S2_XP_HAS_BEEN_RESTORED_REMAINING_FREE_RESURRECTIONS_S3);
		sm.addLong(addToExpValue);
		sm.addInt(percent);
		sm.addInt(Math.max(0, getActiveChar().getVariables().getInt(PlayerVariables.RESURRECTION_COUNT_FREE, 0)));
		getActiveChar().sendPacket(sm);
	}
	
	@Override
	public boolean removeExpAndSp(long addToExp, long addToSp)
	{
		return removeExpAndSp(addToExp, addToSp, true);
	}
	
	public boolean removeExpAndSp(long addToExp, long addToSp, boolean sendMessage)
	{
		final int level = getLevel();
		if (!super.removeExpAndSp(addToExp, addToSp))
		{
			return false;
		}
		if (sendMessage)
		{
			// Send a Server->Client System Message to the PlayerInstance
			SystemMessage sm = new SystemMessage(SystemMessageId.YOUR_XP_HAS_DECREASED_BY_S1);
			sm.addLong(addToExp);
			getActiveChar().sendPacket(sm);
			sm = new SystemMessage(SystemMessageId.YOUR_SP_HAS_DECREASED_BY_S1);
			sm.addLong(addToSp);
			getActiveChar().sendPacket(sm);
			if (getLevel() < level)
			{
				getActiveChar().broadcastStatusUpdate();
			}
		}
		return true;
	}
	
	@Override
	public boolean addLevel(int value)
	{
		int oldLv = getLevel();
		int newLv = oldLv + value;
		if (newLv > (ExperienceData.getInstance().getMaxLevel() - 1))
		{
			return false;
		}
		final boolean levelIncreased = super.addLevel(value);
		if (levelIncreased)
		{
			getActiveChar().setCurrentCp(getMaxCp());
			getActiveChar().broadcastPacket(new SocialAction(getActiveChar().getObjectId(), SocialAction.LEVEL_UP));
			getActiveChar().sendPacket(SystemMessageId.YOUR_LEVEL_HAS_INCREASED);
			getActiveChar().notifyFriends(FriendStatus.MODE_LEVEL);
		}
		// Notify to scripts
		EventDispatcher.getInstance().notifyEventAsync(new OnPlayerLevelChanged(getActiveChar(), getLevel() - value, getLevel()), getActiveChar());
		if (getActiveChar().isFakePlayer())
		{
			FakePlayerManager.getInstance().onLevelUp(getActiveChar(), oldLv, newLv);
		}
		// Give AutoGet skills and all normal skills if Auto-Learn is activated.
		getActiveChar().rewardSkills();
		if (getActiveChar().getClan() != null)
		{
			getActiveChar().getClan().updateClanMember(getActiveChar());
			getActiveChar().getClan().broadcastToOnlineMembers(new PledgeShowMemberListUpdate(getActiveChar()));
		}
		if (getActiveChar().isInParty())
		{
			getActiveChar().getParty().recalculatePartyLevel(); // Recalculate the party level
		}
		// Maybe add some skills when player levels up in transformation.
		getActiveChar().getTransformation().ifPresent(transform -> transform.onLevelUp(getActiveChar()));
		// Synchronize level with pet if possible.
		/*
		 * final Summon sPet = getActiveChar().getPet(); if (sPet != null) { final PetInstance pet = (PetInstance) sPet; if (pet.getPetData().isSynchLevel() && (pet.getLevel() != getLevel())) { final int availableLevel = Math.min(pet.getPetData().getMaxLevel(), getLevel());
		 * pet.getStat().setLevel(availableLevel); pet.getStat().getExpForLevel(availableLevel); pet.setCurrentHp(pet.getMaxHp()); pet.setCurrentMp(pet.getMaxMp()); pet.broadcastPacket(new SocialAction(getActiveChar().getObjectId(), SocialAction.LEVEL_UP)); pet.updateAndBroadcastStatus(1); } }
		 */
		getActiveChar().broadcastStatusUpdate();
		// Update the overloaded status of the PlayerInstance
		getActiveChar().refreshOverloaded(true);
		// Send a Server->Client packet UserInfo to the PlayerInstance
		getActiveChar().sendPacket(new UserInfo(getActiveChar()));
		// Send acquirable skill list
		getActiveChar().sendPacket(new AcquireSkillList(getActiveChar()));
		getActiveChar().sendPacket(new ExVoteSystemInfo(getActiveChar()));
		getActiveChar().sendPacket(new ExOneDayReceiveRewardList(getActiveChar(), true));
		getActiveChar().getVariables().set(PlayerVariables.MAX_LEVEL, newLv);
		getActiveChar().calcLevelReward();
		// Start Bonus
		// if (getActiveChar().getFirstCharCreateDate().getTimeInMillis() >= 1651006800000L) // 2022.04.27
		// {
		// if (newLv > oldLv) {
		// if (newLv >= 40) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_40, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 91767, 30, getActiveChar(), true); // Aden Talisman
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_40, "Y");
		// }
		// }
		// if (newLv >= 61) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_61, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 93303, 35, getActiveChar(), true); // Cloak of Protection
		// getActiveChar().addItem("Start Bonus", 92035, 150, getActiveChar(), true); // Shiny Jewelry Box
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_61, "Y");
		// }
		// }
		// if (newLv >= 70) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_70, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 91864, 40, getActiveChar(), true); // Dragon Belt Pack
		// getActiveChar().addItem("Start Bonus", 93330, 30, getActiveChar(), true); // Talisman of Authority
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_70, "Y");
		// }
		// }
		// if (newLv >= 75) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_75, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 94258, 40, getActiveChar(), true); // Einhasad Pendant
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_75, "Y");
		// }
		// }
		// if (newLv >= 78) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_78, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 97145, 1000, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_78, "Y");
		// }
		// }
		// if (newLv >= 80) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_80, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 92020, 40, getActiveChar(), true); // Talisman of Eva
		// getActiveChar().addItem("Start Bonus", 93685, 40, getActiveChar(), true); // Speed Talisman
		// getActiveChar().addItem("Start Bonus", 97145, 1000, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_80, "Y");
		// }
		// }
		// if (newLv >= 82) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_82, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 96708, 10, getActiveChar(), true); // Beryl Lv. 1
		// getActiveChar().addItem("Start Bonus", 97145, 1000, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_82, "Y");
		// }
		// }
		// if (newLv >= 84) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_84, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 94168, 30, getActiveChar(), true); // Circlet of Hero
		// getActiveChar().addItem("Start Bonus", 97145, 1000, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_84, "Y");
		// }
		// }
		// if (newLv >= 85) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_85, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 94706, 1, getActiveChar(), true); // Talisman of Hellbound Lv. 1
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_85, "Y");
		// }
		// }
		// if (newLv >= 86) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_86, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 91088, 1, getActiveChar(), true); // Venir Lv. 12
		// getActiveChar().addItem("Start Bonus", 97145, 1000, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_86, "Y");
		// }
		// }
		// if (newLv >= 87) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_87, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 97145, 2500, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_87, "Y");
		// }
		// }
		// if (newLv >= 88) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_88, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 97145, 3000, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_88, "Y");
		// }
		// }
		// if (newLv >= 89) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_89, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 97145, 3500, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_89, "Y");
		// }
		// }
		// if (newLv >= 90) {
		// if (getActiveChar().getAccountVariables().getString(AccountVariables.START_BONUS_90, "N").equals("N")) {
		// getActiveChar().addItem("Start Bonus", 97145, 4000, getActiveChar(), true); // Ancient Adena
		// getActiveChar().getAccountVariables().set(AccountVariables.START_BONUS_90, "Y");
		// }
		// }
		// }
		// }
		return levelIncreased;
	}
	
	@Override
	public boolean addSp(long value)
	{
		if (!super.addSp(value))
		{
			return false;
		}
		// getActiveChar().broadcastUserInfo(UserInfoType.CURRENT_HPMPCP_EXP_SP);
		return true;
	}
	
	@Override
	public long getExpForLevel(int level)
	{
		return ExperienceData.getInstance().getExpForLevel(level);
	}
	
	@Override
	public PlayerInstance getActiveChar()
	{
		return (PlayerInstance) super.getActiveChar();
	}
	
	@Override
	public long getExp()
	{
		if (getActiveChar().isSubClassActive())
		{
			return getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).getExp();
		}
		return super.getExp();
	}
	
	@Override
	public void setExp(long value)
	{
		if (getActiveChar().isSubClassActive())
		{
			getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).setExp(value);
		}
		else
		{
			super.setExp(value);
		}
	}
	
	public long getBaseExp()
	{
		return super.getExp();
	}
	
	/**
	 * Gets the maximum talisman count.
	 *
	 * @return the maximum talisman count
	 */
	public int getTalismanSlots()
	{
		return _talismanSlots.get();
	}
	
	public void addTalismanSlots(int count)
	{
		_talismanSlots.addAndGet(count);
	}
	
	public boolean canEquipCloak()
	{
		return _cloakSlot;
	}
	
	public void setCloakSlotStatus(boolean cloakSlot)
	{
		_cloakSlot = cloakSlot;
	}
	
	@Override
	public int getLevel()
	{
		if (getActiveChar().isDualClassActive())
		{
			return getActiveChar().getDualClass().getLevel();
		}
		if (getActiveChar().isSubClassActive())
		{
			return getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).getLevel();
		}
		return super.getLevel();
	}
	
	@Override
	public void setLevel(int value)
	{
		int level = value;
		if (level > (ExperienceData.getInstance().getMaxLevel() - 1))
		{
			level = ExperienceData.getInstance().getMaxLevel() - 1;
		}
		if (getActiveChar().isSubClassActive())
		{
			getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).setLevel(level);
		}
		else
		{
			super.setLevel(level);
		}
	}
	
	public int getBaseLevel()
	{
		return super.getLevel();
	}
	
	@Override
	public long getSp()
	{
		if (getActiveChar().isSubClassActive())
		{
			return getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).getSp();
		}
		return super.getSp();
	}
	
	@Override
	public void setSp(long value)
	{
		if (getActiveChar().isSubClassActive())
		{
			getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).setSp(value);
		}
		else
		{
			super.setSp(value);
		}
	}
	
	public long getBaseSp()
	{
		return super.getSp();
	}
	
	/*
	 * Return current Sayha's Grace points in integer format
	 */
	public int getSayhaGracePoints()
	{
		if (getActiveChar().isSubClassActive())
		{
			return Math.min(MAX_SAYHA_GRACE_POINTS, getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).getSayhaGracePoints());
		}
		return Math.min(Math.max(_sayhaGracePoints, MIN_SAYHA_GRACE_POINTS), MAX_SAYHA_GRACE_POINTS);
	}
	
	public void setSayhaGracePoints(int value)
	{
		if (getActiveChar().isSubClassActive())
		{
			getActiveChar().getSubClasses().get(getActiveChar().getClassIndex()).setSayhaGracePoints(value);
			return;
		}
		_sayhaGracePoints = Math.min(Math.max(value, MIN_SAYHA_GRACE_POINTS), MAX_SAYHA_GRACE_POINTS);
		getActiveChar().sendExpBoostInfo(true);
	}
	
	public int getBaseSayhaGracePoints()
	{
		return Math.min(Math.max(_sayhaGracePoints, MIN_SAYHA_GRACE_POINTS), MAX_SAYHA_GRACE_POINTS);
	}
	
	public double getSayhaGraceExpBonus()
	{
		double bonus = (getSayhaGracePoints() > 0) ? getValue(Stat.SAYHA_GRACE_EXP_RATE, Config.RATE_SAYHA_GRACE_EXP_MULTIPLIER) : 1.0;
		if ((bonus > 1) && PremiumManager.getInstance().hasAnyPremiumBenefits(getActiveChar()))
		{
			float premiumSayhaBonus = PremiumManager.getInstance().getEffectivePremiumRate(getActiveChar(), "SAYHA_GRACE_XP");
			bonus += (premiumSayhaBonus - 1.0f);
		}
		if (bonus == 1)
		{
			if (getActiveChar().getLimitedSayhaGraceEndTime() > System.currentTimeMillis())
			{
				return getLimitedSayhaGraceExpBonus();
			}
		}
		// Update for newbie level 52 to 81 get 300% sayha grace
		if (getActiveChar().getLevel() >= 52 && getActiveChar().getLevel() <= 81)
		{
			bonus += 3.0;
		}
		getActiveChar().setLastKnownSayhaExpBonus((int) (bonus * 100));
		return bonus;
	}
	
	public double getLimitedSayhaGraceExpBonus()
	{
		double bonus = Config.RATE_LIMITED_SAYHA_GRACE_EXP_MULTIPLIER;
		if (PremiumManager.getInstance().hasAnyPremiumBenefits(getActiveChar()))
		{
			float premiumLimitedSayhaBonus = PremiumManager.getInstance().getEffectivePremiumRate(getActiveChar(), "LIMITED_SAYHA_GRACE_XP");
			bonus += (premiumLimitedSayhaBonus - 1.0f);
		}
		return bonus;
	}
	
	/*
	 * Set current Sayha's Grace points to this value if quiet = true - does not send system messages
	 */
	public void setSayhaGracePoints(int value, boolean quiet)
	{
		final int points = Math.min(Math.max(value, MIN_SAYHA_GRACE_POINTS), MAX_SAYHA_GRACE_POINTS);
		if (points == getSayhaGracePoints())
		{
			return;
		}
		if (!quiet)
		{
			if (points < getSayhaGracePoints())
			{
				getActiveChar().sendPacket(SystemMessageId.YOUR_SAYHA_S_GRACE_HAS_DECREASED);
			}
			else
			{
				getActiveChar().sendPacket(SystemMessageId.YOUR_SAYHA_S_GRACE_HAS_INCREASED);
			}
		}
		setSayhaGracePoints(points);
		if (points == 0)
		{
			getActiveChar().sendPacket(SystemMessageId.YOUR_SAYHA_S_GRACE_IS_FULLY_EXHAUSTED);
		}
		else if (points == MAX_SAYHA_GRACE_POINTS)
		{
			getActiveChar().sendPacket(SystemMessageId.YOUR_SAYHA_S_GRACE_IS_AT_MAXIMUM);
		}
		final PlayerInstance player = getActiveChar();
		if ((player.getClient() != null) && (player.getClient().getConnectionState() == ConnectionState.IN_GAME))
		{
			player.sendPacket(new ExSayhaGracePointInfo(getSayhaGracePoints()));
			player.broadcastUserInfo(UserInfoType.VITA_FAME);
			final Party party = player.getParty();
			if (party != null)
			{
				final PartySmallWindowUpdate partyWindow = new PartySmallWindowUpdate(player, false);
				partyWindow.addComponentType(PartySmallWindowUpdateType.SAYHA_GRACE_POINTS);
				party.broadcastToPartyMembers(player, partyWindow);
			}
		}
	}
	
	public void addSayhaGracePoints(int value, boolean quiet)
	{
		setSayhaGracePoints(getSayhaGracePoints() + value, quiet);
	}
	
	public synchronized void updateSayhaGracePoints(int value, boolean useRates, boolean quiet)
	{
		if ((value == 0) || !Config.ENABLE_SAYHA_GRACE)
		{
			return;
		}
		int points = value;
		if (useRates)
		{
			if (getActiveChar().isLucky())
			{
				return;
			}
			if (points < 0) // Sayha's Grace consumed
			{
				double consumeRate = getValue(Stat.SAYHA_GRACE_CONSUME_RATE, 1);
				if (consumeRate <= 0)
				{
					return;
				}
				points *= consumeRate;
			}
			if (points > 0)
			{
				// Sayha's Grace increased
				points *= Config.RATE_SAYHA_GRACE_GAIN;
			}
			else
			{
				// Sayha's Grace decreased
				final PlayerInstance player = getActiveChar();
				if (player.isKamael())
				{
					points *= Config.RATE_SAYHA_GRACE_LOST_KAMAEL_TRANSFORM;
				}
				else
				{
					points *= Config.RATE_SAYHA_GRACE_LOST;
				}
			}
		}
		if (points > 0)
		{
			points = Math.min(getSayhaGracePoints() + points, MAX_SAYHA_GRACE_POINTS);
		}
		else
		{
			points = Math.max(getSayhaGracePoints() + points, MIN_SAYHA_GRACE_POINTS);
		}
		if (Math.abs(points - getSayhaGracePoints()) <= 1e-6)
		{
			return;
		}
		setSayhaGracePoints(points);
	}
	
	public double getExpBonusMultiplier()
	{
		double bonus = 1.0;
		double sayhaGrace = 1.0;
		double bonusExp = 1.0;
		// Bonus from Sayha's Grace System
		sayhaGrace = getSayhaGraceExpBonus();
		// Bonus exp from skills
		bonusExp = 1 + (getValue(Stat.BONUS_EXP, 0) / 100);
		if (sayhaGrace > 1.0)
		{
			bonus += (sayhaGrace - 1);
		}
		if (bonusExp > 1)
		{
			bonus += (bonusExp - 1);
		}
		// Check for abnormal bonuses
		bonus = Math.max(bonus, 1);
		if (Config.MAX_BONUS_EXP > 0)
		{
			bonus = Math.min(bonus, Config.MAX_BONUS_EXP);
		}
		return bonus;
	}
	
	public double getSpBonusMultiplier()
	{
		double bonus = 1.0;
		double sayhaGrace = 1.0;
		double bonusSp = 1.0;
		// Bonus from Sayha's Grace System
		sayhaGrace = getSayhaGraceExpBonus();
		// Bonus sp from skills
		bonusSp = 1 + (getValue(Stat.BONUS_SP, 0) / 100);
		if (sayhaGrace > 1.0)
		{
			bonus += (sayhaGrace - 1);
		}
		if (bonusSp > 1)
		{
			bonus += (bonusSp - 1);
		}
		// Check for abnormal bonuses
		bonus = Math.max(bonus, 1);
		if (Config.MAX_BONUS_SP > 0)
		{
			bonus = Math.min(bonus, Config.MAX_BONUS_SP);
		}
		return bonus;
	}
	
	/**
	 * Gets the maximum brooch jewel count.
	 *
	 * @return the maximum brooch jewel count
	 */
	public int getBroochJewelSlots()
	{
		return (int) getValue(Stat.BROOCH_JEWELS, 0);
	}
	
	/**
	 * Gets the maximum agathion count.
	 *
	 * @return the maximum agathion count
	 */
	public int getAgathionSlots()
	{
		return (int) getValue(Stat.AGATHION_SLOTS, 0);
	}
	
	/**
	 * Gets the maximum artifact book count.
	 *
	 * @return the maximum artifact book count
	 */
	public int getArtifactSlots()
	{
		return (int) getValue(Stat.ARTIFACT_SLOTS, 0);
	}
	
	public double getMagicLampXpBonus()
	{
		return getValue(Stat.MAGIC_LAMP_BONUS_EXP, 1);
	}
	
	public double getElementalSpiritXpBonus()
	{
		return getValue(Stat.ELEMENTAL_SPIRIT_BONUS_EXP, 1);
	}
	
	public double getElementalSpiritPower(ElementalType type, double base)
	{
		return type == null ? 0 : getValue(type.getAttackStat(), base);
	}
	
	public double getElementalSpiritCriticalRate(int base)
	{
		return getValue(Stat.ELEMENTAL_SPIRIT_CRITICAL_RATE, base);
	}
	
	public double getElementalSpiritCriticalDamage(double base)
	{
		return getValue(Stat.ELEMENTAL_SPIRIT_CRITICAL_DAMAGE, base);
	}
	
	public double getElementalSpiritDefense(ElementalType type, double base)
	{
		return type == null ? 0 : getValue(type.getDefenseStat(), base);
	}
	
	@Override
	protected void onRecalculateStatsStart(boolean broadcast)
	{
		super.onRecalculateStatsStart(broadcast);
		final PlayerInstance player = getActiveChar();
		player.setExpPassivesVisualCount(0);
		player.setExpBuffsVisualCount(0);
		player.setExpPassivesVisualBonus(0);
		player.setExpBuffsVisualBonus(0);
		player.setExpSayhaVisualCount(0);
	}
	
	@Override
	protected void onRecalculateStatsEnd(boolean broadcast)
	{
		super.onRecalculateStatsEnd(broadcast);
		final PlayerInstance player = getActiveChar();
		if (player.hasAbnormalType(AbnormalType.ABILITY_CHANGE) && player.hasServitors())
		{
			player.getServitors().values().forEach(servitor -> servitor.getStat().recalculateStats(broadcast));
		}
		player.sendExpBoostInfo(true);
		player.sendUserViewInfoParameter();
	}
}
