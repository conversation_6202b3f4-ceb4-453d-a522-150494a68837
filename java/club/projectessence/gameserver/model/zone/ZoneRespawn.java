/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.zone;

import club.projectessence.Config;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.Location;

import java.util.ArrayList;
import java.util.List;

/**
 * Abstract zone with spawn locations
 *
 * <AUTHOR> <PERSON>yar<PERSON> (rework 10/07/2011)
 */
public abstract class ZoneRespawn extends ZoneType {
	private List<Location> _spawnLocs = null;
	private List<Location> _otherSpawnLocs = null;
	private List<Location> _chaoticSpawnLocs = null;
	private List<Location> _banishSpawnLocs = null;

	protected ZoneRespawn(int id) {
		super(id);
	}

	public void parseLoc(int x, int y, int z, String type) {
		if ((type == null) || type.isEmpty()) {
			addSpawn(x, y, z);
		} else {
			switch (type) {
				case "other": {
					addOtherSpawn(x, y, z);
					break;
				}
				case "chaotic": {
					addChaoticSpawn(x, y, z);
					break;
				}
				case "banish": {
					addBanishSpawn(x, y, z);
					break;
				}
				default: {
					LOGGER.warning(getClass().getSimpleName() + ": Unknown location type: " + type);
				}
			}
		}
	}

	public void addSpawn(int x, int y, int z) {
		if (_spawnLocs == null) {
			_spawnLocs = new ArrayList<>();
		}

		_spawnLocs.add(new Location(x, y, z));
	}

	public void addOtherSpawn(int x, int y, int z) {
		if (_otherSpawnLocs == null) {
			_otherSpawnLocs = new ArrayList<>();
		}

		_otherSpawnLocs.add(new Location(x, y, z));
	}

	public void addChaoticSpawn(int x, int y, int z) {
		if (_chaoticSpawnLocs == null) {
			_chaoticSpawnLocs = new ArrayList<>();
		}

		_chaoticSpawnLocs.add(new Location(x, y, z));
	}

	public void addBanishSpawn(int x, int y, int z) {
		if (_banishSpawnLocs == null) {
			_banishSpawnLocs = new ArrayList<>();
		}

		_banishSpawnLocs.add(new Location(x, y, z));
	}

	public List<Location> getSpawns() {
		return _spawnLocs;
	}

	public Location getSpawnLoc() {
		if (Config.RANDOM_RESPAWN_IN_TOWN_ENABLED) {
			return _spawnLocs.get(Rnd.get(_spawnLocs.size()));
		}
		return _spawnLocs.get(0);
	}

	public Location getOtherSpawnLoc() {
		if (_otherSpawnLocs != null) {
			if (Config.RANDOM_RESPAWN_IN_TOWN_ENABLED) {
				return _otherSpawnLocs.get(Rnd.get(_otherSpawnLocs.size()));
			}
			return _otherSpawnLocs.get(0);
		}
		return getSpawnLoc();
	}

	public Location getChaoticSpawnLoc() {
		if (_chaoticSpawnLocs != null) {
			if (Config.RANDOM_RESPAWN_IN_TOWN_ENABLED) {
				return _chaoticSpawnLocs.get(Rnd.get(_chaoticSpawnLocs.size()));
			}
			return _chaoticSpawnLocs.get(0);
		}
		return getSpawnLoc();
	}

	public Location getBanishSpawnLoc() {
		if (_banishSpawnLocs != null) {
			if (Config.RANDOM_RESPAWN_IN_TOWN_ENABLED) {
				return _banishSpawnLocs.get(Rnd.get(_banishSpawnLocs.size()));
			}
			return _banishSpawnLocs.get(0);
		}
		return getSpawnLoc();
	}
}
