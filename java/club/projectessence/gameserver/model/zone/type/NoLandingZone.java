/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.zone.type;

import club.projectessence.gameserver.enums.MountType;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.model.zone.ZoneType;
import club.projectessence.gameserver.network.SystemMessageId;

/**
 * A no landing zone
 *
 * <AUTHOR>
 */
public class NoLandingZone extends ZoneType {
	private int dismountDelay = 5;

	public NoLandingZone(int id) {
		super(id);
	}

	@Override
	public void setParameter(String name, String value) {
		if (name.equals("dismountDelay")) {
			dismountDelay = Integer.parseInt(value);
		} else {
			super.setParameter(name, value);
		}
	}

	@Override
	protected void onEnter(Creature creature) {
		if (creature.isPlayer()) {
			creature.setInsideZone(ZoneId.NO_LANDING, true);
			if (creature.getActingPlayer().getMountType() == MountType.WYVERN) {
				creature.sendPacket(SystemMessageId.THIS_AREA_CANNOT_BE_ENTERED_WHILE_MOUNTED_ATOP_OF_A_WYVERN_YOU_WILL_BE_DISMOUNTED_FROM_YOUR_WYVERN_IF_YOU_DO_NOT_LEAVE);
				creature.getActingPlayer().enteredNoLanding(dismountDelay);
			}
		}
	}

	@Override
	protected void onExit(Creature creature) {
		if (creature.isPlayer()) {
			creature.setInsideZone(ZoneId.NO_LANDING, false);
			if (creature.getActingPlayer().getMountType() == MountType.WYVERN) {
				creature.getActingPlayer().exitedNoLanding();
			}
		}
	}
}
