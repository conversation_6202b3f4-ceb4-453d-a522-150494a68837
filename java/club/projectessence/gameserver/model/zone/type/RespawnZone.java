/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.zone.type;

import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.zone.ZoneRespawn;

import java.util.ArrayList;
import java.util.List;

/**
 * Respawn zone implementation.
 *
 * <AUTHOR>
 */
public class RespawnZone extends ZoneRespawn {
	private final List<String> _respawnPoints = new ArrayList<>();

	public RespawnZone(int id) {
		super(id);
	}

	@Override
	protected void onEnter(Creature creature) {
	}

	@Override
	protected void onExit(Creature creature) {
	}

	public void addRespawnPoint(String point) {
		_respawnPoints.add(point);
	}

	public List<String> getAllRespawnPoints() {
		return _respawnPoints;
	}

	public String getRespawnPoint() {
		return _respawnPoints.get(Rnd.get(_respawnPoints.size()));
	}
}
