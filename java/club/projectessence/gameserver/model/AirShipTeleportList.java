/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model;

/**
 * Holds a list of all AirShip teleports.
 *
 * <AUTHOR>
 */
public class AirShipTeleportList {
	private final int _location;
	private final int[] _fuel;
	private final VehiclePathPoint[][] _routes;

	public AirShipTeleportList(int loc, int[] f, VehiclePathPoint[][] r) {
		_location = loc;
		_fuel = f;
		_routes = r;
	}

	public int getLocation() {
		return _location;
	}

	public int[] getFuel() {
		return _fuel;
	}

	public VehiclePathPoint[][] getRoute() {
		return _routes;
	}
}
