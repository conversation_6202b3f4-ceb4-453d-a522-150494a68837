# 🤖 Telegram Bot Integration for GVE Prime Points Management

## 📋 Tổng quan

Giải pháp tích hợp Telegram Bot cho phép admin quản lý Prime Points từ xa thông qua Telegram mà không cần đăng nhập vào game. Hệ thống hỗ trợ cập nhật real-time cho người chơi đang online và lưu trữ an toàn cho người chơi offline.

## 🎯 Tính năng chính

### ✅ Chức năng cốt lõi
- **Thêm Prime Points theo tên nhân vật**: `/addprime PlayerName 1000`
- **Thêm Prime Points theo account**: `/addprime_acc account123 5000`
- **Cập nhật real-time**: Người chơi online nhận ngay lập tức
- **Lưu trữ offline**: Người chơi offline sẽ thấy khi đăng nhập lại
- **X<PERSON><PERSON> thực admin**: Chỉ admin được phép sử dụng
- **Rate limiting**: Tránh spam với cooldown 5 giây
- **Audit logging**: <PERSON>hi lại tất cả hoạt động

### 🔒 Bảo mật
- Xác thực nghiêm ngặt qua Chat ID hoặc Username
- HTTPS bắt buộc cho webhook
- Rate limiting và giới hạn số points
- Logging đầy đủ cho audit trail
- Firewall protection cho webhook endpoint

## 📁 Cấu trúc Files

```
java/club/projectessence/gameserver/telegram/
├── TelegramBotManager.java      # Core bot logic
├── TelegramWebhookHandler.java  # Webhook server
├── TelegramConfig.java          # Configuration manager
├── TelegramService.java         # Service lifecycle
└── TelegramIntegration.java     # Server integration

dist/game/config/
└── telegram.properties          # Configuration file

dist/game/data/scripts/handlers/admincommandhandlers/
└── AdminTelegram.java           # In-game admin commands

docs/
└── telegram-bot-setup.md        # Detailed setup guide

scripts/
└── setup-telegram-webhook.sh    # Automated setup script
```

## 🚀 Cài đặt nhanh

### 1. Tạo Telegram Bot
```bash
# 1. Message @BotFather on Telegram
# 2. Send: /newbot
# 3. Follow instructions
# 4. Save the bot token
```

### 2. Cấu hình Server
```bash
# Edit config file
nano dist/game/config/telegram.properties

# Set these values:
TelegramBotEnabled = true
BotToken = YOUR_BOT_TOKEN_HERE
WebhookUrl = https://yourdomain.com/telegram-webhook
AdminChatIds = YOUR_CHAT_ID_HERE
```

### 3. Setup Webhook (Automated)
```bash
chmod +x scripts/setup-telegram-webhook.sh
./scripts/setup-telegram-webhook.sh
```

### 4. Tích hợp vào Server
Thêm vào GameServer startup code:
```java
// In GameServer.main() or similar
TelegramIntegration.onServerStartup();

// In shutdown hook
TelegramIntegration.onServerShutdown();
```

## 🎮 Sử dụng

### Lệnh Telegram Bot

#### Thêm Prime Points cho nhân vật
```
/addprime PlayerName 1000
```

#### Thêm Prime Points cho account
```
/addprime_acc account123 5000
```

#### Xem trợ giúp
```
/help
```

### Admin Commands trong Game

```
//admin_telegram              # Menu quản lý chính
//admin_telegram_status       # Xem trạng thái
//admin_telegram_start        # Khởi động bot
//admin_telegram_stop         # Dừng bot
//admin_telegram_reload       # Reload config
```

## 🔧 Cơ chế hoạt động

### Người chơi Online
```
Telegram → Bot → World.getPlayer() → setPrimePoints() → ExBRGamePoint packet → Client UI
```

### Người chơi Offline
```
Telegram → Bot → Database (account_gsdata) → Player login → Load Prime Points
```

### Database Schema
```sql
-- Prime Points stored in account_gsdata table
INSERT INTO account_gsdata (account_name, var, value) 
VALUES ('account_name', 'PRIME_POINTS', points);
```

## 📊 Kiến trúc hệ thống

```mermaid
graph TD
    A[Telegram Client] -->|HTTPS Webhook| B[TelegramWebhookHandler]
    B --> C[TelegramBotManager]
    C --> D{Player Online?}
    D -->|Yes| E[World.getPlayer()]
    D -->|No| F[Database Update]
    E --> G[setPrimePoints()]
    G --> H[ExBRGamePoint Packet]
    H --> I[Client UI Update]
    F --> J[account_gsdata Table]
    J --> K[Next Login]
    C --> L[Security Check]
    L --> M[Rate Limiting]
    M --> N[Audit Logging]
```

## 🛡️ Bảo mật

### Production Security Checklist
- [ ] HTTPS webhook URL
- [ ] Valid SSL certificate
- [ ] Firewall rules for Telegram IPs
- [ ] Bot token kept secret
- [ ] Admin list regularly reviewed
- [ ] Logs monitored for suspicious activity

### Telegram IP Ranges (for firewall)
```
*************/20
**********/22
```

### Nginx Reverse Proxy Example
```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    location /telegram-webhook {
        proxy_pass http://localhost:8443;
        
        # Only allow Telegram IPs
        allow *************/20;
        allow **********/22;
        deny all;
    }
}
```

## 📈 Monitoring

### Key Metrics
- Command success/failure rate
- Response time
- Active admin count
- Prime Points distributed

### Log Files
- `logs/game.log` - Main server logs
- Search for "TelegramBot" entries

### Health Checks
```bash
# Check webhook status
curl "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"

# Test bot
curl "https://api.telegram.org/bot<TOKEN>/getMe"
```

## 🔧 Troubleshooting

### Common Issues

#### Bot không phản hồi
```bash
# Check config
grep "TelegramBotEnabled" dist/game/config/telegram.properties

# Check logs
tail -f logs/game.log | grep Telegram

# Verify webhook
curl "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"
```

#### Webhook errors
```bash
# Check SSL certificate
openssl s_client -connect yourdomain.com:443

# Test webhook endpoint
curl -X POST https://yourdomain.com/telegram-webhook

# Check firewall
netstat -tulpn | grep 8443
```

#### Prime Points không cập nhật
```sql
-- Check database
SELECT * FROM account_gsdata WHERE var = 'PRIME_POINTS';

-- Check player online
SELECT char_name, online FROM characters WHERE account_name = 'account_name';
```

## 📚 Documentation

- **Setup Guide**: `docs/telegram-bot-setup.md`
- **API Reference**: Xem comments trong source code
- **Configuration**: `dist/game/config/telegram.properties`

## 🤝 Support

### Getting Help
1. Check logs for error messages
2. Verify configuration against setup guide
3. Test with simple commands first
4. Contact development team with detailed logs

### Contributing
1. Fork repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request with detailed description

## 📄 License

This integration is part of the GVE project and follows the same license terms.

---

**🎉 Chúc bạn sử dụng thành công hệ thống Telegram Bot cho GVE Prime Points Management!**
