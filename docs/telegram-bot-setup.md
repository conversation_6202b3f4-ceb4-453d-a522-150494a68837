# Telegram Bot Setup Guide for GVE Prime Points Management

## Tổng quan

Hệ thống Telegram Bot cho phép admin quản lý Prime Points từ xa thông qua Telegram mà không cần đăng nhập vào game. Bot hỗ trợ cập nhật real-time cho người chơi đang online và lưu trữ an toàn cho người chơi offline.

## Tính năng chính

### 🎯 Chức năng cốt lõi
- ✅ Thêm Prime Points cho nhân vật theo tên
- ✅ Thêm Prime Points cho account theo tên account  
- ✅ Cập nhật real-time cho người chơi đang online
- ✅ Lưu trữ an toàn cho người chơi offline
- ✅ Xác thực admin qua Chat ID hoặc Username
- ✅ Rate limiting để tránh spam
- ✅ Logging đầy đủ cho audit

### 🔒 Bảo mật
- <PERSON><PERSON><PERSON> thực admin nghiêm ngặt
- Rate limiting (cooldown 5 giây)
- <PERSON><PERSON><PERSON><PERSON> hạn số Prime Points tối đa mỗi lệnh
- Logging tất cả hoạt động
- Webhook HTTPS bắt buộc trong production

## Cài đặt và cấu hình

### Bước 1: Tạo Telegram Bot

1. Mở Telegram và tìm `@BotFather`
2. Gửi lệnh `/newbot`
3. Đặt tên cho bot (ví dụ: `GVE Prime Points Bot`)
4. Đặt username cho bot (ví dụ: `gve_primepoints_bot`)
5. Lưu lại Bot Token (ví dụ: `*********:ABCdefGHIjklMNOpqrsTUVwxyz`)

### Bước 2: Cấu hình Server

1. Mở file `dist/game/config/telegram.properties`
2. Cập nhật các thông số:

```properties
# Bật Telegram Bot
TelegramBotEnabled = true

# Bot Token từ @BotFather
BotToken = *********:ABCdefGHIjklMNOpqrsTUVwxyz

# Webhook URL (thay yourdomain.com bằng domain thật)
WebhookUrl = https://yourdomain.com/telegram-webhook

# Port cho webhook server
WebhookPort = 8443

# Admin Chat IDs (lấy từ @userinfobot)
AdminChatIds = *********,987654321

# Admin Usernames (không cần @)
AdminUsernames = gamemaster,admin1
```

### Bước 3: Lấy Chat ID

Để lấy Chat ID của admin:
1. Gửi tin nhắn cho bot `@userinfobot`
2. Bot sẽ trả về Chat ID của bạn
3. Thêm Chat ID vào `AdminChatIds` trong config

### Bước 4: Setup Webhook

Sau khi cấu hình xong, chạy lệnh sau để đăng ký webhook:

```bash
curl -X POST "https://api.telegram.org/bot<BOT_TOKEN>/setWebhook" \
     -d "url=https://yourdomain.com/telegram-webhook"
```

Thay `<BOT_TOKEN>` bằng token thật của bot.

### Bước 5: Khởi động Server

1. Khởi động game server
2. Telegram service sẽ tự động start nếu được cấu hình đúng
3. Kiểm tra logs để đảm bảo không có lỗi

## Sử dụng

### Lệnh có sẵn

#### `/addprime <tên_nhân_vật> <số_points>`
Thêm Prime Points cho nhân vật theo tên.

**Ví dụ:**
```
/addprime PlayerName 1000
```

#### `/addprime_acc <tên_account> <số_points>`
Thêm Prime Points cho account theo tên account.

**Ví dụ:**
```
/addprime_acc account123 5000
```

#### `/help`
Hiển thị danh sách lệnh và hướng dẫn sử dụng.

### Giới hạn

- **Số Prime Points:** 1 - 100,000 (có thể cấu hình)
- **Cooldown:** 5 giây giữa các lệnh (có thể cấu hình)
- **Quyền truy cập:** Chỉ admin được phép sử dụng

## Quản lý trong game

### Admin Commands

Sử dụng các lệnh admin sau trong game:

- `//admin_telegram` - Menu quản lý chính
- `//admin_telegram_status` - Xem trạng thái bot
- `//admin_telegram_config` - Xem cấu hình
- `//admin_telegram_start` - Khởi động bot
- `//admin_telegram_stop` - Dừng bot
- `//admin_telegram_reload` - Reload cấu hình

## Cơ chế hoạt động

### Người chơi Online
1. Bot nhận lệnh từ Telegram
2. Tìm player trong World.getInstance().getPlayer()
3. Cập nhật Prime Points trực tiếp: `player.setPrimePoints()`
4. Gửi packet `ExBRGamePoint` để cập nhật UI
5. Thông báo cho player trong game

### Người chơi Offline
1. Bot nhận lệnh từ Telegram
2. Tìm account name từ character name
3. Cập nhật database `account_gsdata` table
4. Player sẽ thấy Prime Points khi đăng nhập lại

### Database Schema

Prime Points được lưu trong bảng `account_gsdata`:
```sql
INSERT INTO account_gsdata (account_name, var, value) 
VALUES ('account_name', 'PRIME_POINTS', points)
```

## Troubleshooting

### Bot không phản hồi
1. Kiểm tra `TelegramBotEnabled = true`
2. Kiểm tra Bot Token đúng
3. Kiểm tra Chat ID trong danh sách admin
4. Xem logs server để tìm lỗi

### Webhook không hoạt động
1. Đảm bảo domain có SSL certificate
2. Port 8443 phải mở và accessible
3. Firewall không block webhook endpoint
4. Kiểm tra webhook URL đúng format

### Lỗi xác thực
1. Kiểm tra Chat ID hoặc Username trong config
2. Đảm bảo `StrictAuthorization = true`
3. User phải có username nếu `RequireUsername = true`

### Prime Points không cập nhật
1. Kiểm tra database connection
2. Xem logs để tìm SQL errors
3. Đảm bảo `account_gsdata` table tồn tại
4. Kiểm tra quyền database user

## Security Best Practices

### 🔒 Bảo mật Production

1. **HTTPS bắt buộc:** Webhook URL phải dùng HTTPS
2. **Firewall:** Chỉ cho phép Telegram IPs truy cập webhook
3. **Bot Token:** Giữ bí mật, không commit vào git
4. **Admin list:** Thường xuyên review danh sách admin
5. **Logs:** Monitor logs để phát hiện truy cập trái phép

### 🛡️ Telegram IPs cho Firewall

Cho phép các IP ranges sau truy cập webhook:
```
*************/20
**********/22
```

### 🔐 Reverse Proxy (Nginx)

Khuyến nghị sử dụng Nginx làm reverse proxy:

```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location /telegram-webhook {
        proxy_pass http://localhost:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Chỉ cho phép Telegram IPs
        allow *************/20;
        allow **********/22;
        deny all;
    }
}
```

## Monitoring và Logs

### Log Files

Kiểm tra các log files sau:
- `logs/game.log` - Logs chính của game server
- `logs/telegram.log` - Logs riêng cho Telegram bot (nếu có)

### Metrics quan trọng

- Số lệnh thành công/thất bại
- Response time của webhook
- Số admin đang hoạt động
- Số Prime Points đã phát

## FAQ

**Q: Bot có thể hoạt động khi server offline không?**
A: Không, bot cần server online để xử lý lệnh.

**Q: Có giới hạn số lượng admin không?**
A: Không có giới hạn cứng, nhưng nên giữ ở mức hợp lý.

**Q: Bot có log lại tất cả hoạt động không?**
A: Có, tất cả lệnh đều được log với timestamp và user info.

**Q: Có thể thay đổi cooldown không?**
A: Có, chỉnh `CommandCooldown` trong config file.

**Q: Bot có hỗ trợ multiple servers không?**
A: Hiện tại chỉ hỗ trợ 1 server, cần custom để hỗ trợ nhiều servers.

## Support

Nếu gặp vấn đề, hãy:
1. Kiểm tra logs server
2. Verify cấu hình theo guide này
3. Test với lệnh đơn giản trước
4. Liên hệ team development với logs chi tiết
