#!/bin/bash

# =================================================================
# Telegram Webhook Setup Script for GVE Prime Points Bot
# =================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONFIG_FILE="../dist/game/config/telegram.properties"
TEMP_CONFIG="/tmp/telegram_config.tmp"

echo -e "${BLUE}==================================================================${NC}"
echo -e "${BLUE}         GVE Telegram Bot Webhook Setup Script${NC}"
echo -e "${BLUE}==================================================================${NC}"
echo ""

# Function to read config value
read_config() {
    local key=$1
    local default=$2
    local value=$(grep "^$key" "$CONFIG_FILE" 2>/dev/null | cut -d'=' -f2- | sed 's/^ *//' | sed 's/ *$//')
    echo "${value:-$default}"
}

# Function to validate URL
validate_url() {
    local url=$1
    if [[ $url =~ ^https://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to validate bot token
validate_token() {
    local token=$1
    if [[ $token =~ ^[0-9]+:[a-zA-Z0-9_-]+$ ]]; then
        return 0
    else
        return 1
    fi
}

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}❌ Config file not found: $CONFIG_FILE${NC}"
    echo -e "${YELLOW}Please make sure the game server is properly installed.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found config file: $CONFIG_FILE${NC}"
echo ""

# Read current configuration
BOT_TOKEN=$(read_config "BotToken" "")
WEBHOOK_URL=$(read_config "WebhookUrl" "")
WEBHOOK_PORT=$(read_config "WebhookPort" "8443")
TELEGRAM_ENABLED=$(read_config "TelegramBotEnabled" "false")

echo -e "${BLUE}Current Configuration:${NC}"
echo "  Bot Enabled: $TELEGRAM_ENABLED"
echo "  Bot Token: ${BOT_TOKEN:0:10}..." # Show only first 10 chars
echo "  Webhook URL: $WEBHOOK_URL"
echo "  Webhook Port: $WEBHOOK_PORT"
echo ""

# Check if bot is enabled
if [ "$TELEGRAM_ENABLED" != "true" ]; then
    echo -e "${YELLOW}⚠️  Telegram bot is currently disabled.${NC}"
    read -p "Do you want to enable it? (y/n): " enable_bot
    if [ "$enable_bot" = "y" ] || [ "$enable_bot" = "Y" ]; then
        sed -i 's/TelegramBotEnabled = false/TelegramBotEnabled = true/' "$CONFIG_FILE"
        echo -e "${GREEN}✅ Telegram bot enabled in config${NC}"
    else
        echo -e "${YELLOW}Bot remains disabled. Exiting.${NC}"
        exit 0
    fi
fi

# Validate bot token
if [ -z "$BOT_TOKEN" ] || [ "$BOT_TOKEN" = "YOUR_BOT_TOKEN_HERE" ]; then
    echo -e "${RED}❌ Bot token is not configured${NC}"
    echo -e "${YELLOW}Please follow these steps:${NC}"
    echo "1. Message @BotFather on Telegram"
    echo "2. Send /newbot command"
    echo "3. Follow instructions to create your bot"
    echo "4. Copy the bot token"
    echo ""
    read -p "Enter your bot token: " new_token
    
    if validate_token "$new_token"; then
        sed -i "s/BotToken = .*/BotToken = $new_token/" "$CONFIG_FILE"
        BOT_TOKEN="$new_token"
        echo -e "${GREEN}✅ Bot token updated${NC}"
    else
        echo -e "${RED}❌ Invalid bot token format${NC}"
        exit 1
    fi
fi

# Validate webhook URL
if [ -z "$WEBHOOK_URL" ] || [[ "$WEBHOOK_URL" == *"yourdomain.com"* ]]; then
    echo -e "${RED}❌ Webhook URL is not configured${NC}"
    echo -e "${YELLOW}Please enter your domain with HTTPS${NC}"
    echo "Example: https://yourgame.com/telegram-webhook"
    echo ""
    read -p "Enter webhook URL: " new_url
    
    if validate_url "$new_url"; then
        sed -i "s|WebhookUrl = .*|WebhookUrl = $new_url|" "$CONFIG_FILE"
        WEBHOOK_URL="$new_url"
        echo -e "${GREEN}✅ Webhook URL updated${NC}"
    else
        echo -e "${RED}❌ Invalid URL format. Must be HTTPS.${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${BLUE}==================================================================${NC}"
echo -e "${BLUE}                    Setting up webhook...${NC}"
echo -e "${BLUE}==================================================================${NC}"

# Test bot token first
echo "🔍 Testing bot token..."
TEST_RESPONSE=$(curl -s "https://api.telegram.org/bot$BOT_TOKEN/getMe")

if echo "$TEST_RESPONSE" | grep -q '"ok":true'; then
    BOT_INFO=$(echo "$TEST_RESPONSE" | grep -o '"username":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Bot token is valid. Bot username: @$BOT_INFO${NC}"
else
    echo -e "${RED}❌ Bot token is invalid or bot is not accessible${NC}"
    echo "Response: $TEST_RESPONSE"
    exit 1
fi

# Set webhook
echo "🔗 Setting webhook URL..."
WEBHOOK_RESPONSE=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/setWebhook" \
    -d "url=$WEBHOOK_URL" \
    -d "max_connections=10" \
    -d "allowed_updates=[\"message\"]")

if echo "$WEBHOOK_RESPONSE" | grep -q '"ok":true'; then
    echo -e "${GREEN}✅ Webhook set successfully${NC}"
    echo "Webhook URL: $WEBHOOK_URL"
else
    echo -e "${RED}❌ Failed to set webhook${NC}"
    echo "Response: $WEBHOOK_RESPONSE"
    
    # Try to get more info about the error
    if echo "$WEBHOOK_RESPONSE" | grep -q "SSL"; then
        echo -e "${YELLOW}💡 SSL certificate issue detected. Make sure your domain has a valid SSL certificate.${NC}"
    fi
    exit 1
fi

# Get webhook info to verify
echo "📋 Verifying webhook info..."
WEBHOOK_INFO=$(curl -s "https://api.telegram.org/bot$BOT_TOKEN/getWebhookInfo")

if echo "$WEBHOOK_INFO" | grep -q '"ok":true'; then
    echo -e "${GREEN}✅ Webhook verification successful${NC}"
    
    # Extract and display webhook info
    WEBHOOK_SET_URL=$(echo "$WEBHOOK_INFO" | grep -o '"url":"[^"]*"' | cut -d'"' -f4)
    PENDING_UPDATES=$(echo "$WEBHOOK_INFO" | grep -o '"pending_update_count":[0-9]*' | cut -d':' -f2)
    LAST_ERROR=$(echo "$WEBHOOK_INFO" | grep -o '"last_error_message":"[^"]*"' | cut -d'"' -f4)
    
    echo "  Current webhook URL: $WEBHOOK_SET_URL"
    echo "  Pending updates: ${PENDING_UPDATES:-0}"
    
    if [ ! -z "$LAST_ERROR" ]; then
        echo -e "  ${YELLOW}Last error: $LAST_ERROR${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Could not verify webhook info${NC}"
fi

echo ""
echo -e "${BLUE}==================================================================${NC}"
echo -e "${BLUE}                    Setup Summary${NC}"
echo -e "${BLUE}==================================================================${NC}"

echo -e "${GREEN}✅ Telegram bot setup completed successfully!${NC}"
echo ""
echo "📋 Configuration Summary:"
echo "  Bot Token: Configured ✅"
echo "  Webhook URL: $WEBHOOK_URL ✅"
echo "  Webhook Port: $WEBHOOK_PORT"
echo ""
echo "🚀 Next Steps:"
echo "1. Start your game server"
echo "2. Check server logs for any errors"
echo "3. Add admin users to the bot configuration"
echo "4. Test the bot by sending /help command"
echo ""
echo "📖 Admin Configuration:"
echo "Edit $CONFIG_FILE and add:"
echo "  AdminChatIds = your_chat_id_here"
echo "  AdminUsernames = your_username_here"
echo ""
echo "💡 To get your Chat ID:"
echo "1. Message @userinfobot on Telegram"
echo "2. Copy the Chat ID number"
echo "3. Add it to AdminChatIds in config"
echo ""
echo -e "${YELLOW}⚠️  Important Security Notes:${NC}"
echo "• Keep your bot token secret"
echo "• Use HTTPS for webhook URL"
echo "• Regularly review admin access list"
echo "• Monitor server logs for security issues"
echo ""
echo -e "${GREEN}🎉 Setup complete! Your Telegram bot is ready to use.${NC}"
