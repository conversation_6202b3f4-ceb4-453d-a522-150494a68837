# ---------------------------------------------------------------------------
# Floodprotector Options
# ---------------------------------------------------------------------------
# The following settings can be applied to each feature:
# Interval - interval in gameserver ticks (1 tick = 100ms) in which only one request is allowed
# LogFlooding - whether flooding should be logged (only first occurrence of flooding and total count of flood requests is logged)
# PunishmentLimit - if number of requests within single interval exceeds specified number then the specified punishment is applied (0 = disables punishment feature)
# PunishmentType - type of the punishment ('none', 'kick', 'ban', 'jail'), valid only if PunishmentLimit is greater than 0
# PunishmentTime - for how many minutes should the player(jail)/account(ban) be punished (0 = forever), valid only for PunishmentType 'jail' or 'ban'

# UseItem - item usage flooding
# Disabled to match retail, if you want to enable this protection change the value to 4 for example.
# Default: 0
FloodProtectorUseItemInterval = 0
FloodProtectorUseItemLogFlooding = False
FloodProtectorUseItemPunishmentLimit = 0
FloodProtectorUseItemPunishmentType = none
FloodProtectorUseItemPunishmentTime = 0

# RollDice - rolling dice flooding
FloodProtectorRollDiceInterval = 42
FloodProtectorRollDiceLogFlooding = False
FloodProtectorRollDicePunishmentLimit = 0
FloodProtectorRollDicePunishmentType = none
FloodProtectorRollDicePunishmentTime = 0

# Firework - firework flooding
FloodProtectorFireworkInterval = 42
FloodProtectorFireworkLogFlooding = False
FloodProtectorFireworkPunishmentLimit = 0
FloodProtectorFireworkPunishmentType = none
FloodProtectorFireworkPunishmentTime = 0

# ItemPetSummon - item summoning and pet mounting flooding
FloodProtectorItemPetSummonInterval = 16
FloodProtectorItemPetSummonLogFlooding = False
FloodProtectorItemPetSummonPunishmentLimit = 0
FloodProtectorItemPetSummonPunishmentType = none
FloodProtectorItemPetSummonPunishmentTime = 0

# HeroVoice - hero voice flooding
FloodProtectorHeroVoiceInterval = 100
FloodProtectorHeroVoiceLogFlooding = False
FloodProtectorHeroVoicePunishmentLimit = 0
FloodProtectorHeroVoicePunishmentType = none
FloodProtectorHeroVoicePunishmentTime = 0

# GlobalChat - global chat flooding
FloodProtectorGlobalChatInterval = 5
FloodProtectorGlobalChatLogFlooding = False
FloodProtectorGlobalChatPunishmentLimit = 0
FloodProtectorGlobalChatPunishmentType = none
FloodProtectorGlobalChatPunishmentTime = 0

# Subclass - subclass flooding
FloodProtectorSubclassInterval = 20
FloodProtectorSubclassLogFlooding = False
FloodProtectorSubclassPunishmentLimit = 0
FloodProtectorSubclassPunishmentType = none
FloodProtectorSubclassPunishmentTime = 0

# DropItem - drop item flooding
FloodProtectorDropItemInterval = 10
FloodProtectorDropItemLogFlooding = False
FloodProtectorDropItemPunishmentLimit = 0
FloodProtectorDropItemPunishmentType = none
FloodProtectorDropItemPunishmentTime = 0

# ServerBypass - server bypass flooding
FloodProtectorServerBypassInterval = 1
FloodProtectorServerBypassLogFlooding = False
FloodProtectorServerBypassPunishmentLimit = 0
FloodProtectorServerBypassPunishmentType = none
FloodProtectorServerBypassPunishmentTime = 0

# ServerBypass - multisell list request flooding
FloodProtectorMultiSellInterval = 0
FloodProtectorMultiSellLogFlooding = False
FloodProtectorMultiSellPunishmentLimit = 0
FloodProtectorMultiSellPunishmentType = none
FloodProtectorMultiSellPunishmentTime = 0

# All kind of other transactions - to/from pet, private store, warehouse, destroy
FloodProtectorTransactionInterval = 10
FloodProtectorTransactionLogFlooding = False
FloodProtectorTransactionPunishmentLimit = 0
FloodProtectorTransactionPunishmentType = none
FloodProtectorTransactionPunishmentTime = 0

# Manufacture
FloodProtectorManufactureInterval = 3
FloodProtectorManufactureLogFlooding = False
FloodProtectorManufacturePunishmentLimit = 0
FloodProtectorManufacturePunishmentType = none
FloodProtectorManufacturePunishmentTime = 0

# Manor
FloodProtectorManorInterval = 30
FloodProtectorManorLogFlooding = False
FloodProtectorManorPunishmentLimit = 0
FloodProtectorManorPunishmentType = none
FloodProtectorManorPunishmentTime = 0

# SendMail - sending mail interval, 10s on retail
FloodProtectorSendMailInterval = 100
FloodProtectorSendMailLogFlooding = False
FloodProtectorSendMailPunishmentLimit = 0
FloodProtectorSendMailPunishmentType = none
FloodProtectorSendMailPunishmentTime = 0

# CharacterSelect - attempts to load character
FloodProtectorCharacterSelectInterval = 30
FloodProtectorCharacterSelectLogFlooding = False
FloodProtectorCharacterSelectPunishmentLimit = 0
FloodProtectorCharacterSelectPunishmentType = none
FloodProtectorCharacterSelectPunishmentTime = 0

# Item Auction - Request for refresh
FloodProtectorItemAuctionInterval = 9
FloodProtectorItemAuctionLogFlooding = False
FloodProtectorItemAuctionPunishmentLimit = 0
FloodProtectorItemAuctionPunishmentType = none
FloodProtectorItemAuctionPunishmentTime = 0
