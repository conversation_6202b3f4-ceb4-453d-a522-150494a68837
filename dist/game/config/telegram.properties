# =================================================================
# Telegram Bot Configuration for GVE Prime Points Management
# =================================================================

# Enable/Disable Telegram Bot functionality
# Default: false
TelegramBotEnabled = false

# Telegram Bot Token (get from @BotFather)
# Example: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz
BotToken = YOUR_BOT_TOKEN_HERE

# =================================================================
# Webhook Configuration
# =================================================================

# Use webhook instead of polling (recommended for production)
# Default: true
UseWebhook = true

# Webhook URL (must be HTTPS in production)
# Example: https://yourdomain.com/telegram-webhook
WebhookUrl = https://yourdomain.com/telegram-webhook

# Webhook server port
# Default: 8443
WebhookPort = 8443

# Webhook path
# Default: /telegram-webhook
WebhookPath = /telegram-webhook

# =================================================================
# Security & Authorization
# =================================================================

# Strict authorization mode
# If true, only users in AdminChatIds or AdminUsernames can use the bot
# Default: true
StrictAuthorization = true

# Authorized admin chat IDs (comma-separated)
# Example: 123456789,987654321
AdminChatIds = 

# Authorized admin usernames (comma-separated, without @)
# Example: admin1,admin2,gamemaster
AdminUsernames = 

# Require username for authorization
# If true, users without username cannot use the bot
# Default: true
RequireUsername = true

# =================================================================
# Rate Limiting & Limits
# =================================================================

# Command cooldown in milliseconds
# Default: 5000 (5 seconds)
CommandCooldown = 5000

# Maximum Prime Points per command
# Default: 100000
MaxPrimePointsPerCommand = 100000

# =================================================================
# Logging Configuration
# =================================================================

# Log all commands (including failed attempts)
# Default: true
LogAllCommands = true

# =================================================================
# Advanced Settings
# =================================================================

# Connection timeout for Telegram API calls (milliseconds)
# Default: 10000 (10 seconds)
ApiTimeout = 10000

# Maximum retries for failed API calls
# Default: 3
MaxRetries = 3

# Enable debug mode (more verbose logging)
# Default: false
DebugMode = false

# =================================================================
# Example Configuration for Production:
# =================================================================
# TelegramBotEnabled = true
# BotToken = 123456789:ABCdefGHIjklMNOpqrsTUVwxyz
# UseWebhook = true
# WebhookUrl = https://yourgame.com/telegram-webhook
# WebhookPort = 8443
# StrictAuthorization = true
# AdminChatIds = 123456789,987654321
# AdminUsernames = gamemaster,admin
# CommandCooldown = 5000
# MaxPrimePointsPerCommand = 50000
# LogAllCommands = true

# =================================================================
# Security Notes:
# =================================================================
# 1. Always use HTTPS for webhook URL in production
# 2. Keep your bot token secret and secure
# 3. Regularly review authorized admin list
# 4. Monitor logs for unauthorized access attempts
# 5. Use firewall to restrict webhook endpoint access
# 6. Consider using reverse proxy (nginx) for additional security

# =================================================================
# Setup Instructions:
# =================================================================
# 1. Create a bot with @BotFather on Telegram
# 2. Get the bot token and set it in BotToken
# 3. Add authorized admin chat IDs or usernames
# 4. Set WebhookUrl to your server's HTTPS endpoint
# 5. Enable TelegramBotEnabled = true
# 6. Restart the game server
# 7. Set webhook with: curl -X POST "https://api.telegram.org/bot<TOKEN>/setWebhook" -d "url=<WEBHOOK_URL>"

# =================================================================
# Available Commands:
# =================================================================
# /addprime <character_name> <points> - Add Prime Points to character
# /addprime_acc <account_name> <points> - Add Prime Points to account
# /help - Show help message
