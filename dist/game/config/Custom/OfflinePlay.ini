# ---------------------------------------------------------------------------
# Offline Auto Play
# ---------------------------------------------------------------------------

# Enable .offlineplay command for logging out and continue auto play.
# Default: False
EnableOfflinePlayCommand = True

# Enable .offlineplay command only for premium players.
# Premium System must be enabled.
# Default: False
OfflinePlayPremium = True

# Logout player on death.
# Default: True
OfflinePlayLogoutOnDeath = True

# Message send to players on login.
# Leave empty to disable.
# Example: You can use .offlineplay to logout and continue auto play.
# You can use .offline_farm to logout and continue auto play.
OfflinePlayLoginMessage = 

# If set to True, name color will be changed then entering offline play mode.
# Default: True
OfflinePlaySetNameColor = False

# Color of the name in offline play mode (if OfflinePlaySetNameColor = True).
# Default: 808080
OfflinePlayNameColor = 808080

# Abnormal effect for offline play.
# Can use multiple enums separated by commas to choose random effect.
# Leave empty to disable.
# Default: DORMANT_USER
OfflinePlayAbnormalEffect = DORMANT_USER

# ---------------------------------------------------------------------------
# Offline Auto-Resurrection Settings
# ---------------------------------------------------------------------------

# Enable auto-resurrection for offline players
# When enabled, offline players will automatically resurrect when they die
# Default: True
OfflinePlayAutoResurrectionEnabled = True

# Auto-disable offline play when player runs out of L-Coin for auto-resurrection
# If True: Player will be logged out when they can't afford auto-resurrection
# If False: Player stays offline but auto-resurrection is disabled
# Default: True
OfflinePlayLogoutOnInsufficientLCoin = True

# Store offline player's hunting location for auto-resurrection return
# This ensures offline players return to their original hunting spot after resurrection
# Default: True
OfflinePlayStoreHuntingLocation = True
