# ---------------------------------------------------------------------------
# Fort Siege Settings
# ---------------------------------------------------------------------------
# The defaults are set to be retail-like. If you modify any of these settings your server will deviate from being retail-like.
# Warning: 
# Please take extreme caution when changing anything. Also please understand what you are changing before you do so on a live server.

# ---------------------------------------------------------------------------
# Standard Settings
# ---------------------------------------------------------------------------

# Length of siege before the count down (in minutes).
SiegeLength = 30

# This defines how long you need to wait until the suspicious merchant will spawn after siege ends (in minutes).
# Keep in mind when merchant spawns, the fort can be immediately sieged.
# Default: 180
SuspiciousMerchantRespawnDelay = 1

# This defines how long you have to kill all commanders once you kill the first one (in minutes).
# After that time (if all commanders not killed) all commanders and doors get respawned.
CountDownLength = 10

# Max number of flags per clan.
MaxFlags = 0

# Minimum clan level needed to sign up.
SiegeClanMinLevel = 4

#Max number of clans that can register on each side.
AttackerMaxClans = 500

# This option, if enabled, will enable register Fortress Siege to Castle owners just in territory.
# Default: True
JustToTerritory = True

# ---------------------------------------------------------------------------
# Fortress Commander Spawns
# ---------------------------------------------------------------------------
# Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
# Name - fortress name
# N - number - 1: Archer Comander
#            - 2: Guard Commander
#            - 3: Support Unit Commander
#            - 4: Main Machine - not supported yet, used General
#            - 5: General - not used yet, will be used once control room is supported
# Please keep the correct order of commanders, otherwise client will show wrong occupied barracks.
# Control room (main machine) currently emulated in client.
# x,y,z - coords
# heading
# npc_id - id of template

# Flag syntax:  NameFlagN = x,y,z,flag_id
# Name - fortress name
# N - number
# x,y,z - coords
# flag_id - id of combat flag

# Shanty Fortress
# Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#ShantyCommander1 = -52435,155188,-1768,20000,35683
#ShantyCommander2 = -52128,157752,-2024,29864,35677
#ShantyCommander3 = -53944,155433,-2024,7304,35680
## Flag syntax:  NameFlagN = x,y,z,flag_id
#ShantyFlag1 = -53086,156493,-1896,93331
#ShantyFlag2 = -53054,156605,-1896,93331
#ShantyFlag3 = -53032,156689,-1896,93331
#
## Southern Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#SouthernCommander1 = -21328,218864,-2952,0,35719
#SouthernCommander2 = -22992,218160,-3208,0,35713
#SouthernCommander3 = -21520,221504,-3208,45328,35716
#SouthernCommander4 = -22728,221746,-3200,33168,35721
## Flag syntax:  NameFlagN = x,y,z,flag_id
#SouthernFlag1 = -22386,219917,-3079,93331
#SouthernFlag2 = -22386,219798,-3079,93331
#SouthernFlag3 = -22386,219679,-3079,93331
#
## Hive Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#HiveCommander1 = 15152,188128,-2640,0,35752
#HiveCommander2 = 17984,187536,-2896,45056,35746
#HiveCommander3 = 16016,189520,-2888,0,35749
## Flag syntax:  NameFlagN = x,y,z,flag_id
#HiveFlag1 = 16685,188358,-2770,93331
#HiveFlag2 = 16761,188306,-2770,93331
#HiveFlag3 = 16847,188257,-2770,93331
#
## Valley Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#ValleyCommander1 = 124768,121856,-2296,0,35788
#ValleyCommander2 = 124299,123614,-2552,49192,35782
#ValleyCommander3 = 124768,124640,-2552,54480,35785
#ValleyCommander4 = 128048,123344,-2536,35028,35790
## Flag syntax:  NameFlagN = x,y,z,flag_id
#ValleyFlag1 = 125970,123653,-2429,93331
#ValleyFlag2 = 126092,123650,-2429,93331
#ValleyFlag3 = 126205,123648,-2429,93331
#
## Ivory Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#IvoryCommander1 = 72400,2896,-2760,0,35821
#IvoryCommander2 = 73788,5479,-3016,55136,35815
#IvoryCommander3 = 71264,4144,-3008,0,35818
## Flag syntax:  NameFlagN = x,y,z,flag_id
#IvoryFlag1 = 72565,4436,-2888,93331
#IvoryFlag2 = 72660,4512,-2888,93331
#IvoryFlag3 = 72759,4594,-2888,93331
#
## Narsell Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#NarsellCommander1 = 154704,53856,-2968,0,35852
#NarsellCommander2 = 155576,56592,-3224,59224,35846
#NarsellCommander3 = 153328,54848,-3216,5512,35849
## Flag syntax:  NameFlagN = x,y,z,flag_id
#NarsellFlag1 = 154567,55397,-3097,93331
#NarsellFlag2 = 154650,55493,-3097,93331
#NarsellFlag3 = 154715,55587,-3097,93331
#
## Bayou Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#BayouCommander1 = 188624,38240,-3128,0,35888
#BayouCommander2 = 188160,39920,-3376,49284,35882
#BayouCommander3 = 188626,41066,-3376,57140,35885
#BayouCommander4 = 191846,39764,-3368,33020,35890
## Flag syntax:  NameFlagN = x,y,z,flag_id
#BayouFlag1 = 189838,40063,-3253,93331
#BayouFlag2 = 189931,40060,-3253,93331
#BayouFlag3 = 190052,40062,-3253,93331
#
## White Sands Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#WhiteSandsCommander1 = 117216,205648,-3048,0,35921
#WhiteSandsCommander2 = 118880,203568,-3304,5396,35915
#WhiteSandsCommander3 = 118560,206560,-3304,48872,35918
## Flag syntax:  NameFlagN = x,y,z,flag_id
#WhiteSandsFlag1 = 118640,205151,-3176,93331
#WhiteSandsFlag2 = 118690,205062,-3176,93331
#WhiteSandsFlag3 = 118742,204968,-3176,93331
#
## Borderland Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#BorderlandCommander1 = 159664,-72224,-2584,0,35957
#BorderlandCommander2 = 157968,-71659,-2832,59020,35951
#BorderlandCommander3 = 157312,-70640,-2832,0,35954
#BorderlandCommander4 = 160194,-68688,-2824,43272,35959
## Flag syntax:  NameFlagN = x,y,z,flag_id
#BorderlandFlag1 = 158817,-70229,-2708,93331
#BorderlandFlag2 = 158883,-70145,-2708,93331
#BorderlandFlag3 = 158946,-70045,-2708,93331
#
## Swamp Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#SwampCommander1 = 71264,-60512,-2504,0,35995
#SwampCommander2 = 71248,-62352,-2752,12388,35989
#SwampCommander3 = 68688,-59648,-2752,56012,35992
#SwampCommander4 = 68005,-60866,-2744,5424,35997
## Flag syntax:  NameFlagN = x,y,z,flag_id
#SwampFlag1 = 69829,-61087,-2629,93331
#SwampFlag2 = 69979,-61144,-2632,93331
#SwampFlag3 = 70069,-61182,-2629,93331
#
## Archaic Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#ArchaicCommander1 = 109856,-142640,-2672,0,36028
#ArchaicCommander2 = 109600,-139735,-2928,62612,36022
#ArchaicCommander3 = 108223,-142209,-2920,8524,36025
## Flag syntax:  NameFlagN = x,y,z,flag_id
#ArchaicFlag1 = 109142,-141243,-2801,93331
#ArchaicFlag2 = 109184,-141129,-2801,93331
#ArchaicFlag3 = 109214,-141016,-2801,93331
#
## Floran Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#FloranCommander1 = 6528,151872,-2608,0,36064
#FloranCommander2 = 7006,148242,-2856,32768,36058
#FloranCommander3 = 4384,150992,-2856,0,36061
#FloranCommander4 = 5246,152319,-2848,49151,36066
## Flag syntax:  NameFlagN = x,y,z,flag_id
#FloranFlag1 = 5293,149624,-2732,93331
#FloranFlag2 = 5306,149743,-2732,93331
#FloranFlag3 = 5299,149870,-2732,93331
#
## Cloud Mountain Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#CloudMountainCommander1 = -55248,90496,-2536,0,36102
#CloudMountainCommander2 = -55791,91856,-2792,0,36096
#CloudMountainCommander3 = -54168,92604,-2784,49196,36099
#CloudMountainCommander4 = -50913,92259,-2776,41188,36104
## Flag syntax:  NameFlagN = x,y,z,flag_id
#CloudMountainFlag1 = -53354,91537,-2664,93331
#CloudMountainFlag2 = -53237,91537,-2664,93331
#CloudMountainFlag3 = -53112,91537,-2664,93331
#
## Tanor Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#TanorCommander1 = 58480,139648,-1464,0,36135
#TanorCommander2 = 61864,139257,-1728,46896,36129
#TanorCommander3 = 59436,140834,-1720,47296,36132
## Flag syntax:  NameFlagN = x,y,z,flag_id
#TanorFlag1 = 60225,139771,-1597,93331
#TanorFlag2 = 60362,139742,-1597,93331
#TanorFlag3 = 60467,139727,-1597,93331
#
## DragonSpine Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#DragonspineCommander1 = 13184,94928,-3144,0,36166
#DragonspineCommander2 = 9472,94992,-3392,0,36160
#DragonspineCommander3 = 12829,96214,-3392,49152,36163
## Flag syntax:  NameFlagN = x,y,z,flag_id
#DragonspineFlag1 = 11459,95308,-3264,93331
#DragonspineFlag2 = 11527,95301,-3264,93331
#DragonspineFlag3 = 11623,95311,-3264,93331
#
## Antharas Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#AntharasCommander1 = 79440,88752,-2600,0,36202
#AntharasCommander2 = 77262,91704,-2856,5112,36196
#AntharasCommander3 = 80929,90510,-2856,40192,36199
#AntharasCommander4 = 80755,89002,-2848,21984,36204
## Flag syntax:  NameFlagN = x,y,z,flag_id
#AntharasFlag1 = 79470,91299,-2728,93331
#AntharasFlag2 = 79528,91187,-2728,93331
#AntharasFlag3 = 79580,91095,-2728,93331
#
## Western Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#WesternCommander1 = 113481,-16058,-712,0,36240
#WesternCommander2 = 109872,-16624,-968,16384,36234
#WesternCommander3 = 112601,-13933,-960,49152,36237
#WesternCommander4 = 113929,-14801,-960,32768,36242
## Flag syntax:  NameFlagN = x,y,z,flag_id
#WesternFlag1 = 111280,-14820,-839,93331
#WesternFlag2 = 111380,-14820,-839,93331
#WesternFlag3 = 111480,-14820,-839,93331
#
## Hunters Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#HuntersCommander1 = 123232,94400,-1856,0,36278
#HuntersCommander2 = 122688,95760,-2112,0,36272
#HuntersCommander3 = 124305,96528,-2104,49151,36275
#HuntersCommander4 = 127632,96240,-2096,40892,36280
## Flag syntax:  NameFlagN = x,y,z,flag_id
#HuntersFlag1 = 125155,95455,-1984,93331
#HuntersFlag2 = 125255,95455,-1984,93331
#HuntersFlag3 = 125355,95455,-1984,93331
#
## Aaru Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#AaruCommander1 = 74288,186912,-2296,0,36311
#AaruCommander2 = 71392,184720,-2552,5528,36305
#AaruCommander3 = 71542,186410,-2552,55088,36308
## Flag syntax:  NameFlagN = x,y,z,flag_id
#AaruFlag1 = 73029,186303,-2424,93331
#AaruFlag2 = 72923,186247,-2424,93331
#AaruFlag3 = 72833,186178,-2424,93331
#
## Demon Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#DemonCommander1 = 100752,-53664,-360,0,36347
#DemonCommander2 = 100688,-57440,-616,16384,36341
#DemonCommander3 = 99484,-54027,-616,0,36344
## Flag syntax:  NameFlagN = x,y,z,flag_id
#DemonFlag1 = 100400,-55401,-488,93331
#DemonFlag2 = 100400,-55301,-488,93331
#DemonFlag3 = 100400,-55201,-488,93331
#
## Monastic Fortress
## Commander syntax:  NameCommanderN = x,y,z,heading,npc_id
#MonasticCommander1 = 73680,-95456,-1144,0,36385
#MonasticCommander2 = 70189,-93935,-1400,61576,36379
#MonasticCommander3 = 73831,-94119,-1400,45536,36382
## Flag syntax:  NameFlagN = x,y,z,flag_id
#MonasticFlag1 = 72174,-94437,-1271,93331
#MonasticFlag2 = 72294,-94481,-1271,93331
#MonasticFlag3 = 72401,-94526,-1271,93331