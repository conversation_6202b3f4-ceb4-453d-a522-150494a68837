<?xml version="1.0" encoding="UTF-8"?>
<instance id="223" maxWorlds="80" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../xsd/instance.xsd">
    <!-- Gladiator's Temple -->
	<time duration="30" empty="0" />
    <locations>
        <enter type="FIXED">
            <location x="212904" y="51016" z="-9960" /> <!-- x="213016" y="52104" z="-8412" -->
        </enter>
        <exit type="ORIGIN" />
    </locations>
    <conditions>
        <condition type="GroupMin">
            <param name="limit" value="2" />
        </condition>
        <condition type="GroupMax">
            <param name="limit" value="10" />
        </condition>
        <condition type="Level">
            <param name="min" value="76" />
        </condition>
        <condition type="Distance" />
        <condition type="Reenter" />
    </conditions>
    <reenter apply="ON_ENTER">
        <reset day="WEDNESDAY" hour="6" minute="30" />
    </reenter>
</instance>
