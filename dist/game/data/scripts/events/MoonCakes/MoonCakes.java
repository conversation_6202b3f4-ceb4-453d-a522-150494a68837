package events.MoonCakes;

import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.quest.LongTimeEvent;
import club.projectessence.gameserver.model.skills.Skill;

public class MoonCakes extends LongTimeEvent
{
	// NPCs
	private static final int	MANAGER						= 34311;
	private static final int	BONFIRE_NPC					= 9023;
	// Skills
	private static final int	COOKING_INGREDIENT_SKILL	= 55901;
	private static final int	COOKING_INGREDIENT_ITEM		= 71035;
	// <PERSON><PERSON><PERSON>u <PERSON>h Trung Thu
	private static final int	BONFIRE_ITEM				= 71030;
	private static final int	SUGAR						= 160048;
	private static final int	FLOUR						= 160049;
	private static final int	LOTUS_SEED					= 160050;
	private static final int	CHICKEN_PIECE				= 160051;
	// Banh Trung Thu
	private static final int	BANH_HAT_SEN				= 71030;
	private static final int	BANH_GA_QUAY				= 71031;
	// Vars
	private static final String	BONFIRE_RECEIVED_VAR		= "BONFIRE_RECEIVED";
	
	private MoonCakes()
	{
		addFirstTalkId(MANAGER);
		addTalkId(MANAGER);
		addSkillSeeId(BONFIRE_NPC);
	}
	
	public static void main(String[] args)
	{
		new MoonCakes();
	}
	
	@Override
	public String onAdvEvent(String event, Npc npc, PlayerInstance player)
	{
		switch (event)
		{
			case "receive_bonfire":
			{
				if (player.getAccountVariables().getString(BONFIRE_RECEIVED_VAR, "N").equals("Y") || (player.getInventory().getFreeSpace() <= 0))
				{
					return "bonfire-error.htm";
				}
				player.addItem("Receive Bonfire", BONFIRE_ITEM, 1, player, true);
				player.getAccountVariables().set(BONFIRE_RECEIVED_VAR, "Y");
				break;
			}
			default:
				return event;
		}
		return null;
	}
	
	@Override
	public String onFirstTalk(Npc npc, PlayerInstance player)
	{
		return npc.getId() + ".htm";
	}
	
	@Override
	public String onSkillSee(Npc npc, PlayerInstance caster, Skill skill, WorldObject[] targets, boolean isPet)
	{
		if ((caster.getTarget() == npc) && (npc.getId() == BONFIRE_NPC) && (skill.getId() == COOKING_INGREDIENT_SKILL))
		{
			caster.destroyItemByItemId("Bonfire Cooking Ingredient", COOKING_INGREDIENT_ITEM, 1, caster, true);
			if (Rnd.get(0, 100) < 30)
			{
				caster.addItem("Bonfire Cooking Ingredient", BANH_GA_QUAY, 1, caster, true);
			}
			else
			{
				caster.addItem("Bonfire Cooking Ingredient", BANH_HAT_SEN, 1, caster, true);
			}
		}
		return super.onSkillSee(npc, caster, skill, targets, isPet);
	}
}
