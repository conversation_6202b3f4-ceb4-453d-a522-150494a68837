/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.playeractions;

import club.projectessence.gameserver.data.xml.PetSkillData;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.handler.IPlayerActionHandler;
import club.projectessence.gameserver.model.ActionDataHolder;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.TargetType;
import club.projectessence.gameserver.util.Util;

/**
 * Summon skill use player action handler.
 *
 * <AUTHOR>
 */
public class ServitorSkillUse implements IPlayerActionHandler {
	@Override
	public void useAction(PlayerInstance player, ActionDataHolder data, boolean ctrlPressed, boolean shiftPressed) {
		// final Summon summon = player.getAnyServitor();
		// if ((summon == null) || !summon.isServitor())
		// {
		// // player.sendPacket(SystemMessageId.YOU_DON_T_HAVE_A_SERVITOR);
		// return;
		// }

		player.getServitors().values().forEach(servitor ->
		{
			if (servitor.isBetrayed()) {
				// player.sendPacket(SystemMessageId.YOUR_SERVITOR_IS_UNRESPONSIVE_AND_WILL_NOT_OBEY_ANY_ORDERS);
				return;
			}
			if ((servitor._lastSkillTimeStamp + 100) > System.currentTimeMillis()) {
				return;
			}

			final int skillLevel = PetSkillData.getInstance().getAvailableLevel(servitor, data.getOptionId());
			if (skillLevel > 0) {
				Skill skill = SkillData.getInstance().getSkill(data.getOptionId(), skillLevel);
				if (player.getTarget() != null) {
					// @formatter:off
					if ((skill.getTargetType() == TargetType.TARGET)
							|| (skill.getTargetType() == TargetType.ENEMY)
							|| (skill.getTargetType() == TargetType.ENEMY_ONLY)
							|| (skill.getTargetType() == TargetType.ENEMY_NOT))
					// @formatter:on
					{
						if (Util.calculateDistance(player, player.getTarget(), false, false) > 1300) {
							// player.sendPacket(SystemMessageId.THE_DISTANCE_IS_TOO_FAR_AND_SO_THE_CASTING_HAS_BEEN_CANCELLED);
							return;
						}
					}
				}
				servitor.setTarget(player.getTarget());
				if (servitor.useMagic(skill, null, ctrlPressed, shiftPressed, false)) {
					servitor._lastSkillTimeStamp = System.currentTimeMillis();
				}
			}
		});
	}
}
