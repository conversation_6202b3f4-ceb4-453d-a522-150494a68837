/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.playeractions;

import club.projectessence.gameserver.data.xml.PetDataTable;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.handler.IPlayerActionHandler;
import club.projectessence.gameserver.model.ActionDataHolder;
import club.projectessence.gameserver.model.actor.instance.PetInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.TargetType;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.util.Util;

/**
 * Pet skill use player action handler.
 *
 * <AUTHOR>
 */
public class PetSkillUse implements IPlayerActionHandler {
	@Override
	public void useAction(PlayerInstance player, ActionDataHolder data, boolean ctrlPressed, boolean shiftPressed) {
		if (player.getTarget() == null) {
			return;
		}

		final PetInstance pet = player.getPet();
		if (pet == null) {
			player.sendPacket(SystemMessageId.YOU_DON_T_HAVE_A_PET);
		} else if (pet.isUncontrollable()) {
			player.sendPacket(SystemMessageId.WHEN_YOUR_PET_S_HUNGER_GAUGE_IS_AT_0_YOU_CANNOT_USE_YOUR_PET);
		} else if (pet.isBetrayed()) {
			player.sendPacket(SystemMessageId.YOUR_SERVITOR_IS_UNRESPONSIVE_AND_WILL_NOT_OBEY_ANY_ORDERS);
		}
		// else if ((pet.getLevel() - player.getLevel()) > 20)
		// {
		// player.sendPacket(SystemMessageId.YOUR_PET_IS_TOO_HIGH_LEVEL_TO_CONTROL);
		// }
		else {
			final int skillLevel = PetDataTable.getInstance().getPetData(pet.getId()).getAvailableLevel(data.getOptionId(), pet.getLevel());
			if (skillLevel > 0) {
				pet.setTarget(player.getTarget());
				pet.useMagic(SkillData.getInstance().getSkill(data.getOptionId(), skillLevel), null, ctrlPressed, shiftPressed, true);
			}

			if (data.getOptionId() == CommonSkill.PET_SWITCH_STANCE.getId()) {
				pet.switchMode();
			}
		}
	}

	public void useAction(PlayerInstance player, Skill skill, boolean ctrlPressed, boolean shiftPressed) {
		if (player.getTarget() == null) {
			return;
		}

		final PetInstance pet = player.getPet();
		if (pet == null) {
			// player.sendPacket(SystemMessageId.YOU_DON_T_HAVE_A_PET);
		} else if (pet.isUncontrollable()) // no food
		{
			// player.sendPacket(SystemMessageId.WHEN_YOUR_PET_S_HUNGER_GAUGE_IS_AT_0_YOU_CANNOT_USE_YOUR_PET);
		} else if (pet.isBetrayed()) {
			// player.sendPacket(SystemMessageId.YOUR_SERVITOR_IS_UNRESPONSIVE_AND_WILL_NOT_OBEY_ANY_ORDERS);
		}
		// else if ((pet.getLevel() - player.getLevel()) > 20)
		// {
		// player.sendPacket(SystemMessageId.YOUR_PET_IS_TOO_HIGH_LEVEL_TO_CONTROL);
		// }

		else if ((pet._lastSkillTimeStamp + 100) > System.currentTimeMillis()) {
			return;
		} else {
			if (player.getTarget() != null) {
				// @formatter:off
				if ((skill.getTargetType() == TargetType.TARGET)
						|| (skill.getTargetType() == TargetType.ENEMY)
						|| (skill.getTargetType() == TargetType.ENEMY_ONLY)
						|| (skill.getTargetType() == TargetType.ENEMY_NOT))
				// @formatter:on
				{
					if (Util.calculateDistance(player, player.getTarget(), false, false) > 1300) {
						// player.sendPacket(SystemMessageId.THE_DISTANCE_IS_TOO_FAR_AND_SO_THE_CASTING_HAS_BEEN_CANCELLED);
						return;
					}
				}
			}
			pet.setTarget(player.getTarget());
			if (pet.useMagic(skill, null, ctrlPressed, shiftPressed, true)) {
				pet._lastSkillTimeStamp = System.currentTimeMillis();
			}
		}
	}
}
