/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.playeractions;

import club.projectessence.gameserver.handler.IPlayerActionHandler;
import club.projectessence.gameserver.model.ActionDataHolder;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;

/**
 * Tactical Signs setting player action handler.
 *
 * <AUTHOR>
 */
public class TacticalSignUse implements IPlayerActionHandler {
	@Override
	public void useAction(PlayerInstance player, ActionDataHolder data, boolean ctrlPressed, boolean shiftPressed) {
		if ((!player.isInParty() || (player.getTarget() == null) || !player.getTarget().isCreature())) {
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		player.getParty().addTacticalSign(player, data.getOptionId(), (Creature) player.getTarget());
	}
}
