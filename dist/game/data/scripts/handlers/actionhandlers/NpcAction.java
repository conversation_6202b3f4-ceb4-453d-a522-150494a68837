/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.actionhandlers;

import club.projectessence.Config;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.IActionHandler;
import club.projectessence.gameserver.instancemanager.events.GameEvent;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.creature.npc.OnNpcFirstTalk;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.MoveToPawn;

public class NpcAction implements IActionHandler {
	/**
	 * Manage actions when a player click on the Npc.<br>
	 * <br>
	 * <b><u>Actions on first click on the Npc (Select it)</u>:</b><br>
	 * <li>Set the Npc as target of the PlayerInstance player (if necessary)</li>
	 * <li>Send a Server->Client packet MyTargetSelected to the PlayerInstance player (display the select window)</li>
	 * <li>If Npc is autoAttackable, send a Server->Client packet StatusUpdate to the PlayerInstance in order to update Npc HP bar</li>
	 * <li>Send a Server->Client packet ValidateLocation to correct the Npc position and heading on the client</li><br>
	 * <br>
	 * <b><u>Actions on second click on the Npc (Attack it/Intercat with it)</u>:</b><br>
	 * <li>Send a Server->Client packet MyTargetSelected to the PlayerInstance player (display the select window)</li>
	 * <li>If Npc is autoAttackable, notify the PlayerInstance AI with AI_INTENTION_ATTACK (after a height verification)</li>
	 * <li>If Npc is NOT autoAttackable, notify the PlayerInstance AI with AI_INTENTION_INTERACT (after a distance verification) and show message</li><br>
	 * <font color=#FF0000><b><u>Caution</u>: Each group of Server->Client packet must be terminated by a ActionFailed packet in order to avoid that client wait an other packet</b></font><br>
	 * <br>
	 * <b><u>Example of use</u>:</b><br>
	 * <li>Client packet : Action, AttackRequest</li><br>
	 *
	 * @param player The PlayerInstance that start an action on the Npc
	 */
	@Override
	public boolean action(PlayerInstance player, WorldObject target, boolean interact) {
		if (!((Npc) target).canTarget(player)) {
			return false;
		}
		player.setLastFolkNPC((Npc) target);
		// Check if the PlayerInstance already target the Npc
		if (target != player.getTarget()) {
			// Set the target of the PlayerInstance player
			player.setTarget(target);
			// Check if the player is attackable (without a forced attack)
			if (target.isAutoAttackable(player)) {
				((Npc) target).getAI(); // wake up ai
			}
		} else if (interact) {
			// Check if the player is attackable (without a forced attack) and isn't dead
			if (target.isAutoAttackable(player) && !((Creature) target).isAlikeDead()) {
				// Check if target is in LoS
				if (GeoEngine.getInstance().canSeeTarget(player, target)) {
					// Set the PlayerInstance Intention to AI_INTENTION_ATTACK
					player.getAI().setIntention(CtrlIntention.AI_INTENTION_ATTACK, target);
				} else {
					// Send a Server->Client ActionFailed to the PlayerInstance in order to avoid that the client wait another packet
					player.sendPacket(ActionFailed.STATIC_PACKET);
				}
			} else if (!target.isAutoAttackable(player)) {
				// Calculate the distance between the PlayerInstance and the Npc
				if (!((Npc) target).canInteract(player)) {
					// Notify the PlayerInstance AI with AI_INTENTION_INTERACT
					player.getAI().setIntention(CtrlIntention.AI_INTENTION_INTERACT, target);
				} else {
					final Npc npc = (Npc) target;
					if (!player.isSitting()) // Needed for Mystic Tavern Globe
					{
						// Turn NPC to the player.
						player.sendPacket(new MoveToPawn(player, npc, 100));
						if (npc.hasRandomAnimation()) {
							npc.onRandomAnimation(Rnd.get(8));
						}
					}

					// Stop movement when trying to talk to a moving NPC.
					if (npc.isMoving()) {
						player.stopMove(null);
					}

					if (player.isMoving()) {
						player.stopMove(null);
					}

					// Open a chat window on client with the text of the Npc
					if (npc.hasVariables() && npc.getVariables().getBoolean("eventmob", false)) {
						GameEvent.showEventHtml(player, String.valueOf(target.getObjectId()));
					} else {
						if (npc.hasListener(EventType.ON_NPC_QUEST_START)) {
							player.setLastQuestNpcObject(target.getObjectId());
						}
						if (npc.hasListener(EventType.ON_NPC_FIRST_TALK)) {
							EventDispatcher.getInstance().notifyEventAsync(new OnNpcFirstTalk(npc, player), npc);
						} else {
							npc.showChatWindow(player);
						}
					}
					if (Config.PLAYER_MOVEMENT_BLOCK_TIME > 0) {
						player.updateNotMoveUntil();
					}
				}
			}
		}
		return true;
	}

	@Override
	public InstanceType getInstanceType() {
		return InstanceType.Npc;
	}
}