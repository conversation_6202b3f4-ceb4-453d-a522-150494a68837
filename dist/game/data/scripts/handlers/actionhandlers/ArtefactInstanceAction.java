/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.actionhandlers;

import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.handler.IActionHandler;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

public class ArtefactInstanceAction implements IActionHandler {
	/**
	 * Manage actions when a player click on the ArtefactInstance.<br>
	 * <br>
	 * <b><u>Actions</u>:</b><br>
	 * <li>Set the NpcInstance as target of the PlayerInstance player (if necessary)</li>
	 * <li>Send a Server->Client packet MyTargetSelected to the PlayerInstance player (display the select window)</li>
	 * <li>Send a Server->Client packet ValidateLocation to correct the NpcInstance position and heading on the client</li><br>
	 * <br>
	 * <b><u>Example of use</u>:</b><br>
	 * <li>Client packet : Action, AttackRequest</li>
	 */
	@Override
	public boolean action(PlayerInstance player, WorldObject target, boolean interact) {
		if (!((Npc) target).canTarget(player)) {
			return false;
		}
		if (player.getTarget() != target) {
			player.setTarget(target);
		}
		// Calculate the distance between the PlayerInstance and the NpcInstance
		else if (interact && !((Npc) target).canInteract(player)) {
			// Notify the PlayerInstance AI with AI_INTENTION_INTERACT
			player.getAI().setIntention(CtrlIntention.AI_INTENTION_INTERACT, target);
		}
		return true;
	}

	@Override
	public InstanceType getInstanceType() {
		return InstanceType.ArtefactInstance;
	}
}