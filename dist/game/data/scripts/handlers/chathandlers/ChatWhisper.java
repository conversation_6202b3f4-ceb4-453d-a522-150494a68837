/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.chathandlers;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.handler.IChatHandler;
import club.projectessence.gameserver.model.BlockList;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

/**
 * Tell chat handler.
 *
 * <AUTHOR>
 */
public class ChatWhisper implements IChatHandler {
	private static final ChatType[] CHAT_TYPES =
			{
					ChatType.WHISPER
			};

	@Override
	public void handleChat(ChatType type, PlayerInstance activeChar, String target, String text, int sharedPositionId) {
		if (activeChar.isChatBanned() && Config.BAN_CHAT_CHANNELS.contains(type)) {
			activeChar.sendPacket(SystemMessageId.CHATTING_IS_CURRENTLY_PROHIBITED_IF_YOU_TRY_TO_CHAT_BEFORE_THE_PROHIBITION_IS_REMOVED_THE_PROHIBITION_TIME_WILL_INCREASE_EVEN_FURTHER);
			return;
		}

		if (Config.JAIL_DISABLE_CHAT && activeChar.isJailed() && !activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS)) {
			activeChar.sendPacket(SystemMessageId.CHATTING_IS_CURRENTLY_PROHIBITED);
			return;
		}

		// Return if no target is set
		if (target == null) {
			return;
		}

		final PlayerInstance receiver = World.getInstance().getPlayer(target);
		if ((receiver != null) && !receiver.isSilenceMode(activeChar.getObjectId())) {
			if (Config.JAIL_DISABLE_CHAT && receiver.isJailed() && !activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS)) {
				activeChar.sendMessage("Player is in jail.");
				return;
			}
			if (receiver.isChatBanned()) {
				activeChar.sendPacket(SystemMessageId.THAT_PERSON_IS_IN_MESSAGE_REFUSAL_MODE);
				return;
			}
			if ((receiver.getClient() == null) || (receiver.getClient().isDetached() && !receiver.isFakePlayer())) {
				activeChar.sendMessage("Player is in offline mode.");
				return;
			}
			if ((activeChar.getLevel() < Config.MINIMUM_CHAT_LEVEL) && !activeChar.getWhisperers().contains(receiver.getObjectId()) && !activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS)) {
				activeChar.sendPacket(new SystemMessage(SystemMessageId.CHARACTERS_LV_S1_OR_LOWER_CAN_RESPOND_TO_A_WHISPER_BUT_CANNOT_INITIATE_IT).addInt(Config.MINIMUM_CHAT_LEVEL));
				return;
			}
			if (!BlockList.isBlocked(receiver, activeChar)) {
				// Allow reciever to send PMs to this char, which is in silence mode.
				if (Config.SILENCE_MODE_EXCLUDE && activeChar.isSilenceMode()) {
					activeChar.addSilenceModeExcluded(receiver.getObjectId());
				}

				receiver.getWhisperers().add(activeChar.getObjectId());

				String en_ru[] = IChatHandler.getItemLinksTranslation(activeChar, text);
				final CreatureSay cs;
				switch (receiver.getLang()) {
					case RUSSIA: {
						cs = new CreatureSay(activeChar, receiver, activeChar.getName(), type, en_ru[1], sharedPositionId);
						break;
					}
					default: {
						cs = new CreatureSay(activeChar, receiver, activeChar.getName(), type, en_ru[0], sharedPositionId);
						break;
					}
				}
				receiver.sendPacket(cs);

				activeChar.sendPacket(new CreatureSay(activeChar, receiver, "->" + receiver.getName(), type, text, sharedPositionId));
			} else {
				activeChar.sendPacket(SystemMessageId.THAT_PERSON_IS_IN_MESSAGE_REFUSAL_MODE);
			}
		} else {
			activeChar.sendPacket(SystemMessageId.THAT_PLAYER_IS_NOT_ONLINE);
		}
	}

	@Override
	public void handleRequestInvitePartyPacket(ChatType type, PlayerInstance activeChar, byte requestType) {
	}

	@Override
	public ChatType[] getChatTypeList() {
		return CHAT_TYPES;
	}
}