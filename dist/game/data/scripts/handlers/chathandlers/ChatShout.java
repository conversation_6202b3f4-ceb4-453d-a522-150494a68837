/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.chathandlers;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.handler.IChatHandler;
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.BlockList;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.ExRequestInviteParty;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

/**
 * Shout chat handler.
 *
 * <AUTHOR>
 */
public class ChatShout implements IChatHandler {
	private static final ChatType[] CHAT_TYPES =
			{
					ChatType.SHOUT,
			};

	@Override
	public void handleChat(ChatType type, PlayerInstance activeChar, String target, String text, int sharedPositionId) {
		if (activeChar.isChatBanned() && Config.BAN_CHAT_CHANNELS.contains(type)) {
			activeChar.sendPacket(SystemMessageId.CHATTING_IS_CURRENTLY_PROHIBITED_IF_YOU_TRY_TO_CHAT_BEFORE_THE_PROHIBITION_IS_REMOVED_THE_PROHIBITION_TIME_WILL_INCREASE_EVEN_FURTHER);
			return;
		}
		if (Config.JAIL_DISABLE_CHAT && activeChar.isJailed() && !activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS)) {
			activeChar.sendPacket(SystemMessageId.CHATTING_IS_CURRENTLY_PROHIBITED);
			return;
		}
		if ((activeChar.getLevel() < Config.MINIMUM_CHAT_LEVEL) && !activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS)) {
			activeChar.sendPacket(new SystemMessage(SystemMessageId.SHOUT_CANNOT_BE_USED_BY_CHARACTERS_LV_S1_OR_LOWER).addInt(Config.MINIMUM_CHAT_LEVEL));
			return;
		}

		// final CreatureSay cs = new CreatureSay(activeChar, type, activeChar.getName(), text, sharedPositionId);
		String en_ru[] = IChatHandler.getItemLinksTranslation(activeChar, text);
//		if (Util.containsCyrylic(en_ru[0])) {
//			activeChar.sendPacket(new ExShowScreenMessage("В общем чате доступен исключительно английский язык", ExShowScreenMessage.TOP_CENTER, 3000, 0, true, true));
//			activeChar.sendMessage("В общем чате доступен исключительно английский язык");
//			return;
//		}
		final CreatureSay csEn = new CreatureSay(activeChar, type, activeChar.getAppearance().getVisibleName(), en_ru[0], sharedPositionId);
		final CreatureSay csRu = new CreatureSay(activeChar, type, activeChar.getAppearance().getVisibleName(), en_ru[1], sharedPositionId);
		if (Config.DEFAULT_GLOBAL_CHAT.equalsIgnoreCase("on") || (Config.DEFAULT_GLOBAL_CHAT.equalsIgnoreCase("gm") && activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS))) {
			final int region = MapRegionManager.getInstance().getMapRegionLocId(activeChar);
			for (PlayerInstance player : World.getInstance().getPlayers()) {
				if ((region == MapRegionManager.getInstance().getMapRegionLocId(player)) && !BlockList.isBlocked(player, activeChar) && (player.getInstanceId() == activeChar.getInstanceId()) && !BlockList.isBlocked(activeChar, player)) {
					switch (player.getLang()) {
						case RUSSIA: {
							player.sendPacket(csRu);
							break;
						}
						default: {
							player.sendPacket(csEn);
							break;
						}
					}
				}
			}
		} else if (Config.DEFAULT_GLOBAL_CHAT.equalsIgnoreCase("global")) {
			if (!activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS) && !activeChar.getFloodProtectors().getGlobalChat().tryPerformAction("global chat")) {
				activeChar.sendMessage("Do not spam shout channel.");
				return;
			}

			for (PlayerInstance player : World.getInstance().getPlayers()) {
				if (!BlockList.isBlocked(player, activeChar)) {
					switch (player.getLang()) {
						case RUSSIA:
							player.sendPacket(csRu);
							break;
						default:
							player.sendPacket(csEn);
							break;
					}
				}
			}
		}
	}

	@Override
	public void handleRequestInvitePartyPacket(ChatType type, PlayerInstance activeChar, byte requestType) {
		if (activeChar.isChatBanned() && Config.BAN_CHAT_CHANNELS.contains(type)) {
			activeChar.sendPacket(SystemMessageId.CHATTING_IS_CURRENTLY_PROHIBITED_IF_YOU_TRY_TO_CHAT_BEFORE_THE_PROHIBITION_IS_REMOVED_THE_PROHIBITION_TIME_WILL_INCREASE_EVEN_FURTHER);
			return;
		}
		if (Config.JAIL_DISABLE_CHAT && activeChar.isJailed() && !activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS)) {
			activeChar.sendPacket(SystemMessageId.CHATTING_IS_CURRENTLY_PROHIBITED);
			return;
		}
		if ((activeChar.getLevel() < Config.MINIMUM_CHAT_LEVEL) && !activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS)) {
			activeChar.sendPacket(new SystemMessage(SystemMessageId.SHOUT_CANNOT_BE_USED_BY_CHARACTERS_LV_S1_OR_LOWER).addInt(Config.MINIMUM_CHAT_LEVEL));
			return;
		}

		int rankGrade = 0;
		int rank = RankManager.getInstance().getPlayerRealRank(activeChar.getObjectId());
		if ((rank == 0) || (rank > 100)) {
			rankGrade = 0;
		} else if (rank == 1) {
			rankGrade = 1;
		} else if (rank <= 30) {
			rankGrade = 2;
		} else if (rank <= 100) {
			rankGrade = 3;
		}
		final ExRequestInviteParty packet = new ExRequestInviteParty(activeChar.getName(), requestType, (byte) type.getClientId(), (byte) rankGrade, (byte) (activeChar.getClan() == null ? 0 : activeChar.getClan().getCastleId()), activeChar.getObjectId());
		if (Config.DEFAULT_GLOBAL_CHAT.equalsIgnoreCase("on") || (Config.DEFAULT_GLOBAL_CHAT.equalsIgnoreCase("gm") && activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS))) {
			final int region = MapRegionManager.getInstance().getMapRegionLocId(activeChar);
			for (PlayerInstance player : World.getInstance().getPlayers()) {
				if ((region == MapRegionManager.getInstance().getMapRegionLocId(player)) && !BlockList.isBlocked(player, activeChar) && (player.getInstanceId() == activeChar.getInstanceId()) && !BlockList.isBlocked(activeChar, player)) {
					player.sendPacket(packet);
				}
			}
		} else if (Config.DEFAULT_GLOBAL_CHAT.equalsIgnoreCase("global")) {
			if (!activeChar.canOverrideCond(PlayerCondOverride.CHAT_CONDITIONS) && !activeChar.getFloodProtectors().getGlobalChat().tryPerformAction("global chat")) {
				activeChar.sendMessage("Do not spam shout channel.");
				return;
			}
			for (PlayerInstance player : World.getInstance().getPlayers()) {
				if (!BlockList.isBlocked(player, activeChar)) {
					player.sendPacket(packet);
				}
			}
		}
	}

	@Override
	public ChatType[] getChatTypeList() {
		return CHAT_TYPES;
	}
}