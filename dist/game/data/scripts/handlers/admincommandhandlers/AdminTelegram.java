package handlers.admincommandhandlers;

import club.projectessence.gameserver.handler.IAdminCommandHandler;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.telegram.TelegramBotManager;
import club.projectessence.gameserver.telegram.TelegramConfig;
import club.projectessence.gameserver.telegram.TelegramWebhookHandler;
import club.projectessence.gameserver.util.BuilderUtil;

/**
 * Admin command handler for Telegram Bot management
 * 
 * <AUTHOR> Team
 */
public class AdminTelegram implements IAdminCommandHandler
{
    private static final String[] ADMIN_COMMANDS = {
        "admin_telegram",
        "admin_telegram_status",
        "admin_telegram_config",
        "admin_telegram_start",
        "admin_telegram_stop",
        "admin_telegram_reload",
        "admin_telegram_addadmin",
        "admin_telegram_removeadmin",
        "admin_telegram_test"
    };
    
    @Override
    public boolean useAdminCommand(String command, PlayerInstance activeChar)
    {
        if (command.equals("admin_telegram")) {
            showMainMenu(activeChar);
        }
        else if (command.equals("admin_telegram_status")) {
            showStatus(activeChar);
        }
        else if (command.equals("admin_telegram_config")) {
            showConfig(activeChar);
        }
        else if (command.equals("admin_telegram_start")) {
            startTelegramBot(activeChar);
        }
        else if (command.equals("admin_telegram_stop")) {
            stopTelegramBot(activeChar);
        }
        else if (command.equals("admin_telegram_reload")) {
            reloadConfig(activeChar);
        }
        else if (command.startsWith("admin_telegram_addadmin")) {
            handleAddAdmin(activeChar, command);
        }
        else if (command.startsWith("admin_telegram_removeadmin")) {
            handleRemoveAdmin(activeChar, command);
        }
        else if (command.equals("admin_telegram_test")) {
            testTelegramBot(activeChar);
        }
        
        return true;
    }
    
    /**
     * Show main Telegram management menu
     */
    private void showMainMenu(PlayerInstance activeChar)
    {
        StringBuilder html = new StringBuilder();
        html.append("<html><body>");
        html.append("<title>Telegram Bot Management</title>");
        html.append("<center>");
        html.append("<table width=270>");
        html.append("<tr><td width=40><button value=\"Main\" action=\"bypass -h admin_admin\" width=40 height=15 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>");
        html.append("<td width=180><center>Telegram Bot Management</center></td>");
        html.append("<td width=40><button value=\"Back\" action=\"bypass -h admin_admin\" width=40 height=15 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("</table>");
        html.append("</center><br>");
        
        // Status section
        html.append("<table width=270>");
        html.append("<tr><td><center><font color=\"LEVEL\">Bot Status</font></center></td></tr>");
        html.append("<tr><td><center>");
        if (TelegramConfig.TELEGRAM_BOT_ENABLED && TelegramConfig.isProperlyConfigured()) {
            html.append("<font color=\"00FF00\">✅ Enabled & Configured</font>");
        } else if (TelegramConfig.TELEGRAM_BOT_ENABLED) {
            html.append("<font color=\"FFFF00\">⚠️ Enabled but Not Configured</font>");
        } else {
            html.append("<font color=\"FF0000\">❌ Disabled</font>");
        }
        html.append("</center></td></tr>");
        html.append("</table><br>");
        
        // Action buttons
        html.append("<table width=270>");
        html.append("<tr><td><button value=\"Status\" action=\"bypass -h admin_telegram_status\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>");
        html.append("<td><button value=\"Config\" action=\"bypass -h admin_telegram_config\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>");
        html.append("<td><button value=\"Reload\" action=\"bypass -h admin_telegram_reload\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        
        html.append("<tr><td><button value=\"Start\" action=\"bypass -h admin_telegram_start\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>");
        html.append("<td><button value=\"Stop\" action=\"bypass -h admin_telegram_stop\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>");
        html.append("<td><button value=\"Test\" action=\"bypass -h admin_telegram_test\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("</table><br>");
        
        // Admin management
        html.append("<table width=270>");
        html.append("<tr><td><center><font color=\"LEVEL\">Admin Management</font></center></td></tr>");
        html.append("<tr><td><center>");
        html.append("<edit var=\"userid\" width=120 height=15>");
        html.append("<button value=\"Add Admin\" action=\"bypass -h admin_telegram_addadmin $userid\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
        html.append("</center></td></tr>");
        html.append("</table>");
        
        html.append("</body></html>");
        
        BuilderUtil.sendHtmlMessage(activeChar, html.toString());
    }
    
    /**
     * Show detailed status
     */
    private void showStatus(PlayerInstance activeChar)
    {
        StringBuilder sb = new StringBuilder();
        sb.append("=== Telegram Bot Status ===\n");
        sb.append("Enabled: ").append(TelegramConfig.TELEGRAM_BOT_ENABLED).append("\n");
        sb.append("Properly Configured: ").append(TelegramConfig.isProperlyConfigured()).append("\n");
        sb.append("Use Webhook: ").append(TelegramConfig.USE_WEBHOOK).append("\n");
        sb.append("Webhook Port: ").append(TelegramConfig.WEBHOOK_PORT).append("\n");
        sb.append("Command Cooldown: ").append(TelegramConfig.COMMAND_COOLDOWN).append("ms\n");
        sb.append("Max Points Per Command: ").append(TelegramConfig.MAX_PRIME_POINTS_PER_COMMAND).append("\n");
        sb.append("Strict Authorization: ").append(TelegramConfig.STRICT_AUTHORIZATION).append("\n");
        sb.append("Admin Chat IDs: ").append(TelegramConfig.ADMIN_CHAT_IDS.isEmpty() ? "None" : TelegramConfig.ADMIN_CHAT_IDS).append("\n");
        sb.append("Admin Usernames: ").append(TelegramConfig.ADMIN_USERNAMES.isEmpty() ? "None" : TelegramConfig.ADMIN_USERNAMES).append("\n");
        
        BuilderUtil.sendSysMessage(activeChar, sb.toString());
        showMainMenu(activeChar);
    }
    
    /**
     * Show configuration details
     */
    private void showConfig(PlayerInstance activeChar)
    {
        StringBuilder sb = new StringBuilder();
        sb.append("=== Telegram Bot Configuration ===\n");
        sb.append(TelegramConfig.getConfigSummary()).append("\n");
        sb.append("Bot Token: ").append(TelegramConfig.BOT_TOKEN.isEmpty() ? "Not Set" : "Set (Hidden)").append("\n");
        sb.append("Webhook URL: ").append(TelegramConfig.WEBHOOK_URL.isEmpty() ? "Not Set" : TelegramConfig.WEBHOOK_URL).append("\n");
        sb.append("Config Valid: ").append(TelegramConfig.validateConfig()).append("\n");
        
        if (!TelegramConfig.validateConfig()) {
            sb.append("\n⚠️ Configuration issues detected! Check server logs for details.");
        }
        
        BuilderUtil.sendSysMessage(activeChar, sb.toString());
        showMainMenu(activeChar);
    }
    
    /**
     * Start Telegram bot
     */
    private void startTelegramBot(PlayerInstance activeChar)
    {
        if (!TelegramConfig.TELEGRAM_BOT_ENABLED) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Telegram bot is disabled in config. Enable it first.");
            showMainMenu(activeChar);
            return;
        }
        
        if (!TelegramConfig.isProperlyConfigured()) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Telegram bot is not properly configured. Check bot token and other settings.");
            showMainMenu(activeChar);
            return;
        }
        
        try {
            if (TelegramConfig.USE_WEBHOOK) {
                TelegramWebhookHandler.startWebhookServer();
                BuilderUtil.sendSysMessage(activeChar, "✅ Telegram webhook server started on port " + TelegramConfig.WEBHOOK_PORT);
            } else {
                BuilderUtil.sendSysMessage(activeChar, "ℹ️ Polling mode not implemented yet. Use webhook mode.");
            }
        } catch (Exception e) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Failed to start Telegram bot: " + e.getMessage());
        }
        
        showMainMenu(activeChar);
    }
    
    /**
     * Stop Telegram bot
     */
    private void stopTelegramBot(PlayerInstance activeChar)
    {
        try {
            TelegramWebhookHandler.stopWebhookServer();
            BuilderUtil.sendSysMessage(activeChar, "✅ Telegram webhook server stopped");
        } catch (Exception e) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Failed to stop Telegram bot: " + e.getMessage());
        }
        
        showMainMenu(activeChar);
    }
    
    /**
     * Reload configuration
     */
    private void reloadConfig(PlayerInstance activeChar)
    {
        try {
            TelegramConfig.reloadConfig();
            BuilderUtil.sendSysMessage(activeChar, "✅ Telegram configuration reloaded");
        } catch (Exception e) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Failed to reload config: " + e.getMessage());
        }
        
        showMainMenu(activeChar);
    }
    
    /**
     * Handle add admin command
     */
    private void handleAddAdmin(PlayerInstance activeChar, String command)
    {
        String[] parts = command.split(" ");
        if (parts.length < 2) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Usage: admin_telegram_addadmin <user_id_or_username>");
            showMainMenu(activeChar);
            return;
        }
        
        String userIdOrUsername = parts[1];
        
        try {
            // Try to parse as user ID first
            long userId = Long.parseLong(userIdOrUsername);
            TelegramBotManager.addAuthorizedAdmin(userId, "Admin_" + userId);
            BuilderUtil.sendSysMessage(activeChar, "✅ Added admin with user ID: " + userId);
        } catch (NumberFormatException e) {
            // It's a username
            TelegramBotManager.addAuthorizedAdmin(0L, userIdOrUsername);
            BuilderUtil.sendSysMessage(activeChar, "✅ Added admin with username: " + userIdOrUsername);
        }
        
        showMainMenu(activeChar);
    }
    
    /**
     * Handle remove admin command
     */
    private void handleRemoveAdmin(PlayerInstance activeChar, String command)
    {
        String[] parts = command.split(" ");
        if (parts.length < 2) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Usage: admin_telegram_removeadmin <user_id>");
            showMainMenu(activeChar);
            return;
        }
        
        try {
            long userId = Long.parseLong(parts[1]);
            TelegramBotManager.removeAuthorizedAdmin(userId);
            BuilderUtil.sendSysMessage(activeChar, "✅ Removed admin with user ID: " + userId);
        } catch (NumberFormatException e) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Invalid user ID: " + parts[1]);
        }
        
        showMainMenu(activeChar);
    }
    
    /**
     * Test Telegram bot
     */
    private void testTelegramBot(PlayerInstance activeChar)
    {
        if (!TelegramConfig.isProperlyConfigured()) {
            BuilderUtil.sendSysMessage(activeChar, "❌ Telegram bot is not properly configured");
            showMainMenu(activeChar);
            return;
        }
        
        // This would send a test message to configured admin chats
        BuilderUtil.sendSysMessage(activeChar, "ℹ️ Test functionality not implemented yet. Check logs for webhook activity.");
        showMainMenu(activeChar);
    }
    
    @Override
    public String[] getAdminCommandList()
    {
        return ADMIN_COMMANDS;
    }
}
