/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.admincommandhandlers;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.data.SpawnTable;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.data.xml.TeleportListData;
import club.projectessence.gameserver.enums.AdminTeleportType;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.IAdminCommandHandler;
import club.projectessence.gameserver.instancemanager.DBSpawnManager;
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.GrandBossInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.instance.RaidBossInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.holders.TeleportListHolder;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;
import club.projectessence.gameserver.util.BuilderUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.NoSuchElementException;
import java.util.StringTokenizer;
import java.util.logging.Logger;

/**
 * This class handles following admin commands: - show_moves - show_teleport - teleport_to_character - move_to - teleport_character
 *
 * @version $Revision: 1.3.2.6.2.4 $ $Date: 2005/04/11 10:06:06 $ con.close() change and small typo fix by Zoey76 24/02/2011
 */
public class AdminTeleport implements IAdminCommandHandler {
	private static final Logger LOGGER = Logger.getLogger(AdminTeleport.class.getName());

	private static final String[] ADMIN_COMMANDS =
			{
					"admin_show_moves",
					"admin_show_moves_other",
					"admin_show_teleport",
					"admin_teleport_to_character",
					"admin_teleportto",
					"admin_teleport_id",
					"admin_teleport",
					"admin_move_to",
					"admin_teleport_character",
					"admin_recall",
					"admin_walk",
					"teleportto",
					"recall",
					"admin_recall_npc",
					"admin_gonorth",
					"admin_gosouth",
					"admin_goeast",
					"admin_gowest",
					"admin_goup",
					"admin_godown",
					"admin_tele",
					"admin_teleto",
					"admin_instant_move",
					"admin_sendhome"
			};

	@Override
	public boolean useAdminCommand(String command, PlayerInstance activeChar) {
		if (command.equals("admin_instant_move")) {
			BuilderUtil.sendSysMessage(activeChar, "Instant move ready. Click where you want to go.");
			activeChar.setTeleMode(AdminTeleportType.DEMONIC);
		} else if (command.equals("admin_teleto sayune")) {
			BuilderUtil.sendSysMessage(activeChar, "Sayune move ready. Click where you want to go.");
			activeChar.setTeleMode(AdminTeleportType.SAYUNE);
		} else if (command.equals("admin_teleto charge")) {
			BuilderUtil.sendSysMessage(activeChar, "Charge move ready. Click where you want to go.");
			activeChar.setTeleMode(AdminTeleportType.CHARGE);
		} else if (command.equals("admin_teleto end")) {
			activeChar.setTeleMode(AdminTeleportType.NORMAL);
		} else if (command.equals("admin_show_moves")) {
			AdminHtml.showAdminHtml(activeChar, "teleports.htm");
		} else if (command.equals("admin_show_moves_other")) {
			AdminHtml.showAdminHtml(activeChar, "tele/other.html");
		} else if (command.equals("admin_show_teleport")) {
			showTeleportCharWindow(activeChar);
		} else if (command.equals("admin_recall_npc")) {
			recallNPC(activeChar);
		} else if (command.equals("admin_teleport_to_character")) {
			teleportToCharacter(activeChar, activeChar.getTarget());
		} else if (command.startsWith("admin_walk")) {
			try {
				final String val = command.substring(11);
				final StringTokenizer st = new StringTokenizer(val);
				final int x = Integer.parseInt(st.nextToken());
				final int y = Integer.parseInt(st.nextToken());
				final int z = Integer.parseInt(st.nextToken());
				activeChar.getAI().setIntention(CtrlIntention.AI_INTENTION_MOVE_TO, new Location(x, y, z, 0));
			} catch (Exception e) {
			}
		} else if (command.startsWith("admin_move_to")) {
			try {
				final String val = command.substring(14);
				teleportTo(activeChar, val);
			} catch (StringIndexOutOfBoundsException e) {
				// Case of empty or missing coordinates
				AdminHtml.showAdminHtml(activeChar, "teleports.htm");
			} catch (NumberFormatException nfe) {
				BuilderUtil.sendSysMessage(activeChar, "Usage: //move_to <x> <y> <z>");
				AdminHtml.showAdminHtml(activeChar, "teleports.htm");
			}
		} else if (command.startsWith("admin_teleport_character")) {
			try {
				final String val = command.substring(25);
				teleportCharacter(activeChar, val);
			} catch (StringIndexOutOfBoundsException e) {
				// Case of empty coordinates
				BuilderUtil.sendSysMessage(activeChar, "Wrong or no Coordinates given.");
				showTeleportCharWindow(activeChar); // back to character teleport
			}
		} else if (command.startsWith("admin_teleportto ")) {
			try {
				final String targetName = command.substring(17);
				final PlayerInstance player = World.getInstance().getPlayer(targetName);
				teleportToCharacter(activeChar, player);
			} catch (StringIndexOutOfBoundsException e) {
			}
		} else if (command.startsWith("admin_teleport_id")) {
			try {
				final StringTokenizer st = new StringTokenizer(command, " ");
				st.nextToken();
				final SkillHolder TELEPORT = new SkillHolder(60018, 1);
				final int id = Integer.parseInt(st.nextToken());
				final TeleportListHolder teleport = TeleportListData.getInstance().getTeleport(id);

				activeChar.abortCast();
				activeChar.stopMove(null);
				AutoPlayTaskManager.getInstance().stopAutoPlay(activeChar);
				activeChar.setTeleportLoc(teleport.getLocation());
				activeChar.doCast(TELEPORT.getSkill());
			} catch (Exception e) {
				BuilderUtil.sendSysMessage(activeChar, "Wrong ID!");
			}
		} else if (command.startsWith("admin_teleport")) {
			try {
				final StringTokenizer st = new StringTokenizer(command, " ");
				st.nextToken();
				String xStr = st.nextToken();
				if (xStr.startsWith("x=\"") && xStr.endsWith("\"")) {
					xStr = xStr.substring(3, xStr.length() - 1);
				}
				final int x = (int) Float.parseFloat(xStr);
				String yStr = st.nextToken();
				if (yStr.startsWith("y=\"") && yStr.endsWith("\"")) {
					yStr = yStr.substring(3, yStr.length() - 1);
				}
				final int y = (int) Float.parseFloat(yStr);
				String zStr = st.hasMoreTokens() ? st.nextToken() : String.valueOf(GeoEngine.getInstance().getHeight(x, y, 10000));
				if (zStr.startsWith("z=\"") && zStr.endsWith("\"")) {
					zStr = zStr.substring(3, zStr.length() - 1);
				}
				final int z = (int) Float.parseFloat(zStr);
				activeChar.teleToLocation(x, y, z);
			} catch (Exception e) {
				BuilderUtil.sendSysMessage(activeChar, "Wrong coordinates!");
			}
		} else if (command.startsWith("admin_recall ")) {
			try {
				final String[] param = command.split(" ");
				if (param.length != 2) {
					BuilderUtil.sendSysMessage(activeChar, "Usage: //recall <playername>");
					return false;
				}
				final String targetName = param[1];
				final PlayerInstance player = World.getInstance().getPlayer(targetName);
				if (player != null) {
					teleportCharacter(player, activeChar.getLocation(), activeChar);
				} else {
					changeCharacterPosition(activeChar, targetName);
				}
			} catch (StringIndexOutOfBoundsException e) {
			}
		} else if (command.equals("admin_tele")) {
			showTeleportWindow(activeChar);
		} else if (command.startsWith("admin_go")) {
			int intVal = 150;
			int x = activeChar.getX();
			int y = activeChar.getY();
			int z = activeChar.getZ();
			try {
				final String val = command.substring(8);
				final StringTokenizer st = new StringTokenizer(val);
				final String dir = st.nextToken();
				if (st.hasMoreTokens()) {
					intVal = Integer.parseInt(st.nextToken());
				}
				if (dir.equals("east")) {
					x += intVal;
				} else if (dir.equals("west")) {
					x -= intVal;
				} else if (dir.equals("north")) {
					y -= intVal;
				} else if (dir.equals("south")) {
					y += intVal;
				} else if (dir.equals("up")) {
					z += intVal;
				} else if (dir.equals("down")) {
					z -= intVal;
				}
				activeChar.teleToLocation(new Location(x, y, z));
				showTeleportWindow(activeChar);
			} catch (Exception e) {
				BuilderUtil.sendSysMessage(activeChar, "Usage: //go<north|south|east|west|up|down> [offset] (default 150)");
			}
		} else if (command.startsWith("admin_sendhome")) {
			final StringTokenizer st = new StringTokenizer(command, " ");
			st.nextToken(); // Skip command.
			if (st.countTokens() > 1) {
				BuilderUtil.sendSysMessage(activeChar, "Usage: //sendhome <playername>");
			} else if (st.countTokens() == 1) {
				final String name = st.nextToken();
				final PlayerInstance player = World.getInstance().getPlayer(name);
				if (player == null) {
					activeChar.sendPacket(SystemMessageId.THAT_PLAYER_IS_NOT_ONLINE);
					return false;
				}
				teleportHome(player);
			} else {
				final WorldObject target = activeChar.getTarget();
				if ((target != null) && target.isPlayer()) {
					teleportHome(target.getActingPlayer());
				} else {
					activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
				}
			}
		}
		return true;
	}

	@Override
	public String[] getAdminCommandList() {
		return ADMIN_COMMANDS;
	}

	/**
	 * This method sends a player to it's home town.
	 *
	 * @param player the player to teleport.
	 */
	private void teleportHome(PlayerInstance player) {
		String regionName;
		switch (player.getRace()) {
			case ELF: {
				regionName = "elf_town";
				break;
			}
			case DARK_ELF: {
				regionName = "darkelf_town";
				break;
			}
			case ORC: {
				regionName = "orc_town";
				break;
			}
			case DWARF: {
				regionName = "dwarf_town";
				break;
			}
			case KAMAEL: {
				regionName = "kamael_town";
				break;
			}
			case HUMAN:
			default: {
				regionName = "talking_island_town";
			}
		}

		player.teleToLocation(MapRegionManager.getInstance().getMapRegionByName(regionName).getSpawnLoc(), true, null);
	}

	private void teleportTo(PlayerInstance activeChar, String coords) {
		try {
			final StringTokenizer st = new StringTokenizer(coords);
			final int x = Integer.parseInt(st.nextToken());
			final int y = Integer.parseInt(st.nextToken());
			final int z = Integer.parseInt(st.nextToken());
			activeChar.getAI().setIntention(CtrlIntention.AI_INTENTION_IDLE);
			activeChar.teleToLocation(x, y, z, null);
			BuilderUtil.sendSysMessage(activeChar, "You have been teleported to " + coords);
		} catch (NoSuchElementException nsee) {
			BuilderUtil.sendSysMessage(activeChar, "Wrong or no Coordinates given.");
		}
	}

	private void showTeleportWindow(PlayerInstance activeChar) {
		AdminHtml.showAdminHtml(activeChar, "move.htm");
	}

	private void showTeleportCharWindow(PlayerInstance activeChar) {
		final WorldObject target = activeChar.getTarget();
		PlayerInstance player = null;
		if ((target != null) && target.isPlayer()) {
			player = (PlayerInstance) target;
		} else {
			activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
			return;
		}
		final NpcHtmlMessage adminReply = new NpcHtmlMessage(0, 1);
		final String replyMSG = "<html><title>Teleport Character</title><body>The character you will teleport is " + player.getName() + ".<br>Co-ordinate x<edit var=\"char_cord_x\" width=110>Co-ordinate y<edit var=\"char_cord_y\" width=110>Co-ordinate z<edit var=\"char_cord_z\" width=110><button value=\"Teleport\" action=\"bypass -h admin_teleport_character $char_cord_x $char_cord_y $char_cord_z\" width=60 height=15 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"><button value=\"Teleport near you\" action=\"bypass -h admin_teleport_character " + activeChar.getX() + " " + activeChar.getY() + " " + activeChar.getZ() + "\" width=115 height=15 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"><center><button value=\"Back\" action=\"bypass -h admin_current_player\" width=40 height=15 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></center></body></html>";
		adminReply.setHtml(replyMSG);
		activeChar.sendPacket(adminReply);
	}

	private void teleportCharacter(PlayerInstance activeChar, String coords) {
		final WorldObject target = activeChar.getTarget();
		PlayerInstance player = null;
		if ((target != null) && target.isPlayer()) {
			player = (PlayerInstance) target;
		} else {
			activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
			return;
		}

		if (player.getObjectId() == activeChar.getObjectId()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_USE_THIS_ON_YOURSELF);
		} else {
			try {
				final StringTokenizer st = new StringTokenizer(coords);
				final String x1 = st.nextToken();
				final int x = Integer.parseInt(x1);
				final String y1 = st.nextToken();
				final int y = Integer.parseInt(y1);
				final String z1 = st.nextToken();
				final int z = Integer.parseInt(z1);
				teleportCharacter(player, new Location(x, y, z), null);
			} catch (NoSuchElementException nsee) {
			}
		}
	}

	/**
	 * @param player
	 * @param loc
	 * @param activeChar
	 */
	private void teleportCharacter(PlayerInstance player, Location loc, PlayerInstance activeChar) {
		if (player != null) {
			// Check for jail
			if (player.isJailed()) {
				BuilderUtil.sendSysMessage(activeChar, "Sorry, player " + player.getName() + " is in Jail.");
			} else {
				BuilderUtil.sendSysMessage(activeChar, "You have recalled " + player.getName());
				player.sendMessage("Admin is teleporting you.");
				player.getAI().setIntention(CtrlIntention.AI_INTENTION_IDLE);
				player.teleToLocation(loc, true, activeChar.getInstanceWorld());
			}
		}
	}

	private void teleportToCharacter(PlayerInstance activeChar, WorldObject target) {
		if ((target == null) || !target.isPlayer()) {
			activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
			return;
		}

		final PlayerInstance player = target.getActingPlayer();
		if (player.getObjectId() == activeChar.getObjectId()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_USE_THIS_ON_YOURSELF);
		} else {
			activeChar.getAI().setIntention(CtrlIntention.AI_INTENTION_IDLE);
			activeChar.teleToLocation(player, true, player.getInstanceWorld());
			BuilderUtil.sendSysMessage(activeChar, "You have teleported to character " + player.getName() + ".");
		}
	}

	private void changeCharacterPosition(PlayerInstance activeChar, String name) {
		final int x = activeChar.getX();
		final int y = activeChar.getY();
		final int z = activeChar.getZ();
		try (Connection con = DatabaseFactory.getConnection()) {
			final PreparedStatement statement = con.prepareStatement("UPDATE characters SET x=?, y=?, z=? WHERE char_name=?");
			statement.setInt(1, x);
			statement.setInt(2, y);
			statement.setInt(3, z);
			statement.setString(4, name);
			statement.execute();
			final int count = statement.getUpdateCount();
			statement.close();
			if (count == 0) {
				BuilderUtil.sendSysMessage(activeChar, "Character not found or position unaltered.");
			} else {
				BuilderUtil.sendSysMessage(activeChar, "Player's [" + name + "] position is now set to (" + x + "," + y + "," + z + ").");
			}
		} catch (SQLException se) {
			BuilderUtil.sendSysMessage(activeChar, "SQLException while changing offline character's position");
		}
	}

	private void recallNPC(PlayerInstance activeChar) {
		final WorldObject obj = activeChar.getTarget();
		if ((obj instanceof Npc) && !((Npc) obj).isMinion() && !(obj instanceof RaidBossInstance) && !(obj instanceof GrandBossInstance)) {
			final Npc target = (Npc) obj;
			final int monsterTemplate = target.getTemplate().getId();
			final NpcTemplate template1 = NpcData.getInstance().getTemplate(monsterTemplate);
			if (template1 == null) {
				BuilderUtil.sendSysMessage(activeChar, "Incorrect monster template.");
				LOGGER.warning("ERROR: NPC " + target.getObjectId() + " has a 'null' template.");
				return;
			}

			Spawn spawn = target.getSpawn();
			if (spawn == null) {
				BuilderUtil.sendSysMessage(activeChar, "Incorrect monster spawn.");
				LOGGER.warning("ERROR: NPC " + target.getObjectId() + " has a 'null' spawn.");
				return;
			}
			final int respawnTime = spawn.getRespawnDelay() / 1000;
			target.deleteMe();
			spawn.stopRespawn();
			SpawnTable.getInstance().deleteSpawn(spawn, true);

			try {
				spawn = new Spawn(template1);
				spawn.setXYZ(activeChar);
				spawn.setAmount(1);
				spawn.setHeading(activeChar.getHeading());
				spawn.setRespawnDelay(respawnTime);
				if (activeChar.isInInstance()) {
					spawn.setInstanceId(activeChar.getInstanceId());
				}
				SpawnTable.getInstance().addNewSpawn(spawn, true);
				spawn.init();
				if (respawnTime <= 0) {
					spawn.stopRespawn();
				}

				BuilderUtil.sendSysMessage(activeChar, "Created " + template1.getName() + " on " + target.getObjectId() + ".");
			} catch (Exception e) {
				BuilderUtil.sendSysMessage(activeChar, "Target is not in game.");
			}
		} else if (obj instanceof RaidBossInstance) {
			final RaidBossInstance target = (RaidBossInstance) obj;
			final Spawn spawn = target.getSpawn();
			final double curHP = target.getCurrentHp();
			final double curMP = target.getCurrentMp();
			if (spawn == null) {
				BuilderUtil.sendSysMessage(activeChar, "Incorrect raid spawn.");
				LOGGER.warning("ERROR: NPC Id" + target.getId() + " has a 'null' spawn.");
				return;
			}
			DBSpawnManager.getInstance().deleteSpawn(spawn, true);
			try {
				final Spawn spawnDat = new Spawn(target.getId());
				spawnDat.setXYZ(activeChar);
				spawnDat.setAmount(1);
				spawnDat.setHeading(activeChar.getHeading());
				spawnDat.setRespawnMinDelay(43200);
				spawnDat.setRespawnMaxDelay(129600);

				DBSpawnManager.getInstance().addNewSpawn(spawnDat, 0, curHP, curMP, true);
			} catch (Exception e) {
				activeChar.sendPacket(SystemMessageId.YOUR_TARGET_CANNOT_BE_FOUND);
			}
		} else {
			activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
		}
	}
}
