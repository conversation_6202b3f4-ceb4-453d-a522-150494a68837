/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.TranscendentInstanceData;
import club.projectessence.gameserver.enums.Position;
import club.projectessence.gameserver.enums.ShotType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Attackable;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.effects.EffectType;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Formulas;
import club.projectessence.gameserver.util.BalanceUtil;

/**
 * Backstab effect implementation.
 *
 * <AUTHOR>
 */
public class Backstab extends AbstractEffect
{
	private final double	_power;
	private final double	_chanceBoost;
	private final double	_criticalChance;
	private final boolean	_overHit;
	
	public Backstab(StatSet params)
	{
		_power = params.getDouble("power");
		_chanceBoost = params.getDouble("chanceBoost");
		_criticalChance = params.getDouble("criticalChance", 0);
		_overHit = params.getBoolean("overHit", false);
	}
	
	@Override
	public boolean calcSuccess(Creature effector, Creature effected, Skill skill)
	{
		// handled in method instant
		return true;
	}
	
	@Override
	public EffectType getEffectType()
	{
		return EffectType.PHYSICAL_ATTACK;
	}
	
	@Override
	public boolean isInstant()
	{
		return true;
	}
	
	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item)
	{
		if (effector.isAlikeDead())
		{
			return;
		}
		if ((_overHit || (effector.isAffected(EffectFlag.TRANSCENDENT_SKILLS) && TranscendentInstanceData.getInstance().isTranscendentSkill(skill.getId()))) && effected.isAttackable())
		{
			((Attackable) effected).overhitEnabled(true);
		}
		// boolean landed = effected.isMonster() || (!effector.isInFrontOf(effected) && !Formulas.calcPhysicalSkillEvasion(effector, effected, skill) && Formulas.calcBlowSuccess(effector, effected, skill, _chanceBoost));
		boolean landed = effected.isMonster() || (!Formulas.calcSkillEvasion(effector, effected, skill) && Formulas.calcBlowSuccess(effector, effected, skill, _chanceBoost, false));
		final boolean ss = skill.useSoulShot() && (effector.isChargedShot(ShotType.SOULSHOTS) || effector.isChargedShot(ShotType.BLESSED_SOULSHOTS));
		final byte shld = Formulas.calcShldUse(effector, effected);
		final boolean crit = Formulas.calcCrit(_criticalChance, effector, effected, skill);
		final double critMod = crit ? Formulas.calcCritDamage(effector, effected, skill, true) : 1;
		final double cAtkAdd = crit ? Formulas.calcCritDamageAdd(effector, effected, skill) : 0;
		double damage = 0;
		switch (Position.getPosition(effector, effected))
		{
			case BACK: // Backstab should not miss from behind.
			{
				landed = true;
			}
		}
		if (landed)
		{
			damage = Formulas.calcBlowDamage(effector, effected, skill, true, _power, shld, ss, false);
			damage = (damage * critMod) + cAtkAdd;
		}
		else
		{
			damage = Formulas.calcAutoAttackDamage(effector, effected, shld, crit, ss, false);
		}
		final double initialDamage = damage;
		if (effector.isPlayer())
		{
			final PlayerInstance player = effector.getActingPlayer();
			damage += getStrBonusDamage(player, initialDamage);
		}
		if (effected.isPlayer())
		{
			final PlayerInstance player = effected.getActingPlayer();
			damage -= getConBonusDefense(player, initialDamage);
		}

		// Balance system now applied at base stats level in IStatFunction
		// No additional PvP damage modification needed

		if (effector.isInFrontOf(effected))
		{
			// Capped Orcs Invasion Event - Giant Orc Deraka
			if (Config.ENABLE_ORC_INVASION_EVENT && effected.getId() == 18818 && effector.isPlayer())
			{
				if (damage > 30000)
				{
					damage = 30000;
				}
			}
			effector.doAttack(damage, effected, skill, false, false, true, false); // Backstab from the front doesn't affect to HP directly
		}
		else
		{
			effector.doAttack(damage, effected, skill, false, true, true, false);
		}
	}
}