/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayableExpChanged;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Stat;

/**
 * <AUTHOR>
 */
public class SoulEating extends AbstractEffect {
	private final int _expNeeded;
	private final int _maxSouls;
	private final double _chance;
	private final int _maxAtOnce;
	private final String _type;

	public SoulEating(StatSet params) {
		_expNeeded = params.getInt("expNeeded");
		_maxSouls = params.getInt("maxSouls");
		_chance = params.getDouble("chance");
		_maxAtOnce = params.getInt("maxAtOnce", 1);
		_type = params.getString("type", "light").toLowerCase();
	}

	@Override
	public void onStart(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (effected.isPlayer()) {
			effected.addListener(new ConsumerEventListener(effected, EventType.ON_PLAYABLE_EXP_CHANGED, (OnPlayableExpChanged event) -> onExperienceReceived(event.getPlayable(), (event.getNewExp() - event.getOldExp())), this));
		}
	}

	@Override
	public void onExit(Creature effector, Creature effected, Skill skill) {
		if (effected.isPlayer()) {
			effected.removeListenerIf(EventType.ON_PLAYABLE_EXP_CHANGED, listener -> listener.getOwner() == this);
		}
	}

	@Override
	public void pump(Creature effected, Skill skill) {
		effected.getStat().mergeAdd(_type.equals("light") ? Stat.MAX_LIGHT_SOULS : Stat.MAX_SHADOW_SOULS, _maxSouls);
	}

	private void onExperienceReceived(Playable playable, long exp) {
		if (playable.isPlayer() && (exp >= _expNeeded)) {
			final PlayerInstance player = playable.getActingPlayer();
			for (int i = 0; i < _maxAtOnce; i++) {
				if (Rnd.get(0D, 100D) < _chance) {
					if (_type.equals("light")) {
						final int maxSouls = (int) player.getStat().getValue(Stat.MAX_LIGHT_SOULS, 0);
						if (player.getLightSouls() >= maxSouls) {
							return;
						}
						player.increaseLightSouls(1);
					} else {
						final int maxSouls = (int) player.getStat().getValue(Stat.MAX_SHADOW_SOULS, 0);
						if (player.getShadowSouls() >= maxSouls) {
							return;
						}
						player.increaseShadowSouls(1);
					}
				}
			}
		}
	}
}
