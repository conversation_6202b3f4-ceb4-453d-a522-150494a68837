/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.ai.CtrlEvent;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.MonsterInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Formulas;
import club.projectessence.gameserver.network.SystemMessageId;

/**
 * Spoil effect implementation.
 *
 * <AUTHOR> Ahmed, Zoey76
 */
public class Spoil extends AbstractEffect {
	public Spoil(StatSet params) {
	}

	@Override
	public boolean calcSuccess(Creature effector, Creature effected, Skill skill) {
		return Formulas.calcMagicSuccess(effector, effected, skill);
	}

	@Override
	public boolean isInstant() {
		return true;
	}

	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (!effected.isMonster()) {
			return;
		}
		if (effected.isDead()) {
			effector.sendPacket(SystemMessageId.INVALID_TARGET);
			return;
		}

		final MonsterInstance target = (MonsterInstance) effected;
		if (target.isSpoiled() || target.wasSpoiledInstantly()) {
			effector.sendPacket(SystemMessageId.IT_HAS_ALREADY_BEEN_SPOILED);
			return;
		}

		target.setSpoilerObjectId(effector.getObjectId());
		effector.sendPacket(SystemMessageId.THE_SPOIL_CONDITION_HAS_BEEN_ACTIVATED);
		target.getAI().notifyEvent(CtrlEvent.EVT_ATTACKED, effector);
	}
}
