/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.RecipeController;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.SystemMessageId;

/**
 * Open Common Recipe Book effect implementation.
 *
 * <AUTHOR>
 */
public class OpenCommonRecipeBook extends AbstractEffect {
	public OpenCommonRecipeBook(StatSet params) {
	}

	@Override
	public boolean isInstant() {
		return true;
	}

	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (!effector.isPlayer()) {
			return;
		}

		final PlayerInstance player = effector.getActingPlayer();
		if (player.getPrivateStoreType() != PrivateStoreType.NONE) {
			player.sendPacket(SystemMessageId.ITEM_CREATION_IS_NOT_POSSIBLE_WHILE_ENGAGED_IN_A_TRADE);
			return;
		}

		RecipeController.getInstance().requestBookOpen(player, false);
	}
}
