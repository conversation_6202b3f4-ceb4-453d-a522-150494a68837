/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Attackable;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;

import java.util.Collection;

/**
 * Sweeper effect implementation.
 *
 * <AUTHOR>
 */
public class Sweeper extends AbstractEffect {
	public Sweeper(StatSet params) {
	}

	@Override
	public boolean isInstant() {
		return true;
	}

	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (!effector.isPlayer() || !effected.isAttackable()) {
			return;
		}

		final PlayerInstance player = effector.getActingPlayer();
		final Attackable monster = (Attackable) effected;
		if (!monster.checkSpoilOwner(player, false)) {
			return;
		}

		if (monster.wasSpoiledInstantly()) {
			return;
		}

		if (!player.getInventory().checkInventorySlotsAndWeight(monster.getSpoilLootItems(), false, false)) {
			return;
		}

		final Collection<ItemHolder> items = monster.takeSweep();
		if (items != null) {
			for (ItemHolder sweepedItem : items) {
				final Party party = player.getParty();
				if (party != null) {
					party.distributeItem(player, sweepedItem, true, monster);
				} else {
					player.addItem("Sweeper", sweepedItem, effected, true);
				}
			}
		}
	}
}
