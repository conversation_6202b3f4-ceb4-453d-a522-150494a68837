/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.xml.CubicData;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.CubicTemplate;
import club.projectessence.gameserver.model.cubic.CubicInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.network.serverpackets.ExUserInfoCubic;

import java.util.logging.Logger;

/**
 * Summon Cubic effect implementation.
 *
 * <AUTHOR>
 */
public class SummonCubic extends AbstractEffect {
	private static final Logger LOGGER = Logger.getLogger(SummonCubic.class.getName());

	private final int _cubicId;
	private final int _cubicLvl;

	public SummonCubic(StatSet params) {
		_cubicId = params.getInt("cubicId", -1);
		_cubicLvl = params.getInt("cubicLvl", 0);
	}

	@Override
	public boolean isInstant() {
		return true;
	}

	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (!effected.isPlayer() || effected.isAlikeDead() || effected.getActingPlayer().inObserverMode()) {
			return;
		}

		if (_cubicId < 0) {
			LOGGER.warning(SummonCubic.class.getSimpleName() + ": Invalid Cubic ID:" + _cubicId + " in skill ID: " + skill.getId());
			return;
		}

		final PlayerInstance player = effected.getActingPlayer();
		if (player.inObserverMode() || player.isMounted()) {
			return;
		}

		// If cubic is already present, it's replaced.
		final CubicInstance cubic = player.getCubicById(_cubicId);
		if (cubic != null) {
			if (cubic.getTemplate().getLevel() > _cubicLvl) {
				// What do we do in such case?
				return;
			}

			cubic.deactivate();
		} else {
			// If maximum amount is reached, random cubic is removed.
			// Players with no mastery can have only one cubic.
			final double allowedCubicCount = player.getStat().getValue(Stat.MAX_CUBIC, 1);

			// Extra cubics are removed, one by one, randomly.
			final int currentCubicCount = player.getCubics().size();
			if (currentCubicCount >= allowedCubicCount) {
				player.getCubics().values().stream().skip((int) (currentCubicCount * Rnd.nextDouble())).findAny().get().deactivate();
			}
		}

		final CubicTemplate template = CubicData.getInstance().getCubicTemplate(_cubicId, _cubicLvl);
		if (template == null) {
			LOGGER.warning("Attempting to summon cubic without existing template id: " + _cubicId + " level: " + _cubicLvl);
			return;
		}

		// Adding a new cubic.
		player.addCubic(new CubicInstance(player, effector.getActingPlayer(), template));
		player.sendPacket(new ExUserInfoCubic(player));
		player.broadcastCharInfo();
	}
}
