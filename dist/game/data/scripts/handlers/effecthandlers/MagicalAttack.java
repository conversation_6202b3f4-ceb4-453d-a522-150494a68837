/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import java.util.ArrayList;
import java.util.List;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.TranscendentInstanceData;
import club.projectessence.gameserver.enums.Race;
import club.projectessence.gameserver.enums.ShotType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Attackable;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.effects.EffectType;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.AbnormalType;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Formulas;

/**
 * Magical Attack effect implementation.
 *
 * <AUTHOR>
 */
public class MagicalAttack extends AbstractEffect
{
	private final double		_power;
	private final boolean		_overHit;
	private final double		_debuffModifier;
	private final AbnormalType	_debuffType;
	@SuppressWarnings("unused")
	private final int			_debuffLevel;
	private final boolean		_debuffRemove;
	private final double		_raceModifier;
	private final List<Race>	_raceList;
	
	public MagicalAttack(StatSet params)
	{
		_power = params.getDouble("power", 0);
		_overHit = params.getBoolean("overHit", false);
		_debuffModifier = params.getDouble("debuffModifier", 1);
		_debuffType = params.getEnum("debuffType", AbnormalType.class, AbnormalType.NONE);
		_debuffLevel = params.getInt("debuffLevel", 1);
		_debuffRemove = params.getBoolean("debuffRemove", false);
		_raceModifier = params.getDouble("raceModifier", 1);
		_raceList = new ArrayList<>();
		String races = params.getString("raceList", "");
		if ((races != null) && !races.isEmpty())
		{
			for (String ngtStack : races.split(";"))
			{
				_raceList.add(Race.getRace(ngtStack));
			}
		}
	}
	
	@Override
	public boolean calcSuccess(Creature effector, Creature effected, Skill skill)
	{
		return !Formulas.calcSkillEvasion(effector, effected, skill);
	}
	
	@Override
	public EffectType getEffectType()
	{
		return EffectType.MAGICAL_ATTACK;
	}
	
	@Override
	public boolean isInstant()
	{
		return true;
	}
	
	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item)
	{
		if (effector.isAlikeDead())
		{
			return;
		}
		if (skill.getCastRange() > 120)
		{
			PlayerInstance tank = Creature.findPossibleTankForSpell(effector, effected, skill);
			if (tank != null)
			{
				instant(effector, tank, skill, item);
				return;
			}
		}
		if (effected.isPlayer() && effected.getActingPlayer().isFakeDeath() && Config.FAKE_DEATH_DAMAGE_STAND)
		{
			effected.stopFakeDeath(true);
		}
		if ((_overHit || (effector.isAffected(EffectFlag.TRANSCENDENT_SKILLS) && TranscendentInstanceData.getInstance().isTranscendentSkill(skill.getId()))) && effected.isAttackable())
		{
			((Attackable) effected).overhitEnabled(true);
		}
		final boolean sps = skill.useSpiritShot() && effector.isChargedShot(ShotType.SPIRITSHOTS);
		final boolean bss = skill.useSpiritShot() && effector.isChargedShot(ShotType.BLESSED_SPIRITSHOTS);
		final boolean mcrit = Formulas.calcCrit(skill.getMagicCriticalRate(), effector, effected, skill);
		double damage = Formulas.calcMagicDam(effector, effected, skill, effector.getMAtk(), _power, effected.getMDef(), sps, bss, mcrit);
		// Apply debuff mod
		if (effected.getEffectList().getDebuffCount() > 0)
		{
			if (_debuffType == AbnormalType.NONE)
			{
				damage *= _debuffModifier;
			}
			else
			{
				if (effected.getEffectList().hasAbnormalType(_debuffType))
				{
					if (_debuffRemove)
					{
						effected.getEffectList().getDebuffs().stream().filter(info -> info.isAbnormalType(_debuffType)).forEach(d -> effected.stopSkillEffects(d.getSkill()));
					}
					damage *= _debuffModifier;
				}
			}
		}
		// Apply race mod
		if (_raceList.contains(effected.getRace()))
		{
			damage *= _raceModifier;
		}

		// Balance system now applied at base stats level in IStatFunction
		// No additional PvP damage modification needed

		effector.doAttack(damage, effected, skill, false, false, mcrit, false);
	}
}