/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.enums.StatModifierType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Stat;

/**
 * <AUTHOR>
 */
public class AbstractStatAddEffect extends AbstractEffect {
	protected final double _amount;
	private final Stat _stat;
	protected final boolean _petOnly;

	public AbstractStatAddEffect(StatSet params, Stat stat) {
		_stat = stat;
		_amount = params.getDouble("amount", 0);
		if (params.getEnum("mode", StatModifierType.class, StatModifierType.DIFF) != StatModifierType.DIFF) {
			LOGGER.warning(getClass().getSimpleName() + " can only use DIFF mode.");
		}

		_petOnly = params.getBoolean("petOnly", false);
	}

	@Override
	public void pump(Creature effected, Skill skill) {
		if (_petOnly && !effected.isPet()) {
			return;
		}
		
		effected.getStat().mergeAdd(_stat, _amount);
	}
}
