/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.effects.EffectType;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Formulas;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

/**
 * <AUTHOR>
 */
public class MagicalAttackMpStatic extends AbstractEffect {
	private final double _power;

	public MagicalAttackMpStatic(StatSet params) {
		_power = params.getDouble("power");
	}

	@Override
	public boolean calcSuccess(Creature effector, Creature effected, Skill skill) {
		if (effected.isMpBlocked()) {
			return false;
		}

		if (!Formulas.verifyFaceOff(effector, effected)) {
			return false;
		}

		if (!Formulas.calcMagicAffected(effector, effected, skill)) {
			if (effector.isPlayer()) {
				effector.sendPacket(SystemMessageId.YOUR_ATTACK_HAS_FAILED);
			}
			if (effected.isPlayer()) {
				final SystemMessage sm = new SystemMessage(SystemMessageId.C1_RESISTED_C2_S_DRAIN);
				sm.addString(effected.getName());
				sm.addString(effector.getName());
				effected.sendPacket(sm);
			}
			return false;
		}
		return true;
	}

	@Override
	public EffectType getEffectType() {
		return EffectType.MAGICAL_ATTACK;
	}

	@Override
	public boolean isInstant() {
		return true;
	}

	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (effector.isAlikeDead()) {
			return;
		}

		final double damage = _power;
		final double mp = Math.min(effected.getCurrentMp(), damage);

		if (damage > 0) {
			effected.stopEffectsOnDamage();
			effected.setCurrentMp(effected.getCurrentMp() - mp);
		}

		if (effected.isPlayer()) {
			final SystemMessage sm = new SystemMessage(SystemMessageId.S2_S_MP_HAS_BEEN_DRAINED_BY_C1);
			sm.addString(effector.getName());
			sm.addInt((int) mp);
			effected.sendPacket(sm);
		}

		if (effector.isPlayer()) {
			final SystemMessage sm2 = new SystemMessage(SystemMessageId.YOUR_OPPONENT_S_MP_WAS_REDUCED_BY_S1);
			sm2.addInt((int) mp);
			effector.sendPacket(sm2);
		}
	}
}
