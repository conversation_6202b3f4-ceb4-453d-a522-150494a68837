/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * <AUTHOR>
 */
public class RevivingAfterDeath extends AbstractEffect {
	public RevivingAfterDeath(StatSet params) {
	}

	@Override
	public void onExit(Creature effector, Creature effected, Skill skill) {
		if ((effected.getCurrentDp() >= 300) && !effected.hasBlockActions() && !effected.isHpBlocked() && !effected.isAffected(EffectFlag.FEAR)) {
			effected.setCurrentHp(effected.getMaxHp());
		}
	}

	@Override
	public EffectFlag getEffectFlags() {
		return EffectFlag.REVIVING_AFTER_DEATH;
	}
}