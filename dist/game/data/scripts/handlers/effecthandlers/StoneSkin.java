package handlers.effecthandlers;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.creature.OnCreatureDamageReceived;
import club.projectessence.gameserver.model.events.listeners.FunctionEventListener;
import club.projectessence.gameserver.model.events.returns.DamageReturn;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * StoneSkin effect: Absorbs incoming damage up to a specified limit, then expires.
 */
public class StoneSkin extends AbstractEffect
{
	private static final Map<Integer, Double>	DAMAGE_HOLDER	= new ConcurrentHashMap<>();
	private final double						_absorptionLimit;
	
	public StoneSkin(StatSet params)
	{
		_absorptionLimit = params.getDouble("power", 0); // Get absorption limit from skill XML
	}
	
	private DamageReturn onDamageReceivedEvent(OnCreatureDamageReceived event, Creature effected, Skill skill)
	{
		// Skip for DoT and reflected damage
		if (event.isDamageOverTime() || event.isReflect())
		{
			return null;
		}
		final int objectId = event.getTarget().getObjectId();
		double newDamage = event.getDamage();
		final double damageLeft = DAMAGE_HOLDER.getOrDefault(objectId, _absorptionLimit);
		if (newDamage <= damageLeft)
		{
			// Absorb all damage
			if (effected.isPlayer())
			{
				effected.sendMessage("Your " + skill.getName() + " absorbed " + (int) newDamage + " damage from " + event.getAttacker().getName() + ".");
			}
			if (event.getAttacker() != null && event.getAttacker().isPlayer())
			{
				event.getAttacker().sendMessage(effected.getName() + "'s " + skill.getName() + " absorbed " + (int) newDamage + " damage.");
			}
			DAMAGE_HOLDER.put(objectId, damageLeft - newDamage);
			return new DamageReturn(false, true, false, 0); // No damage applied
		}
		else
		{
			// Absorb partial damage and terminate effect
			double excessDamage = newDamage - damageLeft;
			if (effected.isPlayer())
			{
				effected.sendMessage("Your " + skill.getName() + " absorbed " + (int) damageLeft + " damage from " + event.getAttacker().getName() + ".");
			}
			if (event.getAttacker() != null && event.getAttacker().isPlayer())
			{
				event.getAttacker().sendMessage(effected.getName() + "'s " + skill.getName() + " absorbed " + (int) damageLeft + " damage.");
			}
			effected.stopSkillEffects(true, skill.getId());
			DAMAGE_HOLDER.remove(objectId);
			return new DamageReturn(false, true, false, excessDamage); // Apply excess damage
		}
	}
	
	@Override
	public void onStart(Creature effector, Creature effected, Skill skill, ItemInstance item)
	{
		DAMAGE_HOLDER.put(effected.getObjectId(), _absorptionLimit);
		effected.addListener(new FunctionEventListener(effected, EventType.ON_CREATURE_DAMAGE_RECEIVED, (OnCreatureDamageReceived event) -> onDamageReceivedEvent(event, effected, skill), this));
	}
	
	@Override
	public void onExit(Creature effector, Creature effected, Skill skill)
	{
		effected.removeListenerIf(EventType.ON_CREATURE_DAMAGE_RECEIVED, listener -> listener.getOwner() == this);
		DAMAGE_HOLDER.remove(effected.getObjectId());
	}
	
	@Override
	public boolean isInstant()
	{
		return false; // Continuous effect
	}
}