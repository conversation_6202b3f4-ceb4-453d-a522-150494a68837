/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.MountType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PetInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * <AUTHOR>
 */
public class Feed extends AbstractEffect {
	private final int _normal;
	private final int _ride;
	private final int _wyvern;

	public Feed(StatSet params) {
		_normal = params.getInt("normal", 0);
		_ride = params.getInt("ride", 0);
		_wyvern = params.getInt("wyvern", 0);
	}

	@Override
	public boolean isInstant() {
		return true;
	}

	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (effected.isPet()) {
			final PetInstance pet = (PetInstance) effected;
			pet.setCurrentFed(pet.getCurrentFed() + (_normal * Config.PET_FOOD_RATE));
		} else if (effected.isPlayer()) {
			final PlayerInstance player = effected.getActingPlayer();
			if (player.getMountType() == MountType.WYVERN) {
				player.setCurrentFeed(player.getCurrentFeed() + _wyvern);
			} else {
				player.setCurrentFeed(player.getCurrentFeed() + _ride);
			}
		}
	}
}
