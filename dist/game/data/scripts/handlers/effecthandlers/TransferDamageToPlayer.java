/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Stat;

/**
 * Transfer Damage effect implementation.
 *
 * <AUTHOR>
 */
public class TransferDamageToPlayer extends AbstractStatAddEffect {
	private final int _range;

	public TransferDamageToPlayer(StatSet params) {
		super(params, Stat.TRANSFER_DAMAGE_TO_PLAYER);
		_range = params.getInt("range", 1000);
	}

	@Override
	public void onExit(Creature effector, Creature effected, Skill skill) {
		if (effected.isPlayable() && effector.isPlayer()) {
			((Playable) effected).setTransferDamageTo(null);
			((Playable) effected).setTransferDamageRange(0);
		}
	}

	@Override
	public void onStart(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (effected.isPlayable() && effector.isPlayer()) {
			((Playable) effected).setTransferDamageTo(effector.getActingPlayer());
			((Playable) effected).setTransferDamageRange(_range);
		}
	}
}