/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Stat;

/**
 * <AUTHOR>
 */
public class AbnormalDebuffRate extends AbstractEffect {
	private final double _amount;

	public AbnormalDebuffRate(StatSet params) {
		_amount = params.getDouble("amount", 0);
	}

	@Override
	public void pump(Creature effected, Skill skill) {
		effected.getStat().mergeMul(Stat.ABNORMAL_DEBUFF_RATE, 1 + (_amount / 100));
	}
}
