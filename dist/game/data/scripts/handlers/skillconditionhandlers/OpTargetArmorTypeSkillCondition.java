/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.skillconditionhandlers;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.ArmorType;
import club.projectessence.gameserver.model.items.type.ItemType;
import club.projectessence.gameserver.model.skills.ISkillCondition;
import club.projectessence.gameserver.model.skills.Skill;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OpTargetArmorTypeSkillCondition implements ISkillCondition {
	private final List<ArmorType> _armorTypes = new ArrayList<>();

	public OpTargetArmorTypeSkillCondition(StatSet params) {
		final List<String> armorTypes = params.getList("armorType", String.class);
		if (armorTypes != null) {
			armorTypes.stream().map(ArmorType::valueOf).forEach(_armorTypes::add);
		}
	}

	@Override
	public boolean canUse(Creature caster, Skill skill, WorldObject target) {
		if ((target == null) || !target.isCreature()) {
			return false;
		}

		final Creature targetCreature = (Creature) target;
		final Inventory inv = targetCreature.getInventory();

		// Get the chest armor.
		final ItemInstance chest = inv.getPaperdollItem(Inventory.PAPERDOLL_CHEST);
		if (chest == null) {
			return false;
		}

		// Get the chest item type.
		final ItemType chestType = chest.getItem().getItemType();

		// Get the chest body part.
		final long chestBodyPart = chest.getItem().getBodyPart();

		// Get the legs armor.
		final ItemInstance legs = inv.getPaperdollItem(Inventory.PAPERDOLL_LEGS);

		// Get the legs item type.
		ItemType legsType = null;
		if (legs != null) {
			legsType = legs.getItem().getItemType();
		}

		for (ArmorType armorType : _armorTypes) {
			// If chest armor is different from the condition one continue.
			if (chestType != armorType) {
				continue;
			}

			// Return true if chest armor is a full armor.
			if (chestBodyPart == Item.BodyPart.SLOT_FULL_ARMOR.getSlot()) {
				return true;
			}

			// Check legs armor.
			if (legs == null) {
				continue;
			}

			// Return true if legs armor matches too.
			if (legsType == armorType) {
				return true;
			}
		}

		return false;
	}
}
