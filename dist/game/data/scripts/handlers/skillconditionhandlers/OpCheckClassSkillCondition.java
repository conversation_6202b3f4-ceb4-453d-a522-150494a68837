/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.skillconditionhandlers;

import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.enums.SkillConditionAffectType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.skills.ISkillCondition;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * <AUTHOR>
 */
public class OpCheckClassSkillCondition implements ISkillCondition {
	private final ClassId _classId;
	private final SkillConditionAffectType _affectType;
	private final boolean _isWithin;

	public OpCheckClassSkillCondition(StatSet params) {
		_classId = params.getEnum("classId", ClassId.class);
		_affectType = params.getEnum("affectType", SkillConditionAffectType.class);
		_isWithin = params.getBoolean("isWithin");
	}

	@Override
	public boolean canUse(Creature caster, Skill skill, WorldObject target) {
		switch (_affectType) {
			case CASTER: {
				return caster.isPlayer() && (_isWithin == (_classId == caster.getActingPlayer().getClassId()));
			}
			case TARGET: {
				if ((target != null) && !target.isPlayer()) {
					return _isWithin == (_classId == target.getActingPlayer().getClassId());
				}
				break;
			}
		}
		return false;
	}
}
