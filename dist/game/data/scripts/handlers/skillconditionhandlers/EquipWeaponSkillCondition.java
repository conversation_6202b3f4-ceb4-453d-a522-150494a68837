/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.skillconditionhandlers;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.type.WeaponType;
import club.projectessence.gameserver.model.skills.ISkillCondition;
import club.projectessence.gameserver.model.skills.Skill;

import java.util.List;

/**
 * <AUTHOR>
 */
public class EquipWeaponSkillCondition implements ISkillCondition {
	private int _weaponTypesMask = 0;

	public EquipWeaponSkillCondition(StatSet params) {
		final List<WeaponType> weaponTypes = params.getEnumList("weaponType", WeaponType.class);
		if (weaponTypes != null) {
			for (WeaponType weaponType : weaponTypes) {
				_weaponTypesMask |= weaponType.mask();
			}
		}
	}

	@Override
	public boolean canUse(Creature caster, Skill skill, WorldObject target) {
		final Item weapon = caster.getActiveWeaponItem();
		if (weapon != null) {
			if (caster.isPlayer()) {
				PlayerInstance player = caster.getActingPlayer();
				// Death Knight skills should not be usable with 2h sword
				if ((weapon.getItemMask() == (1 << WeaponType.SWORD.ordinal())) && ((weapon.getBodyPart() & Item.BodyPart.SLOT_LR_HAND.getSlot()) != 0) && player.isDeathKnight()) {
					return false;
				}
			}
			return ((weapon.getItemMask() & _weaponTypesMask) != 0);
		}
		return false;
	}
}
