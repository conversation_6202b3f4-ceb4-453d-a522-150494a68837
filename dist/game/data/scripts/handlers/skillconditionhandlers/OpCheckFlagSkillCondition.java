/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.skillconditionhandlers;

import club.projectessence.gameserver.instancemanager.FortManager;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.siege.Fort;
import club.projectessence.gameserver.model.skills.ISkillCondition;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.util.Util;

/**
 * <AUTHOR>
 */
public class OpCheckFlagSkillCondition implements ISkillCondition {
	public OpCheckFlagSkillCondition(StatSet params) {
	}

	@Override
	public boolean canUse(Creature caster, Skill skill, WorldObject target) {
		if (!caster.isPlayer()) {
			return false;
		}

		final PlayerInstance player = caster.getActingPlayer();
		boolean canTakeFort = true;
		if (player.isAlikeDead() || player.isCursedWeaponEquipped() || !player.isClanLeader()) {
			canTakeFort = false;
		}

		final Fort fort = FortManager.getInstance().getFort(player);
		final SystemMessage sm;
		if ((fort == null) || (fort.getResidenceId() <= 0) || !fort.getSiege().isInProgress() || (fort.getSiege().getAttackerClan(player.getClan()) == null)) {
			sm = new SystemMessage(SystemMessageId.S1_CANNOT_BE_USED_DUE_TO_UNSUITABLE_TERMS);
			sm.addSkillName(skill);
			player.sendPacket(sm);
			canTakeFort = false;
		} else if (fort.getFlagPole() != target) {
			player.sendPacket(SystemMessageId.INVALID_TARGET);
			canTakeFort = false;
		} else if (!Util.checkIfInRange(200, player, target, true)) {
			player.sendPacket(SystemMessageId.THE_DISTANCE_IS_TOO_FAR_AND_SO_THE_CASTING_HAS_BEEN_CANCELLED);
			canTakeFort = false;
		}
		return canTakeFort;
	}
}
