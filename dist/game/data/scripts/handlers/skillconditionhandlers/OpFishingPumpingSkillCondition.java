/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.skillconditionhandlers;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.skills.ISkillCondition;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.zone.ZoneId;

/**
 * <AUTHOR>
 */
public class OpFishingPumpingSkillCondition implements ISkillCondition {
	public OpFishingPumpingSkillCondition(StatSet params) {
	}

	@Override
	public boolean canUse(Creature caster, Skill skill, WorldObject target) {
		return caster.isInsideZone(ZoneId.FISHING);
	}
}
