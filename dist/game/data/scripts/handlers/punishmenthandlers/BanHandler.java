/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.punishmenthandlers;

import club.projectessence.gameserver.LoginServerThread;
import club.projectessence.gameserver.handler.IPunishmentHandler;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.punishment.PunishmentTask;
import club.projectessence.gameserver.model.punishment.PunishmentType;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.GameClient;

/**
 * This class handles ban punishment.
 *
 * <AUTHOR>
 */
public class BanHandler implements IPunishmentHandler {
	@Override
	public void onStart(PunishmentTask task) {
		switch (task.getAffect()) {
			case CHARACTER: {
				final int objectId = Integer.parseInt(String.valueOf(task.getKey()));
				final PlayerInstance player = World.getInstance().getPlayer(objectId);
				if (player != null) {
					applyToPlayer(player);
				}
				break;
			}
			case ACCOUNT: {
				final String account = String.valueOf(task.getKey());
				final GameClient client = LoginServerThread.getInstance().getClient(account);
				if (client != null) {
					final PlayerInstance player = client.getPlayer();
					if (player != null) {
						applyToPlayer(player);
					} else {
						Disconnection.of(client).logout(false, false);
					}
				}
				break;
			}
			case IP: {
				final String ip = String.valueOf(task.getKey());
				for (PlayerInstance player : World.getInstance().getPlayers()) {
					if (player.getIPAddress().equals(ip)) {
						applyToPlayer(player);
					}
				}
				break;
			}
		}
	}

	@Override
	public void onEnd(PunishmentTask task) {
		// Should not do anything.
	}

	/**
	 * Applies all punishment effects from the player.
	 *
	 * @param player
	 */
	private void applyToPlayer(PlayerInstance player) {
		Disconnection.of(player).logout(false, false);
	}

	@Override
	public PunishmentType getType() {
		return PunishmentType.BAN;
	}
}
