/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.actionshifthandlers;

import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.handler.AdminCommandHandler;
import club.projectessence.gameserver.handler.IActionShiftHandler;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

public class SummonActionShift implements IActionShiftHandler {
	@Override
	public boolean action(PlayerInstance player, WorldObject target, boolean interact) {
		if (player.isGM()) {
			if (player.getTarget() != target) {
				// Set the target of the PlayerInstance player
				player.setTarget(target);
			}

			AdminCommandHandler.getInstance().useAdminCommand(player, "admin_summon_info", true);
		}
		return true;
	}

	@Override
	public InstanceType getInstanceType() {
		return InstanceType.Summon;
	}
}