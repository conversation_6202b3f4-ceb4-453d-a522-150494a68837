/*
 * This file is part of the L2J Mobius project.
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.bypasshandlers;

import club.projectessence.Config;
import club.projectessence.gameserver.handler.IBypassHandler;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;

/**
 * Bypass handler for auto-resurrection toggle.
 * 
 * <AUTHOR>
 */
public class AutoResurrection implements IBypassHandler
{
	private static final String[] COMMANDS =
	{
		"AutoResurrection"
	};
	
	@Override
	public boolean useBypass(String command, PlayerInstance player, Creature target)
	{
		if (!Config.AUTOPLAY_AUTO_RESURRECTION_ENABLED)
		{
			player.sendMessage("Auto-resurrection feature is disabled.");
			return false;
		}
		
		if (command.startsWith("AutoResurrection"))
		{
			final String[] params = command.split(" ");
			if (params.length > 1)
			{
				final String action = params[1];
				switch (action.toLowerCase())
				{
					case "enable":
					{
						AutoPlayTaskManager.getInstance().setAutoResurrection(player, true);
						break;
					}
					case "disable":
					{
						AutoPlayTaskManager.getInstance().setAutoResurrection(player, false);
						break;
					}
					case "toggle":
					{
						boolean currentState = player.getAutoPlaySettings().isAutoResurrectionEnabled();
						AutoPlayTaskManager.getInstance().setAutoResurrection(player, !currentState);
						break;
					}
					case "status":
					{
						showStatus(player);
						break;
					}
					default:
					{
						player.sendMessage("Invalid auto-resurrection command. Use: enable, disable, toggle, or status");
						break;
					}
				}
			}
			else
			{
				// Toggle by default if no parameter specified
				boolean currentState = player.getAutoPlaySettings().isAutoResurrectionEnabled(player);
				AutoPlayTaskManager.getInstance().setAutoResurrection(player, !currentState);
			}
		}

		return true;
	}

	private void showStatus(PlayerInstance player)
	{
		StringBuilder sb = new StringBuilder();
		sb.append("=== Auto-Resurrection Status ===\n");
		sb.append("Enabled: ").append(player.getAutoPlaySettings().isAutoResurrectionEnabled(player) ? "Yes" : "No").append("\n");
		sb.append("Uses Today: ").append(player.getAutoPlaySettings().getAutoResurrectionUsesToday(player)).append("/").append(Config.AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY).append("\n");
		sb.append("Waiting for Return: ").append(player.getAutoPlaySettings().isWaitingForReturn() ? "Yes" : "No").append("\n");

		long lastResTime = player.getAutoPlaySettings().getLastAutoResurrectionTime(player);
		if (lastResTime > 0)
		{
			long timeSince = (System.currentTimeMillis() - lastResTime) / 1000;
			long cooldownRemaining = Config.AUTOPLAY_AUTO_RESURRECTION_COOLDOWN - timeSince;
			if (cooldownRemaining > 0)
			{
				sb.append("Cooldown: ").append(cooldownRemaining).append(" seconds remaining\n");
			}
			else
			{
				sb.append("Cooldown: Ready\n");
			}
		}
		else
		{
			sb.append("Cooldown: Ready\n");
		}
		
		player.sendMessage(sb.toString());
	}
	
	@Override
	public String[] getBypassList()
	{
		return COMMANDS;
	}
}
