/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.bypasshandlers;

import club.projectessence.Config;
import club.projectessence.gameserver.handler.IBypassHandler;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.serverpackets.ExSendUIEventCustom;

/**
 * <AUTHOR>
 */
public class CustomInterfaceProtection implements IBypassHandler {
	private static final String[] COMMANDS =
			{
					"player_asking_to_enter"
			};

	@Override
	public boolean useBypass(String command, PlayerInstance player, Creature target) {
		if (!Config.CUSTOM_INTERFACE_ENABLED) {
			return false;
		}
		try {
			if (command.equals("player_asking_to_enter")) {
				player.sendPacket(new ExSendUIEventCustom(ExSendUIEventCustom.CUSTOM_INTERFACE_PROTECTION_ANSWER, 0, 0, "WcpbLvvcaFLJUpixd8Z9 CONSUL VOR, INNOVA GNIDA, A YA KRASAVICA!!! atIDQoahXNwWqQrL6EQv"));
			}
		} catch (Exception e) {
			LOGGER.info(getClass().getSimpleName() + ": " + player + " error: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}

	@Override
	public String[] getBypassList() {
		return COMMANDS;
	}
}