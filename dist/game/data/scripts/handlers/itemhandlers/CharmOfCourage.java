/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.itemhandlers;

import club.projectessence.gameserver.handler.IItemHandler;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.EtcStatusUpdate;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

/**
 * Charm Of Courage Handler
 *
 * <AUTHOR>
 */
public class CharmOfCourage implements IItemHandler {
	@Override
	public boolean useItem(Playable playable, ItemInstance item, boolean forceUse) {
		if (!playable.isPlayer()) {
			return false;
		}

		final PlayerInstance player = playable.getActingPlayer();
		int level = player.getLevel();
		final int itemLevel = item.getItem().getCrystalType().getLevel();
		if (level < 20) {
			level = 0;
		} else if (level < 40) {
			level = 1;
		} else if (level < 52) {
			level = 2;
		} else if (level < 61) {
			level = 3;
		} else if (level < 76) {
			level = 4;
		} else {
			level = 5;
		}

		if (itemLevel < level) {
			final SystemMessage sm = new SystemMessage(SystemMessageId.S1_CANNOT_BE_USED_DUE_TO_UNSUITABLE_TERMS);
			sm.addItemName(item.getId());
			player.sendPacket(sm);
			return false;
		}

		if (player.destroyItemWithoutTrace("Consume", item.getObjectId(), 1, null, false)) {
			player.setCharmOfCourage(true);
			player.sendPacket(new EtcStatusUpdate(player));
			return true;
		}
		return false;
	}
}
