/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.targethandlers;

import club.projectessence.gameserver.handler.ITargetTypeHandler;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.TargetType;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;

/**
 * Target fortress flagpole
 *
 * <AUTHOR>
 */
public class FortressFlagpole implements ITargetTypeHandler {
	@Override
	public Enum<TargetType> getTargetType() {
		return TargetType.FORTRESS_FLAGPOLE;
	}

	@Override
	public WorldObject getTarget(Creature creature, WorldObject selectedTarget, Skill skill, boolean forceUse, boolean dontMove, boolean sendMessage) {
		final WorldObject target = creature.getTarget();
		if ((target != null) && creature.isInsideZone(ZoneId.HQ) && creature.isInsideZone(ZoneId.FORT) && !target.isPlayable() && target.getName().toLowerCase().contains("flagpole")) {
			return target;
		}

		if (sendMessage) {
			creature.sendPacket(SystemMessageId.THAT_IS_AN_INCORRECT_TARGET);
		}

		return null;
	}
}
