/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.targethandlers.affectscope;

import club.projectessence.gameserver.handler.IAffectScopeHandler;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.AffectScope;

import java.util.function.Consumer;

/**
 * TODO: Wyvern affect scope.
 *
 * <AUTHOR>
 */
public class WyvernScope implements IAffectScopeHandler {
	@Override
	public void forEachAffected(<PERSON><PERSON>ture creature, WorldObject target, Skill skill, Consumer<? super WorldObject> action) {
		// TODO Unknown affect scope.
	}

	@Override
	public Enum<AffectScope> getAffectScopeType() {
		return AffectScope.WYVERN_SCOPE;
	}
}
