/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.targethandlers.affectscope;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Predicate;

import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.AffectObjectHandler;
import club.projectessence.gameserver.handler.IAffectObjectHandler;
import club.projectessence.gameserver.handler.IAffectScopeHandler;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.AffectScope;
import club.projectessence.gameserver.model.stats.Stat;

/**
 * Ring Range affect scope implementation. Gathers objects in ring/donut shaped area with start and end range.
 *
 * <AUTHOR>
 */
public class RingRange implements IAffectScopeHandler
{
	@Override
	public void forEachAffected(Creature creature, WorldObject target, Skill skill, Consumer<? super WorldObject> action)
	{
		final IAffectObjectHandler affectObject = AffectObjectHandler.getInstance().getHandler(skill.getAffectObject());
		final int affectRange = creature.isPlayer() ? (int) (skill.getAffectRange() + creature.getStat().getValue(Stat.SKILL_RADIUS_BOOST, 0)) : skill.getAffectRange();
		final int affectLimit = skill.getAffectLimit();
		final int startRange = skill.getFanRange()[2];
		// Target checks.
		final AtomicInteger affected = new AtomicInteger(0);
		final Predicate<Creature> filter = c ->
		{
			if ((affectLimit > 0) && (affected.get() >= affectLimit))
			{
				return false;
			}
			if (c.isDead())
			{
				return false;
			}
			// Targets before the start range are unaffected.
			if (c.isInsideRadius2D(target, startRange))
			{
				return false;
			}
			if ((affectObject != null) && !affectObject.checkAffectedObject(creature, c))
			{
				return false;
			}
			if (!GeoEngine.getInstance().canSeeTarget(target, c))
			{
				return false;
			}
			if (c.getStat().getValue(Stat.EVADE_AOE_SPELL, 0) > Rnd.get(100))
			{
				return false;
			}
			affected.incrementAndGet();
			return true;
		};
		// Check and add targets.
		World.getInstance().forEachVisibleObjectInRange(target, Creature.class, affectRange, c ->
		{
			if (filter.test(c))
			{
				action.accept(c);
			}
		});
	}
	
	@Override
	public Enum<AffectScope> getAffectScopeType()
	{
		return AffectScope.RING_RANGE;
	}
}
