/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.targethandlers;

import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.ITargetTypeHandler;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.TargetType;
import club.projectessence.gameserver.network.SystemMessageId;

/**
 * <AUTHOR>
 */
public class TargetOrSelf implements ITargetTypeHandler {
	@Override
	public Enum<TargetType> getTargetType() {
		return TargetType.TARGET_OR_SELF;
	}

	@Override
	public WorldObject getTarget(Creature creature, WorldObject selectedTarget, Skill skill, boolean forceUse, boolean dontMove, boolean sendMessage) {
		if (selectedTarget == null) {
			return creature;
		}

		if (!selectedTarget.isCreature()) {
			return creature;
		}

		if (forceUse) {
			return selectedTarget;
		}

		final Creature target = (Creature) selectedTarget;

		// You can always target yourself.
		if (creature == target) {
			return target;
		}

		// Check for cast range if character cannot move. TODO: char will start follow until within castrange, but if his moving is blocked by geodata, this msg will be sent.
		if (dontMove && (creature.calculateDistance2D(target) > skill.getCastRange())) {
			if (sendMessage) {
				creature.sendPacket(SystemMessageId.THE_DISTANCE_IS_TOO_FAR_AND_SO_THE_CASTING_HAS_BEEN_CANCELLED);
			}

			return creature;
		}

		if (skill.isFlyType() && !GeoEngine.getInstance().canMoveToTarget(creature.getX(), creature.getY(), creature.getZ(), target.getX(), target.getY(), target.getZ(), creature.getInstanceWorld())) {
			if (sendMessage) {
				creature.sendPacket(SystemMessageId.THE_TARGET_IS_LOCATED_WHERE_YOU_CANNOT_CHARGE);
			}
			return creature;
		}

		if (selectedTarget.isAutoAttackable(creature)) {
			return creature;
		}

		// Geodata check when character is within range.
		if (!GeoEngine.getInstance().canSeeTarget(creature, target)) {
			if (sendMessage) {
				creature.sendPacket(SystemMessageId.CANNOT_SEE_TARGET);
			}

			return creature;
		}
		return target;
	}
}
