/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.others;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.model.actor.Npc;

/**
 * <AUTHOR>
 */
public class NonLethalableNpcs extends AbstractNpcAI {
	private static final int[] NPCS =
			{
					35062, // Headquarters
			};

	public NonLethalableNpcs() {
		addSpawnId(NPCS);
	}

	public static void main(String[] args) {
		new NonLethalableNpcs();
	}

	@Override
	public String onSpawn(Npc npc) {
		npc.setLethalable(false);
		return super.onSpawn(npc);
	}
}
