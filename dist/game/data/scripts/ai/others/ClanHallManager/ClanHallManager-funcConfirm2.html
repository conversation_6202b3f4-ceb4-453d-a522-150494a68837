<html><body><br>
<font color="LEVEL">[Manage Clan Hall]</font><br>
<table width=275 cellspacing=3 cellpadding=0>
<tr>
<td FIXWIDTH=65><font color="999999">Name</font></td>
<td FIXWIDTH=230>Carpet (MP Recovery)</td>
</tr>
<tr>
<td><font color="999999">Cost</font></td>
<td><font color="00FFFF">%funcCost%</font></td>
</tr>
<tr>
<td valign=top><font color="999999">Use</font></td>
<td>Provides additional MP recovery for clan members in the clan hall.</td>
</tr>
</table>
<br>
The fee for using this feature must be paid in advance.<br>
The initial fee will be deducted from the Clan Leader's <font color="LEVEL">inventory</font>, and subsequent fees will be withdrawn from the  <font color="LEVEL">clan warehouse</font>.<br>
<center>
<table width=200>
<tr><td align=center>
<button action="bypass -h Quest ClanHallManager manageFunctions setFunction %funcId% %funcLv%" value="OK" width=80 height=27 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td> <td align=center>
<button action="bypass -h Quest ClanHallManager manageFunctions" value="Cancel" width=80 height=27 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td></tr>
</table>
</center>
</body></html>