/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.others;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.ListenerRegisterType;
import club.projectessence.gameserver.model.events.annotations.RegisterEvent;
import club.projectessence.gameserver.model.events.annotations.RegisterType;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogout;

/**
 * <AUTHOR> Benetis
 */
public class RankingSkillBonuses extends AbstractNpcAI {

	public static void main(String[] args) {
		new RankingSkillBonuses();
	}

	@RegisterEvent(EventType.ON_PLAYER_LOGIN)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerLogin(OnPlayerLogin event) {
		final PlayerInstance player = event.getPlayer();
		if (player == null) {
			return;
		}
		RankManager.getInstance().applyEffects(player);
	}

	@RegisterEvent(EventType.ON_PLAYER_LOGOUT)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerLogout(OnPlayerLogout event) {
		final PlayerInstance player = event.getPlayer();
		if (player == null) {
			return;
		}
		// Clear throttling data to prevent memory leaks
		RankManager.getInstance().clearPlayerThrottling(player.getObjectId());
	}
}
