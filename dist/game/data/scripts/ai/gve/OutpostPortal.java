package ai.gve;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.instancemanager.OutpostPortalManager;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

public class OutpostPortal extends AbstractNpcAI
{
	private static final int PORTAL_NPC_ID = 505; // ID của NPC portal
	
	private OutpostPortal()
	{
		addSpawnId(PORTAL_NPC_ID);
		addStartNpc(PORTAL_NPC_ID);
		addTalkId(PORTAL_NPC_ID);
		addFirstTalkId(PORTAL_NPC_ID);
	}
	
	@Override
	public String onAdvEvent(String event, Npc npc, PlayerInstance player)
	{
		if (player == null || npc == null)
		{
			return null;
		}
		switch (event)
		{
			case "teleport":
				OutpostPortalManager.getInstance().showTeleportWindow(player);
				break;
			case "confirm_teleport_to_zone":
				String zoneName = event.substring("confirm_teleport_to_zone ".length());
				if (zoneName == null || zoneName.trim().isEmpty())
				{
					player.sendMessage("Không thể dịch chuyển: Zone không hợp lệ!");
					return null;
				}
				OutpostPortalManager.getInstance().teleportToZone(player, zoneName);
				break;
		}
		return super.onAdvEvent(event, npc, player);
	}
	
	@Override
	public String onFirstTalk(Npc npc, PlayerInstance player)
	{
		final String htmltext = null;
		final NpcHtmlMessage packet = new NpcHtmlMessage(npc.getObjectId());
		packet.setHtml(getHtm(player, "data/html/gve/505.htm"));
		packet.replace("%name%", player.getName());
		player.sendPacket(packet);
		return htmltext;
	}
	
	@Override
	public String onTalk(Npc npc, PlayerInstance player)
	{
		return npc.getId() + ".htm";
	}
	
	public static void main(String[] args)
	{
		new OutpostPortal();
	}
}