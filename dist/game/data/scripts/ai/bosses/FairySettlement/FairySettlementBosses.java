/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.bosses.FairySettlement;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.util.Broadcast;

/**
 * <AUTHOR>
 */
public class FairySettlementBosses extends AbstractNpcAI {
	// NPCs
	private static final int DARK_MOTH = 10000;
	private static final int DARK_VAIN = 10001;

	private FairySettlementBosses() {
		addKillId(DARK_MOTH, DARK_VAIN);
		addSpawnId(DARK_MOTH, DARK_VAIN);
	}

	@Override
	public String onKill(Npc npc, Playable killer) {
		World.getInstance().forEachVisibleObjectInRange(npc, ItemInstance.class, 1500, item -> {
			item.getDropProtection().protect(killer.getActingPlayer());
		});
		return super.onKill(npc, killer);
	}

	@Override
	public String onSpawn(Npc npc) {
		Broadcast.toAllOnlinePlayers(new ExShowScreenMessage(npc.getName() + " has spawned in Fairy Settlement", 2, 3500, 0, true, true));
		return super.onSpawn(npc);
	}

	public static void main(String[] args) {
		new FairySettlementBosses();
	}
}