package ai.bosses.SevenSigns;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.instancemanager.GrandBossManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.GrandBossInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.zone.type.NoRestartZone;
import club.projectessence.gameserver.network.serverpackets.PlaySound;
import club.projectessence.gameserver.util.Util;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class Anakim extends AbstractNpcAI {
	private static final NoRestartZone ANAKIM_ZONE = ZoneManager.getInstance().getZoneById(70052, NoRestartZone.class);

	private static final Location ANAKIM_TELEPORT = new Location(-6088, 19064, -5493);
	private static final int MAX_PLAYERS_IN_ZONE = 300;

	private static final int ANAKIM = 25286;
	// STatus
	private static final byte ALIVE = 0;
	private static final byte DEAD = 1;
	// 21900;51900
	// D HH MM. day starts from sunday
	// Анаким : Вторник и пятница в 19:00
	private static final int RESPAWN_DAY_1 = 3; // Tuesday
	private static final int RESPAWN_DAY_2 = 6; // Friday
	private static final int RESPAWN_HOUR = 19;
	private static final int RESPAWN_MINUTE = 0;

	private Anakim() {
		registerMobs(ANAKIM);
		final StatSet info = GrandBossManager.getInstance().getStatSet(ANAKIM);
		final int status = GrandBossManager.getInstance().getBossStatus(ANAKIM);
		if (status == DEAD) {
			final long temp = info.getLong("respawn_time") - System.currentTimeMillis();
			if (temp > 0) {
				startQuestTimer("anakim_respawn", temp, null, null);
			} else {
				final GrandBossInstance anakim = (GrandBossInstance) addSpawn(ANAKIM, -6675, 18505, -5488, 0, false, 0);
				GrandBossManager.getInstance().setBossStatus(ANAKIM, ALIVE);
				spawnBoss(anakim);
			}
		} else {
			final int loc_x = info.getInt("loc_x");
			final int loc_y = info.getInt("loc_y");
			final int loc_z = info.getInt("loc_z");
			final int heading = info.getInt("heading");
			final double hp = info.getDouble("currentHP");
			final double mp = info.getDouble("currentMP");
			final GrandBossInstance anakim = (GrandBossInstance) addSpawn(ANAKIM, loc_x, loc_y, loc_z, heading, false, 0);
			anakim.setCurrentHpMp(hp, mp);
			spawnBoss(anakim);
		}
	}

	public static void main(String[] args) {
		new Anakim();
	}

	private void spawnBoss(GrandBossInstance npc) {
		GrandBossManager.getInstance().addBoss(npc);
		npc.broadcastPacket(new PlaySound(1, "BS01_A", 1, npc.getObjectId(), npc.getX(), npc.getY(), npc.getZ()));
	}

	@Override
	public String onAdvEvent(String event, Npc npc, PlayerInstance player) {
		if (event.equalsIgnoreCase("anakim_respawn")) {
			final GrandBossInstance anakim = (GrandBossInstance) addSpawn(ANAKIM, -6675, 18505, -5488, 0, false, 0);
			GrandBossManager.getInstance().setBossStatus(ANAKIM, ALIVE);
			spawnBoss(anakim);
		}
		return super.onAdvEvent(event, npc, player);
	}

	@Override
	public String onSpawn(Npc npc) {
		npc.setRandomWalking(false);
		return super.onSpawn(npc);
	}

	private long getNextDateMilis(int dayOfWeek) {
		Calendar c = Calendar.getInstance();
		c.set(Calendar.AM_PM, Calendar.PM);
		c.set(Calendar.HOUR_OF_DAY, RESPAWN_HOUR);
		c.set(Calendar.MINUTE, RESPAWN_MINUTE);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		for (int i = 0; i < 7; i++) {
			if ((c.get(Calendar.DAY_OF_WEEK) == dayOfWeek) && (c.getTimeInMillis() > System.currentTimeMillis())) {
				return c.getTimeInMillis();
			}
			c.add(Calendar.DAY_OF_WEEK, 1);
		}
		return c.getTimeInMillis();
	}

	@Override
	public String onKill(Npc npc, Playable killer) {
		if (npc.getId() == ANAKIM) {
			GrandBossManager.getInstance().setBossStatus(ANAKIM, DEAD);
			long nextSpawnTime = Math.min(getNextDateMilis(RESPAWN_DAY_1), getNextDateMilis(RESPAWN_DAY_2));
			startQuestTimer("anakim_respawn", nextSpawnTime - System.currentTimeMillis(), null, null);
			// Also save the respawn time so that the info is maintained past reboots.
			final StatSet info = GrandBossManager.getInstance().getStatSet(ANAKIM);
			info.set("respawn_time", nextSpawnTime);
			GrandBossManager.getInstance().setStatSet(ANAKIM, info);
			LOGGER.info(getClass().getSimpleName() + ": Updated " + npc.getName() + " respawn time to " + Util.formatDate(new Date(nextSpawnTime), "dd.MM.yyyy HH:mm"));
		}
		return super.onKill(npc, killer);
	}
}
