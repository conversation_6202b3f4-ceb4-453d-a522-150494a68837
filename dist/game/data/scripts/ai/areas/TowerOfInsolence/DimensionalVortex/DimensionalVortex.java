/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.areas.TowerOfInsolence.DimensionalVortex;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.skills.SkillCaster;

/**
 * <AUTHOR>
 */
public class DimensionalVortex extends AbstractNpcAI {
	// NPC
	private static final int DIMENTIONAL_VORTEX = 30952;

	private static final SkillHolder HEAVENLY_RIFT_SEEKER = new SkillHolder(48040, 1);
	private static final int HEAVENLY_RIFT_TICKET = 91972;

	private DimensionalVortex() {
		addStartNpc(DIMENTIONAL_VORTEX);
		addTalkId(DIMENTIONAL_VORTEX);
		addFirstTalkId(DIMENTIONAL_VORTEX);
	}

	public static void main(String[] args) {
		new DimensionalVortex();
	}

	@Override
	public String onAdvEvent(String event, Npc npc, PlayerInstance player) {
		if (event.equals("30952.htm") || event.equals("30952-1.htm")) {
			return event;
		}

		if (event.equals("tryenter")) {
			if (player.isAffectedBySkill(HEAVENLY_RIFT_SEEKER.getSkillId())) {
				player.teleToLocation(111920, 13324, 10884, 12000, false);
			} else {
				return "NO_TICKET.htm";
			}
		} else if (event.equals("getbuff")) {
			if (player.getInventory().getItemByItemId(HEAVENLY_RIFT_TICKET) == null) {
				return "NO_TICKET.htm";
			}
			// else if (!HeavenlyRiftManager.getZone1().getAllPlayersInside().isEmpty() && //
			// !HeavenlyRiftManager.getZone2().getAllPlayersInside().isEmpty() && //
			// !HeavenlyRiftManager.getZone3().getAllPlayersInside().isEmpty())
			// {
			// player.sendMessage("All rifts are currently busy.");
			// }
			else if (!player.isAffectedBySkill(HEAVENLY_RIFT_SEEKER.getSkillId()) && player.destroyItemByItemId("Heavenly Rift", HEAVENLY_RIFT_TICKET, 1, player, true)) {
				SkillCaster.triggerCast(npc, player, HEAVENLY_RIFT_SEEKER.getSkill());
			}
		}
		return null;
	}

	@Override
	public String onFirstTalk(Npc npc, PlayerInstance player) {
		return npc.getId() + ".htm";
	}
}
