/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.areas.SeaOfSpores;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

/**
 * <AUTHOR>
 */
public class EarthGuardianWyrm extends AbstractNpcAI {
	private static final int EARTH_GUARDIAN_WYRM = 21657;

	private static final int NPCS[] =
			{
					20555,
					20556,
					20557,
					20558,
					20559,
					20561
			};

	private EarthGuardianWyrm() {
		addKillId(NPCS);
	}

	public static void main(String[] args) {
		new EarthGuardianWyrm();
	}

	@Override
	public String onKill(Npc npc, Playable killer) {
		if (getRandom(100) < 10) {
			int count = Math.min(3, World.getInstance().getVisibleObjectsInRange(npc, PlayerInstance.class, 1100).size());
			for (int i = 0; i < count; i++) {
				addSpawn(EARTH_GUARDIAN_WYRM, npc, false, 300000);
			}
		}
		return super.onKill(npc, killer);
	}
}