/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.areas.GiantsCave;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import ai.AbstractNpcAI;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.xml.SpawnData;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.zone.ZoneType;
import club.projectessence.gameserver.network.NpcStringId;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.NpcSay;
import club.projectessence.gameserver.network.serverpackets.OnEventTrigger;

/**
 * <AUTHOR>
 */
public class GiantsCave extends AbstractNpcAI
{
	private static final ZoneType					INNER_ZONE					= ZoneManager.getInstance().getZoneByName("giants_cave_inner_no_bookmark");
	private static final int						CAVE_CONTROL_DEVICE			= 34301;
	private static final int						CAVE_CONTROL_DEVICE_EFFECT	= 48475;
	private static final Map<Integer, Integer>		CAPTURE_DATA				= new HashMap<>();
	private static final Map<Integer, SkillHolder>	BUFF						= new HashMap<>();
	private static int								_capturerId					= -1;
	private static boolean							_isClanCapture				= false;
	private static int								_colorId					= -1;
	static
	{
		CAPTURE_DATA.put(1, 25190222); // Aquamarine
		CAPTURE_DATA.put(2, 25190224);// Yellow
		CAPTURE_DATA.put(3, 25190226);// Purple
	}
	static
	{
		BUFF.put(1, new SkillHolder(CAVE_CONTROL_DEVICE_EFFECT, 1));
		BUFF.put(2, new SkillHolder(CAVE_CONTROL_DEVICE_EFFECT, 2));
		BUFF.put(3, new SkillHolder(CAVE_CONTROL_DEVICE_EFFECT, 3));
	}
	
	private GiantsCave()
	{
		addSpawnId(CAVE_CONTROL_DEVICE);
		addFirstTalkId(CAVE_CONTROL_DEVICE);
		addTalkId(CAVE_CONTROL_DEVICE);
		addAttackId(CAVE_CONTROL_DEVICE);
		addKillId(CAVE_CONTROL_DEVICE);
		addEnterZoneId(INNER_ZONE.getId());
		addExitZoneId(INNER_ZONE.getId());
		manageSpawns();
	}
	
	public static void main(String[] args)
	{
		new GiantsCave();
	}
	
	@Override
	public String onSpawn(Npc npc)
	{
		npc.disableCoreAI(true);
		npc.setImmobilized(true);
		npc.setAutoAttackable(false);
		npc.setInvul(true);
		npc.setUndying(false);
		npc.getSpawn().stopRespawn();
		_capturerId = -1;
		_isClanCapture = false;
		for (int val : CAPTURE_DATA.values())
		{
			if (_colorId > 0)
			{
				INNER_ZONE.broadcastPacket(new OnEventTrigger(val, false));
				INNER_ZONE.broadcastPacket(new ExShowScreenMessage(NpcStringId.THE_CAVE_CONTROL_DEVICE_IS_DEACTIVATED, 2, 5000, true));
			}
		}
		_colorId = -1;
		for (PlayerInstance p : INNER_ZONE.getAllPlayersInside())
		{
			removeBuff(p);
		}
		return null;
	}
	
	@Override
	public String onKill(Npc npc, Playable killer)
	{
		INNER_ZONE.broadcastPacket(new OnEventTrigger(CAPTURE_DATA.get(_colorId), false));
		INNER_ZONE.broadcastPacket(new ExShowScreenMessage(NpcStringId.THE_CAVE_CONTROL_DEVICE_IS_DEACTIVATED, 2, 5000, true));
		_capturerId = -1;
		_isClanCapture = false;
		_colorId = -1;
		for (PlayerInstance p : INNER_ZONE.getAllPlayersInside())
		{
			removeBuff(p);
		}
		return null;
	}
	
	@Override
	public String onFirstTalk(Npc npc, PlayerInstance player)
	{
		if (_capturerId < 0)
		{
			return npc.getId() + ".htm";
		}
		return npc.getId() + "-1.htm";
	}
	
	@Override
	public String onAdvEvent(String event, Npc npc, PlayerInstance player)
	{
		if (event.equals("capture"))
		{
			Clan clan = player.getClan();
			if (clan == null)
			{
				_capturerId = player.getObjectId();
				_isClanCapture = false;
				npc.setTitle("", true, false);
				npc.setClanId(0);
			}
			else
			{
				_capturerId = clan.getId();
				_isClanCapture = true;
				npc.setTitle(clan.getName(), true, false);
				npc.setClanId(_capturerId);
			}
			_colorId = Rnd.get(1, CAPTURE_DATA.size());
			if (_colorId > 0)
			{
				INNER_ZONE.broadcastPacket(new OnEventTrigger(CAPTURE_DATA.get(_colorId), true));
			}
			for (PlayerInstance p : INNER_ZONE.getAllPlayersInside())
			{
				applyBuff(p);
			}
			npc.setInvul(false);
			npc.broadcastInfo();
		}
		else // back
		{
			return npc.getId() + ".htm";
		}
		return super.onAdvEvent(event, npc, player);
	}
	
	@Override
	public String onAttack(Npc npc, Playable attacker, int damage)
	{
		if (npc.getId() == CAVE_CONTROL_DEVICE)
		{
			if ((_colorId > 0) && (npc.getVariables().getLong("lastAttack", 0) < (System.currentTimeMillis() - 5000)))
			{
				NpcSay msg = new NpcSay(npc, ChatType.NPC_GENERAL, NpcStringId.ATTACK_SIGNAL_FROM_CONTROL_DEVICE_S1_DETECTED);
				msg.addStringParameter(attacker.getName());
				npc.broadcastPacket(msg);
				npc.getVariables().set("lastAttack", System.currentTimeMillis());
			}
		}
		return super.onAttack(npc, attacker, damage);
	}
	
	@Override
	public String onEnterZone(Creature creature, ZoneType zone)
	{
		if (_colorId > 0)
		{
			if (creature.isPlayer())
			{
				applyBuff(creature.getActingPlayer());
				creature.sendPacket(new OnEventTrigger(CAPTURE_DATA.get(_colorId), true));
			}
		}
		return null;
	}
	
	@Override
	public String onExitZone(Creature creature, ZoneType zone)
	{
		if (creature.isPlayer())
		{
			removeBuff(creature.getActingPlayer());
			if (_colorId > 0)
			{
				creature.sendPacket(new OnEventTrigger(CAPTURE_DATA.get(_colorId), false));
			}
		}
		return null;
	}
	
	private void applyBuff(PlayerInstance player)
	{
		if ((!_isClanCapture && (_capturerId == player.getObjectId())) || ((_isClanCapture && (_capturerId == player.getClanId())) && (_colorId > 0)))
		{
			BUFF.get(_colorId).getSkill().applyEffects(player, player);
		}
	}
	
	private void removeBuff(PlayerInstance player)
	{
		player.stopSkillEffects(true, CAVE_CONTROL_DEVICE_EFFECT);
	}
	
	private void manageSpawns()
	{
		SpawnData.getInstance().getSpawns().forEach(spawnTemplate -> spawnTemplate.getGroupsByName("Giants_Cave_Inner_Part_Humans").forEach(holder -> holder.despawnAll()));
		SpawnData.getInstance().getSpawns().forEach(spawnTemplate -> spawnTemplate.getGroupsByName("Giants_Cave_Inner_Part_Giants").forEach(holder -> holder.despawnAll()));
		SpawnData.getInstance().getSpawns().forEach(spawnTemplate -> spawnTemplate.getGroupsByName("Giants_Cave_Inner_Part_Device").forEach(holder -> holder.despawnAll()));
		SpawnData.getInstance().getSpawns().forEach(spawnTemplate -> spawnTemplate.getGroupsByName(Rnd.nextBoolean() ? "Giants_Cave_Inner_Part_Humans" : "Giants_Cave_Inner_Part_Giants").forEach(holder -> holder.spawnAll()));
		SpawnData.getInstance().getSpawns().forEach(spawnTemplate -> spawnTemplate.getGroupsByName("Giants_Cave_Inner_Part_Device").forEach(holder -> holder.spawnAll()));
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(System.currentTimeMillis());
		do
		{
			cal.add(Calendar.HOUR_OF_DAY, 1);
		}
		while ((cal.get(Calendar.HOUR_OF_DAY) % 4) != 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		ThreadPool.get().schedule(this::manageSpawns, cal.getTimeInMillis() - System.currentTimeMillis());
	}
}
