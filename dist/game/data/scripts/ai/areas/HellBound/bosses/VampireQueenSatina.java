/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.areas.HellBound.bosses;

import ai.areas.HellBound.Zones.SatinaLaboratory;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class VampireQueenSatina extends AbstractHellboundBossesAI {
	public static final int VAMPIRE_QUEEN_SATINA = 25934;
	public static final int START_HOUR_1 = 11;
	public static final int START_HOUR_2 = 20;
	public static final int RANDOM_MINUTES = 10;

	private VampireQueenSatina() {
		super(VAMPIRE_QUEEN_SATINA, "Vampire Queen Satina", "SatinasLaboratory", START_HOUR_1, START_HOUR_2, RANDOM_MINUTES, false, SatinaLaboratory.ZONE_IDS);
	}

	public static void main(String[] args) {
		new VampireQueenSatina();
	}
}
