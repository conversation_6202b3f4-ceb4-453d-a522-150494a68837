/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.areas.HellBound.SteelCitadel.Beleth;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class Beleth extends AbstractNpcAI {
	private static final Logger LOGGER = Logger.getLogger(Beleth.class.getName());

	private final static int WARP_GATE = 34201;

	private Beleth() {
		addFirstTalkId(WARP_GATE);
		addTalkId(WARP_GATE);
	}

	public static void main(String[] args) {
		new Beleth();
	}

	@Override
	public String onFirstTalk(Npc npc, PlayerInstance player) {
		return npc.getId() + ".htm";
	}

	@Override
	public String onAdvEvent(String event, Npc npc, PlayerInstance player) {
		switch (event) {
			case "move_to_beleth": {
				LOGGER.warning(getClass().getSimpleName() + ": TODO");
				return "34201-1.htm";
			}
			case "34201.htm": {
				return event;
			}
			default:
				return null;
		}
	}
}
