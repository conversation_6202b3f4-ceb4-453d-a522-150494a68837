/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package instances.TranscendentZone;

import club.projectessence.gameserver.network.NpcStringId;

/**
 * <AUTHOR>
 */
public class Transcendent_DragonValley_80_89 extends AbstractTranscendentInstance {
	private static final int TEMPLATE_ID = 212;
	private static final int JOON = 34124;
	private static final int GUARDIAN_MOB_ID = 22335;

	public Transcendent_DragonValley_80_89() {
		super(TEMPLATE_ID, JOON, 1, NpcStringId.DRAGON_VALLEY_GUARD_APPEARS, GUARDIAN_MOB_ID);
	}

	public static void main(String[] args) {
		new Transcendent_DragonValley_80_89();
	}
}
