package instances.Custom.KartiasLabyrinth;

import ai.AbstractNpcAI;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.impl.creature.OnCreatureAttacked;
import club.projectessence.gameserver.model.events.impl.creature.OnCreatureDeath;
import club.projectessence.gameserver.network.NpcStringId;

/**
 * <AUTHOR>
 */
public class KartiaBoss extends AbstractNpcAI {

	// NPCs
	private static final int BOSS = 25882; // Zellaka (group 85)
	private static final int FIGHTER_GROUP_85 = 19229;
	private static final int MAGE_GROUP_85 = 19230;

	private KartiaBoss() {
		addSpawnId(BOSS);
		setCreatureKillId(this::onCreatureKill, BOSS);
		setCreatureAttackedId(this::onCreatureAttacked, BOSS);
	}

	@Override
	public String onSpawn(Npc npc) {
		getTimers().addRepeatingTimer("NPC_SAY", 20000, npc, null);
		return super.onSpawn(npc);
	}

	@Override
	public void onTimerEvent(String event, StatSet params, Npc npc, PlayerInstance player) {
		if (event.equals("NPC_SAY") && npc.isTargetable()) {
			npc.broadcastSay(ChatType.NPC_SHOUT, NpcStringId.YOU_PUNY_INSECTS_DON_T_KNOW_YOUR_PLACE_YOU_CANNOT_STOP_ME);
		}
	}

	public void onCreatureKill(OnCreatureDeath event) {
		final Npc npc = (Npc) event.getTarget();
		npc.broadcastSay(ChatType.NPC_SHOUT, NpcStringId.NO_HOW_COULD_THIS_BE_I_CAN_T_GO_BACK_TO_NIHIL_LIKE_THIS);
		getTimers().cancelTimersOf(npc);
	}

	public void onCreatureAttacked(OnCreatureAttacked event) {
		final Npc npc = (Npc) event.getTarget();
		if ((npc.getCurrentHpPercent() <= 75) && npc.isScriptValue(0)) {
			spawnMinions(npc);
			npc.setScriptValue(1);
		} else if ((npc.getCurrentHpPercent() <= 50) && npc.isScriptValue(1)) {
			spawnMinions(npc);
			npc.setScriptValue(2);
		} else if ((npc.getCurrentHpPercent() <= 25) && npc.isScriptValue(2)) {
			spawnMinions(npc);
			npc.setScriptValue(3);
		}
	}

	public void spawnMinions(Npc npc) {
		for (int i = 0; i < 3; i++) {
			addSpawn(FIGHTER_GROUP_85, npc, true, 0, false, npc.getInstanceId());
			addSpawn(MAGE_GROUP_85, npc, true, 0, false, npc.getInstanceId());
		}
	}

	public static void main(String[] args) {
		new KartiaBoss();
	}
}
