<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skillTrees.xsd">
    <skillTree classId="10" type="classSkillTree">
        <skill autoGet="true" getLevel="1" skillId="118" skillLevel="1" skillName="Magician's Curiosity" />
        <skill autoGet="true" getLevel="1" skillId="244" skillLevel="1" skillName="Armor Mastery" />
        <skill autoGet="true" getLevel="1" skillId="249" skillLevel="1" skillName="Weapon Mastery" />
        <skill autoGet="true" getLevel="1" skillId="1147" skillLevel="1" skillName="Fireball" />
        <skill autoGet="true" getLevel="1" skillId="1177" skillLevel="1" skillName="Wind Strike" />
        <skill autoGet="true" getLevel="1" skillId="1184" skillLevel="1" skillName="Ice Bolt" />
        <skill autoGet="true" getLevel="1" skillId="1216" skillLevel="1" skillName="Self Heal" />
        <skill getLevel="1" skillId="1372" skillLevel="1" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 1-->
            <item count="1" id="92004" />
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="2" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 2-->
            <item count="1" id="92005" />
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="3" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 3-->
            <item count="1" id="92006" />
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="4" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 4-->
            <item count="1" id="92007" />
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="5" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 5-->
            <item count="1" id="92008" />
        </skill>
        <skill autoGet="true" getLevel="1" skillId="45240" skillLevel="1" skillName="Einhasad's Blessing" />
        <skill getLevel="2" levelUpSp="5" skillId="1040" skillLevel="1" skillName="Shield" />
        <skill getLevel="3" levelUpSp="5" skillId="1068" skillLevel="1" skillName="Might" />
        <skill getLevel="4" levelUpSp="15" skillId="1177" skillLevel="2" skillName="Wind Strike" />
        <skill getLevel="4" levelUpSp="15" skillId="1184" skillLevel="2" skillName="Ice Bolt" />
        <skill getLevel="5" levelUpSp="20" skillId="1216" skillLevel="2" skillName="Self Heal" />
        <skill getLevel="7" levelUpSp="25" skillId="1177" skillLevel="3" skillName="Wind Strike" />
        <skill getLevel="7" levelUpSp="25" skillId="1184" skillLevel="3" skillName="Ice Bolt" />
        <skill getLevel="10" levelUpSp="160" skillId="1015" skillLevel="1" skillName="Battle Heal" />
        <skill getLevel="10" levelUpSp="160" skillId="1147" skillLevel="2" skillName="Fireball" />
        <skill getLevel="10" levelUpSp="200" skillId="1164" skillLevel="1" skillName="Weakness" />
        <skill getLevel="11" levelUpSp="160" skillId="244" skillLevel="2" skillName="Armor Mastery" />
        <skill getLevel="11" levelUpSp="160" skillId="1177" skillLevel="4" skillName="Wind Strike" />
        <skill getLevel="11" levelUpSp="160" skillId="1184" skillLevel="4" skillName="Ice Bolt" />
        <skill getLevel="12" levelUpSp="190" skillId="1015" skillLevel="2" skillName="Battle Heal" />
        <skill getLevel="14" levelUpSp="190" skillId="244" skillLevel="3" skillName="Armor Mastery" />
        <skill getLevel="14" levelUpSp="190" skillId="249" skillLevel="2" skillName="Weapon Mastery" />
        <skill getLevel="14" levelUpSp="190" skillId="1015" skillLevel="3" skillName="Battle Heal" />
        <skill getLevel="14" levelUpSp="190" skillId="1177" skillLevel="5" skillName="Wind Strike" />
        <skill getLevel="14" levelUpSp="190" skillId="1184" skillLevel="5" skillName="Ice Bolt" />
        <skill getLevel="15" levelUpSp="200" skillId="1147" skillLevel="3" skillName="Fireball" />
        <skill getLevel="15" levelUpSp="250" skillId="1216" skillLevel="3" skillName="Self Heal" />
    </skillTree>
</list>