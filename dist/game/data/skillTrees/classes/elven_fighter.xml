<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skillTrees.xsd">
    <skillTree classId="18" type="classSkillTree">
        <skill autoGet="true" getLevel="1" skillId="61" skillLevel="1" skillName="Elven Spirit"/>
        <skill autoGet="true" getLevel="1" skillId="141" skillLevel="1" skillName="Weapon Mastery"/>
        <skill autoGet="true" getLevel="1" skillId="142" skillLevel="1" skillName="Armor Mastery"/>
        <skill getLevel="1" skillId="1372" skillLevel="1" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 1-->
            <item count="1" id="92004"/>
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="2" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 2-->
            <item count="1" id="92005"/>
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="3" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 3-->
            <item count="1" id="92006"/>
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="4" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 4-->
            <item count="1" id="92007"/>
        </skill>
        <skill getLevel="1" skillId="1372" skillLevel="5" skillName="Expand Inventory">
            <!--Inventory Expansion Ticket Lv. 5-->
            <item count="1" id="92008"/>
        </skill>
        <skill getLevel="2" levelUpSp="5" skillId="1040" skillLevel="1" skillName="Shield"/>
        <skill getLevel="3" levelUpSp="5" skillId="3" skillLevel="1" skillName="Power Strike"/>
        <skill getLevel="3" levelUpSp="5" skillId="16" skillLevel="1" skillName="Mortal Blow"/>
        <skill getLevel="3" levelUpSp="5" skillId="56" skillLevel="1" skillName="Power Shot"/>
        <skill getLevel="3" levelUpSp="5" skillId="1068" skillLevel="1" skillName="Might"/>
        <skill getLevel="4" levelUpSp="10" skillId="3" skillLevel="2" skillName="Power Strike"/>
        <skill getLevel="4" levelUpSp="10" skillId="16" skillLevel="2" skillName="Mortal Blow"/>
        <skill getLevel="4" levelUpSp="10" skillId="56" skillLevel="2" skillName="Power Shot"/>
        <skill getLevel="5" levelUpSp="15" skillId="3" skillLevel="3" skillName="Power Strike"/>
        <skill getLevel="5" levelUpSp="15" skillId="16" skillLevel="3" skillName="Mortal Blow"/>
        <skill getLevel="5" levelUpSp="15" skillId="56" skillLevel="3" skillName="Power Shot"/>
        <skill getLevel="5" levelUpSp="15" skillId="91" skillLevel="1" skillName="Ability to Protect"/>
        <skill getLevel="8" levelUpSp="100" skillId="3" skillLevel="4" skillName="Power Strike"/>
        <skill getLevel="8" levelUpSp="100" skillId="16" skillLevel="4" skillName="Mortal Blow"/>
        <skill getLevel="8" levelUpSp="100" skillId="56" skillLevel="4" skillName="Power Shot"/>
        <skill getLevel="8" levelUpSp="100" skillId="142" skillLevel="2" skillName="Armor Mastery"/>
        <skill getLevel="9" levelUpSp="100" skillId="3" skillLevel="5" skillName="Power Strike"/>
        <skill getLevel="9" levelUpSp="100" skillId="16" skillLevel="5" skillName="Mortal Blow"/>
        <skill getLevel="9" levelUpSp="100" skillId="56" skillLevel="5" skillName="Power Shot"/>
        <skill getLevel="10" levelUpSp="100" skillId="3" skillLevel="6" skillName="Power Strike"/>
        <skill getLevel="10" levelUpSp="100" skillId="16" skillLevel="6" skillName="Mortal Blow"/>
        <skill getLevel="10" levelUpSp="100" skillId="56" skillLevel="6" skillName="Power Shot"/>
        <skill getLevel="10" levelUpSp="100" skillId="70" skillLevel="1" skillName="Emergency Rescue"/>
        <skill getLevel="10" levelUpSp="100" skillId="77" skillLevel="1" skillName="Ability to Attack"/>
        <skill getLevel="10" levelUpSp="100" skillId="141" skillLevel="2" skillName="Weapon Mastery"/>
        <skill getLevel="10" levelUpSp="100" skillId="142" skillLevel="3" skillName="Armor Mastery"/>
        <skill getLevel="13" levelUpSp="400" skillId="3" skillLevel="7" skillName="Power Strike"/>
        <skill getLevel="13" levelUpSp="400" skillId="16" skillLevel="7" skillName="Mortal Blow"/>
        <skill getLevel="13" levelUpSp="400" skillId="56" skillLevel="7" skillName="Power Shot"/>
        <skill getLevel="13" levelUpSp="400" skillId="142" skillLevel="4" skillName="Armor Mastery"/>
        <skill getLevel="14" levelUpSp="400" skillId="3" skillLevel="8" skillName="Power Strike"/>
        <skill getLevel="14" levelUpSp="400" skillId="16" skillLevel="8" skillName="Mortal Blow"/>
        <skill getLevel="14" levelUpSp="400" skillId="56" skillLevel="8" skillName="Power Shot"/>
        <skill getLevel="15" levelUpSp="400" skillId="3" skillLevel="9" skillName="Power Strike"/>
        <skill getLevel="15" levelUpSp="400" skillId="16" skillLevel="9" skillName="Mortal Blow"/>
        <skill getLevel="15" levelUpSp="400" skillId="56" skillLevel="9" skillName="Power Shot"/>
        <skill getLevel="15" levelUpSp="400" skillId="70" skillLevel="2" skillName="Emergency Rescue"/>
        <skill getLevel="15" levelUpSp="400" skillId="141" skillLevel="3" skillName="Weapon Mastery"/>
        <skill getLevel="15" levelUpSp="400" skillId="142" skillLevel="5" skillName="Armor Mastery"/>
        </skillTree>
</list>