<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="40205" toLevel="8" name="Aden Adventure's Buff" nameRu="Положительный Эффект Путешествия в Аден">
		<!-- CON +1 for $s1 h. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<isTriggeredSkill>true</isTriggeredSkill>
		<stayAfterDeath>true</stayAfterDeath>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
		</abnormalLevel>
		<abnormalTime>
			<value level="1">3600</value>
			<value level="2">7200</value>
			<value level="3">10800</value>
			<value level="4">14400</value>
			<value level="5">18000</value>
			<value level="6">21600</value>
			<value level="7">25200</value>
			<value level="8">28880</value>
		</abnormalTime>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="StatUp" fromLevel="2" toLevel="8">
				<stat>MEN</stat>
				<amount>1</amount>
			</effect>
			<effect name="StatUp" fromLevel="3" toLevel="8">
				<stat>WIT</stat>
				<amount>1</amount>
			</effect>
			<effect name="StatUp" fromLevel="4" toLevel="8">
				<stat>DEX</stat>
				<amount>1</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="8">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="StatUp" fromLevel="6" toLevel="8">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
		</effects>
	</skill>
	<skill id="40206" toLevel="1" name="Aden Adventure's Buff Cancel" nameRu="Отмена Положительного Эффекта Путешествия в Аден">
		<!-- Cancels the buff of the Aden Adventure. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40207" toLevel="1" name="Spellbook: Air Bike" nameRu="Книга Заклинаний - Парящий Челнок">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40208" toLevel="1" name="Anniversary Cake" nameRu="Праздничный Торт Юбилея">
		<!-- For 5 min., party members' P./ M. Atk. +160, Acquired XP/ SP +160%%, Max HP/ MP +160, Atk. Spd. +160, Casting Spd. +160, P./ M. Def. +160. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40209" toLevel="1" name="Dumpling Soup" nameRu="Суп с Клецками">
		<!-- For 20 min. P./ M. Atk. +150, P./ M. Def. +100, all Critical Rate +10%%, Max HP/ MP +150. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40210" toLevel="1" name="Scroll: Enchant Circlet of Hero" nameRu="Свиток: Модифицировать Венец Героя">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40211" toLevel="1" name="New Year Horoscope - Minor Luck" nameRu="Гороскоп на Новый Год - Малая Удача">
		<!-- Acquired XP/ SP +$s1. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40212" toLevel="1" name="New Year Horoscope - Medium Luck" nameRu="Гороскоп на Новый Год - Средняя Удача">
		<!-- STR +$s1\n, DEX +$s1\nINT +$s1\nCON +$s1\nMEN +$s1\nWIT +$s1\nHP +$s2 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40213" toLevel="1" name="New Year Horoscope - Great Luck" nameRu="Гороскоп на Новый Год - Большая Удача">
		<!-- STR +$s1\n, DEX +$s1\nINT +$s1\nCON +$s1\nMEN +$s1\nWIT +$s1\nP. Atk. +$s2\nM. Atk. +$s2\nP. Def. +$s2\nM. Def. +$s2\nMax MP +$s3\nAll received Critical Damage -$s4 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40214" toLevel="1" name="Scroll of Blessing" nameRu="Свиток Благословения">
		<!-- Turns a weapon into a blessed one. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40215" toLevel="1" name="Random Teleport" nameRu="Случайный Телепорт">
		<!-- Teleports to any place in the Black Anvil Guild Camp at random.\n\nCan be used only in the Black Anvil Guild Camp. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40216" toLevel="1" name="Black Anvil Guild Camp's Time Stone" nameRu="Камень Продления Времени Лагеря Гильдии Черной Наковальни">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40217" toLevel="1" name="Scroll of Blessing" nameRu="Свиток Благословения">
		<!-- Turns a weapon into a blessed one. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40218" toLevel="1" name="Growth Potion" nameRu="Микстура Роста">
		<!-- XP +7,490,000, SP +200,000.\nRequired level: 60+.\nCannot be used by chaotic characters. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40219" toLevel="1" name="Scroll: Boost Attack" nameRu="Свиток: Модифицировать Атаку">
		<!-- P. Atk. +800\nM. Atk. +800\nMax HP +1000\nAll received Critical Damage -100\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SCROLL_BOOST_ATTACK</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94269</itemConsumeId>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<targetType>NONE</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PAtk">
				<amount>800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceCriticalDamage">
				<amount>-100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceMagicCriticalDamage">
				<amount>-100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceSkillCriticalDamage">
				<amount>-100</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40220" toLevel="1" name="Scroll: Boost Attack" nameRu="Свиток: Модифицировать Атаку">
		<!-- P. Atk. +800\nM. Atk. +800\nMax HP +1000\nAll received Critical Damage -100\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<abnormalType>SCROLL_BOOST_ATTACK</abnormalType>
	</skill>
	<skill id="40221" toLevel="1" name="Scroll: Boost Defense" nameRu="Свиток: Модифицировать Защиту">
		<!-- P. Def. +500\nM. Def. +500\nMax MP +800\nSpeed +2\nAll Critical Rate +30\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SCROLL_BOOST_DEFENSE</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94271</itemConsumeId>
		<isMagic>4</isMagic> <!-- Auto generated with parser -->
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PhysicalDefence">
				<amount>500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillCriticalProbability">
				<amount>3</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40222" toLevel="1" name="Scroll: Boost Defense" nameRu="Свиток: Модифицировать Защиту">
		<!-- P. Def. +500\nM. Def. +500\nMax MP +800\nSpeed +2\nAll Critical Rate +30\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<abnormalType>SCROLL_BOOST_DEFENSE</abnormalType>
	</skill>
	<skill id="40223" toLevel="1" name="Scroll: Enchant Stats" nameRu="Свиток: Модифицировать Характеристики">
		<!-- All stats +1\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<icon>icon.skill0000</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>BERSERKER_SCROLL</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94305</itemConsumeId>
		<isMagic>4</isMagic> <!-- Auto generated with parser -->
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40224" toLevel="1" name="Scroll: Enchant Stats" nameRu="Свиток: Модифицировать Характеристики">
		<!-- All stats +1\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<icon>icon.skill0000</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>BERSERKER_SCROLL</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94306</itemConsumeId>
		<hitTime>0</hitTime>
		<isMagic>4</isMagic> <!-- Auto generated with parser -->
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40225" toLevel="1" name="Elixir" nameRu="Эликсир">
		<!-- Stat points +1. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40226" toLevel="25" name="Special Buff Scroll Lv. 1" nameRu="Свиток Особых Эффектов - Ур. 1">
		<!-- STR/ DEX/ CON +1\nINT/ WIT/ MEN +1\n\nThe buff is leveling up every hour, also various items are granted.\n\n<Rewards>\nLv. 1-8: Sayha's Cookie (2 pcs.)\nLv. 9-15: Sayha's Storm Lv. 3 (2 pcs.)\nLv. 16-20: Soulshot Ticket (10 pcs.)\nLv. 21-23: Spirit Ore (100 pcs.)\nLv. 24: Charging Stone of Random Craft (2 pcs.)\n\nNote!\nThe effect does not remain after death. Items cannot be obtained after death. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40227" toLevel="5" name="Special Buff Scroll Reward Lv. 1" nameRu="Награда Свитка Особых Эффектов - Ур. 1">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40228" toLevel="1" name="Forgotten Primeval Garden's Time Stone" nameRu="Камень Продления Времени Забытого Первобытного Сада">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40229" toLevel="1" name="Scroll: Boost Attack - Event" nameRu="Свиток: Модифицировать Атаку - Ивент">
		<!-- P. Atk. +800\nM. Atk. +800\nMax HP +1000\nAll received Critical Damage -100\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SCROLL_BOOST_ATTACK</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94380</itemConsumeId>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<targetType>NONE</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PAtk">
				<amount>800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceCriticalDamage">
				<amount>-100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceMagicCriticalDamage">
				<amount>-100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceSkillCriticalDamage">
				<amount>-100</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40230" toLevel="1" name="Scroll: Boost Defense - Event" nameRu="Свиток: Модифицировать Защиту - Ивент">
		<!-- P. Def. +500\nM. Def. +500\nMax MP +800\nSpeed +2\nAll Critical Rate +30\nThe effect remains after death.\nYou can cancel the effect by yourself. -->
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SCROLL_BOOST_DEFENSE</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94381</itemConsumeId>
		<isMagic>4</isMagic> <!-- Auto generated with parser -->
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PhysicalDefence">
				<amount>500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillCriticalProbability">
				<amount>3</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40231" toLevel="1" name="Maliss' HP Recovery Potion (Event)" nameRu="Зелье Восстановления HP Меллисы - Ивент">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40232" toLevel="1" name="Energy of Development" nameRu="Энергия Развития">
		<!-- STR/ DEX/ CON +1\nINT/ WIT/ MEN +1\nMP Recovery Rate +5\nAcquired XP/ SP +30%%\n\nDuration: 4 h.\nThe effect remains after death. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40233" toLevel="1" name="Scroll: Enchant Rare Accessories" nameRu="Свиток: Модифицировать Редкие Аксессуары">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40234" toLevel="1" name="Scroll: Enchant Rare Accessories - Not Used" nameRu="Свиток: Модифицировать Редкие Аксессуары - Не используется">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40235" toLevel="1" name="Improved Scroll: Enchant Rare Accessories (Exchangeable)" nameRu="Улучшенный Свиток: Модифицировать Редкие Аксессуары (Можно обменять)">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40236" toLevel="1" name="Improved Scroll: Enchant Rare Accessories" nameRu="Улучшенный Свиток: Модифицировать Редкие Аксессуары">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40237" toLevel="1" name="Pet XP Crystal (Exchangeable)" nameRu="Кристалл Опыта Питомца (Можно обменять)">
		<!-- Pet XP +100,000,000. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40238" toLevel="1" name="Pet XP Crystal" nameRu="Кристалл Опыта Питомца">
		<!-- Pet XP +100,000,000. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94480</itemConsumeId>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<magicLevel>1</magicLevel>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<reuseDelay>5</reuseDelay>
		<targetType>PET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="GivePetXp">
				<xp>100000000</xp>
			</effect>
		</effects>
	</skill>
	<skill id="40239" toLevel="1" name="Pet Buff Scroll" nameRu="Свиток Положительных Эффектов для Питомца">
		<!-- Max HP +1000\nP. Def. +300\nM. Def. +300\nHP Recovery Rate +10 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40240" toLevel="1" name="Capture" nameRu="Захват">
		<!-- Allows to catch a pet. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40241" toLevel="1" name="Improved Pet XP Crystal" nameRu="Улучшенный Кристалл Опыта Питомца">
		<!-- Pet XP +2,000,000,000. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94517</itemConsumeId>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<magicLevel>1</magicLevel>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<reuseDelay>5</reuseDelay>
		<targetType>PET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="GivePetXp">
				<xp>2000000000</xp>
			</effect>
		</effects>
	</skill>
	<skill id="40242" toLevel="1" name="Sayha's Blessing Scroll" nameRu="Свиток Благословения Сайхи">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40243" toLevel="1" name="Sayha's Blessing Scroll - Stats" nameRu="Свиток Благословения Сайхи - Характеристики">
		<!-- All stats +1. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40244" toLevel="1" name="Sayha's Blessing Scroll - Recovery Potions' Effect" nameRu="Свиток Благословения Сайхи - Эффективность Зелий Восстановления">
		<!-- HP Recovery Potion Effect +50. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40245" toLevel="1" name="Sayha's Blessing Scroll - MP Recovery" nameRu="Свиток Благословения Сайхи - Восстановление MP">
		<!-- MP Recovery +10. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40246" toLevel="1" name="Sayha's Blessing Scroll - Atk./ Def." nameRu="Свиток Благословения Сайхи - Атака/ Защита">
		<!-- P. Atk. +150\nM. Atk. +150\nP. Def. +150\nM. Def. +150 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40247" toLevel="1" name="Sayha's Blessing Scroll - Atk. Spd./ Casting Spd." nameRu="Свиток Благословения Сайхи - Скор. Атк./ Скор. Маг">
		<!-- Atk. Spd. +100\nCasting Spd. +100. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40248" toLevel="1" name="Primeval Garden's Time Stone" nameRu="Камень Продления Времени Первобытного Сада">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40249" toLevel="1" name="Primeval Isle's Time Stone" nameRu="Камень Продления Времени Первобытного Острова">
		<!-- none -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
	</skill>
	<skill id="40250" toLevel="1" name="Low-grade Pet XP Crystal" nameRu="Кристалл Опыта Питомца Низкого Качества">
		<!-- Pet XP +1,000,000. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94635</itemConsumeId>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<magicLevel>1</magicLevel>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<reuseDelay>5</reuseDelay>
		<targetType>PET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="GivePetXp">
				<xp>1000000</xp>
			</effect>
		</effects>
	</skill>
	<skill id="40251" toLevel="1" name="Pet's XP Growth Scroll" nameRu="Свиток Увеличения Опыта Питомца">
		<!-- Pet XP $s1\n\nRemoved when your pet is dead or recalled. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40252" toLevel="1" name="Scroll: Enchant Necklace of Spirits" nameRu="Свиток: Модифицировать Ожерелье Духов">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40253" toLevel="1" name="Scroll: Enchant Hellbound Ring" nameRu="Свиток: Модифицировать Кольцо Острова Ада">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40254" toLevel="1" name="Scroll of Escape: Ivory Tower" nameRu="Свиток Телепорта - Башня Слоновой Кости">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40255" toLevel="1" name="Scroll of Escape: Ivory Tower" nameRu="Свиток Телепорта - Башня Слоновой Кости">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40256" toLevel="1" name="Scroll of Escape: Ivory Tower" nameRu="Свиток Телепорта - Башня Слоновой Кости">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40257" toLevel="1" name="Scroll of Escape: Ivory Tower" nameRu="Свиток Телепорта - Башня Слоновой Кости">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40258" toLevel="1" name="Berserker's Scroll (Exchangeable)" nameRu="Свиток Берсерка (Можно обменять)">
		<!-- STR $s1 INT $s1 Atk. Spd. $s2 Casting Spd. $s2 Speed $s3 P. Skill Power $s4 M. Skill Power $s4 -->
		<icon>icon.skill0000</icon>
		<abnormalLevel>2</abnormalLevel>
		<abnormalType>BERSERKER_SCROLL</abnormalType>
		<abnormalTime>1200</abnormalTime>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94776</itemConsumeId>
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>5</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalSkillPower">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalSkillPower">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40259" toLevel="1" name="Berserker's Scroll" nameRu="Свиток Берсерка">
		<!-- STR $s1 INT $s1 Atk. Spd. $s2 Casting Spd. $s2 Speed $s3 P. Skill Power $s4 M. Skill Power $s4 -->
		<icon>icon.skill0000</icon>
		<abnormalLevel>2</abnormalLevel>
		<abnormalType>BERSERKER_SCROLL</abnormalType>
		<abnormalTime>1200</abnormalTime>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94777</itemConsumeId>
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>5</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalSkillPower">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalSkillPower">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40260" toLevel="1" name="Aden Transformation Scroll" nameRu="Свиток Трансформации Адена">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<abnormalType>RANDOM_TRANSFORMATION_SCROLL</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94814</itemConsumeId>
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CallRandomSkill">
				<skills>54219,1;54218,1;54217,1;54216,1;54215,1;54214,1</skills>
			</effect>
		</effects>
	</skill>
	<skill id="40261" toLevel="1" name="Scroll: 1,000,000,000 SP" nameRu="Свиток SP 1 000 000 000">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<hitTime>0</hitTime>
		<isMagic>0</isMagic>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94829</itemConsumeId>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>500</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="GiveSp">
				<sp>1000000000</sp>
			</effect>
		</effects>
	</skill>
	<skill id="40262" toLevel="1" name="Buff Candies" nameRu="Сладости Положительного Эффекта">
		<!-- P. Atk. $s1 M. Atk. $s1 P. Def. $s2 M. Def. $s2 Max HP $s3 Max MP $s3 Acquired XP/ SP $s4. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40263" toLevel="1" name="Transformation Scroll" nameRu="Свиток Трансформации">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<abnormalType>RANDOM_TRANSFORMATION_SCROLL</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>94838</itemConsumeId>
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CallRandomSkill">
				<skills>54219,1;54218,1;54216,1;54214,1</skills>
			</effect>
		</effects>
	</skill>
	<skill id="40264" toLevel="3" name="Regeneration" nameRu="Регенерация">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40265" toLevel="6" name="Blessed Body" nameRu="Благословение Тела">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40266" toLevel="6" name="Blessed Soul" nameRu="Благословение Души">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40267" toLevel="3" name="Focus" nameRu="Фокусировка">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40268" toLevel="3" name="Focus" nameRu="Фокусировка">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40269" toLevel="3" name="Death Whisper" nameRu="Шепот Смерти">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40270" toLevel="2" name="Haste" nameRu="Ускорение">
		<!-- For $s1, Atk. Spd. +$s2. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40271" toLevel="3" name="Acumen" nameRu="Проницательность">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40272" toLevel="3" name="Acumen" nameRu="Проницательность">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40273" toLevel="3" name="Might" nameRu="Могущество">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40274" toLevel="3" name="Might" nameRu="Могущество">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40275" toLevel="3" name="Empower" nameRu="Воодушевление">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40276" toLevel="3" name="Empower" nameRu="Воодушевление">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40277" toLevel="3" name="Shield" nameRu="Щит">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40278" toLevel="3" name="Shield" nameRu="Щит">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40279" toLevel="2" name="Magic Barrier" nameRu="Магический Барьер">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40280" toLevel="2" name="Wind Walk" nameRu="Легкая Походка">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40281" toLevel="2" name="Wind Walk" nameRu="Легкая Походка">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40282" toLevel="6" name="Concentration" nameRu="Концентрация">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40283" toLevel="6" name="Concentration" nameRu="Концентрация">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40284" toLevel="3" name="Agility" nameRu="Проворство">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40285" toLevel="3" name="Agility" nameRu="Проворство">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40286" toLevel="3" name="Guidance" nameRu="Наведение">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40287" toLevel="1" name="VIP Secret Shop Exchange Coupon" nameRu="Купон Обмена Секретной Лавки VIP">
		<!-- P. Atk. $s1 M. Atk. $s1 P. Def. $s2 M. Def. $s2 Max HP $s3 Max MP $s3 Acquired XP/ SP $s4. -->
		<icon>icon.skill0000</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3600</abnormalTime>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>1</magicLevel>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ResurrectionFeeMod">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="40288" toLevel="1" name="Life Control Tower's Scroll of Blessing" nameRu="Свиток Благословения Сторожевой Башни">
		<!-- Max HP +5% Max MP +5% Paralysis Resistance +15% Silence Resistance +15% Fear Resistance +15% Acquired XP +30% Acquired SP +30% The effect remains after death. You can cancel the effect by yourself. -->
		<icon>icon.skill0000</icon>
		<abnormalLevel>4</abnormalLevel>
        <abnormalTime>3600</abnormalTime>
       	<abnormalType>LIFE_CONTROL_TOWER_SCROLL</abnormalType>
       	<itemConsumeCount>1</itemConsumeCount>
       	<itemConsumeId>94872</itemConsumeId>
       	<isMagic>4</isMagic> <!-- Auto generated with parser -->
       	<magicLevel>1</magicLevel>
       	<operateType>A2</operateType>
       	<basicProperty>NONE</basicProperty>
       	<magicCriticalRate>5</magicCriticalRate>
       	<targetType>SELF</targetType>
       	<affectScope>SINGLE</affectScope>
       	<isTriggeredSkill>true</isTriggeredSkill>
       	<irreplacableBuff>true</irreplacableBuff>
       	<stayAfterDeath>true</stayAfterDeath>
       	<effects>
       	    <effect name="MaxHp">
       	        <amount>5</amount>
       	        <mode>PER</mode>
            </effect>
       	    <effect name="MaxMp">
       	        <amount>5</amount>
       	        <mode>PER</mode>
            </effect>
            <effect name="ExpModify">
                <amount>30</amount>
            </effect>
            <effect name="SpModify">
                <amount>30</amount>
            </effect>
            <effect name="DefenceTrait">
                <DERANGEMENT>15</DERANGEMENT>
                <PARALYZE>15</PARALYZE>
            </effect>
        </effects>
	</skill>
	<skill id="40289" toLevel="1" name="Grace Potion" nameRu="Зелье Покровительства">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<conditions>
			<condition name="CanUseSayhaGraceIncreaseItem">
				<amount>10000</amount>
			</condition>
		</conditions>
		<effects>
			<effect name="SayhaGraceSupportTrigger"/>
			<effect name="SayhaGracePointUp">
				<value>10000</value>
			</effect>
		</effects>
	</skill>
	<skill id="40290" toLevel="25" name="Five-colored Sweets - Lv. 1" nameRu="Пятицветный Рисовый Пирожок - Ур. 1">
		<!-- When used for +1 hour. STR +1 INT +1 DEX +1 WIT +1 CON +1 MEN +1 Water Attack +$s1 The buff is leveling up every hour. <Rewards> Lv. 1-7: Honey Bean Cakes (Event) 3 pcs. Lv. 8-13: Sayha's Storm Lv. 3 (5 pcs.) Lv. 14-22: Soulshot Ticket (10 pcs.) Lv. 23: Random Transformation Scroll (3 pcs.) Lv. 24: Charging Stone of Random Craft (3 pcs.) Note! Dead characters cannot obtain the gift. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40291" toLevel="1" name="Five-colored Sweets - Reward" nameRu="Пятицветный Рисовый Пирожок - Награда">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40292" toLevel="1" name="Five-colored Sweets - Reward" nameRu="Пятицветный Рисовый Пирожок - Награда">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40293" toLevel="1" name="Five-colored Sweets - Reward" nameRu="Пятицветный Рисовый Пирожок - Награда">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40294" toLevel="1" name="Five-colored Sweets - Reward" nameRu="Пятицветный Рисовый Пирожок - Награда">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40295" toLevel="1" name="Five-colored Sweets - Reward" nameRu="Пятицветный Рисовый Пирожок - Награда">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="40296" toLevel="1" name="Scroll of Escape: Master Kerkir" nameRu="Свиток Телепорта - Мастер Керкир">
		<!-- Can be used to teleport to Master Kerkir in the Wind Village. -->
		<hitTime>500</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>95586</itemConsumeId>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpAlignment">
				<affectType>CASTER</affectType>
				<alignment>LAWFUL</alignment>
			</condition>
			<condition name="OpCanEscape"/>
		</conditions>
		<effects>
			<effect name="Teleport">
				<x>102609</x>
				<y>56031</y>
				<z>-4032</z>
			</effect>
		</effects>
	</skill>
	<skill id="40297" toLevel="1" name="Scroll of Escape: Grocer Evia" nameRu="Свиток Телепорта - Бакалейщик Эвия">
		<!-- Can be used to teleport to Grocer Evia in the Wind Village. -->

		<hitTime>500</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>95587</itemConsumeId>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpAlignment">
				<affectType>CASTER</affectType>
				<alignment>LAWFUL</alignment>
			</condition>
			<condition name="OpCanEscape"/>
		</conditions>
		<effects>
			<effect name="Teleport">
				<x>105091</x>
				<y>57525</y>
				<z>-3928</z>
			</effect>
		</effects>
	</skill>
	<skill id="40298" toLevel="1" name="Scroll of Escape: High Priest Maximilian" nameRu="Свиток Телепорта - Верховный Жрец Максимилиан">
		<!-- Can be used to teleport to High Priest Maximilian in Giran. -->
		<icon>icon.skill0000</icon>
		<hitTime>0</hitTime>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>95595</itemConsumeId>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpAlignment">
				<affectType>CASTER</affectType>
				<alignment>LAWFUL</alignment>
			</condition>
			<condition name="OpCanEscape"/>
		</conditions>
		<effects>
			<effect name="Teleport">
				<x>86919</x>
				<y>148634</y>
				<z>-3410</z>
			</effect>
		</effects>
	</skill>
	<skill id="40299" toLevel="1" name="Scroll of Escape: Gorgon Flower Garden" nameRu="Свиток Телепорта - Сад Горгон">
		<!-- Can be used to teleport to the Gorgon Flower Garden. -->
		<hitTime>500</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>95588</itemConsumeId>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpAlignment">
				<affectType>CASTER</affectType>
				<alignment>LAWFUL</alignment>
			</condition>
			<condition name="OpCanEscape"/>
		</conditions>
		<effects>
			<effect name="Teleport">
				<x>118538</x>
				<y>135668</y>
				<z>-3554</z>
			</effect>
		</effects>

	</skill>
</list>
