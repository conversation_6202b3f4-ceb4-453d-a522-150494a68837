<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="17107" toLevel="1" name="Snowman's Buff" nameRu="Подарок Снеговика">
		<!-- Snowman's present. Once debuffed, no more debuffs can be applied. -->
		<icon>icon.weapon_snowman_stick_i00</icon>
		<operateType>A2</operateType>
		<isMagic>1</isMagic>
		<effectPoint>-2</effectPoint>
		<isDebuff>true</isDebuff>
	</skill>
	<skill id="17108" toLevel="1" name="Holiday Blessing" nameRu="Новогоднее Благословение">
		<!-- Restores Vitality when in a peaceful zone. -->
		<icon>icon.weapon_snowman_stick_i00</icon>
		<operateType>A2</operateType>
		<reuseDelay>30000</reuseDelay>
		<effectPoint>100</effectPoint>
		<isMagic>4</isMagic>
	</skill>
	<skill id="17109" toLevel="1" name="-" nameRu="-">
		<!-- none -->
		<operateType>A1</operateType>
	</skill>
	<skill id="17110" toLevel="1" name="Snowy Pumpkin Nectar" nameRu="Нектар Снежной Тыквы">
		<!-- none -->
		<operateType>A1</operateType>
		<castRange>200</castRange>
		<effectPoint>-150</effectPoint>
		<reuseDelay>2000</reuseDelay>
		<effectRange>300</effectRange>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>37543</itemConsumeId> <!-- Snowy Squash Nectar -->
		<magicLevel>1</magicLevel>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>OTHERS</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCanNotUseAirship" />
			<condition name="OpTargetNpc">
				<npcIds>
					<item>13399</item>
					<item>13400</item>
					<item>13401</item>
					<item>13402</item>
					<item>13403</item>
					<item>13404</item>
					<item>13405</item>
					<item>13406</item>
				</npcIds>
			</condition>
		</conditions>
	</skill>
	<skill id="17111" toLevel="1" name="Snowy Squash Seed" nameRu="Семечко Снежной Тыквы">
		<!-- none -->
		<operateType>A1</operateType>
		<hitTime>1500</hitTime>
		<reuseDelay>10000</reuseDelay>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>38923</itemConsumeId> <!-- Snowy Squash Seed -->
		<magicLevel>1</magicLevel>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>1.5</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCanNotUseAirship" />
		</conditions>
		<effects>
			<effect name="SummonNpc">
				<npcId>13399</npcId> <!-- Snowy Young Squash -->
				<npcCount>1</npcCount>
				<despawnDelay>180000</despawnDelay>
			</effect>
		</effects>
	</skill>
	<skill id="17112" toLevel="1" name="Large Snowy Squash Seed" nameRu="Семечко Снежной Большой Тыквы">
		<!-- none -->
		<operateType>A1</operateType>
		<hitTime>1500</hitTime>
		<reuseDelay>10000</reuseDelay>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>38924</itemConsumeId> <!-- Snowy Large Squash Seed -->
		<magicLevel>1</magicLevel>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>1.5</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCanNotUseAirship" />
		</conditions>
		<effects>
			<effect name="SummonNpc">
				<npcId>13403</npcId> <!-- Snowy Large Young Squash -->
				<npcCount>1</npcCount>
				<despawnDelay>180000</despawnDelay>
			</effect>
		</effects>
	</skill>
	<skill id="17113" toLevel="1" name="Christmas Buff Set 2015" nameRu="Новогодний Набор Положительных Эффектов 2015">
		<!-- none -->
		<operateType>A1</operateType>
	</skill>
	<skill id="17192" toLevel="1" name="Change Hair Accessory" nameRu="Отображение Головного Убора">
		<!-- Change hair accessories. -->
		<icon>icon.skill0810</icon>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<operateType>A1</operateType>
		<reuseDelay>180000</reuseDelay>
		<effectPoint>1</effectPoint>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="HairAccessorySet" />
		</effects>
	</skill>
</list>
