<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="60001" toLevel="1" name="Siege Spectator Mode" nameRu="Режим Наблюдения за Осадой">
		<!-- Turn on Siege Observation Mode -->
		<icon>icon.skill0922</icon>
		<operateType>P</operateType>
		<effectPoint>5000</effectPoint>
	</skill>
	<skill id="60002" toLevel="15" name="Einhasad Overseeing" nameRu="Наблюдение Эйнхасад">
		<!-- Dishonorable killings have drawn attention of Einhasad. P./ M. Def. -$s1, Speed -$s2. -->
		<icon>icon.karma</icon>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed" fromLevel="5" toLevel="8">
				<amount>
					<value level="5">-35</value>
					<value level="6">-35</value>
					<value level="7">-70</value>
					<value level="8">-70</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60003" toLevel="1" name="Server Level Ranking I" nameRu="1-е Место в Рангах за Уровень на Сервере">
		<!-- Bonus for the 1st place in the list of ranks for level on the server.\nAll basic characteristics +1, HP Recovery Potions' Effect +50, Weight Limit +100,000, P. Evasion +20. -->
		<icon>icon.server_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
			<effect name="AdditionalPotionHp">
				<amount>50</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>100000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60004" toLevel="1" name="Server Level Ranking II" nameRu="2-е Место в Рангах за Уровень на Сервере">
		<!-- Bonus for 2nd-30th places in the list of ranks for level on the server.\nCON/ MEN/ DEX/ WIT/ +1, Weight Limit +10,000. -->
		<icon>icon.server_ranking_2</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
			<effect name="WeightLimit">
				<amount>10000</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60005" toLevel="1" name="Server Level Ranking III" nameRu="3-е Место в Рангах за Уровень на Сервере">
		<!-- Bonus for 31-100th places in the list of ranks for level on the server.\nCON/ MEN/ +1, Weight Limit +5,000. -->
		<icon>icon.server_ranking_3</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
			<effect name="WeightLimit">
				<amount>5000</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60006" toLevel="1" name="Human Level Ranking - 1st Place" nameRu="1-е Место в Рангах за Уровень Среди Людей">
		<!-- Bonus for the 1st place in the list of ranks for level among humans.\n\nP. Critical Damage/ M. Skill Critical Damage +300. -->
		<icon>icon.race_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalDamage">
				<amount>300</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillCriticalDamage">
				<amount>300</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalDamage">
				<amount>300</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60007" toLevel="1" name="Elf Level Ranking - 1st Place" nameRu="1-е Место в Рангах за Уровень Среди Эльфов">
		<!-- Bonus for 1st place in the list of ranks for level among elves.\nP./ M. Atk. +300. -->
		<icon>icon.race_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>400</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxCp">
				<amount>300</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60008" toLevel="1" name="Dark Elf Level Ranking - 1st Place" nameRu="1-е Место в Рангах за Уровень Среди Темных Эльфов">
		<!-- Bonus for 1st place in the list of ranks for level among dark elves.\nAtk. Spd./ Casting Spd. +200. -->
		<icon>icon.race_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PveMagicalSkillDefenceBonus">
				<amount>-1.8</amount>
			</effect>
			<effect name="PvePhysicalSkillDefenceBonus">
				<amount>-1.8</amount>
			</effect>
			<effect name="PvpMagicalSkillDefenceBonus">
				<amount>-1.8</amount>
			</effect>
			<effect name="PvpPhysicalSkillDefenceBonus">
				<amount>-1.8</amount>
			</effect>
		</effects>
	</skill>
	<skill id="60009" toLevel="1" name="Orc Level Ranking - 1st Place" nameRu="1-е Место в Рангах за Уровень Среди Орков">
		<!-- Bonus for the 1st place in the list of ranks for level among orcs.\nP. Critical Rate/ M. Skill Critical Rate +20. -->
		<icon>icon.race_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalRate">
				<amount>2</amount>
				<mode>PER</mode>
			</effect>
			<effect name="SkillCriticalProbability">
				<amount>2</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>2</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60010" toLevel="1" name="Dwarf Level Ranking - 1st Place" nameRu="1-е Место в Рангах за Уровень Среди Гномов">
		<!-- Bonus for 1st place in the list of ranks for level among dwarves.\nEnchant success rate for weapon and armor when using scrolls +1%%. -->
		<icon>icon.race_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="EnchantRate">
				<amount>1</amount>
			</effect>
			<effect name="PAtk">
				<amount>300</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>500</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60011" toLevel="1" name="Kamael Level Ranking - 1st Place" nameRu="1-е Место в Рангах за Уровень Среди Камаэль">
		<!-- Bonus for 1st place in the list of ranks for level among Kamael.\nP./ M. Accuracy, P./ M. Evasion +5. -->
		<icon>icon.race_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="ResistAbnormalByCategory">
				<amount>-2</amount>
				<slot>DEBUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="60012" toLevel="1" name="Server Level Ranking Effect I" nameRu="Бонусы за 1 Ранг на Сервере">
		<!-- CON +1.\nMEN +1.\nWeight Limit +5000. -->
		<icon>icon.UI_server_ranking_1</icon>
		<magicLevel>40</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<!-- <effects>
			<effect name="WeightLimit">
				<amount>5000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects> -->
	</skill>
	<skill id="60013" toLevel="1" name="Server Level Ranking Effect II" nameRu="Бонусы за 2 Ранг на Сервере">
		<!-- DEX +1.\nWIT +1.\nWeight Limit +5000. -->
		<magicLevel>40</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<!-- <effects>
			<effect name="WeightLimit">
				<amount>5000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects> -->
	</skill>
	<skill id="60014" toLevel="1" name="Server Level Ranking Effect III" nameRu="Бонусы за 3 Ранг на Сервере">
		<!-- STR +1\nINT +1\nWeight limit +90,000\nP. Evasion +20\nWhen HP Recovery Potions are used, additionally recovers 50 HP. -->
		<icon>icon.UI_server_ranking_3</icon>
		<magicLevel>40</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<!-- <effects>
			<effect name="WeightLimit">
				<amount>90000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="AdditionalPotionHp">
				<amount>50</amount>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
		</effects> -->
	</skill>
	<skill id="60015" toLevel="1" name="Race Ranking Effect" nameRu="Бонус за Ранг Среди Рас">
		<!-- For warriors that have taken the 1st place in rankings among their race. Humans: P. Critical Damage/ M. Skill Critical Damage increased.\nElves: P./ M. Atk. increased.\nDark Elves: Atk. Spd./ Casting Spd. increased.\nOrcs: P. Critical Rate/ M. Skill Critical Rate increased.\nDwarves: Enchant success rate increased.\nKamaels: P./ M. Accuracy, P./ M. Evasion increased.\n\nFor those who placed 1st-3rd:\n\nAll races: Worthy Warrior Transformation with unique appearance available.\nDeath Knights: Unique cloak appearance (only for the 1st place) and Worthy Warrior Bonus available. -->
		<icon>icon.UI_race_ranking_1</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="60016" toLevel="1" name="Effect of Holy Relic 1" nameRu="Действие Священной Реликвии 1">
		<!-- Activate the effect of Holy Relic -->
		<operateType>A1</operateType>
		<isMagic>1</isMagic>
		<hitTime>3500</hitTime>
	</skill>
	<skill id="60017" toLevel="1" name="Effect of Holy Relic 2" nameRu="Действие Священной Реликвии 2">
		<!-- Activate the effect of Holy Relic -->
		<operateType>A1</operateType>
		<isMagic>1</isMagic>
		<hitTime>3500</hitTime>
	</skill>
	<skill id="60018" toLevel="2" name="Teleport" nameRu="Телепорт">
		<!-- Teleport -->
		<icon>icon.skill0000</icon>
		<hitTime>4000</hitTime>
		<isTeleport>true</isTeleport>
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCanEscape" />
		</conditions>
		<effects>
			<effect name="TeleportToVariableLoc" />
		</effects>
	</skill>
	<skill id="60019" toLevel="5" name="Jaban's Blessing" nameRu="Благословение Джейбана">
		<icon>icon.skill30518</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>OLYMPIAD_BUFF</abnormalType>
		<abnormalVisualEffect>
			<value level="1">U_ER_WI_WINDHIDE_AVE</value>
			<value level="2">U_ER_WI_WINDHIDE_AVE</value>
			<value level="3">U_ER_WI_WINDHIDE_AVE</value>
			<value level="4">VP_UP;HERB_PA_UP</value>
			<value level="5">VP_UP;HERB_PA_UP</value>
		</abnormalVisualEffect>
		<castRange>900</castRange>
		<effectPoint>624</effectPoint>
		<effectRange>1500</effectRange>
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>4</isMagic>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">3000</value>
					<value level="2">5000</value>
					<value level="3">8000</value>
					<value level="4">12000</value>
					<value level="5">20000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PvpMagicalSkillDefenceBonus">
				<amount>
					<value level="1">-10</value>
					<value level="2">-10</value>
					<value level="3">-10</value>
					<value level="4">-10</value>
					<value level="5">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalAttackDefenceBonus">
				<amount>
					<value level="1">-10</value>
					<value level="2">-10</value>
					<value level="3">-10</value>
					<value level="4">-10</value>
					<value level="5">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalSkillDefenceBonus">
				<amount>
					<value level="1">-10</value>
					<value level="2">-10</value>
					<value level="3">-10</value>
					<value level="4">-10</value>
					<value level="5">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">0</value>
					<value level="2">1000</value>
					<value level="3">2500</value>
					<value level="4">4000</value>
					<value level="5">8000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">0</value>
					<value level="2">500</value>
					<value level="3">1200</value>
					<value level="4">2000</value>
					<value level="5">3000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">0</value>
					<value level="2">0</value>
					<value level="3">2000</value>
					<value level="4">3500</value>
					<value level="5">6000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">0</value>
					<value level="2">0</value>
					<value level="3">1500</value>
					<value level="4">2500</value>
					<value level="5">4000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">0</value>
					<value level="2">0</value>
					<value level="3">0</value>
					<value level="4">20</value>
					<value level="5">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">0</value>
					<value level="2">0</value>
					<value level="3">0</value>
					<value level="4">15</value>
					<value level="5">25</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="60020" toLevel="1" name="Test - The 4th Place in the List of Ranks for Level on the Server">
		<!-- Bonus for the 4-6th places in the list of ranks for level on the server.\nCON/ MEN +1, Weight Limit +5,000. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60021" toLevel="1" name="Test - The 5th Place in the List of Ranks for Level on the Server">
		<!-- Bonus for the 7-10th places in the list of ranks for level on the server.\nCON/ MEN +1, Weight Limit +5,000. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60022" toLevel="1" name="Test - The 2nd Place in the List of Ranks for Level Among Humans">
		<!-- Bonus for the 2nd and 3rd places in the list of ranks for level among humans.\nP. Critical Damage/ M. Skill Critical Damage +300. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60023" toLevel="1" name="Test - The 2nd Place in the List of Ranks for Level Among Elves">
		<!-- Bonus for the 2nd and 3rd places in the list of ranks for level among elves.\nP./ M. Atk. +300. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60024" toLevel="1" name="Test - The 2nd Place in the List of Ranks for Level Among Dark Elves">
		<!-- Bonus for the 2nd and 3rd places in the list of ranks for level among dark elves.\nAtk. Spd./ Casting Spd. +200. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60025" toLevel="1" name="Test - The 2nd Place in the List of Ranks for Level Among Orcs">
		<!-- Bonus for the 2nd and 3rd places in the list of ranks for level among orcs.\nP. Critical Rate/ M. Skill Critical Rate +20. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60026" toLevel="1" name="Test - The 2nd Place in the List of Ranks for Level Among Dwarves">
		<!-- Bonus for 2nd and 3rd places in the list of ranks for level among dwarves.\nEnchant success rate for weapon and armor when using scrolls +1%%. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60027" toLevel="1" name="Test - The 2nd Place in the List of Ranks for Level Among Kamael">
		<!-- Bonus for 2nd and 3rd places in the list of ranks for level among Kamael.\nP./ M. Accuracy, P./ M. Evasion +5. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60028" toLevel="1" name="Test - The 3rd Place in the List of Ranks for Level Among Humans">
		<!-- Bonus for the 4-5th place in the list of ranks for level among humans.\nP. Critical Damage/ M. Skill Critical Damage +300. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60029" toLevel="1" name="Test - The 3rd Place in the List of Ranks for Level Among Elves">
		<!-- Bonus for the 4-5th place in the list of ranks for level among elves.\nP./ M. Atk. +300. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60030" toLevel="1" name="Test - The 3rd Place in the List of Ranks for Level Among Dark Elves">
		<!-- Bonus for the 4-5th place in the list of ranks for level among dark elves.\nAtk. Spd./ Casting Spd. +200. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60031" toLevel="1" name="Test - The 3rd Place in the List of Ranks for Level Among Orcs">
		<!-- Bonus for the 4-5th place in the list of ranks for level among orcs.\nP. Critical Rate/ M. Skill Critical Rate +20. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60032" toLevel="1" name="Test - The 3rd Place in the List of Ranks for Level Among Dwarves">
		<!-- Bonus for 4-5th place in the list of ranks for level among dwarves.\nEnchant success rate for weapon and armor when using scrolls +1%%. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
<skill id="60033" toLevel="1" name="Basic PvP Damage +10%">
		<!-- Basic PvP Damage +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60034" toLevel="1" name="Basic PvP Damage +15%">
		<!-- Basic PvP Damage +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60035" toLevel="1" name="PvP P. Damage +5%">
		<!-- PvE P. Damage +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60036" toLevel="1" name="PvE P. Damage +10%">
		<!-- PvE P. Damage +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60037" toLevel="1" name="PvE P. Damage +15%">
		<!-- PvE P. Damage +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60038" toLevel="1" name="PvE M. Damage +5%">
		<!-- PvE M. Damage +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60039" toLevel="1" name="PvE M. Damage +10%">
		<!-- PvE M. Damage +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60040" toLevel="1" name="PvE M. Damage +15%">
		<!-- PvE M. Damage +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60041" toLevel="1" name="Basic PvE Damage +5%">
		<!-- Basic PvE Damage +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60042" toLevel="1" name="Basic PvE Damage +10%">
		<!-- Basic PvE Damage +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60043" toLevel="1" name="Basic PvE Damage +15%">
		<!-- Basic PvE Damage +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60044" toLevel="1" name="PvE P. Damage +5%">
		<!-- PvE P. Damage +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60045" toLevel="1" name="PvE P. Damage +10%">
		<!-- PvE P. Damage +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60046" toLevel="1" name="PvE P. Damage +15%">
		<!-- PvE P. Damage +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60047" toLevel="1" name="PvE M. Damage +5%">
		<!-- PvE M. Damage +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60048" toLevel="1" name="PvE M. Damage +10%">
		<!-- PvE M. Damage +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60049" toLevel="1" name="PvE M. Damage +15%">
		<!-- PvE M. Damage +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60050" toLevel="1" name="Basic P. Def. in PvP +5%">
		<!-- Basic P. Def. in PvP +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60051" toLevel="1" name="Basic P. Def. in PvP +10%">
		<!-- Basic P. Def. in PvP +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60052" toLevel="1" name="Basic P. Def. in PvP +15%">
		<!-- Basic P. Def. in PvP +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60053" toLevel="1" name="P. P. Def. in PvP +5%">
		<!-- P. P. Def. in PvP +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60054" toLevel="1" name="P. P. Def. in PvP +10%">
		<!-- P. P. Def. in PvP +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60055" toLevel="1" name="P. P. Def. in PvP +15%">
		<!-- P. P. Def. in PvP +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60056" toLevel="1" name="M. P. Def. in PvP +5%">
		<!-- M. P. Def. in PvP +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60057" toLevel="1" name="M. P. Def. in PvP +10%">
		<!-- M. P. Def. in PvP +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60058" toLevel="1" name="M. P. Def. in PvP +15%">
		<!-- M. P. Def. in PvP +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60059" toLevel="1" name="Basic P. Def. in PvE +5%">
		<!-- Basic P. Def. in PvE +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60060" toLevel="1" name="Basic P. Def. in PvE +10%">
		<!-- Basic P. Def. in PvE +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60061" toLevel="1" name="Basic P. Def. in PvE +15%">
		<!-- Basic P. Def. in PvE +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60062" toLevel="1" name="P. P. Def. in PvE +5%">
		<!-- P. P. Def. in PvE +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60063" toLevel="1" name="P. P. Def. in PvE +10%">
		<!-- P. P. Def. in PvE +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60064" toLevel="1" name="P. P. Def. in PvE +15%">
		<!-- P. P. Def. in PvE +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60065" toLevel="1" name="M. P. Def. in PvE +5%">
		<!-- M. P. Def. in PvE +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60066" toLevel="1" name="M. P. Def. in PvE +10%">
		<!-- M. P. Def. in PvE +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60067" toLevel="1" name="M. P. Def. in PvE +15%">
		<!-- M. P. Def. in PvE +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60068" toLevel="1" name="Received Critical Damage from standard attacks -5%">
		<!-- Received Critical Damage from standard attacks -5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60069" toLevel="1" name="Received Critical Damage from standard attacks -10%">
		<!-- Received Critical Damage from standard attacks -10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60070" toLevel="1" name="Received Critical Damage from standard attacks -15%">
		<!-- Received Critical Damage from standard attacks -15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60071" toLevel="1" name="All received Critical Damage -5">
		<!-- All received Critical Damage -5 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60072" toLevel="1" name="All received Critical Damage -10">
		<!-- All received Critical Damage -10 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60073" toLevel="1" name="All received Critical Damage -15">
		<!-- All received Critical Damage -15 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60074" toLevel="1" name="Received P. Critical Damage -5%">
		<!-- Received P. Critical Damage -5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60075" toLevel="1" name="Received P. Critical Damage -10%">
		<!-- Received P. Critical Damage -10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60076" toLevel="1" name="Received P. Critical Damage -15%">
		<!-- Received P. Critical Damage -15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60077" toLevel="1" name="Received P. Critical Damage -5">
		<!-- Received P. Critical Damage -5 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60078" toLevel="1" name="Received P. Critical Damage -10">
		<!-- Received P. Critical Damage -10 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60079" toLevel="1" name="Received P. Critical Damage -15">
		<!-- Received P. Critical Damage -15 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60080" toLevel="1" name="Received M. Critical Damage -5%">
		<!-- Received M. Critical Damage -5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60081" toLevel="1" name="Received M. Critical Damage -10%">
		<!-- Received M. Critical Damage -10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60082" toLevel="1" name="Received M. Critical Damage -15%">
		<!-- Received M. Critical Damage -15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60083" toLevel="1" name="Received M. Critical Damage -5">
		<!-- Received M. Critical Damage -5 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60084" toLevel="1" name="Received M. Critical Damage -10">
		<!-- Received M. Critical Damage -10 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60085" toLevel="1" name="Received M. Critical Damage -15">
		<!-- Received M. Critical Damage -15 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60086" toLevel="1" name="Shield Defense +5%">
		<!-- Shield Defense +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60087" toLevel="1" name="Shield Defense +10%">
		<!-- Shield Defense +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60088" toLevel="1" name="Shield Defense +15%">
		<!-- Shield Defense +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60089" toLevel="1" name="Shield Defense Bonus +5">
		<!-- Shield Defense Bonus +5 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60090" toLevel="1" name="Shield Defense Bonus +10">
		<!-- Shield Defense Bonus +10 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60091" toLevel="1" name="Shield Defense Bonus +15">
		<!-- Shield Defense Bonus +15 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60092" toLevel="1" name="Shield Defense Rate 5%">
		<!-- Shield Defense Rate 5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60093" toLevel="1" name="Shield Defense Rate 10%">
		<!-- Shield Defense Rate 10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60094" toLevel="1" name="Shield Defense Rate 15%">
		<!-- Shield Defense Rate 15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60095" toLevel="1" name="M. Damage Resistance +5%">
		<!-- M. Damage Resistance +5% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60096" toLevel="1" name="M. Damage Resistance +10%">
		<!-- M. Damage Resistance +10% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60097" toLevel="1" name="M. Damage Resistance +15%">
		<!-- M. Damage Resistance +15% -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60098" toLevel="1" name="M. Damage Resistance Bonus +5">
		<!-- M. Damage Resistance Bonus +5 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="60099" toLevel="1" name="M. Damage Resistance Bonus +10">
		<!-- M. Damage Resistance Bonus +10 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
</list>
