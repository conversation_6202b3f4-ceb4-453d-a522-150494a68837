<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="45200" toLevel="24" name="Transcendent Mortal Blow" nameRu="Невероятный Смертельный Удар">
		<!-- Attacks the target's vital points with $s1 power.\nRequires a dagger.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0016</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-35</value>
			<value level="2">-40</value>
			<value level="3">-45</value>
			<value level="4">-60</value>
			<value level="5">-65</value>
			<value level="6">-70</value>
			<value level="7">-85</value>
			<value level="8">-90</value>
			<value level="9">-95</value>
			<value level="10">-110</value>
			<value level="11">-115</value>
			<value level="12">-120</value>
			<value level="13">-130</value>
			<value level="14">-135</value>
			<value level="15">-140</value>
			<value level="16">-150</value>
			<value level="17">-155</value>
			<value level="18">-160</value>
			<value level="19">-170</value>
			<value level="20">-175</value>
			<value level="21">-180</value>
			<value level="22">-190</value>
			<value level="23">-195</value>
			<value level="24">-200</value>
		</effectPoint>
		<mpConsume>
			<value level="1">5</value>
			<value level="2">6</value>
			<value level="3">6</value>
			<value level="4">9</value>
			<value level="5">9</value>
			<value level="6">9</value>
			<value level="7">11</value>
			<value level="8">12</value>
			<value level="9">13</value>
			<value level="10">15</value>
			<value level="11">16</value>
			<value level="12">17</value>
			<value level="13">18</value>
			<value level="14">18</value>
			<value level="15">19</value>
			<value level="16">20</value>
			<value level="17">21</value>
			<value level="18">22</value>
			<value level="19">23</value>
			<value level="20">24</value>
			<value level="21">25</value>
			<value level="22">26</value>
			<value level="23">27</value>
			<value level="24">27</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>DAGGER</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45201" toLevel="24" name="Transcendent Power Shot" nameRu="Невероятный Мощный Выстрел">
		<!-- Shoots an arrow dealing damage with $s1 power.\nRequires a bow.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0056</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-35</value>
			<value level="2">-40</value>
			<value level="3">-45</value>
			<value level="4">-60</value>
			<value level="5">-65</value>
			<value level="6">-70</value>
			<value level="7">-85</value>
			<value level="8">-90</value>
			<value level="9">-95</value>
			<value level="10">-110</value>
			<value level="11">-115</value>
			<value level="12">-120</value>
			<value level="13">-130</value>
			<value level="14">-135</value>
			<value level="15">-140</value>
			<value level="16">-150</value>
			<value level="17">-155</value>
			<value level="18">-160</value>
			<value level="19">-170</value>
			<value level="20">-175</value>
			<value level="21">-180</value>
			<value level="22">-190</value>
			<value level="23">-195</value>
			<value level="24">-200</value>
		</effectPoint>
		<mpConsume>
			<value level="1">6</value>
			<value level="2">7</value>
			<value level="3">8</value>
			<value level="4">10</value>
			<value level="5">11</value>
			<value level="6">12</value>
			<value level="7">15</value>
			<value level="8">16</value>
			<value level="9">16</value>
			<value level="10">19</value>
			<value level="11">20</value>
			<value level="12">21</value>
			<value level="13">22</value>
			<value level="14">23</value>
			<value level="15">24</value>
			<value level="16">26</value>
			<value level="17">27</value>
			<value level="18">27</value>
			<value level="19">29</value>
			<value level="20">30</value>
			<value level="21">31</value>
			<value level="22">33</value>
			<value level="23">33</value>
			<value level="24">34</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45202" toLevel="24" name="Transcendent Iron Punch" nameRu="Невероятный Железный Удар">
		<!-- Attacks the enemy with $s1 power.\nRequires a fist weapon.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0029</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-35</value>
			<value level="2">-40</value>
			<value level="3">-45</value>
			<value level="4">-60</value>
			<value level="5">-65</value>
			<value level="6">-70</value>
			<value level="7">-85</value>
			<value level="8">-90</value>
			<value level="9">-95</value>
			<value level="10">-110</value>
			<value level="11">-115</value>
			<value level="12">-120</value>
			<value level="13">-130</value>
			<value level="14">-135</value>
			<value level="15">-140</value>
			<value level="16">-150</value>
			<value level="17">-155</value>
			<value level="18">-160</value>
			<value level="19">-170</value>
			<value level="20">-175</value>
			<value level="21">-180</value>
			<value level="22">-190</value>
			<value level="23">-195</value>
			<value level="24">-200</value>
		</effectPoint>
		<mpConsume>
			<value level="1">5</value>
			<value level="2">6</value>
			<value level="3">6</value>
			<value level="4">9</value>
			<value level="5">9</value>
			<value level="6">9</value>
			<value level="7">11</value>
			<value level="8">12</value>
			<value level="9">13</value>
			<value level="10">15</value>
			<value level="11">16</value>
			<value level="12">17</value>
			<value level="13">18</value>
			<value level="14">18</value>
			<value level="15">19</value>
			<value level="16">20</value>
			<value level="17">21</value>
			<value level="18">22</value>
			<value level="19">23</value>
			<value level="20">24</value>
			<value level="21">25</value>
			<value level="22">26</value>
			<value level="23">27</value>
			<value level="24">27</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>DUALFIST</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45203" toLevel="1" name="Master of Aggression">
		<operateType>P</operateType>
		<effects>
            <effect name="EffectFlag">
                <flag>MASTER_OF_AGGRESSION_1</flag>
            </effect>
		</effects>
	</skill>
	<skill id="45204" toLevel="40" name="Transcendent Triple Sonic Slash" nameRu="Невероятное Тройное Звуковое Рассечение">
		<!-- Attacks the enemy with dual swords with $s1 power.\nRequires a dual swords.\nIncreases power upon consuming Momentum (up to 3).\nIgnores the enemy’s defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0261</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-245</value>
			<value level="2">-255</value>
			<value level="3">-265</value>
			<value level="4">-280</value>
			<value level="5">-295</value>
			<value level="6">-300</value>
			<value level="7">-305</value>
			<value level="8">-310</value>
			<value level="9">-315</value>
			<value level="10">-320</value>
			<value level="11">-325</value>
			<value level="12">-330</value>
			<value level="13">-335</value>
			<value level="14">-340</value>
			<value level="15">-345</value>
			<value level="16">-350</value>
			<value level="17">-355</value>
			<value level="18">-360</value>
			<value level="19">-365</value>
			<value level="20">-370</value>
			<value level="21">-375</value>
			<value level="22">-380</value>
			<value level="23">-385</value>
			<value level="24">-390</value>
			<value level="25">-395</value>
			<value level="26">-400</value>
			<value level="27">-405</value>
			<value level="28">-410</value>
			<value level="29">-415</value>
			<value level="30">-420</value>
			<value level="31">-425</value>
			<value level="32">-430</value>
			<value level="33">-435</value>
			<value level="34">-440</value>
			<value level="35">-445</value>
			<value level="36">-450</value>
			<value level="37">-455</value>
			<value level="38">-460</value>
			<value level="39">-465</value>
			<value level="40">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">36</value>
			<value level="2">38</value>
			<value level="3">39</value>
			<value level="4">42</value>
			<value level="5">44</value>
			<value level="6">45</value>
			<value level="7">45</value>
			<value level="8">46</value>
			<value level="9">47</value>
			<value level="10">48</value>
			<value level="11">48</value>
			<value level="12">49</value>
			<value level="13">50</value>
			<value level="14">51</value>
			<value level="15">51</value>
			<value level="16">52</value>
			<value level="17">53</value>
			<value level="18">54</value>
			<value level="19">54</value>
			<value level="20">55</value>
			<value level="21">56</value>
			<value level="22">57</value>
			<value level="23">57</value>
			<value level="24">58</value>
			<value level="25">59</value>
			<value level="26">60</value>
			<value level="27">60</value>
			<value level="28">61</value>
			<value level="29">62</value>
			<value level="30">63</value>
			<value level="31">63</value>
			<value level="32">64</value>
			<value level="33">65</value>
			<value level="34">66</value>
			<value level="35">66</value>
			<value level="36">67</value>
			<value level="37">68</value>
			<value level="38">69</value>
			<value level="39">69</value>
			<value level="40">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45205" toLevel="55" name="Transcendent Double Shot" nameRu="Невероятный Двойной Выстрел">
		<!-- Shoots 2 arrows in succession to attack the enemy with $s1 power.\nRequires a bow.\nCritical.\nOver-hit.\nCan be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0019</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-210</value>
			<value level="2">-215</value>
			<value level="3">-220</value>
			<value level="4">-225</value>
			<value level="5">-230</value>
			<value level="6">-235</value>
			<value level="7">-240</value>
			<value level="8">-245</value>
			<value level="9">-250</value>
			<value level="10">-255</value>
			<value level="11">-260</value>
			<value level="12">-265</value>
			<value level="13">-270</value>
			<value level="14">-275</value>
			<value level="15">-280</value>
			<value level="16">-285</value>
			<value level="17">-290</value>
			<value level="18">-295</value>
			<value level="19">-300</value>
			<value level="20">-305</value>
			<value level="21">-310</value>
			<value level="22">-315</value>
			<value level="23">-320</value>
			<value level="24">-325</value>
			<value level="25">-330</value>
			<value level="26">-335</value>
			<value level="27">-340</value>
			<value level="28">-345</value>
			<value level="29">-350</value>
			<value level="30">-355</value>
			<value level="31">-360</value>
			<value level="32">-365</value>
			<value level="33">-370</value>
			<value level="34">-375</value>
			<value level="35">-380</value>
			<value level="36">-385</value>
			<value level="37">-390</value>
			<value level="38">-400</value>
			<value level="39">-405</value>
			<value level="40">-410</value>
			<value level="41">-420</value>
			<value level="42">-425</value>
			<value level="43">-430</value>
			<value level="44">-435</value>
			<value level="45">-440</value>
			<value level="46">-440</value>
			<value level="47">-440</value>
			<value level="48">-440</value>
			<value level="49">-440</value>
			<value level="50">-440</value>
			<value level="51">-440</value>
			<value level="52">-440</value>
			<value level="53">-440</value>
			<value level="54">-440</value>
			<value level="55">-440</value>
		</effectPoint>
		<mpConsume>
			<value level="1">34</value>
			<value level="2">35</value>
			<value level="3">36</value>
			<value level="4">37</value>
			<value level="5">38</value>
			<value level="6">38</value>
			<value level="7">39</value>
			<value level="8">40</value>
			<value level="9">41</value>
			<value level="10">42</value>
			<value level="11">43</value>
			<value level="12">44</value>
			<value level="13">44</value>
			<value level="14">45</value>
			<value level="15">46</value>
			<value level="16">47</value>
			<value level="17">48</value>
			<value level="18">49</value>
			<value level="19">50</value>
			<value level="20">50</value>
			<value level="21">51</value>
			<value level="22">52</value>
			<value level="23">53</value>
			<value level="24">54</value>
			<value level="25">55</value>
			<value level="26">55</value>
			<value level="27">56</value>
			<value level="28">57</value>
			<value level="29">58</value>
			<value level="30">59</value>
			<value level="31">60</value>
			<value level="32">61</value>
			<value level="33">61</value>
			<value level="34">62</value>
			<value level="35">63</value>
			<value level="36">64</value>
			<value level="37">65</value>
			<value level="38">66</value>
			<value level="39">67</value>
			<value level="40">67</value>
			<value level="41">68</value>
			<value level="42">69</value>
			<value level="43">70</value>
			<value level="44">71</value>
			<value level="45">72</value>
			<value level="46">72</value>
			<value level="47">73</value>
			<value level="48">74</value>
			<value level="49">75</value>
			<value level="50">76</value>
			<value level="51">77</value>
			<value level="52">78</value>
			<value level="53">78</value>
			<value level="54">79</value>
			<value level="55">80</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45206" toLevel="55" name="Transcendent Fatal Strike" nameRu="Невероятный Фатальный Удар">
		<!-- Attacks the target with $s1 power.\nRequires a sword, a blunt weapon, a spear, or dual swords.\nIgnores Shield Defense.\nIgnores $s2 of target's defense.\nCritical.\nOver-hit.\nCan be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0190</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-200</value>
			<value level="2">-205</value>
			<value level="3">-210</value>
			<value level="4">-215</value>
			<value level="5">-220</value>
			<value level="6">-225</value>
			<value level="7">-230</value>
			<value level="8">-235</value>
			<value level="9">-240</value>
			<value level="10">-245</value>
			<value level="11">-250</value>
			<value level="12">-255</value>
			<value level="13">-260</value>
			<value level="14">-265</value>
			<value level="15">-270</value>
			<value level="16">-275</value>
			<value level="17">-280</value>
			<value level="18">-285</value>
			<value level="19">-290</value>
			<value level="20">-295</value>
			<value level="21">-300</value>
			<value level="22">-305</value>
			<value level="23">-310</value>
			<value level="24">-315</value>
			<value level="25">-320</value>
			<value level="26">-325</value>
			<value level="27">-330</value>
			<value level="28">-335</value>
			<value level="29">-340</value>
			<value level="30">-345</value>
			<value level="31">-350</value>
			<value level="32">-355</value>
			<value level="33">-360</value>
			<value level="34">-365</value>
			<value level="35">-370</value>
			<value level="36">-375</value>
			<value level="37">-380</value>
			<value level="38">-385</value>
			<value level="39">-390</value>
			<value level="40">-395</value>
			<value level="41">-400</value>
			<value level="42">-405</value>
			<value level="43">-410</value>
			<value level="44">-415</value>
			<value level="45">-420</value>
			<value level="46">-425</value>
			<value level="47">-430</value>
			<value level="48">-435</value>
			<value level="49">-440</value>
			<value level="50">-445</value>
			<value level="51">-450</value>
			<value level="52">-455</value>
			<value level="53">-460</value>
			<value level="54">-465</value>
			<value level="55">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">30</value>
			<value level="2">30</value>
			<value level="3">31</value>
			<value level="4">32</value>
			<value level="5">33</value>
			<value level="6">33</value>
			<value level="7">34</value>
			<value level="8">35</value>
			<value level="9">36</value>
			<value level="10">36</value>
			<value level="11">37</value>
			<value level="12">38</value>
			<value level="13">39</value>
			<value level="14">39</value>
			<value level="15">40</value>
			<value level="16">41</value>
			<value level="17">42</value>
			<value level="18">42</value>
			<value level="19">43</value>
			<value level="20">44</value>
			<value level="21">45</value>
			<value level="22">45</value>
			<value level="23">46</value>
			<value level="24">47</value>
			<value level="25">48</value>
			<value level="26">48</value>
			<value level="27">49</value>
			<value level="28">50</value>
			<value level="29">51</value>
			<value level="30">51</value>
			<value level="31">52</value>
			<value level="32">53</value>
			<value level="33">54</value>
			<value level="34">54</value>
			<value level="35">55</value>
			<value level="36">56</value>
			<value level="37">57</value>
			<value level="38">57</value>
			<value level="39">58</value>
			<value level="40">59</value>
			<value level="41">60</value>
			<value level="42">60</value>
			<value level="43">61</value>
			<value level="44">62</value>
			<value level="45">63</value>
			<value level="46">63</value>
			<value level="47">64</value>
			<value level="48">65</value>
			<value level="49">66</value>
			<value level="50">66</value>
			<value level="51">67</value>
			<value level="52">68</value>
			<value level="53">69</value>
			<value level="54">69</value>
			<value level="55">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45207" toLevel="55" name="Transcendent Deadly Blow" nameRu="Невероятный Смертельный Импульс">
		<!-- Attacks the target's vital points with $s1 power.\nRequires a dagger.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0263</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-200</value>
			<value level="2">-205</value>
			<value level="3">-210</value>
			<value level="4">-215</value>
			<value level="5">-220</value>
			<value level="6">-225</value>
			<value level="7">-230</value>
			<value level="8">-235</value>
			<value level="9">-240</value>
			<value level="10">-245</value>
			<value level="11">-250</value>
			<value level="12">-255</value>
			<value level="13">-260</value>
			<value level="14">-265</value>
			<value level="15">-270</value>
			<value level="16">-275</value>
			<value level="17">-280</value>
			<value level="18">-285</value>
			<value level="19">-290</value>
			<value level="20">-295</value>
			<value level="21">-300</value>
			<value level="22">-305</value>
			<value level="23">-310</value>
			<value level="24">-315</value>
			<value level="25">-320</value>
			<value level="26">-325</value>
			<value level="27">-330</value>
			<value level="28">-335</value>
			<value level="29">-340</value>
			<value level="30">-345</value>
			<value level="31">-350</value>
			<value level="32">-355</value>
			<value level="33">-360</value>
			<value level="34">-365</value>
			<value level="35">-370</value>
			<value level="36">-375</value>
			<value level="37">-380</value>
			<value level="38">-385</value>
			<value level="39">-390</value>
			<value level="40">-395</value>
			<value level="41">-400</value>
			<value level="42">-405</value>
			<value level="43">-410</value>
			<value level="44">-415</value>
			<value level="45">-420</value>
			<value level="46">-425</value>
			<value level="47">-430</value>
			<value level="48">-435</value>
			<value level="49">-440</value>
			<value level="50">-445</value>
			<value level="51">-450</value>
			<value level="52">-455</value>
			<value level="53">-460</value>
			<value level="54">-465</value>
			<value level="55">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">30</value>
			<value level="2">30</value>
			<value level="3">31</value>
			<value level="4">32</value>
			<value level="5">33</value>
			<value level="6">33</value>
			<value level="7">34</value>
			<value level="8">35</value>
			<value level="9">36</value>
			<value level="10">36</value>
			<value level="11">37</value>
			<value level="12">38</value>
			<value level="13">39</value>
			<value level="14">39</value>
			<value level="15">40</value>
			<value level="16">41</value>
			<value level="17">42</value>
			<value level="18">42</value>
			<value level="19">43</value>
			<value level="20">44</value>
			<value level="21">45</value>
			<value level="22">45</value>
			<value level="23">46</value>
			<value level="24">47</value>
			<value level="25">48</value>
			<value level="26">48</value>
			<value level="27">49</value>
			<value level="28">50</value>
			<value level="29">51</value>
			<value level="30">51</value>
			<value level="31">52</value>
			<value level="32">53</value>
			<value level="33">54</value>
			<value level="34">54</value>
			<value level="35">55</value>
			<value level="36">56</value>
			<value level="37">57</value>
			<value level="38">57</value>
			<value level="39">58</value>
			<value level="40">59</value>
			<value level="41">60</value>
			<value level="42">60</value>
			<value level="43">61</value>
			<value level="44">62</value>
			<value level="45">63</value>
			<value level="46">63</value>
			<value level="47">64</value>
			<value level="48">65</value>
			<value level="49">66</value>
			<value level="50">66</value>
			<value level="51">67</value>
			<value level="52">68</value>
			<value level="53">69</value>
			<value level="54">69</value>
			<value level="55">70</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>DAGGER</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45208" toLevel="55" name="Transcendent Burning Fist" nameRu="Невероятный Горящий Кулак">
		<!-- Hurls a fiery fist to attack the target with $s1 power.\nRequires a fist weapon.\nIgnores Shield Defense.\nIgnores $s2 of target's defense.\nCritical.\nOver-hit.\nCan be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0280</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-200</value>
			<value level="2">-205</value>
			<value level="3">-210</value>
			<value level="4">-215</value>
			<value level="5">-220</value>
			<value level="6">-225</value>
			<value level="7">-230</value>
			<value level="8">-235</value>
			<value level="9">-240</value>
			<value level="10">-245</value>
			<value level="11">-250</value>
			<value level="12">-255</value>
			<value level="13">-260</value>
			<value level="14">-265</value>
			<value level="15">-270</value>
			<value level="16">-275</value>
			<value level="17">-280</value>
			<value level="18">-285</value>
			<value level="19">-290</value>
			<value level="20">-295</value>
			<value level="21">-300</value>
			<value level="22">-305</value>
			<value level="23">-310</value>
			<value level="24">-315</value>
			<value level="25">-320</value>
			<value level="26">-325</value>
			<value level="27">-330</value>
			<value level="28">-335</value>
			<value level="29">-340</value>
			<value level="30">-345</value>
			<value level="31">-350</value>
			<value level="32">-355</value>
			<value level="33">-360</value>
			<value level="34">-365</value>
			<value level="35">-370</value>
			<value level="36">-375</value>
			<value level="37">-380</value>
			<value level="38">-385</value>
			<value level="39">-390</value>
			<value level="40">-395</value>
			<value level="41">-400</value>
			<value level="42">-405</value>
			<value level="43">-410</value>
			<value level="44">-415</value>
			<value level="45">-420</value>
			<value level="46">-425</value>
			<value level="47">-430</value>
			<value level="48">-435</value>
			<value level="49">-440</value>
			<value level="50">-445</value>
			<value level="51">-450</value>
			<value level="52">-455</value>
			<value level="53">-460</value>
			<value level="54">-465</value>
			<value level="55">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">30</value>
			<value level="2">30</value>
			<value level="3">31</value>
			<value level="4">32</value>
			<value level="5">33</value>
			<value level="6">33</value>
			<value level="7">34</value>
			<value level="8">35</value>
			<value level="9">36</value>
			<value level="10">36</value>
			<value level="11">37</value>
			<value level="12">38</value>
			<value level="13">39</value>
			<value level="14">39</value>
			<value level="15">40</value>
			<value level="16">41</value>
			<value level="17">42</value>
			<value level="18">42</value>
			<value level="19">43</value>
			<value level="20">44</value>
			<value level="21">45</value>
			<value level="22">45</value>
			<value level="23">46</value>
			<value level="24">47</value>
			<value level="25">48</value>
			<value level="26">48</value>
			<value level="27">49</value>
			<value level="28">50</value>
			<value level="29">51</value>
			<value level="30">51</value>
			<value level="31">52</value>
			<value level="32">53</value>
			<value level="33">54</value>
			<value level="34">54</value>
			<value level="35">55</value>
			<value level="36">56</value>
			<value level="37">57</value>
			<value level="38">57</value>
			<value level="39">58</value>
			<value level="40">59</value>
			<value level="41">60</value>
			<value level="42">60</value>
			<value level="43">61</value>
			<value level="44">62</value>
			<value level="45">63</value>
			<value level="46">63</value>
			<value level="47">64</value>
			<value level="48">65</value>
			<value level="49">66</value>
			<value level="50">66</value>
			<value level="51">67</value>
			<value level="52">68</value>
			<value level="53">69</value>
			<value level="54">69</value>
			<value level="55">70</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>DUALFIST</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45209" toLevel="60" name="Transcendent Hurricane Assault" nameRu="Невероятное Ураганное Нападение">
		<!-- Strikes the target with $s1 power.\nIncreases power upon consuming Momentum (up to $s2).\n\nRequires a fist weapon.\nIgnores Shield Defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0284</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-175</value>
			<value level="2">-180</value>
			<value level="3">-185</value>
			<value level="4">-190</value>
			<value level="5">-195</value>
			<value level="6">-200</value>
			<value level="7">-205</value>
			<value level="8">-210</value>
			<value level="9">-215</value>
			<value level="10">-220</value>
			<value level="11">-225</value>
			<value level="12">-230</value>
			<value level="13">-235</value>
			<value level="14">-240</value>
			<value level="15">-245</value>
			<value level="16">-250</value>
			<value level="17">-255</value>
			<value level="18">-260</value>
			<value level="19">-265</value>
			<value level="20">-270</value>
			<value level="21">-275</value>
			<value level="22">-280</value>
			<value level="23">-285</value>
			<value level="24">-290</value>
			<value level="25">-295</value>
			<value level="26">-300</value>
			<value level="27">-305</value>
			<value level="28">-310</value>
			<value level="29">-315</value>
			<value level="30">-320</value>
			<value level="31">-325</value>
			<value level="32">-330</value>
			<value level="33">-335</value>
			<value level="34">-340</value>
			<value level="35">-345</value>
			<value level="36">-350</value>
			<value level="37">-355</value>
			<value level="38">-360</value>
			<value level="39">-365</value>
			<value level="40">-370</value>
			<value level="41">-375</value>
			<value level="42">-380</value>
			<value level="43">-385</value>
			<value level="44">-390</value>
			<value level="45">-395</value>
			<value level="46">-400</value>
			<value level="47">-405</value>
			<value level="48">-410</value>
			<value level="49">-415</value>
			<value level="50">-420</value>
			<value level="51">-425</value>
			<value level="52">-430</value>
			<value level="53">-435</value>
			<value level="54">-440</value>
			<value level="55">-445</value>
			<value level="56">-450</value>
			<value level="57">-455</value>
			<value level="58">-460</value>
			<value level="59">-465</value>
			<value level="60">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">26</value>
			<value level="2">27</value>
			<value level="3">27</value>
			<value level="4">28</value>
			<value level="5">29</value>
			<value level="6">30</value>
			<value level="7">30</value>
			<value level="8">31</value>
			<value level="9">32</value>
			<value level="10">33</value>
			<value level="11">33</value>
			<value level="12">34</value>
			<value level="13">35</value>
			<value level="14">36</value>
			<value level="15">36</value>
			<value level="16">37</value>
			<value level="17">38</value>
			<value level="18">39</value>
			<value level="19">39</value>
			<value level="20">40</value>
			<value level="21">41</value>
			<value level="22">42</value>
			<value level="23">42</value>
			<value level="24">43</value>
			<value level="25">44</value>
			<value level="26">45</value>
			<value level="27">45</value>
			<value level="28">46</value>
			<value level="29">47</value>
			<value level="30">48</value>
			<value level="31">48</value>
			<value level="32">49</value>
			<value level="33">50</value>
			<value level="34">51</value>
			<value level="35">51</value>
			<value level="36">52</value>
			<value level="37">53</value>
			<value level="38">54</value>
			<value level="39">54</value>
			<value level="40">55</value>
			<value level="41">56</value>
			<value level="42">57</value>
			<value level="43">57</value>
			<value level="44">58</value>
			<value level="45">59</value>
			<value level="46">60</value>
			<value level="47">60</value>
			<value level="48">61</value>
			<value level="49">62</value>
			<value level="50">63</value>
			<value level="51">63</value>
			<value level="52">64</value>
			<value level="53">65</value>
			<value level="54">66</value>
			<value level="55">66</value>
			<value level="56">67</value>
			<value level="57">68</value>
			<value level="58">69</value>
			<value level="59">69</value>
			<value level="60">70</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>DUALFIST</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45210" toLevel="15" name="Transcendent Lethal Shot" nameRu="Невероятный Смертельный Выстрел">
		<!-- Shoots a deadly arrow with $s1 power.\nRequires a bow.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0343</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">68</value>
			<value level="2">69</value>
			<value level="3">70</value>
			<value level="4">71</value>
			<value level="5">72</value>
			<value level="6">72</value>
			<value level="7">73</value>
			<value level="8">74</value>
			<value level="9">75</value>
			<value level="10">76</value>
			<value level="11">77</value>
			<value level="12">78</value>
			<value level="13">78</value>
			<value level="14">79</value>
			<value level="15">80</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45211" toLevel="15" name="Transcendent Lethal Blow" nameRu="Невероятный Смертоносный Удар">
		<!-- Attacks the enemy’s vital spot with $s1 power.\nRequires a dagger.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0344</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>DAGGER</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45212" toLevel="30" name="Transcendent Tribunal" nameRu="Невероятный Трибунал">
		<!-- Attacks the target with $s1 power.\nFor $s2, enemy’s P. Critical Rate -$s3.\nRequires a sword/ blunt weapon.\nIgnores Shield Defense.\nIgnores $s4 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0400</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-2200</value>
			<value level="2">-2350</value>
			<value level="3">-2500</value>
			<value level="4">-2650</value>
			<value level="5">-2800</value>
			<value level="6">-2950</value>
			<value level="7">-3100</value>
			<value level="8">-3200</value>
			<value level="9">-3300</value>
			<value level="10">-3400</value>
			<value level="11">-3500</value>
			<value level="12">-3600</value>
			<value level="13">-3700</value>
			<value level="14">-3800</value>
			<value level="15">-3900</value>
			<value level="16">-4000</value>
			<value level="17">-4050</value>
			<value level="18">-4100</value>
			<value level="19">-4150</value>
			<value level="20">-4200</value>
			<value level="21">-4250</value>
			<value level="22">-4300</value>
			<value level="23">-4350</value>
			<value level="24">-4400</value>
			<value level="25">-4450</value>
			<value level="26">-4500</value>
			<value level="27">-4550</value>
			<value level="28">-4600</value>
			<value level="29">-4650</value>
			<value level="30">-4700</value>
		</effectPoint>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">35</value>
			<value level="3">37</value>
			<value level="4">39</value>
			<value level="5">42</value>
			<value level="6">44</value>
			<value level="7">46</value>
			<value level="8">48</value>
			<value level="9">49</value>
			<value level="10">51</value>
			<value level="11">52</value>
			<value level="12">54</value>
			<value level="13">55</value>
			<value level="14">57</value>
			<value level="15">58</value>
			<value level="16">60</value>
			<value level="17">60</value>
			<value level="18">61</value>
			<value level="19">62</value>
			<value level="20">63</value>
			<value level="21">63</value>
			<value level="22">64</value>
			<value level="23">65</value>
			<value level="24">66</value>
			<value level="25">66</value>
			<value level="26">67</value>
			<value level="27">68</value>
			<value level="28">69</value>
			<value level="29">69</value>
			<value level="30">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45213" toLevel="30" name="Transcendent Judgment" nameRu="Невероятное Правосудие">
		<!-- Attacks the target with $s1 power.\nFor $s2, enemy’s P. Critical Damage -$s3.\nRequires a sword/ blunt weapon.\nIgnores Shield Defense.\nIgnores $s4 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0401</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-2200</value>
			<value level="2">-2350</value>
			<value level="3">-2500</value>
			<value level="4">-2650</value>
			<value level="5">-2800</value>
			<value level="6">-2950</value>
			<value level="7">-3100</value>
			<value level="8">-3200</value>
			<value level="9">-3300</value>
			<value level="10">-3400</value>
			<value level="11">-3500</value>
			<value level="12">-3600</value>
			<value level="13">-3700</value>
			<value level="14">-3800</value>
			<value level="15">-3900</value>
			<value level="16">-4000</value>
			<value level="17">-4050</value>
			<value level="18">-4100</value>
			<value level="19">-4150</value>
			<value level="20">-4200</value>
			<value level="21">-4250</value>
			<value level="22">-4300</value>
			<value level="23">-4350</value>
			<value level="24">-4400</value>
			<value level="25">-4450</value>
			<value level="26">-4500</value>
			<value level="27">-4550</value>
			<value level="28">-4600</value>
			<value level="29">-4650</value>
			<value level="30">-4700</value>
		</effectPoint>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">35</value>
			<value level="3">37</value>
			<value level="4">39</value>
			<value level="5">42</value>
			<value level="6">44</value>
			<value level="7">46</value>
			<value level="8">48</value>
			<value level="9">49</value>
			<value level="10">51</value>
			<value level="11">52</value>
			<value level="12">54</value>
			<value level="13">55</value>
			<value level="14">57</value>
			<value level="15">58</value>
			<value level="16">60</value>
			<value level="17">60</value>
			<value level="18">61</value>
			<value level="19">62</value>
			<value level="20">63</value>
			<value level="21">63</value>
			<value level="22">64</value>
			<value level="23">65</value>
			<value level="24">66</value>
			<value level="25">66</value>
			<value level="26">67</value>
			<value level="27">68</value>
			<value level="28">69</value>
			<value level="29">69</value>
			<value level="30">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45214" toLevel="15" name="Soul Piercing" nameRu="Пробивание Души">
		<!-- Attacks the target with $s1 power.\nRequires a spear.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0921</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45215" toLevel="30" name="Transcendent Shield Strike" nameRu="Невероятная Атака Щитом">
		<!-- Attacks the enemy using a shield with $s1 power, and provokes the enemy.\nRequires a shield.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0984</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-2200</value>
			<value level="2">-2350</value>
			<value level="3">-2500</value>
			<value level="4">-2650</value>
			<value level="5">-2800</value>
			<value level="6">-2950</value>
			<value level="7">-3100</value>
			<value level="8">-3200</value>
			<value level="9">-3300</value>
			<value level="10">-3400</value>
			<value level="11">-3500</value>
			<value level="12">-3600</value>
			<value level="13">-3700</value>
			<value level="14">-3800</value>
			<value level="15">-3900</value>
			<value level="16">-4000</value>
			<value level="17">-4050</value>
			<value level="18">-4100</value>
			<value level="19">-4200</value>
			<value level="20">-4250</value>
			<value level="21">-4300</value>
			<value level="22">-4350</value>
			<value level="23">-4400</value>
			<value level="24">-4400</value>
			<value level="25">-4400</value>
			<value level="26">-4400</value>
			<value level="27">-4400</value>
			<value level="28">-4400</value>
			<value level="29">-4400</value>
			<value level="30">-4400</value>
		</effectPoint>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">35</value>
			<value level="3">37</value>
			<value level="4">39</value>
			<value level="5">42</value>
			<value level="6">44</value>
			<value level="7">46</value>
			<value level="8">48</value>
			<value level="9">49</value>
			<value level="10">51</value>
			<value level="11">52</value>
			<value level="12">54</value>
			<value level="13">55</value>
			<value level="14">57</value>
			<value level="15">58</value>
			<value level="16">60</value>
			<value level="17">60</value>
			<value level="18">61</value>
			<value level="19">62</value>
			<value level="20">63</value>
			<value level="21">63</value>
			<value level="22">64</value>
			<value level="23">65</value>
			<value level="24">66</value>
			<value level="25">66</value>
			<value level="26">67</value>
			<value level="27">68</value>
			<value level="28">69</value>
			<value level="29">69</value>
			<value level="30">70</value>
		</mpConsume>
		<conditions>
			<condition name="EquipShield"/>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45216" toLevel="15" name="Transcendent Deadly Strike" nameRu="Невероятный Смертельный Ритм">
		<!-- Attacks the target with $s1 power. For 10 sec., enemy's P. Def. -23%%.\nIf the target is affected by Hold and attacked, power +50%%.\nRequires a sword, blunt weapon, or dual swords.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0986</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-420</value>
			<value level="5">-425</value>
			<value level="6">-430</value>
			<value level="7">-435</value>
			<value level="8">-440</value>
			<value level="9">-440</value>
			<value level="10">-440</value>
			<value level="11">-440</value>
			<value level="12">-440</value>
			<value level="13">-440</value>
			<value level="14">-440</value>
			<value level="15">-440</value>
		</effectPoint>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45217" toLevel="5" name="Transcendent Guard Crush" nameRu="Невероятное Сокрушение Защиты">
		<!-- Attacks the target with $s1 power.\nRequires a sword, a blunt weapon, or dual swords.\nIgnores Shield Defense.\nIgnores $s2 of target's defense.\nCritical.\nOver-hit.\nCan be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0496</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
		</effectPoint>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">40</value>
			<value level="3">48</value>
			<value level="4">55</value>
			<value level="5">59</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45218" toLevel="45" name="Transcendent Prominence" nameRu="Невероятный Протуберанец">
		<!-- Deals fire damage with $s1 power.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45219" toLevel="45" name="Transcendent Hydro Blast" nameRu="Невероятная Волна Воды">
		<!-- Summons a powerful water wave to attack the enemy with $s1 power.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45220" toLevel="45" name="Transcendent Hurricane" nameRu="Невероятный Ураган">
		<!-- Summons a hurricane to attack the enemy with $s1 power.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45221" toLevel="40" name="Transcendent Blaze" nameRu="Невероятная Вспышка">
		<!-- The enemy's body is set aflame.\nInflicts magic damage on the enemy with $s1 power.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45222" toLevel="40" name="Transcendent Aqua Swirl" nameRu="Невероятный Водоворот">
		<!-- Releases a water vortex to attack the enemy with $s1 power.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45223" toLevel="40" name="Transcendent Twister" nameRu="Невероятный Смерч">
		<!-- Summons a wind blade.\nInflicts magic damage on the enemy with $s1 power.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45224" toLevel="35" name="Transcendent Might of Heaven" nameRu="Невероятная Сила Небес">
		<!-- Summons light of the heaven.\nDeals magic damage with $s1 power and additional damage to demons and undead.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45225" toLevel="25" name="Transcendent Steal Essence" nameRu="Невероятная Кража Сущности">
		<!-- <Drain Skill>\n\nDeals magic damage to the enemy with $s1 power. Absorbs $s2 of the inflicted damage as HP.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45226" toLevel="1" name="Master of Aggression">
		<operateType>P</operateType>
		<effects>
            <effect name="EffectFlag">
                <flag>MASTER_OF_AGGRESSION_2</flag>
            </effect>
		</effects>
	</skill>
	<skill id="45227" toLevel="8" name="Transcendent Soul Impulse" nameRu="Невероятный Импульс Души">
		<!-- <Ultimate skill>\n\nAttacks the target from a distance with $s1 power.\nRequires an ancient sword or a two-handed sword.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.wizard_danger_warppow</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
			<value level="6">-420</value>
			<value level="7">-445</value>
			<value level="8">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">29</value>
			<value level="2">36</value>
			<value level="3">43</value>
			<value level="4">49</value>
			<value level="5">53</value>
			<value level="6">56</value>
			<value level="7">59</value>
			<value level="8">63</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45228" toLevel="8" name="Transcendent Soul Piercing" nameRu="Невероятное Пробивание Души">
		<!-- <Ultimate skill>\n\nAttacks the opponent 3 times with $s1 power.\nRequires a rapier.\nDeals additional magic damage. M. Def. -10%%.\nIn PvE knocks the opponents back.\nIgnores Shield Defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0504</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
			<value level="6">-420</value>
			<value level="7">-445</value>
			<value level="8">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">29</value>
			<value level="2">36</value>
			<value level="3">43</value>
			<value level="4">49</value>
			<value level="5">53</value>
			<value level="6">56</value>
			<value level="7">59</value>
			<value level="8">63</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45229" toLevel="8" name="Transcendent Soul Spark" nameRu="Невероятная Вспышка Души">
		<!-- Deals magic damage to the enemy with $s1 power.\nRequires a rapier.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45230" toLevel="10" name="Transcendent Twin Shot" nameRu="Невероятный Сдвоенный Выстрел">
		<!-- <Ultimate skill>\n\nShoots 2 arrows to attack the enemy with $s1 power.\nRequires a bow.\nIgnores target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0507</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-120</value>
			<value level="2">-170</value>
			<value level="3">-220</value>
			<value level="4">-270</value>
			<value level="5">-320</value>
			<value level="6">-370</value>
			<value level="7">-395</value>
			<value level="8">-420</value>
			<value level="9">-445</value>
			<value level="10">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">29</value>
			<value level="3">38</value>
			<value level="4">46</value>
			<value level="5">55</value>
			<value level="6">63</value>
			<value level="7">67</value>
			<value level="8">72</value>
			<value level="9">76</value>
			<value level="10">80</value>
		</mpConsume>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45231" toLevel="30" name="Transcendent Death Spike" nameRu="Невероятный Шип Смерти">
		<!-- Throws a bone to inflict damage on the enemy with $s1 power.\n\nRequires one Cursed Bone.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45232" toLevel="1" name="Power of Aggression">
		<operateType>A2</operateType>
		<abnormalTime>3</abnormalTime>
		<isTriggeredSkill>true</isTriggeredSkill>
		<effects>
		    <effect name="DefencePhysicalSkillCriticalRate">
		        <amount>5</amount>
            </effect>
		    <effect name="Speed">
		        <amount>8</amount>
            </effect>
        </effects>
	</skill>
	<skill id="45233" toLevel="1" name="Power of Aggression">
		<operateType>A2</operateType>
		<abnormalTime>3</abnormalTime>
		<isTriggeredSkill>true</isTriggeredSkill>
		<effects>
		    <effect name="DefenceSkillCriticalDamage">
		        <amount>8</amount>
            </effect>
		    <effect name="Speed">
		        <amount>5</amount>
            </effect>
        </effects>
	</skill>
	<skill id="45234" toLevel="1" name="Expand Inventory of Glory" nameRu="Расширенный Инвентарь Славы">
		<!-- Increases the number of items an individual can possess. -->
		<icon>icon.skill0332</icon>
		<excludedFromCheck>true</excludedFromCheck>
		<magicLevel>-1</magicLevel>
		<operateType>P</operateType>
		<effects>
			<effect name="EnlargeSlot">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45235" toLevel="1" name="Increased Weight Limit of Glory" nameRu="Увеличенный Лимит Веса Славы">
		<!-- Weight limit +10000. -->
		<operateType>P</operateType>
		<magicLevel>-1</magicLevel>
		<excludedFromCheck>true</excludedFromCheck>
		<effects>
			<effect name="WeightLimit">
				<amount>10000</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45236" toLevel="2" name="Prophecy of Light" nameRu="Пророчество Света">
		<!-- For $s1, Max HP +$s2, Max MP +$s3, P./ M. Def. +$s4, damage to monsters +$s5, Healing Power +$s6, Debuff Resistance +$s7.\n\nConsumes 20 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>78</magicLevel>
		<itemConsumeCount>20</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<reuseDelay>10000</reuseDelay>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">10</value>
					<value level="2">15</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>
					<value level="1">10</value>
					<value level="2">15</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">10</value>
					<value level="2">15</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">10</value>
					<value level="2">15</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>
					<value level="1">15</value>
					<value level="2">20</value>
				</amount>
				<type>ENEMY_ALL</type>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>
					<value level="1">15</value>
					<value level="2">20</value>
				</amount>
				<type>ENEMY_ALL</type>
				<mode>PER</mode>
			</effect>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>
					<value level="1">15</value>
					<value level="2">20</value>
				</amount>
				<type>ENEMY_ALL</type>
				<mode>PER</mode>
			</effect>
			<effect name="HealEffect">
				<amount>
					<value level="1">15</value>
					<value level="2">30</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>
					<value level="1">-10</value>
					<value level="2">-20</value>
				</amount>
				<slot>DEBUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="45237" toLevel="4" name="Swap Attack" nameRu="Боевое Переключение">
		<!-- M. Atk. -$s1, P. Atk. +$s2.\nWhen activated, constantly spends MP.\n\nNote!\nWhen Battle Switch, Swap Defense and Convert are used simultaneous, MP Consumption is increased according to the number of active skills. -->
		<shortcutToggleType>1</shortcutToggleType>
		<magicLevel>78</magicLevel>
		<operateType>A2</operateType>
		<isTriggeredSkill>true</isTriggeredSkill>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<reuseDelay>10000</reuseDelay>
		<hitTime>1000</hitTime>
		<abnormalTime>1200</abnormalTime>
		<itemConsumeId>3031</itemConsumeId>
		<itemConsumeCount>
			<value level="1">20</value>
			<value level="2">25</value>
			<value level="3">30</value>
			<value level="4">40</value>
		</itemConsumeCount>
		<abnormalLevel>1</abnormalLevel>
		<effects>
			<effect name="MAtk">
				<amount>-70</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">120</value>
					<value level="3">150</value>
					<value level="4">180</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45238" toLevel="4" name="Root Mastery" nameRu="Владение Корнем">
		<!-- Hold Atk. Rate +$s1. -->
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="AttackTrait">
				<HOLD>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">15</value>
					<value level="4">20</value>
				</HOLD>
			</effect>
		</effects>
	</skill>
	<skill id="45239" toLevel="4" name="Sleep Mastery" nameRu="Владение Усыплением">
		<!-- Sleep Atk. Rate +$s1. -->
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="AttackTrait">
				<SLEEP>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">15</value>
					<value level="4">20</value>
				</SLEEP>
			</effect>
		</effects>
	</skill>
	<skill id="45240" toLevel="1" name="Einhasad's Blessing" nameRu="Благословение Эйнхасад">
		<!-- HP Recovery Rate +5%%, MP Recovery Rate +5%%. -->
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="HpRegen">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45241" toLevel="20" name="Divine Beam" nameRu="Святой Луч">
		<!-- A divine beam strikes the target.\nInflicts magical damage with $s1 power.\nWhen you are attacking the enemy affected by Hold, the debuff is cancelled and an extra attack is triggered.\nDeals additional damage to demons and undead.\n\n<PvP effect> When used against the same target, the damage is decreased for 3 sec. -->
		<shortcutToggleType>2</shortcutToggleType>
		<castRange>700</castRange>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<effectPoint>
			<value level="1">-1140</value>
			<value level="2">-1155</value>
			<value level="3">-1170</value>
			<value level="4">-1185</value>
			<value level="5">-1200</value>
			<value level="6">-1215</value>
			<value level="7">-1230</value>
			<value level="8">-1245</value>
			<value level="9">-1260</value>
			<value level="10">-1275</value>
			<value level="11">-1290</value>
			<value level="12">-1305</value>
			<value level="13">-1320</value>
			<value level="14">-1335</value>
			<value level="15">-1350</value>
			<value level="16">-1365</value>
			<value level="17">-1380</value>
			<value level="18">-1395</value>
			<value level="19">-1410</value>
			<value level="20">-1425</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">77</value>
			<value level="3">78</value>
			<value level="4">79</value>
			<value level="5">80</value>
			<value level="6">81</value>
			<value level="7">82</value>
			<value level="8">83</value>
			<value level="9">84</value>
			<value level="10">85</value>
			<value level="11">86</value>
			<value level="12">87</value>
			<value level="13">88</value>
			<value level="14">89</value>
			<value level="15">90</value>
			<value level="16">91</value>
			<value level="17">92</value>
			<value level="18">93</value>
			<value level="19">93</value>
			<value level="20">93</value>
		</magicLevel>
		<mpConsume>
			<value level="1">56</value>
			<value level="2">57</value>
			<value level="3">58</value>
			<value level="4">58</value>
			<value level="5">59</value>
			<value level="6">59</value>
			<value level="7">60</value>
			<value level="8">60</value>
			<value level="9">62</value>
			<value level="10">62</value>
			<value level="11">63</value>
			<value level="12">63</value>
			<value level="13">64</value>
			<value level="14">64</value>
			<value level="15">65</value>
			<value level="16">68</value>
			<value level="17">71</value>
			<value level="18">74</value>
			<value level="19">77</value>
			<value level="20">80</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<reuseDelayGroup>45241</reuseDelayGroup>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">115</value>
					<value level="2">117</value>
					<value level="3">119</value>
					<value level="4">122</value>
					<value level="5">125</value>
					<value level="6">128</value>
					<value level="7">131</value>
					<value level="8">134</value>
					<value level="9">137</value>
					<value level="10">140</value>
					<value level="11">144</value>
					<value level="12">148</value>
					<value level="13">152</value>
					<value level="14">155</value>
					<value level="15">160</value>
                    <value level="16">165</value>
                    <value level="17">170</value>
                    <value level="18">175</value>
                    <value level="19">180</value>
                    <value level="20">185</value>
				</power>
				<raceModifier>1.3</raceModifier>
				<raceList>DEMON;UNDEAD</raceList>
				<debuffModifier>1.1</debuffModifier>
				<debuffType>ROOT_MAGICALLY</debuffType>
				<debuffRemove>false</debuffRemove>
			</effect>
		</effects>
	</skill>
	<skill id="45242" toLevel="15" name="Mass Dryad Root" nameRu="Массовый Корень Дриады">
		<!-- Holds nearby enemies for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>ROOT_MAGICALLY</abnormalType>
		<abnormalVisualEffect>ROOT</abnormalVisualEffect>
		<activateRate>40</activateRate>
		<affectLimit>9-10</affectLimit>
		<affectRange>200</affectRange>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-1140</value>
			<value level="2">-1155</value>
			<value level="3">-1170</value>
			<value level="4">-1185</value>
			<value level="5">-1200</value>
			<value level="6">-1215</value>
			<value level="7">-1230</value>
			<value level="8">-1245</value>
			<value level="9">-1260</value>
			<value level="10">-1275</value>
			<value level="11">-1290</value>
			<value level="12">-1305</value>
			<value level="13">-1320</value>
			<value level="14">-1335</value>
			<value level="15">-1350</value>
		</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">77</value>
			<value level="3">78</value>
			<value level="4">79</value>
			<value level="5">80</value>
			<value level="6">81</value>
			<value level="7">82</value>
			<value level="8">83</value>
			<value level="9">84</value>
			<value level="10">85</value>
			<value level="11">86</value>
			<value level="12">87</value>
			<value level="13">88</value>
			<value level="14">89</value>
			<value level="15">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">140</value>
			<value level="2">145</value>
			<value level="3">150</value>
			<value level="4">155</value>
			<value level="5">160</value>
			<value level="6">165</value>
			<value level="7">170</value>
			<value level="8">175</value>
			<value level="9">180</value>
			<value level="10">185</value>
			<value level="11">190</value>
			<value level="12">195</value>
			<value level="13">200</value>
			<value level="14">215</value>
			<value level="15">220</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>HOLD</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="Root"/>
			<effect name="DefenceTrait">
				<HOLD>100</HOLD>
			</effect>
		</effects>
	</skill>
	<skill id="45243" toLevel="15" name="Mass Wind Shackles" nameRu="Массовые Оковы Ветра">
		<!-- Nearby enemies' Atk. Spd./ Casting Spd. -$s2 for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ATTACK_TIME_UP</abnormalType>
		<trait>SUPPRESSION</trait>
		<activateRate>80</activateRate>
		<affectLimit>9-10</affectLimit>
		<affectRange>200</affectRange>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-1140</value>
			<value level="2">-1155</value>
			<value level="3">-1170</value>
			<value level="4">-1185</value>
			<value level="5">-1200</value>
			<value level="6">-1215</value>
			<value level="7">-1230</value>
			<value level="8">-1245</value>
			<value level="9">-1260</value>
			<value level="10">-1275</value>
			<value level="11">-1290</value>
			<value level="12">-1305</value>
			<value level="13">-1320</value>
			<value level="14">-1335</value>
			<value level="15">-1350</value>
		</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">77</value>
			<value level="3">78</value>
			<value level="4">79</value>
			<value level="5">80</value>
			<value level="6">81</value>
			<value level="7">82</value>
			<value level="8">83</value>
			<value level="9">84</value>
			<value level="10">85</value>
			<value level="11">86</value>
			<value level="12">87</value>
			<value level="13">88</value>
			<value level="14">89</value>
			<value level="15">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">140</value>
			<value level="2">145</value>
			<value level="3">150</value>
			<value level="4">155</value>
			<value level="5">160</value>
			<value level="6">165</value>
			<value level="7">170</value>
			<value level="8">175</value>
			<value level="9">180</value>
			<value level="10">185</value>
			<value level="11">190</value>
			<value level="12">195</value>
			<value level="13">200</value>
			<value level="14">215</value>
			<value level="15">220</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">-17</value>
					<value level="2">-20</value>
					<value level="3">-20</value>
					<value level="4">-20</value>
					<value level="5">-20</value>
					<value level="6">-23</value>
					<value level="7">-23</value>
					<value level="8">-23</value>
					<value level="9">-23</value>
					<value level="10">-23</value>
					<value level="11">-23</value>
					<value level="12">-23</value>
					<value level="13">-23</value>
					<value level="14">-23</value>
					<value level="15">-23</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">-17</value>
					<value level="2">-20</value>
					<value level="3">-20</value>
					<value level="4">-20</value>
					<value level="5">-20</value>
					<value level="6">-23</value>
					<value level="7">-23</value>
					<value level="8">-23</value>
					<value level="9">-23</value>
					<value level="10">-23</value>
					<value level="11">-23</value>
					<value level="12">-23</value>
					<value level="13">-23</value>
					<value level="14">-23</value>
					<value level="15">-23</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45260" toLevel="57" name="Deadly Aggression">
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0028</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>TARGET_LOCK</abnormalType>
		<castRange>
		    <value level="1">400</value>
			<value level="2">400</value>
			<value level="3">400</value>
			<value level="4">400</value>
			<value level="5">400</value>
			<value level="6">400</value>
			<value level="7">400</value>
			<value level="8">400</value>
			<value level="9">400</value>
			<value level="10">400</value>
			<value level="11">400</value>
			<value level="12">400</value>
			<value level="13">600</value>
			<value level="14">600</value>
			<value level="15">600</value>
			<value level="16">600</value>
			<value level="17">600</value>
			<value level="18">600</value>
			<value level="19">600</value>
			<value level="20">600</value>
			<value level="21">600</value>
			<value level="22">600</value>
			<value level="23">600</value>
			<value level="24">600</value>
			<value level="25">600</value>
			<value level="26">600</value>
			<value level="27">600</value>
			<value level="28">600</value>
			<value level="29">600</value>
			<value level="30">600</value>
			<value level="31">600</value>
			<value level="32">600</value>
			<value level="33">600</value>
			<value level="34">700</value>
			<value level="35">700</value>
			<value level="36">700</value>
			<value level="37">700</value>
			<value level="38">700</value>
			<value level="39">700</value>
			<value level="40">700</value>
			<value level="41">700</value>
			<value level="42">700</value>
			<value level="43">700</value>
			<value level="44">700</value>
			<value level="45">700</value>
			<value level="46">700</value>
			<value level="47">700</value>
			<value level="48">700</value>
			<value level="49">700</value>
			<value level="50">700</value>
			<value level="51">700</value>
			<value level="52">700</value>
			<value level="53">700</value>
			<value level="54">700</value>
			<value level="55">700</value>
			<value level="56">700</value>
			<value level="57">700</value>
		</castRange>
		<effectPoint>
			<value level="1">-7180</value>
			<value level="2">-7440</value>
			<value level="3">-7700</value>
			<value level="4">-8250</value>
			<value level="5">-8530</value>
			<value level="6">-8810</value>
			<value level="7">-9380</value>
			<value level="8">-9680</value>
			<value level="9">-9980</value>
			<value level="10">-10590</value>
			<value level="11">-10900</value>
			<value level="12">-11210</value>
			<value level="13">-11840</value>
			<value level="14">-12160</value>
			<value level="15">-12470</value>
			<value level="16">-12800</value>
			<value level="17">-13120</value>
			<value level="18">-13440</value>
			<value level="19">-13770</value>
			<value level="20">-14090</value>
			<value level="21">-14410</value>
			<value level="22">-14740</value>
			<value level="23">-15060</value>
			<value level="24">-15380</value>
			<value level="25">-15690</value>
			<value level="26">-16010</value>
			<value level="27">-16310</value>
			<value level="28">-16620</value>
			<value level="29">-16930</value>
			<value level="30">-17230</value>
			<value level="31">-17520</value>
			<value level="32">-17810</value>
			<value level="33">-18100</value>
			<value level="34">-18370</value>
			<value level="35">-18650</value>
			<value level="36">-18910</value>
			<value level="37">-19170</value>
			<value level="38">-19430</value>
			<value level="39">-19670</value>
			<value level="40">-19900</value>
			<value level="41">-20120</value>
			<value level="42">-20340</value>
			<value level="43">-20550</value>
			<value level="44">-20750</value>
			<value level="45">-20930</value>
			<value level="46">-21110</value>
			<value level="47">-21270</value>
			<value level="48">-21430</value>
			<value level="49">-21570</value>
			<value level="50">-21710</value>
			<value level="51">-21860</value>
			<value level="52">-22000</value>
			<value level="53">-22290</value>
			<value level="54">-22430</value>
			<value level="55">-22570</value>
			<value level="56">-22720</value>
			<value level="57">-22860</value>
		</effectPoint>
		<effectRange>
			<value level="1">900</value>
			<value level="2">900</value>
			<value level="3">900</value>
			<value level="4">900</value>
			<value level="5">900</value>
			<value level="6">900</value>
			<value level="7">900</value>
			<value level="8">900</value>
			<value level="9">900</value>
			<value level="10">900</value>
			<value level="11">900</value>
			<value level="12">900</value>
			<value level="13">1100</value>
			<value level="14">1100</value>
			<value level="15">1100</value>
			<value level="16">1100</value>
			<value level="17">1100</value>
			<value level="18">1100</value>
			<value level="19">1100</value>
			<value level="20">1100</value>
			<value level="21">1100</value>
			<value level="22">1100</value>
			<value level="23">1100</value>
			<value level="24">1100</value>
			<value level="25">1100</value>
			<value level="26">1100</value>
			<value level="27">1100</value>
			<value level="28">1100</value>
			<value level="29">1100</value>
			<value level="30">1100</value>
			<value level="31">1100</value>
			<value level="32">1100</value>
			<value level="33">1100</value>
			<value level="34">1300</value>
			<value level="35">1300</value>
			<value level="36">1300</value>
			<value level="37">1300</value>
			<value level="38">1300</value>
			<value level="39">1300</value>
			<value level="40">1300</value>
			<value level="41">1300</value>
			<value level="42">1300</value>
			<value level="43">1300</value>
			<value level="44">1300</value>
			<value level="45">1300</value>
			<value level="46">1300</value>
			<value level="47">1300</value>
			<value level="48">1300</value>
			<value level="49">1300</value>
			<value level="50">1300</value>
			<value level="51">1300</value>
			<value level="52">1300</value>
			<value level="53">1300</value>
			<value level="54">1300</value>
			<value level="55">1300</value>
			<value level="56">1300</value>
			<value level="57">1300</value>
		</effectRange>
		<hitTime>700</hitTime>
		<coolTime>200</coolTime>
		<isDebuff>true</isDebuff>
		<magicLevel>
			<value level="1">22</value>
			<value level="2">23</value>
			<value level="3">24</value>
			<value level="4">26</value>
			<value level="5">27</value>
			<value level="6">28</value>
			<value level="7">30</value>
			<value level="8">31</value>
			<value level="9">32</value>
			<value level="10">34</value>
			<value level="11">35</value>
			<value level="12">36</value>
			<value level="13">38</value>
			<value level="14">39</value>
			<value level="15">40</value>
			<value level="16">41</value>
			<value level="17">42</value>
			<value level="18">43</value>
			<value level="19">44</value>
			<value level="20">45</value>
			<value level="21">46</value>
			<value level="22">47</value>
			<value level="23">48</value>
			<value level="24">49</value>
			<value level="25">50</value>
			<value level="26">51</value>
			<value level="27">52</value>
			<value level="28">53</value>
			<value level="29">54</value>
			<value level="30">55</value>
			<value level="31">56</value>
			<value level="32">57</value>
			<value level="33">58</value>
			<value level="34">59</value>
			<value level="35">60</value>
			<value level="36">61</value>
			<value level="37">62</value>
			<value level="38">63</value>
			<value level="39">64</value>
			<value level="40">65</value>
			<value level="41">66</value>
			<value level="42">67</value>
			<value level="43">68</value>
			<value level="44">69</value>
			<value level="45">70</value>
			<value level="46">71</value>
			<value level="47">72</value>
			<value level="48">73</value>
			<value level="49">74</value>
			<value level="50">76</value>
			<value level="51">77</value>
			<value level="52">78</value>
			<value level="53">80</value>
			<value level="54">81</value>
			<value level="55">82</value>
			<value level="56">83</value>
			<value level="57">84</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>6000</reuseDelay>
		<reuseDelayGroup>28</reuseDelayGroup>
		<staticReuse>true</staticReuse>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="TargetMe" />
			<effect name="GetAgro" />
		</effects>
		<selfEffects>
		    <effect name="CallSkill">
		        <skillId>45233</skillId>
		        <skillLevel>1</skillLevel>
            </effect>
		</selfEffects>
	</skill>
	<skill id="45245" toLevel="1" name="Machine Assistance" nameRu="Помощь Механизма">
		<!-- For $s1, an attack has a certain chance to summon a Golem who attacks the enemy. -->
		<shortcutToggleType>1</shortcutToggleType>
		<operateType>A2</operateType>
		<magicLevel>50</magicLevel>
		<hitTime>2000</hitTime>
		<reuseDelay>10000</reuseDelay>
		<abnormalTime>1200</abnormalTime>
		<effects>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>30</chance> <!-- Guessed from video, probably more but retards will cry -->
				<targetType>TARGET</targetType>
				<isCritical>false</isCritical>
				<allowWeapons>ALL</allowWeapons>
				<allowNormalAttack>true</allowNormalAttack>
				<allowSkillAttack>true</allowSkillAttack>
				<excludeSkills>45246</excludeSkills>
				<skillId>45246</skillId>
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45246" toLevel="1" name="Machine Assistance" nameRu="Помощь Механизма">
		<!-- Attacks the enemy. -->
		<icon>icon.skill0025</icon>
		<operateType>A1</operateType>
		<effectPoint>-500</effectPoint>
		<hitTime>500</hitTime>
		<effects>
			<effect name="PhysicalAttack">
				<power>2500</power> <!-- Guessed from video -->
			</effect>
		</effects>
	</skill>
	<skill id="45247" toLevel="10" name="Vampiric Touch" nameRu="Прикосновение Вампира">
		<!-- <Drain Skill>\n\nDeals magic damage to the enemy with $s1 power. Absorbs $s2 of the inflicted damage as HP. -->
		<shortcutToggleType>2</shortcutToggleType>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-110</value>
			<value level="2">-120</value>
			<value level="3">-130</value>
			<value level="4">-140</value>
			<value level="5">-150</value>
			<value level="6">-160</value>
			<value level="7">-170</value>
			<value level="8">-180</value>
			<value level="9">-190</value>
			<value level="10">-200</value>
		</effectPoint>
		<effectRange>1000</effectRange>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">22</value>
			<value level="3">24</value>
			<value level="4">26</value>
			<value level="5">28</value>
			<value level="6">30</value>
			<value level="7">32</value>
			<value level="8">34</value>
			<value level="9">36</value>
			<value level="10">38</value>
		</magicLevel>
		<mpConsume>
			<value level="1">19</value>
			<value level="2">20</value>
			<value level="3">22</value>
			<value level="4">23</value>
			<value level="5">24</value>
			<value level="6">26</value>
			<value level="7">27</value>
			<value level="8">28</value>
			<value level="9">29</value>
			<value level="10">31</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="HpDrain">
				<power>
					<value level="1">24</value>
					<value level="2">26</value>
					<value level="3">28</value>
					<value level="4">31</value>
					<value level="5">33</value>
					<value level="6">36</value>
					<value level="7">39</value>
					<value level="8">41</value>
					<value level="9">44</value>
					<value level="10">46</value>
				</power>
				<percentage>40</percentage>
			</effect>
		</effects>
	</skill>
	<skill id="45248" toLevel="2" name="Collecting Mastery" nameRu="Владение Сбором">
		<!-- Craft points from materials +$s1 when hunting monsters. -->
		<magicLevel>
			<value level="1">40</value>
			<value level="2">77</value>
		</magicLevel>
		<operateType>P</operateType>
		<effects>
			<effect name="RandomCraftHerbBonus">
				<amount>
					<value level="1">1</value>
					<value level="2">10</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45249" toLevel="20" name="Earth Tremor" nameRu="Сокрушение Земли">
		<!-- Releases earth rage to attack the enemy with $s1 power.\nRequires a sword, a blunt weapon, or a spear.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1922</icon>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
			<value level="16">-475</value>
			<value level="17">-480</value>
			<value level="18">-485</value>
			<value level="19">-490</value>
			<value level="20">-495</value>
		</effectPoint>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
			<value level="16">72</value>
			<value level="17">74</value>
			<value level="18">76</value>
			<value level="19">78</value>
			<value level="20">80</value>
		</mpConsume>
		<castRange>40</castRange>
		<effectRange>400</effectRange>
		<hitTime>1000</hitTime>
		<coolTime>500</coolTime>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">77</value>
			<value level="3">78</value>
			<value level="4">79</value>
			<value level="5">80</value>
			<value level="6">81</value>
			<value level="7">82</value>
			<value level="8">83</value>
			<value level="9">84</value>
			<value level="10">85</value>
			<value level="11">86</value>
			<value level="12">87</value>
			<value level="13">88</value>
			<value level="14">89</value>
			<value level="15">90</value>
			<value level="16">91</value>
			<value level="17">92</value>
			<value level="18">93</value>
			<value level="19">93</value>
			<value level="20">93</value>
		</magicLevel>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>BLUNT</item>
					<item>POLE</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">4907</value>
					<value level="2">4976</value>
					<value level="3">5046</value>
					<value level="4">5117</value>
					<value level="5">5186</value>
					<value level="6">5256</value>
					<value level="7">5327</value>
					<value level="8">5397</value>
					<value level="9">5468</value>
					<value level="10">5538</value>
					<value level="11">5608</value>
					<value level="12">5679</value>
					<value level="13">5749</value>
					<value level="14">5821</value>
					<value level="15">5891</value>
					<value level="16">6067</value>
					<value level="17">6249</value>
					<value level="18">6436</value>
					<value level="19">6629</value>
					<value level="20">6827</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
				<pDefMod>0.9</pDefMod>
			</effect>
		</effects>
	</skill>
	<skill id="45250" toLevel="40" name="Drain HP" nameRu="Кража Здоровья">
		<!-- <Drain Skill>\n\nInflicts magical damage on the target with $s1 power.\nAbsorbs $s2 of the inflicted damage as HP. -->
		<shortcutToggleType>2</shortcutToggleType>
		<castRange>400</castRange>
		<effectPoint>
			<value level="1">-39</value>
			<value level="2">-42</value>
			<value level="3">-48</value>
			<value level="4">-51</value>
			<value level="5">-55</value>
			<value level="6">-57</value>
			<value level="7">-59</value>
			<value level="8">-126</value>
			<value level="9">-130</value>
			<value level="10">-134</value>
			<value level="11">-143</value>
			<value level="12">-147</value>
			<value level="13">-152</value>
			<value level="14">-161</value>
			<value level="15">-166</value>
			<value level="16">-171</value>
			<value level="17">-180</value>
			<value level="18">-185</value>
			<value level="19">-190</value>
			<value level="20">-195</value>
			<value level="21">-200</value>
			<value level="22">-204</value>
			<value level="23">-209</value>
			<value level="24">-214</value>
			<value level="25">-219</value>
			<value level="26">-224</value>
			<value level="27">-229</value>
			<value level="28">-234</value>
			<value level="29">-239</value>
			<value level="30">-243</value>
			<value level="31">-248</value>
			<value level="32">-253</value>
			<value level="33">-257</value>
			<value level="34">-262</value>
			<value level="35">-266</value>
			<value level="36">-271</value>
			<value level="37">-275</value>
			<value level="38">-279</value>
			<value level="39">-283</value>
			<value level="40">-287</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">24</value>
			<value level="3">28</value>
			<value level="4">32</value>
			<value level="5">36</value>
			<value level="6">40</value>
			<value level="7">42</value>
			<value level="8">44</value>
			<value level="9">46</value>
			<value level="10">48</value>
			<value level="11">50</value>
			<value level="12">52</value>
			<value level="13">54</value>
			<value level="14">56</value>
			<value level="15">58</value>
			<value level="16">60</value>
			<value level="17">62</value>
			<value level="18">64</value>
			<value level="19">66</value>
			<value level="20">68</value>
			<value level="21">70</value>
			<value level="22">72</value>
			<value level="23">73</value>
			<value level="24">74</value>
			<value level="25">75</value>
			<value level="26">76</value>
			<value level="27">77</value>
			<value level="28">78</value>
			<value level="29">79</value>
			<value level="30">80</value>
			<value level="31">81</value>
			<value level="32">82</value>
			<value level="33">83</value>
			<value level="34">84</value>
			<value level="35">85</value>
			<value level="36">86</value>
			<value level="37">87</value>
			<value level="38">88</value>
			<value level="39">89</value>
			<value level="40">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">11</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">16</value>
			<value level="5">17</value>
			<value level="6">19</value>
			<value level="7">20</value>
			<value level="8">22</value>
			<value level="9">23</value>
			<value level="10">25</value>
			<value level="11">26</value>
			<value level="12">28</value>
			<value level="13">29</value>
			<value level="14">31</value>
			<value level="15">32</value>
			<value level="16">34</value>
			<value level="17">35</value>
			<value level="18">37</value>
			<value level="19">38</value>
			<value level="20">40</value>
			<value level="21">41</value>
			<value level="22">43</value>
			<value level="23">44</value>
			<value level="24">46</value>
			<value level="25">47</value>
			<value level="26">49</value>
			<value level="27">50</value>
			<value level="28">52</value>
			<value level="29">53</value>
			<value level="30">55</value>
			<value level="31">56</value>
			<value level="32">58</value>
			<value level="33">59</value>
			<value level="34">61</value>
			<value level="35">62</value>
			<value level="36">64</value>
			<value level="37">65</value>
			<value level="38">67</value>
			<value level="39">68</value>
			<value level="40">70</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>4000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="HpDrain">
				<power>
					<value level="1">30</value>
					<value level="2">36</value>
					<value level="3">42</value>
					<value level="4">48</value>
					<value level="5">54</value>
					<value level="6">60</value>
					<value level="7">63</value>
					<value level="8">66</value>
					<value level="9">69</value>
					<value level="10">72</value>
					<value level="11">75</value>
					<value level="12">78</value>
					<value level="13">81</value>
					<value level="14">84</value>
					<value level="15">87</value>
					<value level="16">90</value>
					<value level="17">93</value>
					<value level="18">96</value>
					<value level="19">99</value>
					<value level="20">102</value>
					<value level="21">105</value>
					<value level="22">108</value>
					<value level="23">109</value>
					<value level="24">111</value>
					<value level="25">112</value>
					<value level="26">114</value>
					<value level="27">115</value>
					<value level="28">117</value>
					<value level="29">118</value>
					<value level="30">120</value>
					<value level="31">121</value>
					<value level="32">123</value>
					<value level="33">124</value>
					<value level="34">126</value>
					<value level="35">127</value>
					<value level="36">129</value>
					<value level="37">130</value>
					<value level="38">132</value>
					<value level="39">133</value>
					<value level="40">135</value>
				</power>
				<percentage>20</percentage>
			</effect>
		</effects>
	</skill>
	<skill id="45251" toLevel="1" name="Powerful Rush" nameRu="Усиленный Натиск">
		<!-- The Rush skill now stuns a target. The Stun Atk. Rate for Rush Impact is increased. -->
		<operateType>P</operateType>
		<effects>
			<effect name="TriggerSkillBySkill">
				<castSkillId>994</castSkillId>
				<skillId>5600</skillId>
				<skillLevel>1</skillLevel>
				<chance>30</chance>
				<targetType>ENEMY</targetType>
			</effect>
			<effect name="TriggerSkillBySkill">
				<castSkillId>45252</castSkillId>
				<skillId>5600</skillId>
				<skillLevel>1</skillLevel>
				<chance>30</chance>
				<targetType>ENEMY</targetType>
			</effect>
			<effect name="EffectFlag">
			    <flag>POWERFUL_RUSH</flag>
            </effect>
		</effects>
	</skill>
	<skill id="45252" toLevel="3" name="Rush" nameRu="Стремительный Бросок">
		<!-- Charges into the target, if the distance between the target and the character is more than $s1. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0493</icon>
		<castRange>
			<value level="1">400</value>
			<value level="2">450</value>
			<value level="3">500</value>
		</castRange>
		<effectRange>1500</effectRange>
		<coolTime>200</coolTime>
		<effectPoint>
			<value level="1">-300</value>
			<value level="2">-400</value>
			<value level="3">-500</value>
		</effectPoint>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">60</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">35</value>
			<value level="2">40</value>
			<value level="3">45</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>DA1</operateType>
		<!--<reuseDelay>3000</reuseDelay>-->
		<reuseDelay>5000</reuseDelay> <!-- Custom -->
		<reuseDelayPve>2000</reuseDelayPve> <!-- Custom -->
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCheckCastRange">
				<distance>
					<value level="1">150</value>
					<value level="2">120</value>
					<value level="3">100</value>
				</distance>
			</condition>
		</conditions>
	</skill>
	<skill id="45253" toLevel="6" name="Rush Impact" nameRu="Стремительный Натиск">
		<!-- Rushes forward to attack enemies at the front with $s1 power added to P. Atk. Inflicts stun for $s2 Can be used if the distance to the target is more than $s3. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0793</icon>
		<operateType>A1</operateType>
		<castRange>
			<value level="1">500</value>
			<value level="2">600</value>
			<value fromLevel="3" toLevel="6">700</value>
		</castRange>
		<hitTime>800</hitTime>
		<coolTime>200</coolTime>
		<reuseDelay>1000</reuseDelay>
		<reuseDelayGroup>45159</reuseDelayGroup>
		<effectPoint>
			<value level="1">-600</value>
			<value level="2">-700</value>
			<value level="3">-800</value>
			<value level="4">-850</value>
			<value level="5">-900</value>
			<value level="6">-910</value>
		</effectPoint>
		<mpConsume>
			<value level="1">40</value>
			<value level="2">40</value>
			<value level="3">45</value>
			<value level="4">45</value>
			<value level="5">50</value>
			<value level="6">50</value>
		</mpConsume>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">80</value>
			<value level="4">85</value>
			<value level="5">90</value>
			<value level="6">91</value>
		</magicLevel>
		<basicProperty>PHYSICAL</basicProperty>
		<effectRange>1400</effectRange>
		<affectRange>200</affectRange>
		<fanRange>
			<value level="1">0;0;600;60</value>
			<value level="2">0;0;700;60</value>
			<value level="3">0;0;800;60</value>
			<value level="4">0;0;800;60</value>
			<value level="5">0;0;800;60</value>
			<value level="6">0;0;800;60</value>
		</fanRange>
		<lvlBonusRate>20</lvlBonusRate>
		<nextAction>ATTACK</nextAction>
		<operateType>DA1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>SHOCK</trait>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="OpCheckCastRange">
				<distance>
					<value level="1">130</value>
					<value level="2">100</value>
					<value level="3">80</value>
					<value level="4">80</value>
					<value level="5">80</value>
					<value level="6">80</value>
				</distance>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1700</value>
					<value level="2">2200</value>
					<value level="3">2800</value>
					<value level="4">4200</value>
					<value level="5">5800</value>
					<value level="6">7500</value>
				</power>
				<overHit>true</overHit>
				<criticalChance>10</criticalChance>
			</effect>
			<effect name="CallSkill">
				<skillId>45254</skillId>
				<skillLevel>1</skillLevel>
				<chance>30</chance>
			</effect>
		</effects>
	</skill>
	<skill id="45254" toLevel="1" name="Rush" nameRu="Стремительный Бросок">
		<!-- Cannot move while stunned for 3 sec. -->
		<affectRange>1000</affectRange>
		<effectPoint>-800</effectPoint>
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>81</magicLevel>
		<operateType>A2</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<effectRange>1400</effectRange>
		<isDebuff>true</isDebuff>
		<abnormalTime>3</abnormalTime>
		<trait>SHOCK</trait>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>60</activateRate>
		<affectLimit>5-12</affectLimit>
		<castRange>700</castRange>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>35016</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="45255" toLevel="35" name="Freezing Strike" nameRu="Замораживающий Удар">
		<!-- Speed -$s1. -->
		<icon>icon.skill0105</icon>
		<operateType>A2</operateType>
		<abnormalType>SPEED_DOWN</abnormalType>
		<trait>SUPPRESSION</trait>
		<abnormalTime>5</abnormalTime>
		<isDebuff>true</isDebuff>
		<activateRate>100</activateRate>
		<lvlBonusRate>20</lvlBonusRate>
		<reuseDelay>10000</reuseDelay>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">33</value>
			<value level="3">36</value>
			<value level="4">38</value>
			<value level="5">40</value>
			<value level="6">42</value>
			<value level="7">44</value>
			<value level="8">46</value>
			<value level="9">48</value>
			<value level="10">50</value>
			<value level="11">51</value>
			<value level="12">52</value>
			<value level="13">53</value>
			<value level="14">54</value>
			<value level="15">55</value>
			<value level="16">56</value>
			<value level="17">57</value>
			<value level="18">58</value>
			<value level="19">59</value>
			<value level="20">60</value>
			<value level="21">62</value>
			<value level="22">64</value>
			<value level="23">66</value>
			<value level="24">68</value>
			<value level="25">70</value>
			<value level="26">72</value>
			<value level="27">74</value>
			<value level="28">76</value>
			<value level="29">78</value>
			<value level="30">80</value>
			<value level="31">82</value>
			<value level="32">84</value>
			<value level="33">86</value>
			<value level="34">88</value>
			<value level="35">90</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-300</value>
			<value level="2">-330</value>
			<value level="3">-360</value>
			<value level="4">-380</value>
			<value level="5">-400</value>
			<value level="6">-420</value>
			<value level="7">-440</value>
			<value level="8">-460</value>
			<value level="9">-480</value>
			<value level="10">-500</value>
			<value level="11">-510</value>
			<value level="12">-520</value>
			<value level="13">-530</value>
			<value level="14">-540</value>
			<value level="15">-550</value>
			<value level="16">-560</value>
			<value level="17">-570</value>
			<value level="18">-580</value>
			<value level="19">-590</value>
			<value level="20">-600</value>
			<value level="21">-620</value>
			<value level="22">-640</value>
			<value level="23">-660</value>
			<value level="24">-680</value>
			<value level="25">-700</value>
			<value level="26">-720</value>
			<value level="27">-740</value>
			<value level="28">-760</value>
			<value level="29">-780</value>
			<value level="30">-800</value>
			<value level="31">-820</value>
			<value level="32">-840</value>
			<value level="33">-860</value>
			<value level="34">-880</value>
			<value level="35">-900</value>
		</effectPoint>
		<reuseDelay>20000</reuseDelay>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">64</value>
					<value level="2">69</value>
					<value level="3">75</value>
					<value level="4">78</value>
					<value level="5">147</value>
					<value level="6">169</value>
					<value level="7">191</value>
					<value level="8">213</value>
					<value level="9">235</value>
					<value level="10">258</value>
					<value level="11">269</value>
					<value level="12">280</value>
					<value level="13">292</value>
					<value level="14">303</value>
					<value level="15">314</value>
					<value level="16">326</value>
					<value level="17">337</value>
					<value level="18">349</value>
					<value level="19">361</value>
					<value level="20">372</value>
					<value level="21">396</value>
					<value level="22">419</value>
					<value level="23">443</value>
					<value level="24">466</value>
					<value level="25">490</value>
					<value level="26">514</value>
					<value level="27">538</value>
					<value level="28">669</value>
					<value level="29">688</value>
					<value level="30">707</value>
					<value level="31">726</value>
					<value level="32">745</value>
					<value level="33">764</value>
					<value level="34">784</value>
					<value level="35">803</value>
				</power>
			</effect>
			<effect name="Speed">
				<amount>
					<value level="1">-20</value>
					<value level="2">-20</value>
					<value level="3">-20</value>
					<value level="4">-20</value>
					<value level="5">-20</value>
					<value level="6">-20</value>
					<value level="7">-20</value>
					<value level="8">-20</value>
					<value level="9">-20</value>
					<value level="10">-20</value>
					<value level="11">-25</value>
					<value level="12">-25</value>
					<value level="13">-25</value>
					<value level="14">-25</value>
					<value level="15">-25</value>
					<value level="16">-25</value>
					<value level="17">-25</value>
					<value level="18">-25</value>
					<value level="19">-25</value>
					<value level="20">-25</value>
					<value level="21">-25</value>
					<value level="22">-25</value>
					<value level="23">-25</value>
					<value level="24">-25</value>
					<value level="25">-25</value>
					<value level="26">-30</value>
					<value level="27">-30</value>
					<value level="28">-30</value>
					<value level="29">-30</value>
					<value level="30">-30</value>
					<value level="31">-30</value>
					<value level="32">-30</value>
					<value level="33">-30</value>
					<value level="34">-30</value>
					<value level="35">-30</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45256" toLevel="65" name="Bloody Strike" nameRu="Кровавый Удар">
		<!-- Constantly drains HP. -->
		<icon>icon.skill0223</icon>
		<operateType>A2</operateType>
		<subordinationAbnormalType>BLEEDING</subordinationAbnormalType>
		<abnormalType>BLEEDING</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<trait>BLEED</trait>
		<abnormalTime>5</abnormalTime>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">33</value>
			<value level="3">36</value>
			<value level="4">38</value>
			<value level="5">40</value>
			<value level="6">42</value>
			<value level="7">44</value>
			<value level="8">46</value>
			<value level="9">48</value>
			<value level="10">50</value>
			<value level="11">51</value>
			<value level="12">52</value>
			<value level="13">53</value>
			<value level="14">54</value>
			<value level="15">55</value>
			<value level="16">56</value>
			<value level="17">57</value>
			<value level="18">58</value>
			<value level="19">59</value>
			<value level="20">60</value>
			<value level="21">62</value>
			<value level="22">64</value>
			<value level="23">66</value>
			<value level="24">68</value>
			<value level="25">70</value>
			<value level="26">72</value>
			<value level="27">74</value>
			<value level="28">76</value>
			<value level="29">78</value>
			<value level="30">80</value>
			<value level="31">82</value>
			<value level="32">84</value>
			<value level="33">86</value>
			<value level="34">88</value>
			<value level="35">90</value>
		</magicLevel>
		<isDebuff>true</isDebuff>
		<activateRate>100</activateRate>
		<lvlBonusRate>20</lvlBonusRate>
		<reuseDelay>10000</reuseDelay>
		<effectPoint>
			<value level="1">-200</value>
			<value level="2">-230</value>
			<value level="3">-260</value>
			<value level="4">-280</value>
			<value level="5">-300</value>
			<value level="6">-310</value>
			<value level="7">-320</value>
			<value level="8">-330</value>
			<value level="9">-340</value>
			<value level="10">-350</value>
			<value level="11">-360</value>
			<value level="12">-370</value>
			<value level="13">-380</value>
			<value level="14">-390</value>
			<value level="15">-400</value>
			<value level="16">-410</value>
			<value level="17">-420</value>
			<value level="18">-430</value>
			<value level="19">-440</value>
			<value level="20">-450</value>
			<value level="21">-460</value>
			<value level="22">-470</value>
			<value level="23">-480</value>
			<value level="24">-490</value>
			<value level="25">-500</value>
			<value level="26">-510</value>
			<value level="27">-520</value>
			<value level="28">-530</value>
			<value level="29">-540</value>
			<value level="30">-550</value>
			<value level="31">-560</value>
			<value level="32">-570</value>
			<value level="33">-580</value>
			<value level="34">-590</value>
			<value level="35">-600</value>
			<value level="36">-610</value>
			<value level="37">-620</value>
			<value level="38">-630</value>
			<value level="39">-640</value>
			<value level="40">-650</value>
			<value level="41">-660</value>
			<value level="42">-670</value>
			<value level="43">-680</value>
			<value level="44">-690</value>
			<value level="45">-700</value>
			<value level="46">-710</value>
			<value level="47">-720</value>
			<value level="48">-730</value>
			<value level="49">-740</value>
			<value level="50">-750</value>
			<value level="51">-760</value>
			<value level="52">-770</value>
			<value level="53">-780</value>
			<value level="54">-790</value>
			<value level="55">-800</value>
			<value level="56">-810</value>
			<value level="57">-820</value>
			<value level="58">-830</value>
			<value level="59">-840</value>
			<value level="60">-850</value>
			<value level="61">-860</value>
			<value level="62">-870</value>
			<value level="63">-880</value>
			<value level="64">-890</value>
			<value level="65">-900</value>
		</effectPoint>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">46</value>
					<value level="2">51</value>
					<value level="3">57</value>
					<value level="4">60</value>
					<value level="5">64</value>
					<value level="6">66</value>
					<value level="7">68</value>
					<value level="8">69</value>
					<value level="9">71</value>
					<value level="10">73</value>
					<value level="11">75</value>
					<value level="12">77</value>
					<value level="13">78</value>
					<value level="14">80</value>
					<value level="15">147</value>
					<value level="16">158</value>
					<value level="17">169</value>
					<value level="18">180</value>
					<value level="19">191</value>
					<value level="20">202</value>
					<value level="21">213</value>
					<value level="22">224</value>
					<value level="23">235</value>
					<value level="24">246</value>
					<value level="25">258</value>
					<value level="26">269</value>
					<value level="27">280</value>
					<value level="28">292</value>
					<value level="29">303</value>
					<value level="30">314</value>
					<value level="31">326</value>
					<value level="32">337</value>
					<value level="33">349</value>
					<value level="34">361</value>
					<value level="35">372</value>
					<value level="36">384</value>
					<value level="37">396</value>
					<value level="38">407</value>
					<value level="39">419</value>
					<value level="40">431</value>
					<value level="41">443</value>
					<value level="42">454</value>
					<value level="43">466</value>
					<value level="44">478</value>
					<value level="45">490</value>
					<value level="46">502</value>
					<value level="47">514</value>
					<value level="48">526</value>
					<value level="49">538</value>
					<value level="50">550</value>
					<value level="51">669</value>
					<value level="52">678</value>
					<value level="53">688</value>
					<value level="54">697</value>
					<value level="55">707</value>
					<value level="56">716</value>
					<value level="57">726</value>
					<value level="58">736</value>
					<value level="59">745</value>
					<value level="60">755</value>
					<value level="61">764</value>
					<value level="62">774</value>
					<value level="63">784</value>
					<value level="64">793</value>
					<value level="65">803</value>
				</power>
			</effect>
			<effect name="DamOverTime">
				<power>
					<value level="1">36</value>
					<value level="2">37</value>
					<value level="3">38</value>
					<value level="4">39</value>
					<value level="5">40</value>
					<value level="6">41</value>
					<value level="7">42</value>
					<value level="8">43</value>
					<value level="9">44</value>
					<value level="10">45</value>
					<value level="11">46</value>
					<value level="12">47</value>
					<value level="13">48</value>
					<value level="14">49</value>
					<value level="15">50</value>
					<value level="16">51</value>
					<value level="17">52</value>
					<value level="18">53</value>
					<value level="19">54</value>
					<value level="20">55</value>
					<value level="21">56</value>
					<value level="22">57</value>
					<value level="23">58</value>
					<value level="24">59</value>
					<value level="25">60</value>
					<value level="26">61</value>
					<value level="27">62</value>
					<value level="28">63</value>
					<value level="29">64</value>
					<value level="30">65</value>
					<value level="31">66</value>
					<value level="32">67</value>
					<value level="33">68</value>
					<value level="34">69</value>
					<value level="35">70</value>
					<value level="36">71</value>
					<value level="37">72</value>
					<value level="38">73</value>
					<value level="39">74</value>
					<value level="40">75</value>
					<value level="41">76</value>
					<value level="42">77</value>
					<value level="43">78</value>
					<value level="44">79</value>
					<value level="45">80</value>
					<value level="46">81</value>
					<value level="47">82</value>
					<value level="48">83</value>
					<value level="49">84</value>
					<value level="50">85</value>
					<value level="51">86</value>
					<value level="52">87</value>
					<value level="53">88</value>
					<value level="54">89</value>
					<value level="55">90</value>
					<value level="56">91</value>
					<value level="57">92</value>
					<value level="58">93</value>
					<value level="59">94</value>
					<value level="60">95</value>
					<value level="61">96</value>
					<value level="62">97</value>
					<value level="63">98</value>
					<value level="64">99</value>
					<value level="65">100</value>
				</power>
				<ticks>3</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="45257" toLevel="30" name="Dark Strike" nameRu="Удар Тьмой">
		<!-- <Skill with fixed cooldown>\n\nInflicts damage with $s1 power.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill4336_new</icon>
		<operateType>A1</operateType>
		<castRange>400</castRange>
		<!-- <staticReuse>true</staticReuse> -->
		<staticReuse>
			<value level="1">true</value>
			<value level="2">true</value>
			<value level="3">true</value>
			<value level="4">true</value>
			<value level="5">true</value>
			<value level="6">true</value>
			<value level="7">true</value>
			<value level="8">true</value>
			<value level="9">true</value>
			<value level="10">true</value>
			<value level="11">true</value>
			<value level="12">true</value>
			<value level="13">true</value>
			<value level="14">true</value>
			<value level="15">true</value>
			<value level="16">true</value>
			<value level="17">true</value>
			<value level="18">true</value>
			<value level="19">true</value>
			<value level="20">true</value>
			<value level="21">true</value>
			<value level="22">true</value>
			<value level="23">true</value>
			<value level="24">true</value>
			<value level="25">true</value>
			<value level="26">false</value>
			<value level="27">false</value>
			<value level="28">false</value>
			<value level="29">false</value>
			<value level="30">false</value>
		</staticReuse>
		<effectRange>900</effectRange>
		<hitTime>2000</hitTime>
		<effectPoint>
			<value level="1">-240</value>
			<value level="2">-250</value>
			<value level="3">-255</value>
			<value level="4">-265</value>
			<value level="5">-270</value>
			<value level="6">-280</value>
			<value level="7">-285</value>
			<value level="8">-295</value>
			<value level="9">-300</value>
			<value level="10">-310</value>
			<value level="11">-315</value>
			<value level="12">-320</value>
			<value level="13">-325</value>
			<value level="14">-330</value>
			<value level="15">-335</value>
			<value level="16">-340</value>
			<value level="17">-345</value>
			<value level="18">-350</value>
			<value level="19">-355</value>
			<value level="20">-360</value>
			<value level="21">-365</value>
			<value level="22">-370</value>
			<value level="23">-375</value>
			<value level="24">-380</value>
			<value level="25">-385</value>
			<value level="26">-390</value>
			<value level="27">-390</value>
			<value level="28">-390</value>
			<value level="29">-390</value>
			<value level="30">-390</value>
		</effectPoint>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">43</value>
			<value level="3">46</value>
			<value level="4">49</value>
			<value level="5">52</value>
			<value level="6">55</value>
			<value level="7">58</value>
			<value level="8">60</value>
			<value level="9">62</value>
			<value level="10">64</value>
			<value level="11">66</value>
			<value level="12">68</value>
			<value level="13">70</value>
			<value level="14">72</value>
			<value level="15">74</value>
			<value level="16">76</value>
			<value level="17">77</value>
			<value level="18">78</value>
			<value level="19">79</value>
			<value level="20">80</value>
			<value level="21">81</value>
			<value level="22">82</value>
			<value level="23">83</value>
			<value level="24">84</value>
			<value level="25">85</value>
			<value level="26">86</value>
			<value level="27">87</value>
			<value level="28">88</value>
			<value level="29">89</value>
			<value level="30">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">5</value>
			<value level="2">7</value>
			<value level="3">9</value>
			<value level="4">11</value>
			<value level="5">13</value>
			<value level="6">15</value>
			<value level="7">17</value>
			<value level="8">19</value>
			<value level="9">21</value>
			<value level="10">23</value>
			<value level="11">25</value>
			<value level="12">27</value>
			<value level="13">29</value>
			<value level="14">31</value>
			<value level="15">33</value>
			<value level="16">35</value>
			<value level="17">37</value>
			<value level="18">39</value>
			<value level="19">41</value>
			<value level="20">43</value>
			<value level="21">45</value>
			<value level="22">47</value>
			<value level="23">49</value>
			<value level="24">51</value>
			<value level="25">53</value>
			<value level="26">55</value>
			<value level="27">57</value>
			<value level="28">59</value>
			<value level="29">61</value>
			<value level="30">63</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>
			<value level="1">5000</value>
			<value level="2">5000</value>
			<value level="3">5000</value>
			<value level="4">5000</value>
			<value level="5">5000</value>
			<value level="6">5000</value>
			<value level="7">5000</value>
			<value level="8">5000</value>
			<value level="9">5000</value>
			<value level="10">5000</value>
			<value level="11">5000</value>
			<value level="12">5000</value>
			<value level="13">5000</value>
			<value level="14">5000</value>
			<value level="15">5000</value>
			<value level="16">5000</value>
			<value level="17">5000</value>
			<value level="18">5000</value>
			<value level="19">5000</value>
			<value level="20">5000</value>
			<value level="21">5000</value>
			<value level="22">5000</value>
			<value level="23">5000</value>
			<value level="24">5000</value>
			<value level="25">5000</value>
			<value level="26">1000</value>
			<value level="27">1000</value>
			<value level="28">1000</value>
			<value level="29">1000</value>
			<value level="30">1000</value>
		</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">670</value>
					<value level="2">819</value>
					<value level="3">970</value>
					<value level="4">1071</value>
					<value level="5">1174</value>
					<value level="6">1277</value>
					<value level="7">1380</value>
					<value level="8">1485</value>
					<value level="9">1590</value>
					<value level="10">1695</value>
					<value level="11">1748</value>
					<value level="12">1802</value>
					<value level="13">1855</value>
					<value level="14">1908</value>
					<value level="15">1962</value>
					<value level="16">2016</value>
					<value level="17">2070</value>
					<value level="18">2124</value>
					<value level="19">2178</value>
					<value level="20">2232</value>
					<value level="21">2287</value>
					<value level="22">2341</value>
					<value level="23">2396</value>
					<value level="24">2451</value>
					<value level="25">2506</value>
					<value level="26">2561</value>
					<value level="27">2616</value>
					<value level="28">2671</value>
					<value level="29">2727</value>
					<value level="30">2782</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="45258" toLevel="1" name="Rescuing Fanatic" nameRu="Освобождение Фанатика">
		<!-- Removes Fanatic effect. -->
		<operateType>A1</operateType>
	</skill>
	<skill id="45259" toLevel="1" name="Losing Courage/ Frenzy" nameRu="Лишение Мужества/ Безумия">
		<!-- Removes Courage/ Frenzy effect. -->
		<operateType>A1</operateType>
	</skill>
	<skill id="45244" toLevel="57" name="Deadly Aggression">
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0028</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>TARGET_LOCK</abnormalType>
		<castRange>
		    <value level="1">400</value>
			<value level="2">400</value>
			<value level="3">400</value>
			<value level="4">400</value>
			<value level="5">400</value>
			<value level="6">400</value>
			<value level="7">400</value>
			<value level="8">400</value>
			<value level="9">400</value>
			<value level="10">400</value>
			<value level="11">400</value>
			<value level="12">400</value>
			<value level="13">600</value>
			<value level="14">600</value>
			<value level="15">600</value>
			<value level="16">600</value>
			<value level="17">600</value>
			<value level="18">600</value>
			<value level="19">600</value>
			<value level="20">600</value>
			<value level="21">600</value>
			<value level="22">600</value>
			<value level="23">600</value>
			<value level="24">600</value>
			<value level="25">600</value>
			<value level="26">600</value>
			<value level="27">600</value>
			<value level="28">600</value>
			<value level="29">600</value>
			<value level="30">600</value>
			<value level="31">600</value>
			<value level="32">600</value>
			<value level="33">600</value>
			<value level="34">700</value>
			<value level="35">700</value>
			<value level="36">700</value>
			<value level="37">700</value>
			<value level="38">700</value>
			<value level="39">700</value>
			<value level="40">700</value>
			<value level="41">700</value>
			<value level="42">700</value>
			<value level="43">700</value>
			<value level="44">700</value>
			<value level="45">700</value>
			<value level="46">700</value>
			<value level="47">700</value>
			<value level="48">700</value>
			<value level="49">700</value>
			<value level="50">700</value>
			<value level="51">700</value>
			<value level="52">700</value>
			<value level="53">700</value>
			<value level="54">700</value>
			<value level="55">700</value>
			<value level="56">700</value>
			<value level="57">700</value>
		</castRange>
		<effectPoint>
			<value level="1">-7180</value>
			<value level="2">-7440</value>
			<value level="3">-7700</value>
			<value level="4">-8250</value>
			<value level="5">-8530</value>
			<value level="6">-8810</value>
			<value level="7">-9380</value>
			<value level="8">-9680</value>
			<value level="9">-9980</value>
			<value level="10">-10590</value>
			<value level="11">-10900</value>
			<value level="12">-11210</value>
			<value level="13">-11840</value>
			<value level="14">-12160</value>
			<value level="15">-12470</value>
			<value level="16">-12800</value>
			<value level="17">-13120</value>
			<value level="18">-13440</value>
			<value level="19">-13770</value>
			<value level="20">-14090</value>
			<value level="21">-14410</value>
			<value level="22">-14740</value>
			<value level="23">-15060</value>
			<value level="24">-15380</value>
			<value level="25">-15690</value>
			<value level="26">-16010</value>
			<value level="27">-16310</value>
			<value level="28">-16620</value>
			<value level="29">-16930</value>
			<value level="30">-17230</value>
			<value level="31">-17520</value>
			<value level="32">-17810</value>
			<value level="33">-18100</value>
			<value level="34">-18370</value>
			<value level="35">-18650</value>
			<value level="36">-18910</value>
			<value level="37">-19170</value>
			<value level="38">-19430</value>
			<value level="39">-19670</value>
			<value level="40">-19900</value>
			<value level="41">-20120</value>
			<value level="42">-20340</value>
			<value level="43">-20550</value>
			<value level="44">-20750</value>
			<value level="45">-20930</value>
			<value level="46">-21110</value>
			<value level="47">-21270</value>
			<value level="48">-21430</value>
			<value level="49">-21570</value>
			<value level="50">-21710</value>
			<value level="51">-21860</value>
			<value level="52">-22000</value>
			<value level="53">-22290</value>
			<value level="54">-22430</value>
			<value level="55">-22570</value>
			<value level="56">-22720</value>
			<value level="57">-22860</value>
		</effectPoint>
		<effectRange>
			<value level="1">900</value>
			<value level="2">900</value>
			<value level="3">900</value>
			<value level="4">900</value>
			<value level="5">900</value>
			<value level="6">900</value>
			<value level="7">900</value>
			<value level="8">900</value>
			<value level="9">900</value>
			<value level="10">900</value>
			<value level="11">900</value>
			<value level="12">900</value>
			<value level="13">1100</value>
			<value level="14">1100</value>
			<value level="15">1100</value>
			<value level="16">1100</value>
			<value level="17">1100</value>
			<value level="18">1100</value>
			<value level="19">1100</value>
			<value level="20">1100</value>
			<value level="21">1100</value>
			<value level="22">1100</value>
			<value level="23">1100</value>
			<value level="24">1100</value>
			<value level="25">1100</value>
			<value level="26">1100</value>
			<value level="27">1100</value>
			<value level="28">1100</value>
			<value level="29">1100</value>
			<value level="30">1100</value>
			<value level="31">1100</value>
			<value level="32">1100</value>
			<value level="33">1100</value>
			<value level="34">1300</value>
			<value level="35">1300</value>
			<value level="36">1300</value>
			<value level="37">1300</value>
			<value level="38">1300</value>
			<value level="39">1300</value>
			<value level="40">1300</value>
			<value level="41">1300</value>
			<value level="42">1300</value>
			<value level="43">1300</value>
			<value level="44">1300</value>
			<value level="45">1300</value>
			<value level="46">1300</value>
			<value level="47">1300</value>
			<value level="48">1300</value>
			<value level="49">1300</value>
			<value level="50">1300</value>
			<value level="51">1300</value>
			<value level="52">1300</value>
			<value level="53">1300</value>
			<value level="54">1300</value>
			<value level="55">1300</value>
			<value level="56">1300</value>
			<value level="57">1300</value>
		</effectRange>
		<hitTime>700</hitTime>
		<coolTime>200</coolTime>
		<isDebuff>true</isDebuff>
		<magicLevel>
			<value level="1">22</value>
			<value level="2">23</value>
			<value level="3">24</value>
			<value level="4">26</value>
			<value level="5">27</value>
			<value level="6">28</value>
			<value level="7">30</value>
			<value level="8">31</value>
			<value level="9">32</value>
			<value level="10">34</value>
			<value level="11">35</value>
			<value level="12">36</value>
			<value level="13">38</value>
			<value level="14">39</value>
			<value level="15">40</value>
			<value level="16">41</value>
			<value level="17">42</value>
			<value level="18">43</value>
			<value level="19">44</value>
			<value level="20">45</value>
			<value level="21">46</value>
			<value level="22">47</value>
			<value level="23">48</value>
			<value level="24">49</value>
			<value level="25">50</value>
			<value level="26">51</value>
			<value level="27">52</value>
			<value level="28">53</value>
			<value level="29">54</value>
			<value level="30">55</value>
			<value level="31">56</value>
			<value level="32">57</value>
			<value level="33">58</value>
			<value level="34">59</value>
			<value level="35">60</value>
			<value level="36">61</value>
			<value level="37">62</value>
			<value level="38">63</value>
			<value level="39">64</value>
			<value level="40">65</value>
			<value level="41">66</value>
			<value level="42">67</value>
			<value level="43">68</value>
			<value level="44">69</value>
			<value level="45">70</value>
			<value level="46">71</value>
			<value level="47">72</value>
			<value level="48">73</value>
			<value level="49">74</value>
			<value level="50">76</value>
			<value level="51">77</value>
			<value level="52">78</value>
			<value level="53">80</value>
			<value level="54">81</value>
			<value level="55">82</value>
			<value level="56">83</value>
			<value level="57">84</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>6000</reuseDelay>
		<reuseDelayGroup>28</reuseDelayGroup>
		<staticReuse>true</staticReuse>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="TargetMe" />
			<effect name="GetAgro" />
		</effects>
		<selfEffects>
		    <effect name="CallSkill">
		        <skillId>45232</skillId>
		        <skillLevel>1</skillLevel>
            </effect>
		</selfEffects>
	</skill>
	<skill id="45261" toLevel="10" name="Transcendent Divine Strike" nameRu="Невероятный Божественный Удар">
		<!-- Summons holy light.\nDeals magic damage with $s1 power and additional damage to demons and undead.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45262" toLevel="15" name="Transcendent Divine Beam" nameRu="Невероятный Святой Луч">
		<!-- A divine beam strikes the target.\nInflicts magical damage with $s1 power.\nWhen you are attacking the enemy affected by Hold, the debuff is cancelled and an extra attack is triggered.\nDeals additional damage to demons and undead.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45263" toLevel="2" name="Knight's Power Lv. 8">
		<icon>icon.skill0000</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalType>KNIGHTS_POWER</abnormalType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">875</value>
					<value level="2">1400</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45264" toLevel="2" name="Knight's Power Lv. 9">
		<icon>icon.skill0000</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalType>KNIGHTS_POWER</abnormalType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">1000</value>
					<value level="2">1600</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45265" toLevel="10" name="Transcendent Life Drain" nameRu="Невероятная Кража Жизни">
		<!-- <Drain Skill>\n\nDeals magic damage to the enemy with $s1 power. Absorbs $s2 of the inflicted damage as HP.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<operateType>A1</operateType>
	</skill>
	<skill id="45266" toLevel="15" name="Transcendent Demolition Impact" nameRu="Невероятный Удар Низвержения">
		<!-- Unleashes a destructive shockwave at the target with $s1 power.\nRequires a sword or a blunt weapon.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0777</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45267" toLevel="15" name="Transcendent Earth Tremor" nameRu="Невероятное Сокрушение Земли">
		<!-- Releases earth rage to attack the enemy with $s1 power.\nRequires a sword, blunt weapon or a spear.\nIgnores Shield Defense.\nIgnores $s2 of target's defense.\nCritical.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1922</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45268" toLevel="15" name="Transcendent Spoil Crush" nameRu="Невероятная Сокрушительная Оценка">
		<!-- Attacks the target with $s1 power.\nRequires a sword, a blunt weapon, or a spear.\nIgnores Shield Defense.\nIgnores $s2 of target's defense.\nCritical.\nOver-hit.\nCan be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0348</icon>
		<operateType>A1</operateType>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
	<skill id="45269" toLevel="1" name="Expanded Potential of Dyes" nameRu="Расширенный Потенциал Красок">
		<operateType>P</operateType>
		<effects>
			<effect name="HennaSlotsAdd">
				<amount>1</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45270" toLevel="2" name="Knight's Power Lv. 10">
		<icon>icon.skill0000</icon>
		<magicLevel>40</magicLevel>
		<operateType>A2</operateType>
		<abnormalType>KNIGHTS_POWER</abnormalType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">1250</value>
					<value level="2">2000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45271" toLevel="5" name="Extended Life">
		<icon>icon.skill0332</icon>
		<magicLevel>-1</magicLevel>
		<operateType>P</operateType>
		<effects>
		    <effect name="MaxHp">
		        <amount>
		            <value level="1">600</value>
		            <value level="2">1350</value>
		            <value level="3">2100</value>
		            <value level="4">3000</value>
		            <value level="5">4300</value>
                </amount>
                <mode>DIFF</mode>
            </effect>
		    <!-- Level bonus hardcoded in MaxHpFinalizer -->
		    <effect name="EffectFlag">
		        <flag>
		            <value level="1">EXTENDED_LIFE_1</value>
		            <value level="2">EXTENDED_LIFE_2</value>
		            <value level="3">EXTENDED_LIFE_3</value>
		            <value level="4">EXTENDED_LIFE_4</value>
		            <value level="5">EXTENDED_LIFE_5</value>
                </flag>
            </effect>
		</effects>
	</skill>
</list>
