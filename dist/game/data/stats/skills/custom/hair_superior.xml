<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/skills.xsd">
	<!-- TODO : Hair Superior -->
	<!-- Plastic Hair -->
	<skill id="98046" displayId="98046" toLevel="1" name="Plastic Hair">
		<icon>BranchSys.icon.br_plastic_hair_f_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200046</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="MaxHp">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>40</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<!-- Jester Hat -->
	<skill id="98047" displayId="98047" toLevel="1" name="Jester Hat">
		<icon>icon.accessory_bear_cap_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200049</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="StatUp">
				<amount>-2</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>-2</amount>
				<stat>INT</stat>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>130</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>2</amount>
			</effect>
			<effect name="SkillEvasion">
				<magicType>1</magicType>
				<amount>2</amount>
			</effect>
			<effect name="Speed">
				<amount>5</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<DERANGEMENT>90</DERANGEMENT>
			</effect>
			<effect name="HealEffect">
				<amount>-25</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicMpCost">
				<amount>25</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>25</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>25</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
			<effect name="SkillReuseChange">
				<amount>-1200</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<!-- Piggy Hat -->
	<skill id="98048" displayId="98048" toLevel="1" name="Piggy Hat">
		<icon>icon.accessory_pig_cap_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200048</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PAtk">
				<amount>22</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>12</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<!-- Teddy Bear Hat -->
	<skill id="98049" displayId="98049" toLevel="1" name="Teddy Bear Hat">
		<icon>icon.accessory_jester_cap_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200049</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="MAtk">
				<amount>35</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>12</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<!-- Leather Cap -->
	<skill id="98050" displayId="98050" toLevel="1" name="Leather Cap">
		<icon>icon.Accessary_middle_ages_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200050</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="ShieldDefence">
				<amount>900</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ShieldDefenceRate">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ShieldDefenceAngle">
				<amount>180</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="TankBow">
				<amount>33</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxCp">
				<amount>2000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>5</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="RushDistAdd">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackRange">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicAttackRange">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Accuracy">
				<amount>5</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>POLE</item>
				</weaponType>
			</effect>
			<effect name="Speed">
				<amount>7</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>POLE</item>
				</weaponType>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>40</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>POLE</item>
				</weaponType>
			</effect>
		</effects>
	</skill>
	<!-- Luxurious Gold Circlet -->
	<skill id="98051" displayId="98051" toLevel="1" name="Luxurious Gold Circlet">
		<icon>icon.Accessary_gold_circlet_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200051</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="MagicalDefence">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>2</amount>
				<mode>PER</mode>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>2</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>2</amount>
				<stat>CON</stat>
			</effect>
			<effect name="MagicMpCost">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
			<effect name="MaxHp">
				<amount>700</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<!-- Luxurious Silver Circlet -->
	<skill id="98052" displayId="98052" toLevel="1" name="Luxurious Silver Circlet">
		<icon>icon.Accessary_gold_circlet_i01</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200052</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="MagicalDefence">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>2</amount>
				<mode>PER</mode>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
			<effect name="StatUp">
				<amount>2</amount>
				<stat>WIT</stat>
			</effect>
			<effect name="StatUp">
				<amount>2</amount>
				<stat>MEN</stat>
			</effect>
			<effect name="MagicMpCost">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
			<effect name="MaxHp">
				<amount>700</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<!-- Santa's Antlers -->
	<skill id="98053" displayId="98053" toLevel="1" name="Santa's Antlers">
		<icon>icon.accessory_santas_antler_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200053</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PhysicalAttackRange">
				<amount>110</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicAttackRange">
				<amount>110</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillRadiusBoost">
				<amount>60</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="AttackAngleBoost">
				<amount>45</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>5</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillRadiusBoost">
				<amount>60</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<!-- Straw Hat -->
	<skill id="98054" displayId="98054" toLevel="1" name="Straw Hat">
		<icon>icon.accessory_straw_hat_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200054</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PhysicalEvasion">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Accuracy">
				<amount>5</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="AttackReuse">
				<amount>-16</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CritMaxAdd">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceAttribute">
				<amount>30</amount>
				<attribute>FIRE</attribute>
			</effect>
			<effect name="DefenceAttribute">
				<amount>30</amount>
				<attribute>WATER</attribute>
			</effect>
			<effect name="DefenceAttribute">
				<amount>30</amount>
				<attribute>WIND</attribute>
			</effect>
			<effect name="DefenceAttribute">
				<amount>30</amount>
				<attribute>EARTH</attribute>
			</effect>
		</effects>
	</skill>
	<!-- Wolf Hat -->
	<skill id="98055" displayId="98055" toLevel="1" name="Wolf Hat">
		<icon>icon.accessory_wolf_cap_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200055</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="FatalBlowRate">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="VampiricAttack">
				<amount>6</amount>
				<chance>100</chance>
			</effect>
			<effect name="Speed">
				<amount>8</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="CriticalDmgAddBleeding">
				<amount>150</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>0.3</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalDamage">
				<amount>0.3</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillCriticalProbability">
				<amount>3</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>80</amount>
				<mode>PER</mode>
				<atNight>true</atNight>
			</effect>
		</effects>
	</skill>
	<!-- Metal Eye Patch -->
	<skill id="98056" displayId="98056" toLevel="1" name="Metal Eye Patch">
		<icon>icon.Accessary_one_eye_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200056</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="Accuracy">
				<amount>-8</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="FatalBlowRate">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>0.2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalDamage">
				<amount>0.2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>12</amount>
			</effect>
			<effect name="DefenceCriticalDamage">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="EvadeAOEHit">
				<amount>40</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<!-- Goddess Circlet -->
	<skill id="98057" displayId="98057" toLevel="1" name="Goddess Circlet">
		<icon>icon.Accessary_goddess_circlet_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200057</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="HealEffect">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>40</amount>
				<mode>PER</mode>
			</effect>
			<effect name="TransferDamageToSummon">
				<amount>10</amount>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-70</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="DefenceAttribute">
				<amount>30</amount>
				<attribute>EARTH</attribute>
			</effect>
			<effect name="DefenceAttribute">
				<amount>20</amount>
				<attribute>WIND</attribute>
			</effect>
		</effects>
	</skill>
	<!-- Pirate Hat -->
	<skill id="98058" displayId="98058" toLevel="1" name="Pirate Hat">
		<icon>icon.Accessary_sea_robber_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200058</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="PhysicalEvasion">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Reuse">
				<amount>-20</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>5</amount>
			</effect>
			<effect name="SkillEvasion">
				<magicType>1</magicType>
				<amount>5</amount>
			</effect>
		</effects>
	</skill>
	<!-- Romantic Chapeau -->
	<skill id="98059" displayId="98059" toLevel="1" name="Romantic Chapeau">
		<icon>icon.accessory_romantic_chaperon_i00</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>HAIR</abnormalType>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>200059</itemConsumeId>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="TransferDamageToSummon">
				<amount>15</amount>
			</effect>
			<effect name="PhysicalDefence">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Reuse">
				<amount>-15</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="Reuse">
				<amount>-15</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="Reuse">
				<amount>-15</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
			<effect name="ReduceCancel">
				<amount>-15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="LionHeart">
				<amount>15</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
</list>