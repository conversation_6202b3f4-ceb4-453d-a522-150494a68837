<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/skills.xsd">
	<skill id="200000" toLevel="1" name="Frost Lord Enhancement">
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<abnormalTime>21600</abnormalTime>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<abnormalVisualEffect>V_ORC_COLD_FLAME_A_AVE</abnormalVisualEffect>
		<shortcutToggleType>1</shortcutToggleType>
		<effects>
			<effect name="ExpModify">
				<amount>25</amount>
			</effect>
			<effect name="SpModify">
				<amount>25</amount>
			</effect>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpMagicalSkillDamageBonus">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalAttackDamageBonus">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalSkillDamageBonus">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="200001" toLevel="1" name="Project Essence's Buff">
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<abnormalTime>28800</abnormalTime>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<shortcutToggleType>1</shortcutToggleType>
		<effects>
			<effect name="ExpModify">
				<amount>40</amount>
			</effect>
			<effect name="SpModify">
				<amount>40</amount>
			</effect>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="200002" toLevel="1" name="Giants' Enhancement">
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<abnormalTime>14400</abnormalTime>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<shortcutToggleType>1</shortcutToggleType>
		<abnormalVisualEffect>SEED_TALISMAN8</abnormalVisualEffect>
		<effects>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpMagicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalAttackDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="200003" toLevel="1" name="Giants' Enhancement">
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<abnormalTime>1200</abnormalTime>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<shortcutToggleType>1</shortcutToggleType>
		<abnormalVisualEffect>SEED_TALISMAN8</abnormalVisualEffect>
		<effects>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpMagicalSkillDamageBonus">
				<amount>3</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalAttackDamageBonus">
				<amount>3</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalSkillDamageBonus">
				<amount>3</amount>
				<mode>PER</mode>
			</effect>
			<effect name="EffectFlag">
				<flag>EVENT_LEOGUL_SPAWN</flag>
			</effect>
		</effects>
	</skill>
	<skill id="200004" toLevel="3" name="Trick o' Treat Buff">
		<!-- P./ M. Atk. and P./ M. Def. +$s1, Acquired XP/ SP +$s2. When $s3 effect expires, the character obtains Lucky Box and $s4 buff.\n\nNote!\nYou cannot obtain the item if you are dead or your inventory is full when you receive it. You can't get the next level buff if your character is dead. -->
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</magicLevel>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<abnormalTime>
			<value level="1">28800</value> <!-- 8 hours -->
			<value level="2">28800</value> <!-- 8 hours -->
			<value level="3">25200</value> <!-- 7 hours -->
		</abnormalTime>
		<irreplacableBuff>true</irreplacableBuff>
		<stayAfterDeath>true</stayAfterDeath>
		<isTriggeredSkill>true</isTriggeredSkill>
		<canBeDispelled>false</canBeDispelled>
		<abnormalType>GOOD_LUCK_BUFF</abnormalType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isMagic>4</isMagic>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">150</value>
					<value level="3">200</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">150</value>
					<value level="3">200</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">100</value>
					<value level="2">150</value>
					<value level="3">200</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">100</value>
					<value level="2">150</value>
					<value level="3">200</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ExpModify">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">15</value>
				</amount>
			</effect>
			<effect name="SpModify">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">15</value>
				</amount>
			</effect>
			<effect name="EffectFlag">
				<flag>EVENT_HALLOWEEN_APPEARANCE</flag>
			</effect>
		</effects>
		<endEffects>
			<effect name="CallSkill" fromLevel="1" toLevel="2">
				<skillId>200004</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
				</skillLevel>
			</effect>
			<effect name="CallSkill">
				<skillId>200005</skillId> <!-- Trick o' Treat Buff - Reward -->
				<skillLevel>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</skillLevel>
			</effect>
		</endEffects>
	</skill>
	<skill id="200005" toLevel="3" name="Trick o' Treat Buff - Reward">
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Restoration" fromLevel="1" toLevel="1">
				<itemId>120005</itemId> <!-- Lucky Box Lv. 1 -->
				<itemCount>1</itemCount>
			</effect>
			<effect name="Restoration" fromLevel="2" toLevel="2">
				<itemId>120006</itemId> <!-- Lucky Box Lv. 2 -->
				<itemCount>1</itemCount>
			</effect>
			<effect name="Restoration" fromLevel="3" toLevel="3">
				<itemId>120007</itemId> <!-- Lucky Box Lv. 3 -->
				<itemCount>1</itemCount>
			</effect>
		</effects>
	</skill>



	<skill id="200006" toLevel="1" name="VIP Lv. 8" nameRu="VIP 8-го уровня">
		<icon>BranchIcon.Icon.g_skill_vip8</icon>
		<operateType>P</operateType>
		<effects>
		    <effect name="ExpModify">
		        <amount>25</amount>
            </effect>
		    <effect name="SpModify">
		        <amount>25</amount>
            </effect>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="200007" toLevel="1" name="VIP Lv. 9" nameRu="VIP 9-го уровня">
		<icon>BranchIcon.Icon.g_skill_vip9</icon>
		<operateType>P</operateType>
		<effects>
		    <effect name="ExpModify">
		        <amount>25</amount>
            </effect>
		    <effect name="SpModify">
		        <amount>25</amount>
            </effect>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>6</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>6</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>6</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="200008" toLevel="1" name="VIP Lv. 10" nameRu="VIP 10-го уровня">
		<icon>BranchIcon.Icon.g_skill_vip10</icon>
		<operateType>P</operateType>
		<effects>
		    <effect name="ExpModify">
		        <amount>30</amount>
            </effect>
		    <effect name="SpModify">
		        <amount>30</amount>
            </effect>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>7</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
</list>
