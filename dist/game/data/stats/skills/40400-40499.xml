<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="40411" toLevel="1" name="Spellbook: Mount Red Wyvern" nameRu="Книга Заклинаний - Оседлать Красную Виверну">
		<icon>icon.skill0000</icon>
		<hitTime>1500</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>97203</itemConsumeId> <!-- Spellbook: Mount Red Wyvern -->
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="SetSkill">
				<skillId>54240</skillId> <!-- Mount Red Wyvern -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="40412" toLevel="1" name="Scroll of Escape: Guardian Tanai" nameRu="Свиток Телепорта - Хранитель Танаи">
		<hitTime>0</hitTime>
		<isMagic>0</isMagic>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>97230</itemConsumeId>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpAlignment">
				<affectType>CASTER</affectType>
				<alignment>LAWFUL</alignment>
			</condition>
			<condition name="OpCanEscape" />
		</conditions>
		<effects>
			<effect name="Teleport">
				<x>-45150</x>
				<y>-113669</y>
				<z>-208</z>
			</effect>
		</effects>
	</skill>
	<skill id="40413" toLevel="1" name="Scroll of Escape: Gantaki Zu Urutu" nameRu="Свиток Телепорта - Гентаки Зу Уруту">
		<hitTime>0</hitTime>
		<isMagic>0</isMagic>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>97231</itemConsumeId>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpAlignment">
				<affectType>CASTER</affectType>
				<alignment>LAWFUL</alignment>
			</condition>
			<condition name="OpCanEscape" />
		</conditions>
		<effects>
			<effect name="Teleport">
				<x>-44686</x>
				<y>-111870</y>
				<z>-240</z>
			</effect>
		</effects>
	</skill>
	<skill id="40460" toLevel="1" name="MP Recovery Elixir">
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<itemConsumeId>98033</itemConsumeId> <!-- MP Recovery Elixir -->
		<itemConsumeCount>1</itemConsumeCount>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Mp">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
</list>
