<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="5700" toLevel="7" name="Decrease M. Def.">
		<!-- Decreases M. Def. -->
		<icon>icon.skill1263</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>3600</abnormalTime>
		<abnormalType>MD_DOWN</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>29</magicLevel>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="MagicalDefence">
				<amount>-66</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="5701" toLevel="7" name="Decrease P. Atk.">
		<!-- Decreases P. Atk. -->
		<icon>icon.skill4037</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>3600</abnormalTime>
		<abnormalType>PA_DOWN</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>29</magicLevel>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="PhysicalAttack">
				<amount>-66</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="5702" toLevel="2" name="Adiantum Round Fighter">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>
			<value level="1">SELF</value>
			<value level="2">ENEMY</value>
		</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>400</affectRange>
		<affectScope>
			<value level="1">POINT_BLANK</value>
			<value level="2">RANGE</value>
		</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>
			<value level="1">0</value>
			<value level="2">40</value>
		</castRange>
		<effectPoint>-341</effectPoint>
		<effectRange>
			<value level="1">-1</value>
			<value level="2">400</value>
		</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>36</magicLevel>
		<mpConsume>5</mpConsume>
		<mpInitialConsume>2</mpInitialConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>69</power>
			</effect>
		</effects>
	</skill>
	<skill id="5703" toLevel="2" name="Adiantum Water Strike Deflect">
		<icon>icon.skill0000</icon>
		<operateType>A3</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>6</abnormalTime>
		<abnormalType>ARMOR_FIRE</abnormalType>
		<affectScope>SINGLE</affectScope>
		<attributeType>WATER</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>NONE</basicProperty>
		<castRange>40</castRange>
		<effectPoint>-341</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1000</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>36</magicLevel>
		<mpConsume>7</mpConsume>
		<reuseDelay>6000</reuseDelay>
	</skill>
	<skill id="5704" toLevel="1" name="Water Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>WATER</attributeType>
		<attributeValue>40</attributeValue>
		<castRange>900</castRange>
		<effectPoint>-341</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>36</magicLevel>
		<mpConsume>7</mpConsume>
		<reuseDelay>6000</reuseDelay>
	</skill>
	<skill id="5705" toLevel="1" name="Fire Trap">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<effectPoint>-341</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>36</magicLevel>
	</skill>
	<skill id="5706" toLevel="1" name="Poison">
		<!-- HP decreases from Poison momentarily. The body is paralyzed after a set amount of time. -->
		<icon>icon.skill4036</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>7</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<activateRate>70</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitCancelTime>1.5</hitCancelTime>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<mpConsume>57</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<trait>POISON</trait>
	</skill>
	<skill id="5707" toLevel="1" name="Paralysis">
		<!-- Paralyzed and unable to move momentarily. -->
		<icon>icon.skill1170</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>-100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<trait>PARALYZE</trait>
	</skill>
	<skill id="5708" toLevel="2" name="Water Cannon">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-15</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<attributeType>WATER</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-574</value>
			<value level="2">-684</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<fanRange>0;0;400;60</fanRange>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">61</value>
			<value level="2">83</value>
		</magicLevel>
		<mpConsume>
			<value level="1">57</value>
			<value level="2">74</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>160</power>
			</effect>
		</effects>
	</skill>
	<skill id="5709" toLevel="2" name="Whirlpool">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>WATER</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>900</castRange>
		<effectPoint>
			<value level="1">-574</value>
			<value level="2">-684</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>1200</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">61</value>
			<value level="2">83</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">74</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>220</power>
			</effect>
		</effects>
	</skill>
	<skill id="5710" toLevel="2" name="Triple Sword">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-15</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<castRange>900</castRange>
		<effectPoint>
			<value level="1">-574</value>
			<value level="2">-684</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<fanRange>0;0;500;60</fanRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">61</value>
			<value level="2">83</value>
		</magicLevel>
		<mpConsume>
			<value level="1">47</value>
			<value level="2">82</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>320</power>
			</effect>
		</effects>
	</skill>
	<skill id="5711" toLevel="1" name="Power of Rage">
		<!-- P. Atk. is greatly increased because of rage. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<activateRate>0</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>1</effectPoint>
		<hitCancelTime>1.5</hitCancelTime>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<mpConsume>57</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5712" toLevel="1" name="Energy Ditch">
		<!-- Delivers one's own energy to Lematan. The delivery status is cancelled when damage is incurred. -->
		<icon>icon.skill0000</icon>
		<operateType>CA5</operateType>
		<targetType>OTHERS</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>1300</castRange>
		<channelingSkillId>5713</channelingSkillId> <!-- Energy Ditch -->
		<channelingStart>3.6</channelingStart>
		<channelingTickInterval>2</channelingTickInterval>
		<effectPoint>1</effectPoint>
		<effectRange>1700</effectRange>
		<hitTime>15000</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<mpPerChanneling>15</mpPerChanneling>
		<reuseDelay>10000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>200</power>
			</effect>
		</effects>
	</skill>
	<skill id="5713" toLevel="5" name="Energy Ditch">
		<!-- Has been given minions' M. Atk. Recovery bonus increased. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
		</abnormalLevel>
		<abnormalTime>3600</abnormalTime>
		<abnormalType>MAGICAL_STANCE</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5714" toLevel="1" name="Boom Up">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectRange>600</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<effectPoint>-574</effectPoint>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<mpConsume>57</mpConsume>
		<reuseDelay>6000</reuseDelay>
	</skill>
	<skill id="5715" toLevel="1" name="Electric Flame">
		<!-- Incurring continuous flame damage and has decreased Speed. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>FIRE_DOT</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>400</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>-975</effectPoint>
		<hitTime>2500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>73</magicLevel>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>213</power>
			</effect>
			<effect name="DamOverTime">
				<power>55</power>
				<ticks>1</ticks>
			</effect>
			<effect name="Speed">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="5716" toLevel="1" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>30</activateRate>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>1500</castRange>
		<effectPoint>-975</effectPoint>
		<effectRange>2000</effectRange>
		<fanRange>0;0;1500;60</fanRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>73</magicLevel>
		<reuseDelay>3000</reuseDelay>
		<trait>SHOCK</trait>
	</skill>
	<skill id="5717" toLevel="1" name="Fire Breath">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>1500</castRange>
		<effectPoint>-650</effectPoint>
		<effectRange>2000</effectRange>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>73</magicLevel>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="5718" toLevel="1" name="Anger">
		<!-- P. Def. is decreased and P. Atk. is greatly increased because of rage. -->
		<icon>icon.skill0176</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<activateRate>0</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>73</magicLevel>
		<reuseDelay>5000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5719" toLevel="1" name="Kanabion Susceptibility">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>PD_DOWN</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>-1</magicLevel>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5720" toLevel="6" name="Blade Cut">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>800</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1136</value>
					<value level="2">1626</value>
					<value level="3">1876</value>
					<value level="4">2112</value>
					<value level="5">2320</value>
					<value level="6">2487</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5721" toLevel="6" name="Blade Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>800</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>2500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">2272</value>
					<value level="2">3252</value>
					<value level="3">3752</value>
					<value level="4">4224</value>
					<value level="5">4640</value>
					<value level="6">4974</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5722" toLevel="6" name="Hammer Assault">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>15000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1136</value>
					<value level="2">1626</value>
					<value level="3">1876</value>
					<value level="4">2112</value>
					<value level="5">2320</value>
					<value level="6">2487</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5723" toLevel="6" name="Hammer Swing">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<castRange>100</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>500</effectRange>
		<hitTime>2500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">625</value>
					<value level="2">895</value>
					<value level="3">1032</value>
					<value level="4">1162</value>
					<value level="5">1276</value>
					<value level="6">1368</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5724" toLevel="6" name="Broom Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1250</value>
					<value level="2">1789</value>
					<value level="3">2064</value>
					<value level="4">2323</value>
					<value level="5">2552</value>
					<value level="6">2736</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5725" toLevel="6" name="Broom Thrust">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1800</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">121</value>
			<value level="2">142</value>
			<value level="3">151</value>
			<value level="4">159</value>
			<value level="5">165</value>
			<value level="6">168</value>
		</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">2272</value>
					<value level="2">3252</value>
					<value level="3">3752</value>
					<value level="4">4224</value>
					<value level="5">4640</value>
					<value level="6">4974</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5726" toLevel="6" name="Scissors Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>500</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1136</value>
					<value level="2">1626</value>
					<value level="3">1876</value>
					<value level="4">2112</value>
					<value level="5">2320</value>
					<value level="6">2487</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5727" toLevel="6" name="Scissors Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>2500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">121</value>
			<value level="2">142</value>
			<value level="3">151</value>
			<value level="4">159</value>
			<value level="5">165</value>
			<value level="6">168</value>
		</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">2329</value>
					<value level="2">3333</value>
					<value level="3">3845</value>
					<value level="4">4329</value>
					<value level="5">4756</value>
					<value level="6">5099</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5728" toLevel="6" name="Shovel Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>500</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1136</value>
					<value level="2">1626</value>
					<value level="3">1876</value>
					<value level="4">2112</value>
					<value level="5">2320</value>
					<value level="6">2487</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5729" toLevel="6" name="Shovel Whirlwind">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>-100</effectPoint>
		<hitTime>2500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>20000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">568</value>
					<value level="2">813</value>
					<value level="3">938</value>
					<value level="4">1056</value>
					<value level="5">1160</value>
					<value level="6">1244</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5730" toLevel="6" name="Made Fireball">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1080</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">55</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">61</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">11</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">15</value>
			<value level="5">15</value>
			<value level="6">16</value>
		</mpInitialConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">90</value>
					<value level="2">107</value>
					<value level="3">115</value>
					<value level="4">122</value>
					<value level="5">128</value>
					<value level="6">132</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5731" toLevel="6" name="Incense of Death">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<coolTime>800</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">55</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">61</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">11</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">15</value>
			<value level="5">15</value>
			<value level="6">16</value>
		</mpInitialConsume>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">85</value>
					<value level="2">102</value>
					<value level="3">110</value>
					<value level="4">116</value>
					<value level="5">122</value>
					<value level="6">126</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5732" toLevel="6" name="Flame Strike">
		<icon>icon.skill1181</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>500</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>500</castRange>
		<effectPoint>
			<value level="1">-574</value>
			<value level="2">-641</value>
			<value level="3">-662</value>
			<value level="4">-679</value>
			<value level="5">-692</value>
			<value level="6">-702</value>
		</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">55</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">61</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">11</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">15</value>
			<value level="5">15</value>
			<value level="6">16</value>
		</mpInitialConsume>
		<reuseDelay>15000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">47</value>
					<value level="2">56</value>
					<value level="3">60</value>
					<value level="4">64</value>
					<value level="5">67</value>
					<value level="6">70</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5733" toLevel="6" name="Fear of Steward">
		<!-- Afflicted with Fear and moving against own will momentarily. -->
		<icon>icon.skill1092</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>TURN_FLEE</abnormalType>
		<abnormalVisualEffect>TURN_FLEE</abnormalVisualEffect>
		<activateRate>20</activateRate>
		<affectLimit>10-10</affectLimit>
		<affectScope>SINGLE</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">55</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">61</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">11</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">15</value>
			<value level="5">15</value>
			<value level="6">16</value>
		</mpInitialConsume>
		<reuseDelay>6000</reuseDelay>
		<trait>DERANGEMENT</trait>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">43</value>
					<value level="2">51</value>
					<value level="3">55</value>
					<value level="4">58</value>
					<value level="5">61</value>
					<value level="6">63</value>
				</power>
			</effect>
			<effect name="BlockControl" />
			<effect name="Fear" />
		</effects>
	</skill>
	<skill id="5734" toLevel="6" name="Gust of Wind">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>WIND</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">55</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">61</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">11</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">15</value>
			<value level="5">15</value>
			<value level="6">16</value>
		</mpInitialConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">85</value>
					<value level="2">102</value>
					<value level="3">110</value>
					<value level="4">116</value>
					<value level="5">122</value>
					<value level="6">126</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5735" toLevel="6" name="Curse of Steward">
		<!-- Momentarily inflicts Sleep on the target. Additional chance to be put into sleep greatly decreases while the effect lasts. -->
		<icon>icon.skill1069</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>SLEEP</abnormalType>
		<abnormalVisualEffect>SLEEP</abnormalVisualEffect>
		<activateRate>25</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-566</value>
			<value level="2">-635</value>
			<value level="3">-659</value>
			<value level="4">-676</value>
			<value level="5">-689</value>
			<value level="6">-700</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">55</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">61</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">11</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">15</value>
			<value level="5">15</value>
			<value level="6">16</value>
		</mpInitialConsume>
		<removedOnDamage>true</removedOnDamage>
		<reuseDelay>15000</reuseDelay>
		<trait>SLEEP</trait>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">43</value>
					<value level="2">51</value>
					<value level="3">55</value>
					<value level="4">58</value>
					<value level="5">61</value>
					<value level="6">63</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
			<effect name="DefenceTrait">
				<SLEEP>100</SLEEP>
			</effect>
		</effects>
	</skill>
	<skill id="5736" toLevel="6" name="Katar Trusting">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>1800</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">182</value>
			<value level="2">212</value>
			<value level="3">227</value>
			<value level="4">238</value>
			<value level="5">247</value>
			<value level="6">252</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">3408</value>
					<value level="2">4878</value>
					<value level="3">5627</value>
					<value level="4">6335</value>
					<value level="5">6960</value>
					<value level="6">7461</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5737" toLevel="6" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>-100</effectPoint>
		<hitTime>2500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>20000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">568</value>
					<value level="2">813</value>
					<value level="3">938</value>
					<value level="4">1056</value>
					<value level="5">1160</value>
					<value level="6">1244</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5738" toLevel="6" name="Power Roar">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>-100</effectPoint>
		<hitTime>2500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">61</value>
			<value level="2">71</value>
			<value level="3">76</value>
			<value level="4">80</value>
			<value level="5">83</value>
			<value level="6">84</value>
		</mpConsume>
		<reuseDelay>20000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">568</value>
					<value level="2">813</value>
					<value level="3">938</value>
					<value level="4">1056</value>
					<value level="5">1160</value>
					<value level="6">1244</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5739" toLevel="1" name="Invincibility">
		<!-- Recently resurrected and temporarily immobile. -->
		<icon>icon.skill5739</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>INVINCIBILITY</abnormalType>
		<abnormalVisualEffect>INVINCIBILITY</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectRange>900</effectRange>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="DamageBlock">
				<type>BLOCK_HP</type>
			</effect>
			<effect name="DamageBlock">
				<type>BLOCK_MP</type>
			</effect>
			<effect name="DebuffBlock" />
		</effects>
	</skill>
	<skill id="5740" toLevel="1" name="Recovery Pot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectRange>1400</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<effects>
			<effect name="HealPercent">
				<power>100</power>
			</effect>
			<effect name="ManaHealPercent">
				<power>100</power>
			</effect>
			<effect name="Cp">
				<amount>5000</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="5741" toLevel="1" name="Mental Pot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectRange>1400</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<effects>
			<effect name="ManaHealPercent">
				<power>100</power>
			</effect>
		</effects>
	</skill>
	<skill id="5742" toLevel="5" name="Recharge">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
		</magicLevel>
	</skill>
	<skill id="5743" toLevel="1" name="Full Recovery">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
	</skill>
	<skill id="5744" toLevel="1" name="Invincibility">
		<!-- Temporarily invincible. -->
		<icon>icon.skill1418</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>INVINCIBILITY</abnormalType>
		<abnormalVisualEffect>INVINCIBILITY</abnormalVisualEffect>
		<activateRate>10</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5745" toLevel="12" name="Death Blow">
		<!-- Level 1: Draws on your HP to summon your strength and unleash a punishing attack. Power 183 -->
		<!-- Level 2: Draws on your HP to summon your strength and unleash a punishing attack. Power 419 -->
		<!-- Level 3: Draws on your HP to summon your strength and unleash a punishing attack. Power 875 -->
		<!-- Level 4: Draws on your HP to summon your strength and unleash a punishing attack. Power 1665 -->
		<!-- Level 5: Draws on your HP to summon your strength and unleash a punishing attack. Power 2885 -->
		<!-- Level 6: Draws on your HP to summon your strength and unleash a punishing attack. Power 4544 -->
		<!-- Level 7: Draws on your HP to summon your strength and unleash a punishing attack. Power 6503 -->
		<!-- Level 8: Draws on your HP to summon your strength and unleash a punishing attack. Power 7503 -->
		<!-- Level 9: Draws on your HP to summon your strength and unleash a punishing attack. Power 8447 -->
		<!-- Level 10: Draws on your HP to summon your strength and unleash a punishing attack. Power 9280 -->
		<!-- Level 11: Draws on your HP to summon your strength and unleash a punishing attack. Power 9948 -->
		<!-- Level 12: Draws on your HP to summon your strength and unleash a punishing attack. Power 10466 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-206</value>
			<value level="2">-305</value>
			<value level="3">-428</value>
			<value level="4">-568</value>
			<value level="5">-715</value>
			<value level="6">-849</value>
			<value level="7">-953</value>
			<value level="8">-988</value>
			<value level="9">-1013</value>
			<value level="10">-1033</value>
			<value level="11">-1050</value>
			<value level="12">-1063</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>800</hitTime>
		<hpConsume>
			<value level="1">49</value>
			<value level="2">79</value>
			<value level="3">112</value>
			<value level="4">152</value>
			<value level="5">197</value>
			<value level="6">242</value>
			<value level="7">283</value>
			<value level="8">302</value>
			<value level="9">318</value>
			<value level="10">329</value>
			<value level="11">336</value>
			<value level="12">340</value>
		</hpConsume>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">37</value>
			<value level="2">60</value>
			<value level="3">84</value>
			<value level="4">114</value>
			<value level="5">148</value>
			<value level="6">182</value>
			<value level="7">212</value>
			<value level="8">227</value>
			<value level="9">238</value>
			<value level="10">247</value>
			<value level="11">252</value>
			<value level="12">255</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>20000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">183</value>
					<value level="2">419</value>
					<value level="3">875</value>
					<value level="4">1665</value>
					<value level="5">2885</value>
					<value level="6">4544</value>
					<value level="7">6503</value>
					<value level="8">7503</value>
					<value level="9">8447</value>
					<value level="10">9280</value>
					<value level="11">9948</value>
					<value level="12">10466</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5746" toLevel="12" name="Double Attack">
		<!-- Level 1: Inflicts 2 devastatingly rapid attacks. Power 137. -->
		<!-- Level 2: Inflicts 2 devastatingly rapid attacks. Power 314. -->
		<!-- Level 3: Inflicts 2 devastatingly rapid attacks. Power 656. -->
		<!-- Level 4: Inflicts 2 devastatingly rapid attacks. Power 1249. -->
		<!-- Level 5: Inflicts 2 devastatingly rapid attacks. Power 2164. -->
		<!-- Level 6: Inflicts 2 devastatingly rapid attacks. Power 3408. -->
		<!-- Level 7: Inflicts 2 devastatingly rapid attacks. Power 4878. -->
		<!-- Level 8: Inflicts 2 devastatingly rapid attacks. Power 5627. -->
		<!-- Level 9: Inflicts 2 devastatingly rapid attacks. Power 6335. -->
		<!-- Level 10: Inflicts 2 devastatingly rapid attacks. Power 6960. -->
		<!-- Level 11: Inflicts 2 devastatingly rapid attacks. Power 7461. -->
		<!-- Level 12: Inflicts 2 devastatingly rapid attacks. Power 7850. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>167</coolTime>
		<effectPoint>
			<value level="1">-172</value>
			<value level="2">-254</value>
			<value level="3">-357</value>
			<value level="4">-474</value>
			<value level="5">-596</value>
			<value level="6">-708</value>
			<value level="7">-794</value>
			<value level="8">-823</value>
			<value level="9">-844</value>
			<value level="10">-861</value>
			<value level="11">-875</value>
			<value level="12">-886</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>800</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">25</value>
			<value level="3">35</value>
			<value level="4">48</value>
			<value level="5">62</value>
			<value level="6">76</value>
			<value level="7">89</value>
			<value level="8">95</value>
			<value level="9">100</value>
			<value level="10">103</value>
			<value level="11">105</value>
			<value level="12">107</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>15000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">137</value>
					<value level="2">314</value>
					<value level="3">656</value>
					<value level="4">1249</value>
					<value level="5">2164</value>
					<value level="6">3408</value>
					<value level="7">4878</value>
					<value level="8">5627</value>
					<value level="9">6335</value>
					<value level="10">6960</value>
					<value level="11">7461</value>
					<value level="12">7850</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5747" toLevel="12" name="Spin Attack">
		<!-- Level 1: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 55 -->
		<!-- Level 2: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 126 -->
		<!-- Level 3: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 263 -->
		<!-- Level 4: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 500 -->
		<!-- Level 5: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 866 -->
		<!-- Level 6: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 1364 -->
		<!-- Level 7: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 1951 -->
		<!-- Level 8: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 2251 -->
		<!-- Level 9: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 2534 -->
		<!-- Level 10: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 2784 -->
		<!-- Level 11: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 2985 -->
		<!-- Level 12: Deals damage and shock simultaneously. Momentarily inflicts stun on the enemy. Power 3140 -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>9</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
			<value level="1">-172</value>
			<value level="2">-179</value>
			<value level="3">-187</value>
			<value level="4">-194</value>
			<value level="5">-202</value>
			<value level="6">-210</value>
			<value level="7">-218</value>
			<value level="8">-227</value>
			<value level="9">-235</value>
			<value level="10">-244</value>
			<value level="11">-253</value>
			<value level="12">-262</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">13</value>
			<value level="2">20</value>
			<value level="3">28</value>
			<value level="4">38</value>
			<value level="5">50</value>
			<value level="6">61</value>
			<value level="7">71</value>
			<value level="8">76</value>
			<value level="9">80</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>13000</reuseDelay>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">55</value>
					<value level="2">126</value>
					<value level="3">263</value>
					<value level="4">500</value>
					<value level="5">866</value>
					<value level="6">1364</value>
					<value level="7">1951</value>
					<value level="8">2251</value>
					<value level="9">2534</value>
					<value level="10">2784</value>
					<value level="11">2985</value>
					<value level="12">3140</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="5748" toLevel="12" name="Meteor Shower">
		<!-- Level 1: Drops meteors to attack surrounding enemies. Power 23 -->
		<!-- Level 2: Drops meteors to attack surrounding enemies. Power 53 -->
		<!-- Level 3: Drops meteors to attack surrounding enemies. Power 110 -->
		<!-- Level 4: Drops meteors to attack surrounding enemies. Power 209 -->
		<!-- Level 5: Drops meteors to attack surrounding enemies. Power 361 -->
		<!-- Level 6: Drops meteors to attack surrounding enemies. Power 568 -->
		<!-- Level 7: Drops meteors to attack surrounding enemies. Power 813 -->
		<!-- Level 8: Drops meteors to attack surrounding enemies. Power 938 -->
		<!-- Level 9: Drops meteors to attack surrounding enemies. Power 1056 -->
		<!-- Level 10: Drops meteors to attack surrounding enemies. Power 1160 -->
		<!-- Level 11: Drops meteors to attack surrounding enemies. Power 1244 -->
		<!-- Level 12: Drops meteors to attack surrounding enemies. Power 1309 -->
		<icon>icon.skill1467</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>400</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>720</coolTime>
		<effectPoint>
			<value level="1">-138</value>
			<value level="2">-204</value>
			<value level="3">-285</value>
			<value level="4">-379</value>
			<value level="5">-477</value>
			<value level="6">-566</value>
			<value level="7">-635</value>
			<value level="8">-659</value>
			<value level="9">-676</value>
			<value level="10">-689</value>
			<value level="11">-700</value>
			<value level="12">-709</value>
		</effectPoint>
		<hitTime>1080</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">13</value>
			<value level="2">20</value>
			<value level="3">28</value>
			<value level="4">38</value>
			<value level="5">50</value>
			<value level="6">61</value>
			<value level="7">71</value>
			<value level="8">76</value>
			<value level="9">80</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>20000</reuseDelay>
	</skill>
	<skill id="5749" toLevel="12" name="Thunder Bolt">
		<!-- Level 1: Pours the power of thunder upon the enemies at the front. Power 18 -->
		<!-- Level 2: Pours the power of thunder upon the enemies at the front. Power 26 -->
		<!-- Level 3: Pours the power of thunder upon the enemies at the front. Power 38 -->
		<!-- Level 4: Pours the power of thunder upon the enemies at the front. Power 52 -->
		<!-- Level 5: Pours the power of thunder upon the enemies at the front. Power 68 -->
		<!-- Level 6: Pours the power of thunder upon the enemies at the front. Power 85 -->
		<!-- Level 7: Pours the power of thunder upon the enemies at the front. Power 102 -->
		<!-- Level 8: Pours the power of thunder upon the enemies at the front. Power 110 -->
		<!-- Level 9: Pours the power of thunder upon the enemies at the front. Power 116 -->
		<!-- Level 10: Pours the power of thunder upon the enemies at the front. Power 122 -->
		<!-- Level 11: Pours the power of thunder upon the enemies at the front. Power 126 -->
		<!-- Level 12: Pours the power of thunder upon the enemies at the front. Power 129 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>
			<value level="1">-138</value>
			<value level="2">-204</value>
			<value level="3">-285</value>
			<value level="4">-379</value>
			<value level="5">-477</value>
			<value level="6">-566</value>
			<value level="7">-635</value>
			<value level="8">-659</value>
			<value level="9">-676</value>
			<value level="10">-689</value>
			<value level="11">-700</value>
			<value level="12">-709</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">16</value>
			<value level="3">21</value>
			<value level="4">28</value>
			<value level="5">36</value>
			<value level="6">44</value>
			<value level="7">52</value>
			<value level="8">55</value>
			<value level="9">58</value>
			<value level="10">60</value>
			<value level="11">61</value>
			<value level="12">62</value>
		</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<pveEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">18</value>
					<value level="2">26</value>
					<value level="3">38</value>
					<value level="4">52</value>
					<value level="5">68</value>
					<value level="6">85</value>
					<value level="7">102</value>
					<value level="8">110</value>
					<value level="9">116</value>
					<value level="10">122</value>
					<value level="11">126</value>
					<value level="12">129</value>
				</power>
			</effect>
		</pveEffects>
		<pvpEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">13</value>
					<value level="2">20</value>
					<value level="3">28</value>
					<value level="4">39</value>
					<value level="5">51</value>
					<value level="6">64</value>
					<value level="7">77</value>
					<value level="8">82</value>
					<value level="9">87</value>
					<value level="10">91</value>
					<value level="11">95</value>
					<value level="12">97</value>
				</power>
			</effect>
		</pvpEffects>
	</skill>
	<skill id="5750" toLevel="12" name="Blaze">
		<!-- Level 1: Inflicts a swift magic attack upon enemies nearby. Power 11 -->
		<!-- Level 2: Inflicts a swift magic attack upon enemies nearby. Power 16 -->
		<!-- Level 3: Inflicts a swift magic attack upon enemies nearby. Power 23 -->
		<!-- Level 4: Inflicts a swift magic attack upon enemies nearby. Power 31 -->
		<!-- Level 5: Inflicts a swift magic attack upon enemies nearby. Power 41 -->
		<!-- Level 6: Inflicts a swift magic attack upon enemies nearby. Power 51 -->
		<!-- Level 7: Inflicts a swift magic attack upon enemies nearby. Power 61 -->
		<!-- Level 8: Inflicts a swift magic attack upon enemies nearby. Power 66 -->
		<!-- Level 9: Inflicts a swift magic attack upon enemies nearby. Power 70 -->
		<!-- Level 10: Inflicts a swift magic attack upon enemies nearby. Power 73 -->
		<!-- Level 11: Inflicts a swift magic attack upon enemies nearby. Power 76 -->
		<!-- Level 12: Inflicts a swift magic attack upon enemies nearby. Power 78 -->
		<icon>icon.skill1417</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-138</value>
			<value level="2">-204</value>
			<value level="3">-285</value>
			<value level="4">-379</value>
			<value level="5">-477</value>
			<value level="6">-566</value>
			<value level="7">-635</value>
			<value level="8">-659</value>
			<value level="9">-676</value>
			<value level="10">-689</value>
			<value level="11">-700</value>
			<value level="12">-709</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">16</value>
			<value level="3">21</value>
			<value level="4">28</value>
			<value level="5">36</value>
			<value level="6">44</value>
			<value level="7">52</value>
			<value level="8">55</value>
			<value level="9">58</value>
			<value level="10">60</value>
			<value level="11">61</value>
			<value level="12">62</value>
		</mpConsume>
		<reuseDelay>2000</reuseDelay>
		<pveEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">11</value>
					<value level="2">16</value>
					<value level="3">23</value>
					<value level="4">31</value>
					<value level="5">41</value>
					<value level="6">51</value>
					<value level="7">61</value>
					<value level="8">66</value>
					<value level="9">70</value>
					<value level="10">73</value>
					<value level="11">76</value>
					<value level="12">78</value>
				</power>
			</effect>
		</pveEffects>
		<pvpEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">8</value>
					<value level="2">12</value>
					<value level="3">17</value>
					<value level="4">24</value>
					<value level="5">31</value>
					<value level="6">39</value>
					<value level="7">46</value>
					<value level="8">50</value>
					<value level="9">53</value>
					<value level="10">55</value>
					<value level="11">57</value>
					<value level="12">58</value>
				</power>
			</effect>
		</pvpEffects>
	</skill>
	<skill id="5751" toLevel="12" name="Lightning Wave">
		<!-- Level 1: Inflicts the power of thunder upon surrounding enemies. Power 9 -->
		<!-- Level 2: Inflicts the power of thunder upon surrounding enemies. Power 13 -->
		<!-- Level 3: Inflicts the power of thunder upon surrounding enemies. Power 19 -->
		<!-- Level 4: Inflicts the power of thunder upon surrounding enemies. Power 26 -->
		<!-- Level 5: Inflicts the power of thunder upon surrounding enemies. Power 34 -->
		<!-- Level 6: Inflicts the power of thunder upon surrounding enemies. Power 43 -->
		<!-- Level 7: Inflicts the power of thunder upon surrounding enemies. Power 51 -->
		<!-- Level 8: Inflicts the power of thunder upon surrounding enemies. Power 55 -->
		<!-- Level 9: Inflicts the power of thunder upon surrounding enemies. Power 58 -->
		<!-- Level 10: Inflicts the power of thunder upon surrounding enemies. Power 61 -->
		<!-- Level 11: Inflicts the power of thunder upon surrounding enemies. Power 63 -->
		<!-- Level 12: Inflicts the power of thunder upon surrounding enemies. Power 65 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>
			<value level="1">-138</value>
			<value level="2">-204</value>
			<value level="3">-285</value>
			<value level="4">-379</value>
			<value level="5">-477</value>
			<value level="6">-566</value>
			<value level="7">-635</value>
			<value level="8">-659</value>
			<value level="9">-676</value>
			<value level="10">-689</value>
			<value level="11">-700</value>
			<value level="12">-709</value>
		</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">16</value>
			<value level="3">21</value>
			<value level="4">28</value>
			<value level="5">36</value>
			<value level="6">44</value>
			<value level="7">52</value>
			<value level="8">55</value>
			<value level="9">58</value>
			<value level="10">60</value>
			<value level="11">61</value>
			<value level="12">62</value>
		</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<pveEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">9</value>
					<value level="2">13</value>
					<value level="3">19</value>
					<value level="4">26</value>
					<value level="5">34</value>
					<value level="6">43</value>
					<value level="7">51</value>
					<value level="8">55</value>
					<value level="9">58</value>
					<value level="10">61</value>
					<value level="11">63</value>
					<value level="12">65</value>
				</power>
			</effect>
		</pveEffects>
		<pvpEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">7</value>
					<value level="2">10</value>
					<value level="3">14</value>
					<value level="4">20</value>
					<value level="5">26</value>
					<value level="6">32</value>
					<value level="7">39</value>
					<value level="8">41</value>
					<value level="9">44</value>
					<value level="10">46</value>
					<value level="11">48</value>
					<value level="12">49</value>
				</power>
			</effect>
		</pvpEffects>
	</skill>
	<skill id="5752" toLevel="12" name="Flare">
		<!-- Level 1: Consumes one's own HP to inflict powerful magic on the enemy. Power 35 -->
		<!-- Level 2: Consumes one's own HP to inflict powerful magic on the enemy. Power 52 -->
		<!-- Level 3: Consumes one's own HP to inflict powerful magic on the enemy. Power 75 -->
		<!-- Level 4: Consumes one's own HP to inflict powerful magic on the enemy. Power 103 -->
		<!-- Level 5: Consumes one's own HP to inflict powerful magic on the enemy. Power 136 -->
		<!-- Level 6: Consumes one's own HP to inflict powerful magic on the enemy. Power 170. -->
		<!-- Level 7: Consumes one's own HP to inflict powerful magic on the enemy. Power 204 -->
		<!-- Level 8: Consumes one's own HP to inflict powerful magic on the enemy. Power 219 -->
		<!-- Level 9: Consumes one's own HP to inflict powerful magic on the enemy. Power 232. -->
		<!-- Level 10: Consumes one's own HP to inflict powerful magic on the enemy. Power 243. -->
		<!-- Level 11: Consumes one's own HP to inflict powerful magic on the enemy. Power 252. -->
		<!-- Level 12: Consumes one's own HP to inflict powerful magic on the enemy. Power 258. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>
			<value level="1">-138</value>
			<value level="2">-204</value>
			<value level="3">-285</value>
			<value level="4">-379</value>
			<value level="5">-477</value>
			<value level="6">-566</value>
			<value level="7">-635</value>
			<value level="8">-659</value>
			<value level="9">-676</value>
			<value level="10">-689</value>
			<value level="11">-700</value>
			<value level="12">-709</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4000</hitTime>
		<hpConsume>
			<value level="1">49</value>
			<value level="2">79</value>
			<value level="3">112</value>
			<value level="4">152</value>
			<value level="5">197</value>
			<value level="6">242</value>
			<value level="7">283</value>
			<value level="8">302</value>
			<value level="9">318</value>
			<value level="10">329</value>
			<value level="11">336</value>
			<value level="12">340</value>
		</hpConsume>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">16</value>
			<value level="3">21</value>
			<value level="4">28</value>
			<value level="5">36</value>
			<value level="6">44</value>
			<value level="7">52</value>
			<value level="8">55</value>
			<value level="9">58</value>
			<value level="10">60</value>
			<value level="11">61</value>
			<value level="12">62</value>
		</mpConsume>
		<reuseDelay>20000</reuseDelay>
		<pveEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">35</value>
					<value level="2">52</value>
					<value level="3">75</value>
					<value level="4">103</value>
					<value level="5">136</value>
					<value level="6">170</value>
					<value level="7">204</value>
					<value level="8">219</value>
					<value level="9">232</value>
					<value level="10">243</value>
					<value level="11">252</value>
					<value level="12">258</value>
				</power>
			</effect>
		</pveEffects>
		<pvpEffects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">39</value>
					<value level="2">58</value>
					<value level="3">84</value>
					<value level="4">116</value>
					<value level="5">153</value>
					<value level="6">191</value>
					<value level="7">229</value>
					<value level="8">246</value>
					<value level="9">261</value>
					<value level="10">273</value>
					<value level="11">283</value>
					<value level="12">290</value>
				</power>
			</effect>
		</pvpEffects>
	</skill>
	<skill id="5753" toLevel="1" name="Awakening">
		<icon>icon.action117</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PET_FURY</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<mpConsume>52</mpConsume>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5754" toLevel="1" name="Presentation - Adiantum Round Fighter">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>400</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="5755" toLevel="1" name="Presentation - Trap On">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>400</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>-100</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="5756" toLevel="1" name="Presentation - Summon Follower">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>-100</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="5757" toLevel="1" name="Presentation - Energy Ditch">
		<icon>icon.skill0000</icon>
		<operateType>CA5</operateType>
		<targetType>OTHERS</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<channelingSkillId>5713</channelingSkillId> <!-- Energy Ditch -->
		<channelingStart>3.6</channelingStart>
		<channelingTickInterval>2</channelingTickInterval>
		<effectPoint>1</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>15000</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<mpPerChanneling>15</mpPerChanneling>
		<reuseDelay>30000</reuseDelay>
	</skill>
	<skill id="5758" toLevel="1" name="Presentation - Large Firecracker">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="5759" toLevel="1" name="Presentation - The Rise of Latana">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>9700</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>73</magicLevel>
	</skill>
	<skill id="5760" toLevel="9" name="Critical Hit">
		<!-- P. Critical Rate +30%, Atk. Spd. +33%. Successful physical critical attacks allow to gather energy for powerful distance attacks. -->
		<icon>icon.skill5760</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">2</value>
			<value level="2">3</value>
			<value level="3">4</value>
			<value level="4">5</value>
			<value level="5">6</value>
			<value level="6">7</value>
			<value level="7">8</value>
			<value level="8">9</value>
			<value level="9">10</value>
		</abnormalLevel>
		<abnormalTime>3600</abnormalTime>
		<abnormalType>SEED_OF_CRITICAL</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>4</isMagic>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5761" toLevel="2" name="Power Strike">
		<!-- Strong pounding attack. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>-574</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>61</magicLevel>
		<mpConsume>
			<value level="1">91</value>
			<value level="2">122</value>
		</mpConsume>
		<reuseDelay>20000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">2500</value>
					<value level="2">2800</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="5762" toLevel="1" name="Blessing of Pathfinder">
		<!-- The Pathfinder's blessing. All battle abilities are increased. -->
		<icon>icon.skill5762</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>7200</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>900</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1400</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5763" toLevel="1" name="Wink">
		<!-- See Agathion's cute trick. -->
		<icon>icon.skill_agathion_cute</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<conditions>
			<condition name="OpNeedAgathion" />
		</conditions>
	</skill>
	<skill id="5764" toLevel="1" name="Terror of the Pumpkin Ghost">
		<!-- Curse of the Pumpkin Ghost. Causes spontaneous movement. -->
		<icon>icon.skill1092</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>TURN_FLEE</abnormalType>
		<abnormalVisualEffect>TURN_FLEE</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<coolTime>1000</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2060</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>95</magicLevel>
		<mpConsume>43</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<trait>DERANGEMENT</trait>
	</skill>
	<skill id="5765" toLevel="1" name="Naia Sprout">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<coolTime>800</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>75</magicLevel>
		<mpConsume>76</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>4472</power>
			</effect>
		</effects>
	</skill>
	<skill id="5766" toLevel="1" name="Naia Sprout">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>WATER</attributeType>
		<attributeValue>20</attributeValue>
		<coolTime>800</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>75</magicLevel>
		<mpConsume>76</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>4472</power>
			</effect>
		</effects>
	</skill>
	<skill id="5767" toLevel="1" name="Naia Sprout">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>WIND</attributeType>
		<attributeValue>20</attributeValue>
		<coolTime>800</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>75</magicLevel>
		<mpConsume>76</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>4472</power>
			</effect>
		</effects>
	</skill>
	<skill id="5768" toLevel="1" name="Naia Sprout">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>20</attributeValue>
		<coolTime>800</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>78</magicLevel>
		<mpConsume>78</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>4472</power>
			</effect>
		</effects>
	</skill>
	<skill id="5769" toLevel="12" name="not_used">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>400</castRange>
		<effectPoint>418</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>39</mpConsume>
		<reuseDelay>6000</reuseDelay>
	</skill>
	<skill id="5770" toLevel="12" name="not_used">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>400</castRange>
		<effectPoint>418</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>39</mpConsume>
		<reuseDelay>6000</reuseDelay>
	</skill>
	<skill id="5771" toLevel="1" name="Buff Control">
		<!-- Limits to prevent a buff upon the master. -->
		<icon>icon.action113</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>300</abnormalTime>
		<abnormalType>MAGICAL_STANCE</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5772" toLevel="1" name="Surrender to Fire">
		<!-- Fire Resistance decreases and Water Resistance increases momentarily. -->
		<icon>icon.skill4279_new</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>ARMOR_FIRE</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitCancelTime>1.5</hitCancelTime>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>-1</magicLevel>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5773" toLevel="1" name="Surrender to Water">
		<!-- Fire Resistance decreases and Water Resistance increases momentarily. -->
		<icon>icon.skill4280_new</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>ARMOR_WATER</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitCancelTime>1.5</hitCancelTime>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>-1</magicLevel>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5775" toLevel="1" name="Greater Blessing of Pathfinder">
		<!-- The Pathfinder's blessing. All battle abilities are increased. -->
		<icon>icon.skill5762</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>7200</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>900</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1400</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>99</magicLevel>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5776" toLevel="1" name="Raid Boss">
		<!-- Lost Warden: Kamaloka - Leader of those who watch over the Labyrinth of the Abyss. He watches over those inside Kamaloka to make sure they properly perform their roles. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="5777" toLevel="1" name="Raid Boss">
		<!-- Lost Captain: Kamaloka - Leader of those who guard the Labyrinth of the Abyss. They protect Kamaloka from all enemies to try to infiltrate Kamaloka. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="5778" toLevel="1" name="Invincible After-effect">
		<!-- Temporarily unable to receive invincible effect. -->
		<icon>icon.skill0837</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>90</abnormalTime>
		<abnormalType>INVINCIBILITY</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>1</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>-1</magicLevel>
		<reuseDelay>1000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="5779" toLevel="42" name="Transform Effect">
		<!-- Transforms both your physical form and abilities. -->
		<icon>icon.skilltransform3</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">7</value>
			<value level="2">14</value>
			<value level="3">20</value>
			<value level="4">23</value>
			<value level="5">25</value>
			<value level="6">28</value>
			<value level="7">30</value>
			<value level="8">33</value>
			<value level="9">35</value>
			<value level="10">38</value>
			<value level="11">39</value>
			<value level="12">40</value>
			<value level="13">42</value>
			<value level="14">43</value>
			<value level="15">44</value>
			<value level="16">46</value>
			<value level="17">47</value>
			<value level="18">48</value>
			<value level="19">50</value>
			<value level="20">51</value>
			<value level="21">52</value>
			<value level="22">54</value>
			<value level="23">55</value>
			<value level="24">56</value>
			<value level="25">57</value>
			<value level="26">58</value>
			<value level="27">59</value>
			<value level="28">60</value>
			<value level="29">61</value>
			<value level="30">62</value>
			<value level="31">63</value>
			<value level="32">64</value>
			<value level="33">65</value>
			<value level="34">66</value>
			<value level="35">67</value>
			<value level="36">68</value>
			<value level="37">69</value>
			<value level="38">70</value>
			<value level="39">71</value>
			<value level="40">72</value>
			<value level="41">73</value>
			<value level="42">74</value>
		</magicLevel>
		<passiveConditions>
			<condition name="OpCheckClassList">
				<classIds>
					<item>FIGHTER</item>
					<item>WARRIOR</item>
					<item>GLADIATOR</item>
					<item>DUELIST</item>
					<item>WARLORD</item>
					<item>DREADNOUGHT</item>
					<item>KNIGHT</item>
					<item>PALADIN</item>
					<item>PHOENIX_KNIGHT</item>
					<item>DARK_AVENGER</item>
					<item>HELL_KNIGHT</item>
					<item>ROGUE</item>
					<item>TREASURE_HUNTER</item>
					<item>SAGITTARIUS</item>
					<item>HAWKEYE</item>
					<item>ADVENTURER</item>
					<item>ELVEN_FIGHTER</item>
					<item>ELVEN_KNIGHT</item>
					<item>TEMPLE_KNIGHT</item>
					<item>EVA_TEMPLAR</item>
					<item>SWORDSINGER</item>
					<item>SWORD_MUSE</item>
					<item>ELVEN_SCOUT</item>
					<item>PLAINS_WALKER</item>
					<item>WIND_RIDER</item>
					<item>SILVER_RANGER</item>
					<item>MOONLIGHT_SENTINEL</item>
					<item>DARK_FIGHTER</item>
					<item>PALUS_KNIGHT</item>
					<item>SHILLIEN_KNIGHT</item>
					<item>SHILLIEN_TEMPLAR</item>
					<item>BLADEDANCER</item>
					<item>SPECTRAL_DANCER</item>
					<item>ASSASSIN</item>
					<item>ABYSS_WALKER</item>
					<item>GHOST_HUNTER</item>
					<item>PHANTOM_RANGER</item>
					<item>GHOST_SENTINEL</item>
					<item>ORC_FIGHTER</item>
					<item>ORC_RAIDER</item>
					<item>DESTROYER</item>
					<item>TITAN</item>
					<item>ORC_MONK</item>
					<item>TYRANT</item>
					<item>GRAND_KHAVATARI</item>
					<item>DWARVEN_FIGHTER</item>
					<item>SCAVENGER</item>
					<item>BOUNTY_HUNTER</item>
					<item>FORTUNE_SEEKER</item>
					<item>ARTISAN</item>
					<item>WARSMITH</item>
					<item>MAESTRO</item>
					<item>MALE_SOLDIER</item>
					<item>TROOPER</item>
					<item>BERSERKER</item>
					<item>DOOMBRINGER</item>
					<item>MALE_SOULBREAKER</item>
					<item>MALE_SOUL_HOUND</item>
					<item>FEMALE_SOLDIER</item>
					<item>WARDER</item>
					<item>FEMALE_SOULBREAKER</item>
					<item>FEMALE_SOUL_HOUND</item>
					<item>ARBALESTER</item>
					<item>TRICKSTER</item>
					<item>INSPECTOR</item>
					<item>JUDICATOR</item>
				</classIds>
				<affectType>CASTER</affectType>
				<isWithin>true</isWithin>
			</condition>
		</passiveConditions>
	</skill>
	<skill id="5781" toLevel="1" name="Presentation - Lindvior Approach">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>17000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5782" toLevel="1" name="Presentation - Lindvior Gust">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>10000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5783" toLevel="1" name="Presentation - Lindvior Descent">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>15000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5784" toLevel="1" name="Presentation - Lindvior Dust">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>5000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5785" toLevel="1" name="Presentation - Dimensional Door">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5786" toLevel="1" name="Presentation - Ekimus Separation">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>22000</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5787" toLevel="1" name="Presentation - Ekimus Approach">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>11000</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5788" toLevel="1" name="Presentation - Ekimus Blood Hail">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>2100</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5789" toLevel="1" name="Presentation - Ekimus Sneer">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>13000</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5790" toLevel="1" name="Presentation - Ekimus Aftereffect">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5791" toLevel="1" name="Presentation - Magyun's Entrance">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>4200</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5792" toLevel="1" name="Presentation - Summon Banshee Queen Boomeye">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5793" toLevel="1" name="Presentation - Summon Boomeye">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="5794" toLevel="1" name="Decrease Speed">
		<!-- Immediately target's Speed -30%. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>SPEED_DOWN</abnormalType>
		<activateRate>80</activateRate>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-684</effectPoint>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;5</fanRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>83</magicLevel>
		<mpConsume>74</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="5795" toLevel="1" name="Poison">
		<!-- Poisons the target, causing them to lose 66 HP every sec. for 30 sec. -->
		<icon>icon.skill4035</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<activateRate>70</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>600</castRange>
		<effectPoint>-684</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>83</magicLevel>
		<mpConsume>74</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<trait>POISON</trait>
	</skill>
	<skill id="5796" toLevel="1" name="Decrease Speed">
		<!-- Immediately target's Speed -30%. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>SPEED_DOWN</abnormalType>
		<activateRate>80</activateRate>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<attributeType>WIND</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-684</effectPoint>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;30</fanRange>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>83</magicLevel>
		<mpConsume>74</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="5797" toLevel="1" name="Bursting Flame">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>600</castRange>
		<effectPoint>-676</effectPoint>
		<effectRange>1100</effectRange>
		<fanRange>0;0;600;60</fanRange>
		<hitTime>3500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>109</mpConsume>
		<reuseDelay>4000</reuseDelay>
	</skill>
	<skill id="5798" toLevel="1" name="Fireball">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>900</castRange>
		<effectPoint>-676</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<reuseDelay>4000</reuseDelay>
	</skill>
	<skill id="5799" toLevel="1" name="Bleed">
		<!-- Causes the target to bleed and lose 78 HP every sec. for 20 sec. -->
		<icon>icon.skill0096</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>20</abnormalTime>
		<abnormalType>BLEEDING</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>90</activateRate>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>600</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>50</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<effectPoint>-689</effectPoint>
		<hitTime>1800</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>165</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<subordinationAbnormalType>BLEEDING</subordinationAbnormalType>
		<trait>BLEED</trait>
	</skill>
</list>
