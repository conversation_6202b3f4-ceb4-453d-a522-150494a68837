<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="50200" toLevel="10" name="Onyx" nameRu="Оникс">
		<!-- Beast Soulshot Damage +$s1 -->
		<icon>icon.etc_bm_jewel_onyx_i00</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="50201" toLevel="10" name="Onyx" nameRu="Оникс">
		<!-- Spiritshot Damage +$s1 -->
		<icon>icon.etc_bm_jewel_onyx_i00</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="50202" toLevel="10" name="Onyx" nameRu="Оникс">
		<!-- Blessed Spiritshot Damage +$s1 -->
		<icon>icon.etc_bm_jewel_onyx_i00</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="50203" toLevel="10" name="Onyx" nameRu="Оникс">
		<!-- Beast Spiritshot Damage +$s1 -->
		<icon>icon.etc_bm_jewel_onyx_i00</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="50204" toLevel="10" name="Onyx" nameRu="Оникс">
		<!-- Soulshot damage +$s1\nSpiritshot damage +$s1 -->
		<icon>icon.etc_bm_jewel_onyx_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="SoulshotDamage">
				<amount>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">5</value>
					<value level="4">8</value>
					<value level="5">12</value>
					<value level="6">16</value>
					<value level="7">20</value>
					<value level="8">25</value>
					<value level="9">20</value>
					<value level="10">21</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="SpiritshotDamage">
				<amount>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">5</value>
					<value level="4">8</value>
					<value level="5">12</value>
					<value level="6">16</value>
					<value level="7">20</value>
					<value level="8">25</value>
					<value level="9">20</value>
					<value level="10">21</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk" fromLevel="6" toLevel="10">
				<amount>
					<value level="6">50</value>
					<value level="7">150</value>
					<value level="8">300</value>
					<value level="9">400</value>
					<value level="10">500</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk" fromLevel="6" toLevel="10">
				<amount>
					<value level="6">50</value>
					<value level="7">150</value>
					<value level="8">300</value>
					<value level="9">500</value>
					<value level="10">600</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="CriticalRate" fromLevel="10" toLevel="10">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalRate" fromLevel="10" toLevel="10">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50205" toLevel="10" name="Spinel - Mana Steal" nameRu="Шпинель - Воровство Маны">
		<!--  -->
		<icon>icon.etc_bm_jewel_garnet_i00</icon>
		<effectPoint>0</effectPoint>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="8">8</value> <!-- FIXME: Auto generated with parser -->
			<value level="9">8</value> <!-- FIXME: Auto generated with parser -->
			<value level="10">8</value> <!-- FIXME: Auto generated with parser -->
		</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Mp">
				<amount>
					<value level="1">12</value>
					<value level="2">16</value>
					<value level="3">20</value>
					<value level="4">30</value>
					<value level="5">40</value>
					<value level="6">50</value>
					<value level="7">60</value>
					<value level="8">70</value>
					<value level="9">70</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">70</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50206" toLevel="10" name="Spinel" nameRu="Шпинель">
		<!-- P. Def. +$s1\nM. Def. +$s2\nCON +$s3\nWith a certain chance, recovers $s4 MP when using skills. -->
		<icon>icon.etc_bm_jewel_garnet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">20</value>
					<value level="2">30</value>
					<value level="3">60</value>
					<value level="4">100</value>
					<value level="5">150</value>
					<value level="6">200</value>
					<value level="7">250</value>
					<value level="8">350</value>
					<value level="9">350</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">350</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
					<value level="4">60</value>
					<value level="5">100</value>
					<value level="6">150</value>
					<value level="7">220</value>
					<value level="8">300</value>
					<value level="9">300</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">300</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">3</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">3</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">3</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">3</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">3</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">3</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>
					<value level="4">1</value>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">3</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">3</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">3</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">3</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">3</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">3</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceCriticalDamage">
				<amount>
					<value level="4">-4</value>
					<value level="5">-7</value>
					<value level="6">-10</value>
					<value level="7">-15</value>
					<value level="8">-20</value>
					<value level="9">-20</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">-20</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceSkillCriticalDamage">
				<amount>
					<value level="4">-4</value>
					<value level="5">-7</value>
					<value level="6">-10</value>
					<value level="7">-15</value>
					<value level="8">-20</value>
					<value level="9">-20</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">-20</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceMagicCriticalDamage">
				<amount>
					<value level="4">-4</value>
					<value level="5">-7</value>
					<value level="6">-10</value>
					<value level="7">-15</value>
					<value level="8">-20</value>
					<value level="9">-20</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">-20</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="TriggerSkillByAttack" fromLevel="1" toLevel="20">
				<attackerType>Creature</attackerType>
				
				
				<minDamage>0</minDamage>
				<chance>3</chance>
				<targetType>SELF</targetType>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<isCritical>false</isCritical>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50205</skillId>
				<skillLevel>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
					<value level="4">4</value>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">8</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">8</value> <!-- FIXME: Auto generated with parser -->
				</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50207" toLevel="10" name="Zircon" nameRu="Циркон">
		<!-- P. Critical Damage +$s1\nM. Skill Critical Damage +$s1 -->
		<icon>icon.etc_bm_jewel_bloodstone_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="CriticalDamage">
				<amount>
					<value level="1">2</value>
					<value level="2">4</value>
					<value level="3">6</value>
					<value level="4">8</value>
					<value level="5">11</value>
					<value level="6">15</value>
					<value level="7">15</value>
					<value level="8">15</value>
					<value level="8">15</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">15</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">15</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalDamage">
				<amount>
					<value level="1">2</value>
					<value level="2">4</value>
					<value level="3">6</value>
					<value level="4">8</value>
					<value level="5">11</value>
					<value level="6">15</value>
					<value level="7">19</value>
					<value level="8">23</value>
					<value level="8">23</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">23</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">23</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="SkillCriticalDamage">
				<amount>
					<value level="6">2</value>
					<value level="7">5</value>
					<value level="8">9</value>
					<value level="8">9</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">9</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">9</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<!-- <effect name="SkillPowerAdd">
				<amount>
					<value level="7">2</value>
					<value level="8">5</value>
					<value level="8">5</value> FIXME: Auto generated with parser
					<value level="9">5</value> FIXME: Auto generated with parser
					<value level="10">5</value> FIXME: Auto generated with parser
				</amount>
				<mode>DIFF</mode>
			</effect> -->
			<effect name="PhysicalSkillPower">
				<amount>
					<value level="7">2</value>
					<value level="8">5</value>
					<value level="8">5</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">5</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">5</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalSkillPower">
				<amount>
					<value level="7">2</value>
					<value level="8">5</value>
					<value level="8">5</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">5</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">5</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50208" toLevel="10" name="Moonstone" nameRu="Лунный Камень">
		<!-- When Sayha's Grace is used, acquired XP +$s1\nWhen Sayha's Grace is used, acquired SP +$s1 -->
		<icon>icon.etc_bm_jewel_moonstone_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="SayhaGraceExpRate">
				<amount>
					<value level="1">3</value>
					<value level="2">4</value>
					<value level="3">6</value>
					<value level="4">8</value>
					<value level="5">11</value>
					<value level="6">15</value>
					<value level="7">20</value>
					<value level="8">25</value>
					<value level="8">25</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">25</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">25</value> <!-- FIXME: Auto generated with parser -->
				</amount>
			</effect>
			<effect name="SayhaGracePointsRate" fromLevel="4" toLevel="8">
				<amount>
					<value level="4">-10</value>
					<value level="5">-20</value>
					<value level="6">-30</value>
					<value level="7">-30</value>
					<value level="8">-30</value>
					<value level="8">-30</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">-30</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">-30</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicLampExpModify" fromLevel="6" toLevel="8">
				<amount>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">5</value>
					<value level="8">5</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">5</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">5</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50209" toLevel="1" name="A-grade: Dark Crystal Helmet (Heavy)" nameRu="Снаряжение Ранга A: Шлем Кристалла Тьмы - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +10\nP. Atk. +4%%\nHP Recovery Rate +5.5%%\nMax HP +100 -->
		<icon>icon.armor_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>4</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>5.5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxHp">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50210" toLevel="1" name="A-grade: Dark Crystal Helmet (Robe)" nameRu="Снаряжение Ранга A: Шлем Кристалла Тьмы - Магический Доспех">
		<!-- <A-grade equipment effect>\nMEN +1\nOverweight limit +1500\nM. Def. +10\nM. Atk. +8%%\nMP Recovery Rate +5.5%%\nP. Def. +8%% -->
		<icon>icon.armor_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>5.5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50211" toLevel="1" name="A-grade: Dark Crystal Helmet (Light)" nameRu="Снаряжение Ранга A: Шлем Кристалла Тьмы - Легкий Доспех">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +10\n P. Atk. +4%%\nMP Recovery Rate +5.5%%\nP. Critical Damage +30 -->
		<icon>icon.armor_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>4</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>5.5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50212" toLevel="1" name="A-grade: Majestic Circlet (Heavy)" nameRu="Снаряжение Ранга A: Диадема Величия - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +10\nP. Atk. +4%%\nHP Recovery Rate +8%% -->
		<icon>icon.armor_leather_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>4</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50213" toLevel="1" name="A-grade: Majestic Circlet (Robe)" nameRu="Снаряжение Ранга A: Диадема Величия - Магический Доспех">
		<!-- <A-grade equipment effect>\nMEN +1\nOverweight limit +1500\nM. Def. +10\nM. Atk. +8%%\nMP Recovery Rate +8%%\nMax MP +300 -->
		<icon>icon.armor_leather_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>300</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50214" toLevel="1" name="A-grade: Majestic Circlet (Light)" nameRu="Снаряжение Ранга A: Диадема Величия - Легкий Доспех">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +10\nP. Atk. +4%%\nMP Recovery Rate +5.5%%\nP. Atk. +8%% when using a bow -->
		<icon>icon.armor_leather_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>4</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>5.5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>8</amount>
				<mode>PER</mode>
				<weaponType>
					<item>BOW</item>
					<item>CROSSBOW</item>
					<item>PISTOLS</item>
				</weaponType>
			</effect>
		</effects>
	</skill>
	<skill id="50215" toLevel="1" name="A-grade: Helm of Nightmare (Heavy)" nameRu="Снаряжение Ранга A: Шлем Кошмаров - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +10\nP. Atk. +4%%\nHP Recovery Rate +8%%\nReflects 5%% of received damage -->
		<icon>icon.armor_leather_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>4</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DamageShield">
				<amount>5</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50216" toLevel="1" name="A-grade: Helm of Nightmare (Robe)" nameRu="Снаряжение Ранга A: Шлем Кошмаров - Магический Доспех">
		<!-- <A-grade equipment effect>\nMEN +1\nOverweight limit +1500\nM. Def. +10\nM. Atk. +8%%\nMP Recovery Rate +8%%\nM. Skill Critical Damage +30 -->
		<icon>icon.armor_leather_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalDamage">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50217" toLevel="1" name="A-grade: Helm of Nightmare (Light)" nameRu="Снаряжение Ранга A: Шлем Кошмаров - Легкий Доспех">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +10\nP. Atk. +4%%\nMP Recovery Rate +8%% -->
		<icon>icon.armor_leather_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>4</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50218" toLevel="1" name="A-grade: Tallum Helmet (Heavy)" nameRu="Снаряжение Ранга A: Шлем Таллума - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +10\nP. Atk. +4%%\nHP Recovery Rate +5.5%%\nMax MP +50 -->
		<icon>icon.armor_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>4</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>5.5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50219" toLevel="1" name="A-grade: Tallum Helmet (Robe)" nameRu="Снаряжение Ранга A: Шлем Таллума - Магический Доспех">
		<!-- <A-grade equipment effect>\nMEN +1\nOverweight limit +1500\nM. Def. +10\nM. Atk. +8%%\nMP Recovery Rate +5.5%%\nM. Def. +8%% -->
		<icon>icon.armor_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>5.5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50220" toLevel="1" name="A-grade: Tallum Helmet (Light)" nameRu="Снаряжение Ранга A: Шлем Таллума - Легкий Доспех">
		<!-- <A-grade equipment effect>\nMEN +1\nOverweight limit +1500\nM. Def. +10\nM. Atk. +8%%\nMP Recovery Rate +8%% -->
		<icon>icon.armor_helmet_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50221" toLevel="1" name="A-grade: Dark Crystal Breastplate" nameRu="Снаряжение Ранга A: Кираса Кристалла Тьмы">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1500\nM. Def. +15\nParalysis Resistance +50%% -->
		<icon>icon.armor_t74_u_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>15</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<PARALYZE>50</PARALYZE>
			</effect>
		</effects>
	</skill>
	<skill id="50222" toLevel="1" name="A-grade: Dark Crystal Leather Armor" nameRu="Снаряжение Ранга A: Кожаный Доспех Кристалла Тьмы">
		<!-- <A-grade equipment effect>\nDEX +1\nOverweight limit +1500\nM. Def. +15\nParalysis Resistance +50%% -->
		<icon>icon.armor_t75_u_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>15</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<PARALYZE>50</PARALYZE>
			</effect>
		</effects>
	</skill>
	<skill id="50223" toLevel="1" name="A-grade: Tallum Tunic" nameRu="Снаряжение Ранга A: Туника Таллума">
		<!-- <A-grade equipment effect>\nWIT +1\nOverweight limit +1500\nM. Def. +15\nSilence Resistance +50%%\nFear Resistance +50%% -->
		<icon>icon.armor_t79_u_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>15</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<DERANGEMENT>50</DERANGEMENT>
			</effect>
		</effects>
	</skill>
	<skill id="50224" toLevel="1" name="A-grade: Dark Crystal Gaiters" nameRu="Снаряжение Ранга A: Набедренники Кристалла Тьмы">
		<!-- <A-grade equipment effect>\nCON +1\nOverweight limit +1000\nM. Def. +10\nMax HP +200 -->
		<icon>icon.armor_t74_l_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50225" toLevel="1" name="A-grade: Dark Crystal Leggings" nameRu="Снаряжение Ранга A: Поножи Кристалла Тьмы">
		<!-- <A-grade equipment effect>\nDEX +1\nOverweight limit +1000\nM. Def. +10\nMax HP +150\nMax MP +80 -->
		<icon>icon.armor_t75_l_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>150</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>80</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50226" toLevel="1" name="A-grade: Tallum Stockings" nameRu="Снаряжение Ранга A: Штаны Таллума">
		<!-- <A-grade equipment effect>\nWIT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +100\nMax MP +120 -->
		<icon>icon.armor_t79_l_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>120</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50227" toLevel="1" name="A-grade: Dark Crystal Robe" nameRu="Снаряжение Ранга A: Мантия Кристалла Тьмы">
		<!-- <A-grade equipment effect>\nWIT +2\nOverweight limit +2500\nM. Def. +25\nMax HP +100\nMax MP +120\nParalysis Resistance +50%% -->
		<icon>icon.armor_t76_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>120</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<PARALYZE>50</PARALYZE>
			</effect>
		</effects>
	</skill>
	<skill id="50228" toLevel="1" name="A-grade: Majestic Plate Armor" nameRu="Снаряжение Ранга A: Латный Доспех Величия">
		<!-- <A-grade equipment effect>\nCON +2\nOverweight limit +2500\nM. Def. +25\nMax HP +200\nStun Resistance +50%% -->
		<icon>icon.armor_t83_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<SHOCK>50</SHOCK>
			</effect>
		</effects>
	</skill>
	<skill id="50229" toLevel="1" name="A-grade: Majestic Robe" nameRu="Снаряжение Ранга A: Мантия Величия">
		<!-- <A-grade equipment effect>\nWIT +2\nOverweight limit +2500\nM. Def. +25\nMax HP +100\nMax MP +120\nStun Resistance +50%% -->
		<icon>icon.armor_t85_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>120</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<SHOCK>50</SHOCK>
			</effect>
		</effects>
	</skill>
	<skill id="50230" toLevel="1" name="A-grade: Majestic Leather Armor" nameRu="Снаряжение Ранга A: Кожаный Доспех Величия">
		<!-- <A-grade equipment effect>\nDEX +2\nOverweight limit +2500\nM. Def. +25\nMax HP +150\nMax MP +80\nStun Resistance +50%% -->
		<icon>icon.armor_t84_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>150</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>80</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<SHOCK>50</SHOCK>
			</effect>
		</effects>
	</skill>
	<skill id="50231" toLevel="1" name="A-grade: Armor of Nightmare" nameRu="Снаряжение Ранга A: Доспех Кошмаров">
		<!-- <A-grade equipment effect>\nCON +2\nOverweight limit +2500\nM. Def. +25\nMax HP +200\nSleep Resistance +70%%\nHold Resistance +70%% -->
		<icon>icon.armor_t80_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<SLEEP>70</SLEEP>
				<HOLD>70</HOLD>
			</effect>
		</effects>
	</skill>
	<skill id="50232" toLevel="1" name="A-grade: Robe of Nightmare" nameRu="Снаряжение Ранга A: Мантия Кошмаров">
		<!-- <A-grade equipment effect>\nWIT +2\nOverweight limit +2500\nM. Def. +25\nMax HP +100\nMax MP +120\nSleep Resistance +70%%\nHold Resistance +70%% -->
		<icon>icon.armor_t82_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>100</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>120</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<SLEEP>70</SLEEP>
				<HOLD>70</HOLD>
			</effect>
		</effects>
	</skill>
	<skill id="50233" toLevel="1" name="A-grade: Leather Armor of Nightmare" nameRu="Снаряжение Ранга A: Кожаный Доспех Кошмаров">
		<!-- <A-grade equipment effect>\nDEX +2\nOverweight limit +2500\nM. Def. +25\nMax HP +150\nMax MP +80\nSleep Resistance +70%%\nHold Resistance +70%% -->
		<icon>icon.armor_t81_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>150</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>80</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<SLEEP>70</SLEEP>
				<HOLD>70</HOLD>
			</effect>
		</effects>
	</skill>
	<skill id="50234" toLevel="1" name="A-grade: Tallum Plate Armor" nameRu="Снаряжение Ранга A: Латный Доспех Таллума">
		<!-- <A-grade equipment effect>\nCON +2\nOverweight limit +2500\nM. Def. +25\nMax HP +200\nSilence Resistance +50%%\nFear Resistance +50%% -->
		<icon>icon.armor_t77_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<DERANGEMENT>50</DERANGEMENT>
			</effect>
		</effects>
	</skill>
	<skill id="50235" toLevel="1" name="A-grade: Tallum Leather Armor" nameRu="Снаряжение Ранга A: Кожаный Доспех Таллума">
		<!-- <A-grade equipment effect>\nMEN +2\nOverweight limit +2500\nM. Def. +25\nMax HP +150\nMax MP +80\nSilence Resistance +50%%\nFear Resistance +50%% -->
		<icon>icon.armor_t78_ul_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>2</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>2500</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>25</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>150</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>80</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceTrait">
				<DERANGEMENT>70</DERANGEMENT>
			</effect>
		</effects>
	</skill>
	<skill id="50236" toLevel="1" name="A-grade: Dark Crystal Gloves (Heavy)" nameRu="Снаряжение Ранга A: Перчатки Кристалла Тьмы - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +200\nAtk. Spd. +8%%\nReceived Healing +2%% -->
		<icon>icon.armor_t74_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HealEffect">
				<amount>2</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50237" toLevel="1" name="A-grade: Dark Crystal Gloves (Robe)" nameRu="Снаряжение Ранга A: Перчатки Кристалла Тьмы - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nCasting Spd. +15%%\nCasting Interruption Rate -25 -->
		<icon>icon.armor_t76_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50238" toLevel="1" name="A-grade: Dark Crystal Gloves (Light)" nameRu="Снаряжение Ранга A: Перчатки Кристалла Тьмы - Легкий Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nAtk. Spd. +8%%\nP. Evasion +2 -->
		<icon>icon.armor_t75_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50239" toLevel="1" name="A-grade: Majestic Gauntlets (Heavy)" nameRu="Снаряжение Ранга A: Рукавицы Величия - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nAtk. Spd. +8%%\nAtk. Spd. +50 -->
		<icon>icon.armor_t83_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50240" toLevel="1" name="A-grade: Majestic Gauntlets (Robe)" nameRu="Снаряжение Ранга A: Рукавицы Величия - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nCasting Spd. +15%%\nCasting Spd. +50 -->
		<icon>icon.armor_t85_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50241" toLevel="1" name="A-grade: Majestic Gauntlets (Light)" nameRu="Снаряжение Ранга A: Рукавицы Величия - Легкий Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nAtk. Spd. +8%%\nP. Skill Critical Damage +50 -->
		<icon>icon.armor_t84_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="SkillCriticalDamage">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50242" toLevel="1" name="A-grade: Gauntlets of Nightmare (Heavy)" nameRu="Снаряжение Ранга A: Рукавицы Кошмаров - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nAtk. Spd. +8%%\nP. Atk. +50 -->
		<icon>icon.armor_t80_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50243" toLevel="1" name="A-grade: Gauntlets of Nightmare (Robe)" nameRu="Снаряжение Ранга A: Рукавицы Кошмаров - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nCasting Spd. +15%%\nM. Atk. +50 -->
		<icon>icon.armor_t82_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50244" toLevel="1" name="A-grade: Gauntlets of Nightmare (Light)" nameRu="Снаряжение Ранга A: Рукавицы Кошмаров - Легкий Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nAtk. Spd. +8%%\nAbsorbs 2%% of inflicted damage as HP -->
		<icon>icon.armor_t81_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="VampiricAttack">
				<amount>2</amount>
				<chance>100</chance>
			</effect>
		</effects>
	</skill>
	<skill id="50245" toLevel="1" name="A-grade: Tallum Gloves (Heavy)" nameRu="Снаряжение Ранга A: Перчатки Таллума - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nAtk. Spd. +8%%\nP. Def. +10 -->
		<icon>icon.armor_t77_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50246" toLevel="1" name="A-grade: Tallum Gloves (Robe)" nameRu="Снаряжение Ранга A: Перчатки Таллума - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +20\nMax HP +50\nCasting Spd. +15%% -->
		<icon>icon.armor_t79_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50247" toLevel="1" name="A-grade: Tallum Gloves (Light)" nameRu="Снаряжение Ранга A: Перчатки Таллума - Легкий Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nMax MP +120\nCasting Spd. +15%% -->
		<icon>icon.armor_t78_g_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>120</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50248" toLevel="1" name="A-grade: Dark Crystal Boots (Heavy)" nameRu="Снаряжение Ранга A: Сапоги Кристалла Тьмы - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +200\nSpeed +4\nReceived Healing +2%% -->
		<icon>icon.armor_t74_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="HealEffect">
				<amount>2</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50249" toLevel="1" name="A-grade: Dark Crystal Boots (Robe)" nameRu="Снаряжение Ранга A: Сапоги Кристалла Тьмы - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +7\nCasting Interruption Rate -25 -->
		<icon>icon.armor_t76_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>7</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50250" toLevel="1" name="A-grade: Dark Crystal Boots (Light)" nameRu="Снаряжение Ранга A: Сапоги Кристалла Тьмы - Легкий Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +4\nP. Evasion +2 -->
		<icon>icon.armor_t75_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50251" toLevel="1" name="A-grade: Majestic Boots (Heavy)" nameRu="Снаряжение Ранга A: Сапоги Величия - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +7\nAtk. Spd. +50 -->
		<icon>icon.armor_t83_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>7</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50252" toLevel="1" name="A-grade: Majestic Boots (Robe)" nameRu="Снаряжение Ранга A: Сапоги Величия - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +4\nCasting Spd. +50 -->
		<icon>icon.armor_t85_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50253" toLevel="1" name="A-grade: Majestic Boots (Light)" nameRu="Снаряжение Ранга A: Сапоги Величия - Легкий Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +4\nP. Skill Critical Damage +50 -->
		<icon>icon.armor_t84_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillCriticalDamage">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50254" toLevel="1" name="A-grade: Boots of Nightmare (Heavy)" nameRu="Снаряжение Ранга A: Сапоги Кошмаров - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +4\nP. Atk. +50 -->
		<icon>icon.armor_t80_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PAtk">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50255" toLevel="1" name="A-grade: Boots of Nightmare (Robe)" nameRu="Снаряжение Ранга A: Сапоги Кошмаров - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +4\nM. Atk. +50 -->
		<icon>icon.armor_t82_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50256" toLevel="1" name="A-grade: Boots of Nightmare (Light)" nameRu="Снаряжение Ранга A: Сапоги Кошмаров - Легкий Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +4\nAbsorbs 2%% of inflicted damage as HP -->
		<icon>icon.armor_t81_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="VampiricAttack">
				<amount>2</amount>
				<chance>100</chance>
			</effect>
		</effects>
	</skill>
	<skill id="50257" toLevel="1" name="A-grade: Tallum Boots (Heavy)" nameRu="Снаряжение Ранга A: Сапоги Таллума - Тяжелый Доспех">
		<!-- <A-grade equipment effect>\nSTR +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nSpeed +4\nP. Def. +10 -->
		<icon>icon.armor_t77_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50258" toLevel="1" name="A-grade: Tallum Boots (Robe)" nameRu="Снаряжение Ранга A: Сапоги Таллума - Магический Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +20\nMax HP +50\nSpeed +4 -->
		<icon>icon.armor_t79_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50259" toLevel="1" name="A-grade: Tallum Boots (Light)" nameRu="Снаряжение Ранга A: Сапоги Таллума - Легкий Доспех">
		<!-- <A-grade equipment effect>\nINT +1\nOverweight limit +1000\nM. Def. +10\nMax HP +50\nMax MP +120\nSpeed +4 -->
		<icon>icon.armor_t78_b_i00</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>1</amount>
			</effect>
			<effect name="WeightLimit">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp">
				<amount>120</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Speed">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50260" toLevel="10" name="Enchant Effect: A-grade Upper/ Lower Armor" nameRu="Эффект Модификации: Верх и Низ Доспехов Ранга A">
		<!--  -->
		<icon>icon.armor_t89_ul_i02</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="HpRegen" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">5</value>
					<value level="6">10</value>
					<value level="7">11</value>
					<value level="8">12</value>
					<value level="9">13</value>
					<value level="10">15</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MpRegen" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">3</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceCriticalDamage" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-2</value>
					<value level="6">-3</value>
					<value level="7">-4</value>
					<value level="8">-5</value>
					<value level="9">-6</value>
					<value level="10">-8</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50261" toLevel="10" name="Enchant Effect: A-grade Upper Armor" nameRu="Эффект Модификации: Верх Доспехов Ранга A">
		<!--  -->
		<icon>icon.armor_t601_u_i01</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="HpRegen" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">5</value>
					<value level="6">10</value>
					<value level="7">11</value>
					<value level="8">12</value>
					<value level="9">13</value>
					<value level="10">15</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceSkillCriticalDamage" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-2</value>
					<value level="6">-3</value>
					<value level="7">-4</value>
					<value level="8">-5</value>
					<value level="9">-6</value>
					<value level="10">-8</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceCriticalDamage" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-2</value>
					<value level="6">-3</value>
					<value level="7">-4</value>
					<value level="8">-5</value>
					<value level="9">-6</value>
					<value level="10">-8</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50262" toLevel="10" name="Enchant Effect: A-grade Lower Armor" nameRu="Эффект Модификации: Низ Доспехов Ранга A">
		<!--  -->
		<icon>icon.armor_t601_l_i01</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="MpRegen" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">3</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceMagicCriticalDamage" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-2</value>
					<value level="6">-3</value>
					<value level="7">-4</value>
					<value level="8">-5</value>
					<value level="9">-6</value>
					<value level="10">-8</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50263" toLevel="10" name="Enchant Effect: A-grade Gloves (Physical)" nameRu="Эффект Модификации: Перчатки Ранга A (Физ.)">
		<!--  -->
		<icon>icon.armor_t601_g_i01</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="PhysicalSkillPower" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">2</value>
					<value level="6">3</value>
					<value level="7">5</value>
					<value level="8">7</value>
					<value level="9">10</value>
					<value level="10">15</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50264" toLevel="10" name="Enchant Effect: A-grade Gloves (Magic)" nameRu="Эффект Модификации: Перчатки Ранга A (Маг.)">
		<!--  -->
		<icon>icon.armor_t601_g_i01</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="MagicalSkillPower" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">2</value>
					<value level="6">3</value>
					<value level="7">5</value>
					<value level="8">7</value>
					<value level="9">10</value>
					<value level="10">15</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50265" toLevel="10" name="Enchant Effect: A-grade Boots (Physical)" nameRu="Эффект Модификации: Обувь Ранга A (Физ.)">
		<!--  -->
		<icon>icon.armor_t601_b_i01</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="Reuse" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-1</value>
					<value level="6">-2</value>
					<value level="7">-3</value>
					<value level="8">-5</value>
					<value level="9">-7</value>
					<value level="10">-10</value>
				</amount>
				<magicType>0</magicType>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50266" toLevel="10" name="Enchant Effect: A-grade Boots (Magic)" nameRu="Эффект Модификации: Обувь Ранга A (Маг.)">
		<!--  -->
		<icon>icon.armor_t601_b_i01</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="Reuse" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-1</value>
					<value level="6">-2</value>
					<value level="7">-3</value>
					<value level="8">-5</value>
					<value level="9">-7</value>
					<value level="10">-10</value>
				</amount>
				<magicType>1</magicType>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50267" toLevel="10" name="Enchant Effect: A-grade Helmet" nameRu="Эффект Модификации: Шлем Ранга A">
		<!--  -->
		<icon>icon.armor_helmet_i02</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="MaxHp" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">100</value>
					<value level="6">200</value>
					<value level="7">300</value>
					<value level="8">500</value>
					<value level="9">800</value>
					<value level="10">1200</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="DefenceCriticalRate" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-2</value>
					<value level="6">-3</value>
					<value level="7">-4</value>
					<value level="8">-5</value>
					<value level="9">-6</value>
					<value level="10">-8</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceMagicCriticalRate" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-2</value>
					<value level="6">-3</value>
					<value level="7">-4</value>
					<value level="8">-5</value>
					<value level="9">-6</value>
					<value level="10">-8</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefencePhysicalSkillCriticalRate" fromLevel="5" toLevel="10">
				<amount>
					<value level="5">-2</value>
					<value level="6">-3</value>
					<value level="7">-4</value>
					<value level="8">-5</value>
					<value level="9">-6</value>
					<value level="10">-8</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50268" toLevel="1" name="Baium's Thunder Breaker" nameRu="Громолом Баюма">
		<!-- Imbues a weapon with Baium's Soul.\n\n<When triggered>\nBaium's Thunder Bolt -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50269</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50269</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50269</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50269" toLevel="22" name="Baium’s Thunder Bolt" nameRu="Удар Грома Баюма">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">113</value>
					<value level="2">115</value>
					<value level="3">117</value>
					<value level="4">119</value>
					<value level="5">123</value>
					<value level="6">127</value>
					<value level="7">132</value>
					<value level="8">136</value>
					<value level="9">140</value>
					<value level="10">145</value>
					<value level="11">149</value>
					<value level="11">149</value> 
					<value level="12">67</value> 
					<value level="13">69</value> 
					<value level="14">70</value> 
					<value level="15">71</value> 
					<value level="16">73</value> 
					<value level="17">76</value> 
					<value level="18">79</value> 
					<value level="19">81</value> 
					<value level="20">84</value> 
					<value level="21">87</value> 
					<value level="22">89</value> 
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50270" toLevel="10" name="+1 Baium's Thunder Breaker" nameRu="Громолом Баюма +1">
		<!-- Imbues a weapon with Baium's Soul.\n\n<When triggered>\nBaium's Thunder Bolt -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50269</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50269</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50269</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50271</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50271</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50271</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>STR</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>INT</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50271" toLevel="20" name="Baium’s Enhanced Thunder Bolt" nameRu="Усиленный Удар Грома Баюма">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="5">191</value>
					<value level="6">198</value>
					<value level="7">204</value>
					<value level="8">211</value>
					<value level="9">217</value>
					<value level="10">224</value>
					<value level="11">0</value> 
					<value level="12">0</value>
					<value level="13">0</value>
					<value level="14">0</value>
					<value level="15">114</value>
					<value level="16">118</value>
					<value level="17">122</value>
					<value level="18">126</value>
					<value level="19">130</value>
					<value level="20">134</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50273" toLevel="1" name="Queen Ant's Stone Breaker" nameRu="Каменный Разрушитель Королевы Муравьев">
		<!-- Imbues a weapon with Queen Ant's Soul.\n\n<When triggered>\nQueen Ant's Rain of Stones -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50274</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50274</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50274</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50274" toLevel="22" name="Queen Ant's Rain of Stones" nameRu="Каменный Дождь Королевы Муравьев">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">72</value>
					<value level="2">73</value>
					<value level="3">74</value>
					<value level="4">76</value>
					<value level="5">78</value>
					<value level="6">81</value>
					<value level="7">84</value>
					<value level="8">86</value>
					<value level="9">89</value>
					<value level="10">92</value>
					<value level="11">95</value>
					<value level="12">43</value>
					<value level="13">44</value>
					<value level="14">44</value>
					<value level="15">45</value>
					<value level="16">47</value>
					<value level="17">48</value>
					<value level="18">50</value>
					<value level="19">52</value>
					<value level="20">53</value>
					<value level="21">55</value>
					<value level="22">57</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50275" toLevel="10" name="+1 Queen Ant's Stone Breaker" nameRu="Каменный Разрушитель Королевы Муравьев +1">
		<!-- Imbues a weapon with Queen Ant's Soul.\n\n<When triggered>\nQueen Ant's Rain of Stones -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50274</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50274</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50274</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50276</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50276</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50276</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>DEX</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>WIT</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50276" toLevel="20" name="Queen Ant's Enhanced Rain of Stones" nameRu="Усиленный Каменный Дождь Королевы Муравьев">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack" fromLevel="5" toLevel="10">
				<power>
					<value level="5">121</value>
					<value level="6">126</value>
					<value level="7">130</value>
					<value level="8">134</value>
					<value level="9">138</value>
					<value level="10">142</value>
				</power>
			</effect>
			<effect name="MagicalAttack" fromLevel="15" toLevel="20">
				<power>
					<value level="15">73</value>
					<value level="16">75</value>
					<value level="17">78</value>
					<value level="18">80</value>
					<value level="19">83</value>
					<value level="20">85</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50278" toLevel="1" name="Zaken's Blood Sword" nameRu="Кровавый Меч Закена">
		<!-- Imbues a weapon with Zaken's Soul.\n\n<When triggered>\nZaken's Blood Prison -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50279</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50279</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50279</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50279" toLevel="22" name="Zaken's Blood Prison" nameRu="Кровавая Тюрьма Закена">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">103</value>
					<value level="2">105</value>
					<value level="3">107</value>
					<value level="4">109</value>
					<value level="5">112</value>
					<value level="6">116</value>
					<value level="7">120</value>
					<value level="8">124</value>
					<value level="9">128</value>
					<value level="10">132</value>
					<value level="11">136</value>
					<value level="12">61</value>
					<value level="13">63</value>
					<value level="14">64</value>
					<value level="15">65</value>
					<value level="16">67</value>
					<value level="17">69</value>
					<value level="18">72</value>
					<value level="19">74</value>
					<value level="20">76</value>
					<value level="21">79</value>
					<value level="22">81</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50280" toLevel="10" name="+1 Zaken's Blood Sword" nameRu="Кровавый Меч Закена +1">
		<!-- Imbues a weapon with Zaken's Soul.\n\n<When triggered>\nZaken's Blood Prison -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50279</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50279</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50279</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50281</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50281</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50281</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>CON</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>MEN</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50281" toLevel="20" name="Zaken's Enhanced Blood Prison" nameRu="Усиленная Кровавая Тюрьма Закена">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="5">174</value>
					<value level="6">180</value>
					<value level="7">186</value>
					<value level="8">192</value>
					<value level="9">198</value>
					<value level="10">204</value>
					<value level="10">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="11">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="12">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="13">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="14">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="15">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="16">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="17">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="18">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="19">204</value> <!-- FIXME: Auto generated with parser -->
					<value level="20">204</value> <!-- FIXME: Auto generated with parser -->
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50283" toLevel="1" name="Orfen's Venom Sword" nameRu="Ядовитый Меч Орфен">
		<!-- Imbues a weapon with Orfen's Soul.\n\n<When triggered>\nOrfen's Poison Storm -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50284</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50284</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50284</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50284" toLevel="22" name="Orfen's Poison Storm" nameRu="Буря Яда Орфен">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">103</value>
					<value level="2">105</value>
					<value level="3">107</value>
					<value level="4">109</value>
					<value level="5">112</value>
					<value level="6">116</value>
					<value level="7">120</value>
					<value level="8">124</value>
					<value level="9">128</value>
					<value level="10">132</value>
					<value level="11">136</value>
					<value level="12">61</value>
					<value level="13">63</value>
					<value level="14">64</value>
					<value level="15">65</value>
					<value level="16">67</value>
					<value level="17">69</value>
					<value level="18">72</value>
					<value level="19">74</value>
					<value level="20">76</value>
					<value level="21">79</value>
					<value level="22">81</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50285" toLevel="10" name="+1 Orfen's Venom Sword" nameRu="Ядовитый Меч Орфен +1">
		<!-- Imbues a weapon with Orfen's Soul.\n\n<When triggered>\nOrfen's Poison Storm -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50284</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50284</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50284</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50286</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50286</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50286</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>DEX</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>CON</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50286" toLevel="20" name="Orfen's Enhanced Poison Storm" nameRu="Улучшенная Буря Яда Орфен">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack" fromLevel="5" toLevel="10">
				<power>
					<value level="5">174</value>
					<value level="6">180</value>
					<value level="7">186</value>
					<value level="8">192</value>
					<value level="9">198</value>
					<value level="10">204</value>
				</power>
			</effect>
			<effect name="MagicalAttack" fromLevel="15" toLevel="20">
				<power>
					<value level="15">104</value>
					<value level="16">108</value>
					<value level="17">111</value>
					<value level="18">115</value>
					<value level="19">118</value>
					<value level="20">122</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50288" toLevel="1" name="Core's Plasmic Bow" nameRu="Плазменный Лук Ядра">
		<!-- Imbues a weapon with Core's Soul.\n\n<When triggered>\nCore's Plasmic Shock -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50289</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50289</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50289</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50289" toLevel="22" name="Core's Plasmic Shock" nameRu="Плазменный Шок Ядра">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">72</value>
					<value level="2">73</value>
					<value level="3">74</value>
					<value level="4">76</value>
					<value level="5">78</value>
					<value level="6">81</value>
					<value level="7">84</value>
					<value level="8">86</value>
					<value level="9">89</value>
					<value level="10">92</value>
					<value level="11">95</value>
					<value level="12">43</value>
					<value level="13">44</value>
					<value level="14">44</value>
					<value level="15">45</value>
					<value level="16">47</value>
					<value level="17">48</value>
					<value level="18">50</value>
					<value level="19">52</value>
					<value level="20">53</value>
					<value level="21">55</value>
					<value level="22">57</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50290" toLevel="10" name="+1 Core's Plasmic Bow" nameRu="Плазменный Лук Ядра +1">
		<!-- Imbues a weapon with Core's Soul.\n\n<When triggered>\nCore's Plasmic Shock -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50289</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50289</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50289</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50291</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50291</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50289</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>STR</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>DEX</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50291" toLevel="20" name="Core's Enhanced Plasmic Shock" nameRu="Усиленный Плазменный Шок Ядра">
		<!--  -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack" fromLevel="5" toLevel="10">
				<power>
					<value level="5">121</value>
					<value level="6">126</value>
					<value level="7">130</value>
					<value level="8">134</value>
					<value level="9">138</value>
					<value level="10">142</value>
				</power>
			</effect>
			<effect name="MagicalAttack" fromLevel="15" toLevel="20">
				<power>
					<value level="15">73</value>
					<value level="16">75</value>
					<value level="17">78</value>
					<value level="18">80</value>
					<value level="19">83</value>
					<value level="20">85</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50293" toLevel="22" name="Talisman of Speed" nameRu="Талисман Скорости">
		<!-- A talisman imbued with speed power.\n\n<Effect>\nP. Def. $s1\nM. Def. $s1 -->
		<icon>
			<value level="1">icon.tallisman_rapid</value>
			<value level="2">icon.tallisman_rapid_01</value>
			<value level="3">icon.tallisman_rapid</value>
			<value level="4">icon.tallisman_rapid_01</value>
			<value level="5">icon.tallisman_rapid</value>
			<value level="6">icon.tallisman_rapid_01</value>
			<value level="7">icon.tallisman_rapid</value>
			<value level="8">icon.tallisman_rapid_01</value>
			<value level="9">icon.tallisman_rapid</value>
			<value level="10">icon.tallisman_rapid_01</value>
			<value level="11">icon.tallisman_rapid</value>
			<value level="12">icon.tallisman_rapid_01</value>
			<value level="13">icon.tallisman_rapid</value>
			<value level="14">icon.tallisman_rapid_01</value>
			<value level="15">icon.tallisman_rapid</value>
			<value level="16">icon.tallisman_rapid_01</value>
			<value level="17">icon.tallisman_rapid</value>
			<value level="18">icon.tallisman_rapid_01</value>
			<value level="19">icon.tallisman_rapid</value>
			<value level="20">icon.tallisman_rapid_01</value>
			<value level="21">icon.tallisman_rapid</value>
			<value level="22">icon.tallisman_rapid_01</value>
		</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">5</value>
					<value level="2">5</value>
					<value level="3">10</value>
					<value level="4">10</value>
					<value level="5">20</value>
					<value level="6">20</value>
					<value level="7">30</value>
					<value level="8">30</value>
					<value level="9">40</value>
					<value level="10">45</value>
					<value level="11">50</value>
					<value level="12">55</value>
					<value level="13">60</value>
					<value level="14">70</value>
					<value level="15">80</value>
					<value level="16">190</value>
					<value level="17">200</value>
					<value level="18">225</value>
					<value level="19">250</value>
					<value level="20">275</value>
					<value level="21">300</value>
					<value level="22">400</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">5</value>
					<value level="2">5</value>
					<value level="3">10</value>
					<value level="4">10</value>
					<value level="5">20</value>
					<value level="6">20</value>
					<value level="7">30</value>
					<value level="8">30</value>
					<value level="9">40</value>
					<value level="10">45</value>
					<value level="11">50</value>
					<value level="12">55</value>
					<value level="13">60</value>
					<value level="14">70</value>
					<value level="15">80</value>
					<value level="16">190</value>
					<value level="17">200</value>
					<value level="18">225</value>
					<value level="19">250</value>
					<value level="20">275</value>
					<value level="21">300</value>
					<value level="22">400</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp" fromLevel="3" toLevel="22">
				<amount>
					<value level="3">50</value>
					<value level="4">50</value>
					<value level="5">100</value>
					<value level="6">100</value>
					<value level="7">200</value>
					<value level="8">200</value>
					<value level="9">300</value>
					<value level="10">400</value>
					<value level="11">600</value>
					<value level="12">800</value>
					<value level="13">1000</value>
					<value level="14">1250</value>
					<value level="15">1500</value>
					<value level="16">1750</value>
					<value level="17">2000</value>
					<value level="18">2500</value>
					<value level="19">3000</value>
					<value level="20">4000</value>
					<value level="21">5000</value>
					<value level="22">6000</value>
				</amount>
				<mode>DIFF</mode> 
			</effect>
			<effect name="Speed" fromLevel="3" toLevel="22">
				<amount>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">1</value>
					<value level="9">1</value>
					<value level="10">1</value>
					<value level="11">1</value>
					<value level="12">1</value>
					<value level="13">2</value>
					<value level="14">2</value>
					<value level="15">3</value>
					<value level="16">3</value>
					<value level="17">4</value>
					<value level="18">5</value>
					<value level="19">6</value>
					<value level="20">7</value>
					<value level="21">8</value>
					<value level="22">10</value>
				</amount>
				<mode>DIFF</mode> 
			</effect>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>100</chance>
				<targetType>SELF</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<isCritical>false</isCritical>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50294</skillId>
				<skillLevel>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">2</value>
					<value level="4">2</value>
					<value level="5">3</value>
					<value level="6">3</value>
					<value level="7">4</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
					<value level="11">7</value>
					<value level="12">8</value>
					<value level="13">9</value>
					<value level="14">10</value>
					<value level="15">11</value>
					<value level="16">12</value>
					<value level="17">13</value>
					<value level="18">14</value>
					<value level="19">15</value>
					<value level="20">16</value>
					<value level="21">17</value>
					<value level="22">18</value>
				</skillLevel>
			</effect>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>100</chance>
				<targetType>SELF</targetType>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<isCritical>false</isCritical>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50294</skillId>
				<skillLevel>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">2</value>
					<value level="4">2</value>
					<value level="5">3</value>
					<value level="6">3</value>
					<value level="7">4</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
					<value level="11">7</value>
					<value level="12">8</value>
					<value level="13">9</value>
					<value level="14">10</value>
					<value level="15">11</value>
					<value level="16">12</value>
					<value level="17">13</value>
					<value level="18">14</value>
					<value level="19">15</value>
					<value level="20">16</value>
					<value level="21">17</value>
					<value level="22">18</value>
				</skillLevel>
			</effect>
			<effect name="AttackTrait" fromLevel="22" toLevel="22">
			    <SWORD>5</SWORD>
                <BLUNT>5</BLUNT>
                <DAGGER>5</DAGGER>
                <POLE>5</POLE>
                <FIST>5</FIST>
                <BOW>5</BOW>
                <DUAL>5</DUAL>
                <DUALFIST>5</DUALFIST>
                <RAPIER>5</RAPIER>
                <CROSSBOW>5</CROSSBOW>
                <ANCIENTSWORD>5</ANCIENTSWORD>
                <DUALDAGGER>5</DUALDAGGER>
                <PISTOLS>5</PISTOLS>
            </effect>
		</effects>
	</skill>
	<skill id="50294" toLevel="18" name="Energy of Speed" nameRu="Энергия Скорости">
		<!-- A character becomes full of speed power.\n\n<Effect>\nAtk. Spd. +$s1 -->
		<icon>icon.skill5662</icon>
		<operateType>A2</operateType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="9">9</value>
			<value level="10">10</value>
			<value level="11">11</value>
			<value level="12">12</value>
			<value level="13">13</value>
			<value level="14">14</value>
			<value level="15">15</value>
			<value level="16">16</value>
			<value level="17">17</value>
			<value level="18">18</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="9">9</value>
			<value level="10">10</value>
			<value level="11">11</value>
			<value level="12">12</value>
			<value level="13">13</value>
			<value level="14">14</value>
			<value level="15">15</value>
			<value level="16">16</value>
			<value level="17">17</value>
			<value level="18">18</value>
		</magicLevel>
		<abnormalTime>
			<value fromLevel="1" toLevel="10">5</value>
			<value fromLevel="11" toLevel="16">10</value>
			<value fromLevel="17" toLevel="18">15</value>
		</abnormalTime>
		<reuseDelay>10000</reuseDelay>
		<staticReuse>true</staticReuse>
		<isTriggeredSkill>true</isTriggeredSkill>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">30</value>
					<value level="4">50</value>
					<value level="5">80</value>
					<value level="6">115</value>
					<value level="7">150</value>
					<value level="8">160</value>
					<value level="9">170</value>
					<value level="10">185</value>
					<value level="11">200</value>
					<value level="12">215</value>
					<value level="13">230</value>
					<value level="14">245</value>
					<value level="15">260</value>
					<value level="16">280</value>
					<value level="17">300</value>
					<value level="18">300</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">30</value>
					<value level="4">50</value>
					<value level="5">80</value>
					<value level="6">115</value>
					<value level="7">150</value>
					<value level="8">160</value>
					<value level="9">170</value>
					<value level="10">185</value>
					<value level="11">200</value>
					<value level="12">215</value>
					<value level="13">230</value>
					<value level="14">245</value>
					<value level="15">260</value>
					<value level="16">280</value>
					<value level="17">300</value>
					<value level="18">300</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillCriticalDamage" fromLevel="5" toLevel="18">
				<amount>
					<value level="5">30</value>
					<value level="6">45</value>
					<value level="7">60</value>
					<value level="8">80</value>
					<value level="9">100</value>
					<value level="10">200</value>
					<value level="11">300</value>
					<value level="12">450</value>
					<value level="13">600</value>
					<value level="14">850</value>
					<value level="15">1100</value>
					<value level="16">1300</value>
					<value level="17">1500</value>
					<value level="18">1500</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalDamage" fromLevel="5" toLevel="18">
				<amount>
					<value level="5">30</value>
					<value level="6">45</value>
					<value level="7">60</value>
					<value level="8">80</value>
					<value level="9">100</value>
					<value level="10">200</value>
					<value level="11">300</value>
					<value level="12">450</value>
					<value level="13">600</value>
					<value level="14">850</value>
					<value level="15">1100</value>
					<value level="16">1300</value>
					<value level="17">1500</value>
					<value level="18">1500</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50295" toLevel="1" name="Baium's Thunder Breaker" nameRu="Громолом Баюма">
		<!-- A dagger containing Baium's Soul.\n\n<Level Bonus Effect>\nP./ M. Atk. are increased depending on level.\n\n<Lv. 60-69>\nP. Atk. +3-5\nM. Atk. +2-3\n<Lv. 70-79>\nP. Atk. +8\nM. Atk. +5\n<Lv. 80-89>\nP. Atk. +13-50\nM. Atk. +8-51\n<Lv. 90+>\nP. Atk. +56 and higher\nM. Atk. +34 and higher -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="50296" toLevel="1" name="Queen Ant's Stone Spear" nameRu="Каменное Копье Королевы Муравьев">
		<!-- A spear containing Queen Ant's Soul and being able to use Queen Ant's power. -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="50297" toLevel="1" name="Zaken's Blood Sword" nameRu="Кровавый Меч Закена">
		<!-- A sword containing Zaken's Soul and being able to use Zaken's power. -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="50298" toLevel="1" name="Orfen's Venom Sword" nameRu="Ядовитый Меч Орфен">
		<!-- A sword containing Orfen's Soul and being able to use Orfen's power. -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="50299" toLevel="1" name="Core's Plasmic Bow" nameRu="Плазменный Лук Ядра">
		<!-- A bow containing Core's Soul and being able to use Core's power. -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
	</skill>
</list>
