<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="62001" toLevel="1" name="White Assassin Transformation" nameRu="Трансформация в Светлого Ассасина">
		<!-- You are transformed into White Assassin. For 20 min., Max HP/ MP +7%%, P./ M. Accuracy +2, P./ M. Atk. +3%%, P./ M. Def. +3%%, Atk. Spd. +5%%, M. Skill Critical Rate +20, Speed +5. The effect remains after death. -->
		<icon>icon.scroll_of_verification_i06</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<reuseDelay>60000</reuseDelay>
		<effectPoint>1</effectPoint>
	</skill>
	<skill id="62002" toLevel="1" name="<PERSON> Griffin" nameRu="Оседлать Грифона">
		<!-- Allows to mount <PERSON>. -->
		<icon>icon.griffin_vehicle</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3600</abnormalTime>
		<abnormalType>TRANSFORM</abnormalType>
		<blockedInOlympiad>true</blockedInOlympiad>
		<operateType>A2</operateType>
		<effectPoint>1</effectPoint>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<irreplacableBuff>true</irreplacableBuff>
		<isMagic>0</isMagic>
		<hitTime>1000</hitTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="CanTransform">
				<transformId>204</transformId>
			</condition>
		</conditions>
		<effects>
			<effect name="Transformation">
				<transformationId>204</transformationId>
			</effect>
		</effects>
	</skill>
</list>
