<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="49200" toLevel="1" name="Latent Ability" nameRu="Скрытая Способность">
		<!-- Activates a pet latent ability.\nIncreases one of 6 stats at random. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="49201" toLevel="3" name="STR +1" nameRu="СИЛ +1">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>STR</stat>
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="49202" toLevel="3" name="DEX +1" nameRu="ЛВК +1">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>DEX</stat>
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="49203" toLevel="3" name="CON +1" nameRu="ВЫН +1">
		<!-- AUTO GENERATED SKILL TODO: FIX IT -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>CON</stat>
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="49204" toLevel="3" name="INT +1" nameRu="ИНТ +1">
		<!-- INT +$s1 -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>INT</stat>
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="49205" toLevel="3" name="WIT +1" nameRu="МДР +1">
		<!-- WIT +$s1 -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>WIT</stat>
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="49206" toLevel="3" name="MEN +1" nameRu="ДУХ +1">
		<!-- MEN +$s1 -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<stat>MEN</stat>
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="49207" toLevel="2" name="Steel Skin" nameRu="Стальная Кожа">
		<!-- Increases defense abilities according to the skill level.\nFor each level, P. Def. +$s1, M. Def. +$s2. -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<!-- Level bonus hardcoded in PDefenseFinalizer -->
		<!-- Level bonus hardcoded in MDefenseFinalizer -->
	</skill>
	<skill id="49208" toLevel="2" name="Physical Training lv1" nameRu="">
		<!-- Increases defense abilities according to the skill level.\nFor each level, P. Def. +$s1, M. Def. +$s2. -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="AdditionalPotionHp">
				<amount>30</amount>
			</effect>			
			<effect name="PveMagicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PveRaidMagicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PveRaidPhysicalAttackDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PveRaidPhysicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>			
			<effect name="PvpMagicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalAttackDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvpPhysicalSkillDamageBonus">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
</list>
