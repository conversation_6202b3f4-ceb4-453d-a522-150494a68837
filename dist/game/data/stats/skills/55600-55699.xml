<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="55600" toLevel="1" name="Blessed Support Cube">
		<icon>[icon.etc_golden_ore_cube_pc_i00]</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55601" toLevel="1" name="Party Support Cube - Lv. 2">
		<icon>[icon.etc_silver_ore_cube_pc_i00]</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55602" toLevel="1" name="Fishing Support Cube">
		<icon>[icon.etc_cupreous_ore_cube_pc_i00]</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55603" toLevel="1" name="Potion Support Cube - Lv. 2">
		<icon>[icon.etc_earthen_ore_cube_pc_i00]</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55604" toLevel="1" name="Growth Support Cube">
		<icon>[icon.etc_crystal_cube_event_i00]</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55605" toLevel="1" name="Artisan's Goggles (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.accessory_dwarf_goggle_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55606" toLevel="1" name="Artisan's Goggles (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.accessory_dwarf_goggle_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55607" toLevel="1" name="Artisan's Goggles (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.accessory_dwarf_goggle_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55608" toLevel="1" name="Uniform Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys.icon.br_Uniform_Hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55609" toLevel="1" name="Uniform Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys.icon.br_Uniform_Hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55610" toLevel="1" name="Uniform Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys.icon.br_Uniform_Hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55611" toLevel="1" name="Assassin's Bamboo Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys.icon.br_Bamboo_Hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55612" toLevel="1" name="Assassin's Bamboo Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys.icon.br_Bamboo_Hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55613" toLevel="1" name="Assassin's Bamboo Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys.icon.br_Bamboo_Hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55614" toLevel="1" name="Valkyrie Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys.icon.br_valkyrie_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55615" toLevel="1" name="Valkyrie Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys.icon.br_valkyrie_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55616" toLevel="1" name="Valkyrie Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys.icon.br_valkyrie_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55617" toLevel="1" name="Gatekeeper Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys.icon.br_gatekeeper_of_hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55618" toLevel="1" name="Gatekeeper Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys.icon.br_gatekeeper_of_hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55619" toLevel="1" name="Gatekeeper Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys.icon.br_gatekeeper_of_hat_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55620" toLevel="1" name="Wedding Veil (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys2.icon.br_wedding_vail_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55621" toLevel="1" name="Wedding Veil (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys2.icon.br_wedding_vail_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55622" toLevel="1" name="Wedding Veil (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys2.icon.br_wedding_vail_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55623" toLevel="1" name="Maid Hair Accessory (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys3.icon1.g_co_cutie_maid_hair</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55624" toLevel="1" name="Maid Hair Accessory (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys3.icon1.g_co_cutie_maid_hair</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55625" toLevel="1" name="Maid Hair Accessory (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys3.icon1.g_co_cutie_maid_hair</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55626" toLevel="1" name="Refined Romantic Chapeau: Red (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.bm_romantic_chaperon_red</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55627" toLevel="1" name="Refined Romantic Chapeau: Red (DEX+1 WIT+1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.bm_romantic_chaperon_red</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55628" toLevel="1" name="Refined Romantic Chapeau: Red (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.bm_romantic_chaperon_red</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55629" toLevel="1" name="Refined Angel Ring (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.accessory_angel_ring_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55630" toLevel="1" name="Refined Angel Ring (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.accessory_angel_ring_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55631" toLevel="1" name="Refined Angel Ring (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.accessory_angel_ring_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55632" toLevel="1" name="Refined Wizard Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.accessory_magician_cap2_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55633" toLevel="1" name="Refined Wizard Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.accessory_magician_cap2_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55634" toLevel="1" name="Refined Wizard Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.accessory_magician_cap2_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55635" toLevel="1" name="Laborer Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys.icon.br_people_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55636" toLevel="1" name="Laborer Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys.icon.br_people_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55637" toLevel="1" name="Laborer Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys.icon.br_people_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55638" toLevel="1" name="Top Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.accessory_magicial_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55639" toLevel="1" name="Top Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.accessory_magicial_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55640" toLevel="1" name="Wizard Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.accessory_magicial_cap_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55641" toLevel="1" name="Vigilante Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.zorocap</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55642" toLevel="1" name="Vigilante Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.zorocap</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55643" toLevel="1" name="Vigilante Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.zorocap</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55644" toLevel="1" name="White Uniform Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys2.icon2.pi_navy_cap</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55645" toLevel="1" name="White Uniform Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys2.icon2.pi_navy_cap</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55646" toLevel="1" name="White Uniform Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys2.icon2.pi_navy_cap</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55647" toLevel="1" name="Warrior's Helmet (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys2.icon.br_close_helmet_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55648" toLevel="1" name="Warrior's Helmet (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys2.icon.br_close_helmet_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55649" toLevel="1" name="Warrior's Helmet (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys2.icon.br_close_helmet_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55650" toLevel="1" name="First Mate Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.Accessary_middle_ages_i01</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55651" toLevel="1" name="First Mate Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.Accessary_middle_ages_i01</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55652" toLevel="1" name="First Mate Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.Accessary_middle_ages_i01</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55653" toLevel="1" name="Refined Romantic Chapeau: Blue (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>icon.bm_romantic_chaperon_blue</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55654" toLevel="1" name="Refined Romantic Chapeau: Blue (DEX+1 WIT+1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>icon.bm_romantic_chaperon_blue</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55655" toLevel="1" name="Refined Romantic Chapeau: Blue (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>icon.bm_romantic_chaperon_blue</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55656" toLevel="1" name="Gold-rimmed Glasses (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys2.icon.br_gold_eyeglass_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55657" toLevel="1" name="Gold-rimmed Glasses (DEX+1 WIT+1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys2.icon.br_gold_eyeglass_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55658" toLevel="1" name="Gold-rimmed Glasses (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys2.icon.br_gold_eyeglass_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55659" toLevel="1" name="Horn-rimmed Glasses (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys.icon.br_Eye_Glasses_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55660" toLevel="1" name="Horn-rimmed Glasses (DEX+1 WIT+1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys.icon.br_Eye_Glasses_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55661" toLevel="1" name="Horn-rimmed Glasses (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys.icon.br_Eye_Glasses_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55662" toLevel="1" name="Stylish Straw Hat (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys3.icon.g_straw_hat</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55663" toLevel="1" name="Stylish Straw Hat (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys3.icon.g_straw_hat</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55664" toLevel="1" name="Stylish Straw Hat (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys3.icon.g_straw_hat</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55665" toLevel="1" name="Chic Silver Chapeau (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys2.icon2.pi_silverchaperon</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55666" toLevel="1" name="Chic Silver Chapeau (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys2.icon2.pi_silverchaperon</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55667" toLevel="1" name="Chic Silver Chapeau (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys2.icon2.pi_silverchaperon</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55668" toLevel="1" name="Afro Hair (STR+1 INT+1)">
		<!-- STR+1, INT+1. -->
		<icon>BranchSys.icon.br_Afro_hair_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>INT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55669" toLevel="1" name="Afro Hair (DEX +1 WIT +1)">
		<!-- DEX +1, and WIT +1. -->
		<icon>BranchSys.icon.br_Afro_hair_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>WIT</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55670" toLevel="1" name="Afro Hair (CON+1 MEN+1)">
		<!-- CON+1, MEN+1 -->
		<icon>BranchSys.icon.br_Afro_hair_i00</icon>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>1</amount>
				<stat>MEN</stat>
			</effect>
		</effects>
	</skill>
	<skill id="55671" toLevel="1" name="Eva's Scroll: Enchant Hair Accessory">
		<!-- To Enchant Hair Accessory. -->
		<icon>icon.scroll_of_unidentify_am_d</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55672" toLevel="1" name="Stabilized Eva's Scroll: Enchant Hair Accessory">
		<!-- To Enchant Hair Accessory. -->
		<icon>icon.scroll_of_unidentify_am_d</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55673" toLevel="1" name="Eva's Enhancement Pack - Hair Accessory">
		<icon>Branchsys2.icon.br_lucky_bag_box_i00</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
		<hitTime>500</hitTime>
		<reuseDelay>1000</reuseDelay>
	</skill>
	<skill id="55674" toLevel="1" name="Eva's Hair Accessory Pack">
		<icon>BranchSys.icon.br_silver_box_i00</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<coolTime>500</coolTime>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>500</hitTime>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>70758</itemConsumeId>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>1</magicLevel>
		<conditions>
			<condition name="OpEncumbered">
				<weightPercent>20</weightPercent>
				<slotsPercent>10</slotsPercent>
			</condition>
		</conditions>
		<effects>
			<effect name="RestorationRandom">
				<items>
					<item chance="5.5">
						<item id="70726" count="1" /> <!-- White Uniform Hat <STR+1 INT+1> -->
					</item>
					<item chance="5.5">
						<item id="70727" count="1" /> <!-- White Uniform Hat <DEX+1 WIT+1> -->
					</item>
					<item chance="5.5">
						<item id="70728" count="1" /> <!-- White Uniform Hat <CON+1 MEN+1> -->
					</item>
					<item chance="5.5">
						<item id="90852" count="1" /> <!-- Artisan's Goggles <STR+1 INT+1> -->
					</item>
					<item chance="5.5">
						<item id="90853" count="1" /> <!-- Artisan's Goggles <DEX+1 WIT+1> -->
					</item>
					<item chance="5.5">
						<item id="90854" count="1" /> <!-- Artisan's Goggles <CON+1 MEN+1> -->
					</item>
					<item chance="5.5">
						<item id="90843" count="1" /> <!-- Ninja Hair Accessory <STR+1 INT+1> -->
					</item>
					<item chance="5.5">
						<item id="90844" count="1" /> <!-- Ninja Hair Accessory <DEX+1 WIT+1> -->
					</item>
					<item chance="5.5">
						<item id="90845" count="1" /> <!-- Ninja Hair Accessory <CON+1 MEN+1> -->
					</item>
					<item chance="5.5">
						<item id="90840" count="1" /> <!-- Napoleon Hat <STR+1 INT+1> -->
					</item>
					<item chance="5.5">
						<item id="90841" count="1" /> <!-- Napoleon Hat <DEX+1 WIT+1> -->
					</item>
					<item chance="5.5">
						<item id="90842" count="1" /> <!-- Napoleon Hat <CON+1 MEN+1> -->
					</item>
					<item chance="5.5">
						<item id="90849" count="1" /> <!-- First Mate Hat <STR+1 INT+1> -->
					</item>
					<item chance="5.5">
						<item id="90850" count="1" /> <!-- First Mate Hat <DEX+1 WIT+1> -->
					</item>
					<item chance="5.5">
						<item id="90851" count="1" /> <!-- First Mate Hat <CON+1 MEN+1> -->
					</item>
					<item chance="5.5">
						<item id="90846" count="1" /> <!-- Warrior's Helmet <STR+1 INT+1> -->
					</item>
					<item chance="5.5">
						<item id="90847" count="1" /> <!-- Warrior's Helmet <DEX+1 WIT+1> -->
					</item>
					<item chance="5.5">
						<item id="90848" count="1" /> <!-- Warrior's Helmet <CON+1 MEN+1> -->
					</item>
				</items>
			</effect>
		</effects>
	</skill>
	<skill id="55675" toLevel="1" name="Eva's Enhancement Pack - Hair Accessory">
		<icon>Branchsys2.icon.br_lucky_bag_box_i00</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
		<hitTime>500</hitTime>
		<reuseDelay>1000</reuseDelay>
	</skill>
	<skill id="55676" toLevel="1" name="Scroll: Enchant Weapon (A-grade)">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55677" toLevel="1" name="Shining Pendant Varnish">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55678" toLevel="1" name="Improved Scroll: Enchant Weapon (A-grade)">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55679" toLevel="1" name="Strawberry Juice">
		<!-- For 5 min., Max HP +10%, HP Recovery Bonus +10%, P. Atk. +10%, P. Def. +10%, Atk. Spd. +5%, Debuff Resistance +10%. Cannot be stacked with Prophecy-type buffs. -->
		<icon>icon.etc_fruit_cocktail_i01</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
	</skill>
	<skill id="55680" toLevel="1" name="Mango Juice">
		<!-- For 5 min., MP Recovery Bonus +10%, M. Atk. +10%, M. Def. +10%, Casting Spd. +5%, Debuff Resistance +10%. Skill MP Consumption -3%. Cannot be stacked with Prophecy-type buffs. -->
		<icon>icon.etc_fruit_cocktail_i03</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
	</skill>
	<skill id="55681" toLevel="1" name="Cherry Juice">
		<!-- For 5 min., Evasion +3, Accuracy +3, Critical Rate +10%, Critical Damage +15%, Speed +10%, Debuff Resistance +10%. -->
		<icon>icon.etc_fruit_cocktail_i02</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
	</skill>
	<skill id="55682" toLevel="1" name="Special Dragons Fruit">
		<!-- For 20 min., XP/ SP +10%, P./ M. Atk. +12%, P./ M. Critical Rate +10, P./ M. Critical Damage +5%, P./ M. Def. +15%, Max HP/ MP/ CP +25%, HP/ MP Recovery Bonus +30%. Has a chance of absorbing 5% of damage inflicted on enemy as HP. Effect remains after death. -->
		<icon>icon.bm_dragon_fruit</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>DRAGON_FRUIT</abnormalType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>1</effectPoint>
		<isMagic>4</isMagic>
		<itemConsumeCount>1</itemConsumeCount>
		<itemConsumeId>90508</itemConsumeId>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>1</magicLevel>
		<reuseDelay>60000</reuseDelay>
		<reuseDelay>60000</reuseDelay>
		<stayAfterDeath>true</stayAfterDeath>
		<effects>
			<effect name="ExpModify">
				<amount>10</amount>
			</effect>
			<effect name="SpModify">
				<amount>10</amount>
			</effect>
			<effect name="PhysicalAttack">
				<amount>12</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>12</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalDamage">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxHp">
				<amount>25</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>25</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxCp">
				<amount>25</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="VampiricAttack">
				<amount>5</amount>
				<chance>80</chance>
			</effect>
		</effects>
	</skill>
	<skill id="55683" toLevel="1" name="Royal Tears">
		<!-- The essence of Frintezza's feelings. P. Atk./ M. Atk. +15%, P. Def./ M. Def. +15%. Changes the appearance of your weapon. Deleted upon logout. Effect remains after death. -->
		<icon>BranchSys2.icon.g_illumination_skyblue</icon>
		<operateType>A1</operateType>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<isMagic>4</isMagic>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="55684" toLevel="1" name="Red Candy of the Royal Family">
		<!-- Acquired XP/ SP +25%. Can be once stacked with the Blue Candy of the Royal Family. -->
		<icon>etc_whiteday_candy_i05</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="55685" toLevel="1" name="Blue Candy of the Royal Family">
		<!-- Acquired XP/ SP +25%. Can be once stacked with the Red Candy of the Royal Family. -->
		<icon>etc_whiteday_candy_i06</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="55686" toLevel="1" name="The Royal Confectionary Box">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<hitTime>500</hitTime>
	</skill>
	<skill id="55687" toLevel="1" name="Ultimate Confidence">
		<!-- You feel unusually confident about yourself and you are ready to express your feelings as they are. For 2 h., acquired XP/ SP +5%, Atk. Spd./ Casting Spd. +10%, Speed +20. Cannot be stacked with Blessing of Light. Changes the appearance of your weapon. Effect remains after death. -->
		<icon>icon.release_thirst</icon>
		<operateType>A1</operateType>
		<effectPoint>100</effectPoint>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="55688" toLevel="1" name="Pirates Treasure Chest">
		<icon>icon.card_event_blue_box</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
		<hitTime>500</hitTime>
	</skill>
	<skill id="55689" toLevel="1" name="Newbie Talisman">
		<!-- When equipped P. Def. +18, M. Def. +9, P./ M. Skill Evasion +1, Max. HP/ MP/ CP +50, Atk. Spd./ Casting Spd. +6, Speed +4. -->
		<icon>icon.npoint_talisman_stat</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="55690" toLevel="1" name="Transformation Scroll: Pirate">
		<!-- Transformation into a Pirate. Increases your combat stats. Effect remains after death. -->
		<icon>icon.etc_charm_of_courage_i03</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
		<isMagic>4</isMagic>
	</skill>
	<skill id="55691" toLevel="1" name="Transformation Scroll: Dark Assassin">
		<!-- Transformation into a Dark Assassin. Increases your combat stats. Effect remains after death. -->
		<icon>icon.etc_charm_of_courage_i05</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
		<isMagic>4</isMagic>
	</skill>
	<skill id="55692" toLevel="1" name="Transformation Scroll: White Assassin">
		<!-- Transformation into a White Assassin. Increases your combat stats. Effect remains after death. -->
		<icon>icon.etc_charm_of_courage_i00</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
		<isMagic>4</isMagic>
	</skill>
	<skill id="55693" toLevel="1" name="Blue Dynasty Transformation Scroll">
		<!-- Makes your armor look like Blue Dynasty one. Increases your combat stats. Effect remains after death. -->
		<icon>icon.etc_charm_of_courage_i01</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
		<isMagic>4</isMagic>
	</skill>
	<skill id="55694" toLevel="1" name="Red Zubei Transformation Scroll">
		<!-- Makes your armor look like Red Zubei one. Increases your combat stats. Effect remains after death. -->
		<icon>icon.etc_charm_of_courage_i04</icon>
		<operateType>A1</operateType>
		<effectPoint>1</effectPoint>
		<isMagic>4</isMagic>
	</skill>
	<skill id="55695" toLevel="1" name="Transformation Support Cube">
		<icon>icon.etc_golden_ore_cube_pc_i00</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="55696" toLevel="1" name="Newbie Amulet">
		<!-- Acquired XP/ SP +50%. -->
		<icon>BranchSys2.lcon.br_life_rune_i00</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="55697" toLevel="1" name="Kusto Lucky Enchant Pack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
		<hitTime>500</hitTime>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="55698" toLevel="1" name="Giant's Treasure Chest">
		<icon>icon.card_event_blue_box</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
		<hitTime>500</hitTime>
	</skill>
	<skill id="55699" toLevel="1" name="Scroll: Hunter's Song">
		<!-- For 10 min., P. Critical Rate +100%. -->
		<icon>icon.skill0269</icon>
		<operateType>A1</operateType>
		<hitTime>2500</hitTime>
		<reuseDelay>3000</reuseDelay>
	</skill>
</list>
