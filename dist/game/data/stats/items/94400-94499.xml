<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="94400" name="Package: +4 Ring of Core" nameRu="Упаковка: Кольцо Ядра +4" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_ring_of_core_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94401" name="Package: +5 Ring of Core" nameRu="Упаковка: Кольцо Ядра +5" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_ring_of_core_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94402" name="Package: +1 Baium's Ring" nameRu="Упаковка: Кольцо Баюма +1" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_ring_of_baium_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94403" name="Package: +2 Baium's Ring" nameRu="Упаковка: Кольцо Баюма +2" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_ring_of_baium_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94404" name="Package: +3 Baium's Ring" nameRu="Упаковка: Кольцо Баюма +3" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_ring_of_baium_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94405" name="Package: +4 Baium's Ring" nameRu="Упаковка: Кольцо Баюма +4" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_ring_of_baium_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94406" name="Package: +5 Baium's Ring" nameRu="Упаковка: Кольцо Баюма +5" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_ring_of_baium_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94407" name="Package: +1 Zaken's Earring" nameRu="Упаковка: Серьга Закена +1" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_zaken_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94408" name="Package: +2 Zaken's Earring" nameRu="Упаковка: Серьга Закена +2" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_zaken_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94409" name="Package: +3 Zaken's Earring" nameRu="Упаковка: Серьга Закена +3" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_zaken_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94410" name="Package: +4 Zaken's Earring" nameRu="Упаковка: Серьга Закена +4" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_zaken_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94411" name="Package: +5 Zaken's Earring" nameRu="Упаковка: Серьга Закена +5" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_zaken_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94412" name="Package: +1 Frintezza's Necklace" nameRu="Упаковка: Ожерелье Фринтезы +1" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_necklace_of_frintessa_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94413" name="Package: +2 Frintezza's Necklace" nameRu="Упаковка: Ожерелье Фринтезы +2" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_necklace_of_frintessa_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94414" name="Package: +3 Frintezza's Necklace" nameRu="Упаковка: Ожерелье Фринтезы +3" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_necklace_of_frintessa_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94415" name="Package: +4 Frintezza's Necklace" nameRu="Упаковка: Ожерелье Фринтезы +4" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_necklace_of_frintessa_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94416" name="Package: +5 Frintezza's Necklace" nameRu="Упаковка: Ожерелье Фринтезы +5" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_necklace_of_frintessa_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94417" name="Package: +1 Antharas' Earring" nameRu="Упаковка: Серьга Антараса +1" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_antaras_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94418" name="Package: +2 Antharas' Earring" nameRu="Упаковка: Серьга Антараса +2" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_antaras_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94419" name="Package: +3 Antharas' Earring" nameRu="Упаковка: Серьга Антараса +3" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_antaras_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94420" name="Package: +4 Antharas' Earring" nameRu="Упаковка: Серьга Антараса +4" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_antaras_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94421" name="Package: +5 Antharas' Earring" nameRu="Упаковка: Серьга Антараса +5" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.accessory_earring_of_antaras_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94422" name="Elixir Powder Pouch" nameRu="Мешочек с Порошком Эликсира" type="EtcItem">
		<!-- Double-click to obtain one of the following items. -->
		<set name="icon" val="icon.pouch_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="94328" min="1" max="1" chance="70" />
			<item id="94328" min="2" max="2" chance="20" />
			<item id="94328" min="3" max="3" chance="10" />
		</capsuled_items>
	</item>
	<item id="94423" name="Life Stone - Accessories" nameRu="Камень Жизни - Аксессуары" type="EtcItem">
		<!-- A mineral formed inside a monster under immense impact. Used for weapon augmentation by Blacksmiths Jeris in Giran and Jeros in Aden.\n\nA-grade Gemstones (2 pcs.) are required for augmentation. -->
		<set name="icon" val="icon.renewal_mineral_rareacc" />
		<set name="material" val="STEEL" />
		<set name="weight" val="2" />
		<set name="price" val="0" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94424" name="Life Stone - Accessories" nameRu="Камень Жизни - Аксессуары" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- A mineral formed inside a monster under immense impact. Used for weapon augmentation by Blacksmiths Jeris in Giran and Jeros in Aden.\n\nA-grade Gemstones (2 pcs.) are required for augmentation. -->
		<set name="icon" val="icon.renewal_mineral_rareacc" />
		<set name="material" val="STEEL" />
		<set name="weight" val="2" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
	</item>
	<item id="94426" name="Package: Frintezza's Necklace" nameRu="Упаковка: Ожерелье Фринтезы" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.accessory_necklace_of_frintessa_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<capsuled_items>
			<item id="91550" min="1" max="1" chance="100" />
		</capsuled_items>
	</item>
	<item id="94429" name="Primeval Isle Supplies (Time-limited)" nameRu="Припасы Первобытного Острова (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_treasure_box_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="93134" min="1" max="1" chance="10" /> <!-- Package: Agathion Ignis -->
			<item id="93135" min="1" max="1" chance="10" /> <!-- Package: Agathion Nebula -->
			<item id="93136" min="1" max="1" chance="10" /> <!-- Package: Agathion Procella -->
			<item id="93137" min="1" max="1" chance="10" /> <!-- Package: Agathion Petram -->
			<item id="93138" min="1" max="1" chance="10" /> <!-- Package: Agathion Joy -->
			<item id="94212" min="1" max="1" chance="20" /> <!-- Life Stone Lv. 2 - Armor - Sealed -->
			<item id="93848" min="10" max="10" chance="30" /> <!-- Sayha's Cookie (Time-limited) - Sealed -->
		</capsuled_items>
	</item>
	<item id="94430" name="Elmoreden Tower Supplies (Time-limited)" nameRu="Припасы Первобытного Сада (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_treasure_box_i07" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="93848" min="10" max="10" chance="30" /> <!-- Sayha's Cookie (Time-limited) - Sealed -->
			<item id="94424" min="1" max="1" chance="14" /> <!-- Augmenting Stone: Accessory - Sealed -->
			<item id="92036" min="1" max="1" chance="8" /> <!-- Amber Lv. 1 - Sealed -->
			<item id="92046" min="1" max="1" chance="8" /> <!-- Opal Lv. 1 - Sealed -->
			<item id="92056" min="1" max="1" chance="8" /> <!-- Coral Lv. 1 - Sealed -->
			<item id="92066" min="1" max="1" chance="8" /> <!-- Onyx Lv. 1 - Sealed -->
			<item id="92076" min="1" max="1" chance="8" /> <!-- Spinel Lv. 1 - Sealed -->
			<item id="92086" min="1" max="1" chance="8" /> <!-- Zircon Lv. 1 - Sealed -->
			<item id="92096" min="1" max="1" chance="8" /> <!-- Moonstone Lv. 1 - Sealed -->
		</capsuled_items>
	</item>
	<item id="94431" name="Alligator Island Supplies (Time-limited)" nameRu="Припасы Острова Аллигаторов (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_treasure_box_i05" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="93848" min="10" max="10" chance="30" /> <!-- Sayha's Cookie (Time-limited) - Sealed -->
			<item id="94210" min="1" max="1" chance="20" /> <!-- Augmenting Stone: Weapon - Sealed -->
			<item id="91767" min="1" max="1" chance="12.5" /> <!-- Enchant Kit: Talisman of Aden - Sealed -->
			<item id="92020" min="1" max="1" chance="12.5" /> <!-- Enchant Kit: Talisman of Eva - Sealed -->
			<item id="93330" min="1" max="1" chance="12.5" /> <!-- Enchant Kit: Talisman of Authority - Sealed -->
			<item id="93685" min="1" max="1" chance="12.5" /> <!-- Enchant Kit: Talisman of Speed - Sealed -->
		</capsuled_items>
	</item>
	<item id="94432" name="+7 Agathion Pack (Time-limited)" nameRu="Сундук с Агатионом +7 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.star_agathion_cube" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="93128" min="1" max="1" minEnchant="7" maxEnchant="7" chance="20" />
			<item id="93129" min="1" max="1" minEnchant="7" maxEnchant="7" chance="20" />
			<item id="93130" min="1" max="1" minEnchant="7" maxEnchant="7" chance="20" />
			<item id="93131" min="1" max="1" minEnchant="7" maxEnchant="7" chance="20" />
			<item id="93132" min="1" max="1" minEnchant="7" maxEnchant="7" chance="20" />
		</capsuled_items>
	</item>
	<item id="94433" name="+5 Agathion Pack (Time-limited)" nameRu="Сундук с Агатионом +5 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.star_agathion_cube" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="93128" min="1" max="1" minEnchant="5" maxEnchant="5" chance="20" />
			<item id="93129" min="1" max="1" minEnchant="5" maxEnchant="5" chance="20" />
			<item id="93130" min="1" max="1" minEnchant="5" maxEnchant="5" chance="20" />
			<item id="93131" min="1" max="1" minEnchant="5" maxEnchant="5" chance="20" />
			<item id="93132" min="1" max="1" minEnchant="5" maxEnchant="5" chance="20" />
		</capsuled_items>
	</item>
	<item id="94434" name="Jewelry Box Lv. 6 (Time-limited)" nameRu="Ларец Ур. 6 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_bm_jewelbox_main_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="92040" min="1" max="1" chance="14.285" />
			<item id="92050" min="1" max="1" chance="14.285" />
			<item id="92060" min="1" max="1" chance="14.286" />
			<item id="92070" min="1" max="1" chance="14.286" />
			<item id="92080" min="1" max="1" chance="14.286" />
			<item id="92090" min="1" max="1" chance="14.286" />
			<item id="92100" min="1" max="1" chance="14.286" />
		</capsuled_items>
	</item>
	<item id="94435" name="Jewelry Box Lv. 4 (Time-limited)" nameRu="Ларец Ур. 4 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_bm_jewelbox_main_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="92039" min="1" max="1" chance="14.285" />
			<item id="92049" min="1" max="1" chance="14.285" />
			<item id="92059" min="1" max="1" chance="14.286" />
			<item id="92069" min="1" max="1" chance="14.286" />
			<item id="92079" min="1" max="1" chance="14.286" />
			<item id="92089" min="1" max="1" chance="14.286" />
			<item id="92099" min="1" max="1" chance="14.286" />
		</capsuled_items>
	</item>
	<item id="94436" name="Jewelry Box Lv. 3 (Time-limited)" nameRu="Ларец Ур. 3" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_bm_jewelbox_main_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="92038" min="1" max="1" chance="14.285" />
			<item id="92048" min="1" max="1" chance="14.285" />
			<item id="92058" min="1" max="1" chance="14.286" />
			<item id="92068" min="1" max="1" chance="14.286" />
			<item id="92078" min="1" max="1" chance="14.286" />
			<item id="92088" min="1" max="1" chance="14.286" />
			<item id="92098" min="1" max="1" chance="14.286" />
		</capsuled_items>
	</item>
	<item id="94437" name="+7 Talisman Pack (Time-limited)" nameRu="Сундук с Талисманом +7 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.bm_special_pendant_abradant_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="92449" min="1" max="1" chance="25" />
			<item id="92457" min="1" max="1" chance="25" />
			<item id="92466" min="1" max="1" chance="25" />
			<item id="94094" min="1" max="1" chance="25" />
		</capsuled_items>
	</item>
	<item id="94438" name="+5 Talisman Pack (Time-limited)" nameRu="Сундук с Талисманом +5 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.bm_special_pendant_abradant_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="STEEL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<set name="extractableCountMin" val="1" />
		<set name="extractableCountMax" val="1" />
		<capsuled_items>
			<item id="92447" min="1" max="1" chance="25" />
			<item id="92455" min="1" max="1" chance="25" />
			<item id="92464" min="1" max="1" chance="25" />
			<item id="94354" min="1" max="1" chance="25" />
		</capsuled_items>
	</item>
	<item id="94445" name="Augustina's Coupon (Time-limited)" nameRu="Купон Августины (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_level_bustup_ticket_i02" />
		<set name="material" val="PAPER" />
		<set name="weight" val="0" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94446" name="Augustina's Seeds (Time-limited)" nameRu="Семена Августины (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_help_seed_i00" />
		<set name="material" val="PAPER" />
		<set name="weight" val="0" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94447" name="Augustina's Daily Gift Box (Time-limited)" nameRu="Ежедневный Подарочный Сундук Августины (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nAugustina's Gift Box\n\n(Can be used only once a day, 14 times in total.) -->
		<set name="icon" val="icon.event_six_party_cube_i00" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="94448" name="Augustina's Gift Box (Time-limited)" nameRu="Подарочная Коробка Августины (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<set name="icon" val="icon.etc_pi_box_love_pack" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="FISH" />
		<set name="weight" val="0" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<capsuled_items>
			<!-- <item id="93274" min="10" max="10" chance="100" /> --> <!-- Sayha's Cookie -->
			<item id="90907" min="10" max="10" chance="100" /> <!-- Soulshot Ticket -->
			<item id="3031" min="300" max="300" chance="100" /> <!-- Spirit Ore -->
			<item id="94445" min="2" max="2" chance="100" /> <!-- Augustina's Coupon -->
			<item id="91857" min="100" max="100" chance="33" /> <!-- Mid-grade HP Recovery Potion -->
			<item id="94269" min="5" max="5" chance="33" /> <!-- Scroll: Enchant Attack -->
			<item id="94271" min="5" max="5" chance="33" /> <!-- Scroll: Enchant Protection -->
		</capsuled_items>
	</item>
	<item id="94449" name="Kit: Baium's Trial Coupon (Time-limited)" nameRu="Набор: Билет на Испытание Баюма (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nBaium's Trial Coupon (14 pcs.) -->
		<set name="icon" val="icon.etc_ticket_blue_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94450" name="Augustina's Support Box Lv. 40 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 40 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nDragon Belt Pack\nAugustina's Support Box Lv. 1 (required level: 50+) -->
		<set name="icon" val="icon.etc_pi_box_joy_pack" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94451" name="Augustina's Support Box Lv. 50 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 50 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nPackage: Cloak of Protection\nAugustina's Support Box Lv. 1 (required level: 55+) -->
		<set name="icon" val="icon.npoint_earthworm_30day_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94452" name="Augustina's Support Box Lv. 55 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 55 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nShiny Jewelry Box\nEnchanted B-grade Armor Coupon\nSayha's Cookie (10 pcs.)\nAugustina's Support Box Lv. 1 (required level: 60+) -->
		<set name="icon" val="icon.etc_pi_box_belief_pack" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94453" name="Augustina's Support Box Lv. 60 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 60 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nAgathion Pack\nEnchanted B-grade Armor Coupon\nSayha's Cookie (20 pcs.)\nAugustina's Support Box Lv. 1 (required level: 65+) -->
		<set name="icon" val="icon.npoint_valakas_30day_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94454" name="Augustina's Support Box Lv. 65 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 65 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nTalisman Fortune Box\nEnchanted B-grade Armor Coupon\nSayha's Cookie (40 pcs.)\nAugustina's Support Box Lv. 1 (required level: 70+) -->
		<set name="icon" val="icon.npoint_tauti_30day_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94455" name="Augustina's Support Box Lv. 70 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 70 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nPackage: Einhasad's Pendant Lv. 1\nEnchanted B-grade Armor Coupon\nSayha's Cookie (100 pcs.)\nAugustina's Support Box Lv. 1 (required level: 76+) -->
		<set name="icon" val="icon.npoint_antaras_30day_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94456" name="Augustina's Support Box Lv. 76 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 76 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nEnchant Kit: Circlet of Hero Lv. 1\nInventory Expansion Ticket Lv. 1\nSayha's Cookie (200 pcs.) -->
		<set name="icon" val="icon.etc_pi_box_wish_pack" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94457" name="Augustina's Support Box Lv. 70 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 70 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nDragon Belt Pack\nSupply Pouch (5 pcs.)\nSayha's Cookie (10 pcs.)\nAugustina's Support Box Lv. 2 (required level: 76+) -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94458" name="Augustina's Support Box Lv. 76 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 76 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nPackage: Cloak of Protection\nSupply Pouch (10 pcs.)\nSayha's Cookie (10 pcs.)\nAugustina's Support Box Lv. 2 (required level: 80+) -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94459" name="Augustina's Support Box Lv. 80 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 80 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nEnchanted B-grade Weapon Coupon\nSupply Pouch (5 pcs.)\nSayha's Cookie (10 pcs.)\nAugustina's Support Box Lv. 2 (required level: 81+) -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94460" name="Augustina's Support Box Lv. 81 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 81 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nPackage: Einhasad's Pendant Lv. 1\nSupply Pouch (10 pcs.)\nSayha's Cookie (20 pcs.)\nAugustina's Support Box Lv. 2 (required level: 82+) -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i01" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94461" name="Augustina's Support Box Lv. 82 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 82 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nEnchant Kit: Circlet of Hero\nSupply Pouch (20 pcs.)\nSayha's Cookie (30 pcs.)\nAugustina's Support Box Lv. 2 (required level: 83+) -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94462" name="Augustina's Support Box Lv. 83 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 83 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nPackage: Einhasad's Pendant Lv. 1\nSupply Pouch (30 pcs.)\nSayha's Cookie (40 pcs.)\nAugustina's Support Box Lv. 2 (required level: 84+) -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i05" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94463" name="Augustina's Support Box Lv. 84 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 84 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nEnchant Kit: Circlet of Hero\nSupply Pouch (40 pcs.)\nSayha's Cookie (50 pcs.)\nAugustina's Support Box Lv. 2 (required level: 85+) -->
		<set name="icon" val="icon.bm_sayha_silver_present" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94464" name="Augustina's Support Box Lv. 85 (Time-limited)" nameRu="Сундук Поддержки от Августины - Ур. 85 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nA-grade Weapon Coupon\nSupply Pouch (50 pcs.)\nSayha's Cookie (50 pcs.) -->
		<set name="icon" val="icon.bm_sayha_gold_present" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94465" name="Free Welcome Kit (Time-limited)" nameRu="Бесплатный Приветственный Набор (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items.\n\n<Guaranteed>\nEnchanted C-grade Weapon Coupon\nValakas' Necklace\nRefined Romantic Chapeau: Gold\nScroll: Boost Attack (50 pcs.)\nScroll: Boost Defense (50 pcs.)\nSayha's Cookie (200 pcs.)\nSpirit Ore (2000 pcs.)\nSoulshot Ticket (200 pcs.) -->
		<set name="icon" val="icon.etc_pi_gift_box_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94466" name="Daily Kit: Equipment (Time-limited)" nameRu="Ежедневный Набор: Снаряжение (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the specified item. -->
		<set name="icon" val="icon.freya_box_i01" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94467" name="Daily Kit: Augmentation (Time-limited)" nameRu="Ежедневный Набор: Зачарование (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the following items. -->
		<set name="icon" val="icon.freya_box_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94468" name="Pet Book: Chain Block" nameRu="Учебник для Питомцев - Блокировка Цепью" type="EtcItem">
		<!-- A book for learning the Chain Block pet skill.\n\nSkill effect: Binds the target and does not allow it to use range attacks.\n\nPet: Kookaburra -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94469" name="Pet Book: Blocked Target Change" nameRu="Учебник для Питомцев - Замена Блокированной Цели" type="EtcItem">
		<!-- A book for learning the Blocked Target Change pet skill.\n\nSkill effect: The target and the pet switch places.\n\nPet: Kookaburra -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94470" name="Pet Book: Hide" nameRu="Учебник для Питомцев - Исчезновение" type="EtcItem">
		<!-- A book for learning the Hide pet skill.\n\nSkill effect: Grants invisibility.\n\nPet: Tiger -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94471" name="Pet Book: Strong Bite" nameRu="Учебник для Питомцев - Сильный Укус" type="EtcItem">
		<!-- A book for learning the Strong Bite pet skill.\n\nSkill effect: Bites the enemy.\n\nPet: Tiger -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94472" name="Pet Book: Last Dire Claw" nameRu="Учебник для Питомцев - Последний Лютый Коготь" type="EtcItem">
		<!-- A book for learning the Last Dire Claw pet skill.\n\nSkill effect: Delivers a powerful attack with claws.\n\nPet: Wolf -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94473" name="Pet Book: Scout's Strike" nameRu="Учебник для Питомцев - Удар Дозорного" type="EtcItem">
		<!-- A book for learning the Scout's Strike pet skill.\n\nSkill effect: Attacks and stuns the target.\n\nPet: Hawk -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94474" name="Pet Book: Feather Storm" nameRu="Учебник для Питомцев - Буря Перьев" type="EtcItem">
		<!-- A book for learning the Feather Storm pet skill.\n\nSkill effect: Makes a tornado of feathers all around the target and decreases Speed.\n\nPet: Hawk -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94475" name="Pet Book: Exploding Feather" nameRu="Учебник для Питомцев - Взрывающееся Перо" type="EtcItem">
		<!-- A book for learning the Exploding Feather pet skill.\n\nSkill effect: Explodes feathers and deals damage to the enemy.\n\nPet: Hawk -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94476" name="Pet Book: Pulling Body" nameRu="Учебник для Питомцев - Притягивающее Тело" type="EtcItem">
		<!-- A book for learning the Pulling Body pet skill.\n\nSkill effect: Increases defense, immobilizes, and pulls nearby enemies.\n\nPet: Buffalo -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94477" name="Pet Book: Back Explosion" nameRu="Учебник для Питомцев - Взрыв Позади" type="EtcItem">
		<!-- A book for learning the Back Explosion pet skill.\n\nSkill effect: Has a certain chance to stun and pull the target after an explosion.\n\nPet: Dragon -->
		<set name="icon" val="icon.etc_codex_of_giant_i03" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94478" name="Pet Book: Steel Skin" nameRu="Учебник для Питомцев - Стальная Кожа" type="EtcItem">
		<!-- A book for learning the Steel Skin pet skill.\n\nSkill effect: Increases Defense according to the level.\n\nPet: All pets -->
		<set name="icon" val="icon.etc_codex_of_giant_i02" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94479" name="Pet XP Crystal (Exchangeable) - Not Available" nameRu="Кристалл Опыта Питомца (Можно обменять) - Недоступно" additionalName="Not used" additionalNameRu="Не используется" type="EtcItem">
		<!-- Applies the following effect.\n\n<When used>\nXP +100,000,000 for a summoned pet\n\nNote!\nCan be used only when a pet is summoned. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94480" name="Pet XP Crystal" nameRu="Кристалл Опыта Питомца" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Applies the following effect.\n\n<When used>\nXP +100,000,000 for a summoned pet\n\nNote!\nCan be used only when a pet is summoned. -->
		<set name="icon" val="icon.r99_soul_stone_piece_i04" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="PetItemSkills" />
		<skills>
			<skill id="40238" level="1" /> <!-- XP +100,000,000 -->
		</skills>
	</item>
	<item id="94481" name="Clan XP" nameRu="Опыт Клана" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Clan XP is granted as a reward. -->
		<set name="icon" val="icon.etc_bloodpledge_point_i00" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="shortcutToggleType" val="1" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="reuse_delay" val="120" />
		<skills>
			<skill id="90006" level="1" /> <!-- Clan XP -->
		</skills>
	</item>
	<item id="94482" name="Pet Treat (Time-limited)" nameRu="Лакомство для Питомца (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Can be exchanged through Pet Manager for various items such as Sayha's Cookie. -->
		<set name="icon" val="icon.etc_meat_of_monstereye_i00" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94483" name="Treat Coupon (Time-limited)" nameRu="Купон на Лакомство (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Can be exchanged through Pet Manager for a treat. -->
		<set name="icon" val="icon.etc_nobless_teleport_coupon_i00" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94484" name="Pet Buff Scroll (Time-limited)" nameRu="Свиток Положительных Эффектов для Питомца (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Applies a buff when used.\n\n<Buff effect>\nPet's HP +1000\nPet's P. Def. +300\nPet's M. Def. +300\nPet's HP Recovery +10\nWhen the buff is disappeared, Treat Coupon is granted.\n\nDuration: 3 h.\nCooldown: 1 min.\nThe effect disappears after death. -->
		<set name="icon" val="icon.newyear_dog" />
		<set name="shortcutToggleType" val="1" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94485" name="Pet Trap (Time-limited)" nameRu="Ловушка на Питомца (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Applies a buff when used.\n\n<Buff effect>\nHas a certain chance to capture a pet. -->
		<set name="icon" val="icon.energy_condenser_i00" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="94486" name="Pet Training Box (Time-limited)" nameRu="Коробка для Тренировки Питомца (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain one of the following items. -->
		<set name="icon" val="icon.etc_treasure_box_i01" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94487" name="Pet Book Box (Time-limited)" nameRu="Коробка с Учебником для Питомцев (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain one of the following items. -->
		<set name="icon" val="icon.bm_pirate_battle_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94488" name="Kit: Pet Support (Time-limited)" nameRu="Набор: Поддержка Питомца (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain the specified item. -->
		<set name="icon" val="icon.etc_grave_goods_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94489" name="Mysterious Pack Lv. 1 (Time-limited)" nameRu="Мистический Сундук Ур. 1 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Double-click to obtain one of the following items. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94490" name="Mysterious Pack Lv. 2 (Time-limited)" nameRu="Мистический Сундук Ур. 2 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Unused packs can be exchanged through Game Assistant for various items.\nDouble-click to obtain one of the specified items. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94491" name="Mysterious Pack Lv. 3 (Time-limited)" nameRu="Мистический Сундук Ур. 3 (Временно)" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- Unused packs can be exchanged through Game Assistant for various items.\nDouble-click to obtain one of the specified items. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i03" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94492" name="Mysterious Pack Lv. 4 (Time-limited)" nameRu="Мистический Сундук Ур. 4 (Временно)" type="EtcItem">
		<!-- Unused packs can be exchanged through Game Assistant for various items.\nDouble-click to obtain one of the specified items. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i01" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94493" name="Mysterious Pack Lv. 5 (Time-limited)" nameRu="Мистический Сундук Ур. 5 (Временно)" type="EtcItem">
		<!-- Unused packs can be exchanged through Game Assistant for various items.\nDouble-click to obtain one of the specified items. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94494" name="Mysterious Pack Lv. 6 (Time-limited)" nameRu="Мистический Сундук Ур. 6 (Временно)" type="EtcItem">
		<!-- Unused packs can be exchanged through Game Assistant for various items.\nDouble-click to obtain one of the specified items. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i05" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94495" name="Mysterious Pack Lv. 7 (Time-limited)" nameRu="Мистический Сундук Ур. 7 (Временно)" type="EtcItem">
		<!-- Unused packs can be exchanged through Game Assistant for various items.\nDouble-click to obtain one of the specified items. -->
		<set name="icon" val="icon.bm_sayha_silver_present" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94496" name="Mysterious Pack Lv. 8 (Time-limited)" nameRu="Мистический Сундук Ур. 8 (Временно)" type="EtcItem">
		<!-- Unused packs can be exchanged through Game Assistant for various items.\nDouble-click to obtain various items as well as specified. -->
		<set name="icon" val="icon.bm_sayha_gold_present" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94497" name="Spellbook: Life Force Harmony" nameRu="Колдовская Книга - Баланс Жизненной Силы" type="EtcItem">
		<!-- A spellbook for learning the Life Force Harmony skill.\n\n<Classes>\nTitan\nGrand Khavatari\n\n<Skill effect>\nIncreases Max HP and other stats depending on current HP. -->
		<set name="icon" val="icon.etc_spell_books_hfighter_i00" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94498" name="Spellbook: Titan Champion" nameRu="Колдовская Книга - Чемпион Титанов" type="EtcItem">
		<!-- A spellbook for learning the Titan Champion skill.\n\n<Classes>\nTitan\n\n<Skill effect>\nSignificantly increases P. Atk., P. Critical Rate, P. Critical Damage, Max HP and Speed. -->
		<set name="icon" val="icon.etc_spell_books_hfighter_i00" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="94499" name="Spellbook: Titanic Weapon" nameRu="Колдовская Книга - Гигантское Оружие" type="EtcItem">
		<!-- A spellbook for learning the Titanic Weapon skill.\n\n<Classes>\nTitan\n\n<Skill effect>\nIncreases P. Atk. When a two-handed sword is used, increases P. Atk., Atk. Spd. and attack range. -->
		<set name="icon" val="icon.etc_spell_books_hfighter_i00" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
</list>
