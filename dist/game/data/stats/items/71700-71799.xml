<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="71700" name="Festival Ticket" type="EtcItem">
		<!-- A ticket that allows you to participate in the festive activities. -->
		<set name="icon" val="BranchSys2.Icon.br_weddin_ticket_i00" />
		<set name="material" val="PAPER" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71701" name="Fire Dragon Pendant Lv. 1" type="Armor">
		<!-- A pendant that contains the energy of Fire Dragon. Can be enchanted up to +10 with Pendant Polish/ Shining Pendant Polish. Two pendants of the same level can be combined by the Dimensional Merchant to add new effects. The number of effects is increased by the enchanting. Enchanting is safe up to +1, after that in case of failure, the item disappears. <Note!>. After combining the pendant's enchant value is reset. Enhanced pendants can't be used for combining. -->
		<set name="icon" val="BranchIcon.Icon.bm_pendant_pve" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="chest" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CRYSTAL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pDef">28</stat>
		</stats>
	</item>
	<item id="71702" name="Water Dragon Pendant Lv. 1" type="Armor">
		<!-- A pendant that contains the Water Dragon's energy. Can be enchanted up to +10 with Pendant Polish/ Shining Pendant Polish. Two pendants of the same level can be combined by the Dimensional Merchant to add new effects. The number of effects is increased by the enchanting. Enchanting is safe up to +1, after that in case of failure, the item disappears. <Note!>. After combining the pendant's enchant value is reset. Enhanced pendants can't be used for combining. -->
		<set name="icon" val="BranchIcon.Icon.bm_pendant_pvp_blue" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="chest" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CRYSTAL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pDef">28</stat>
		</stats>
	</item>
	<item id="71703" name="Wind Dragon Pendant Lv. 1" type="Armor">
		<!-- A pendant that contains the Wind Dragon's energy. Can be enchanted up to +10 with Pendant Polish/ Shining Pendant Polish. Two pendants of the same level can be combined by the Dimensional Merchant to add new effects. The number of effects is increased by the enchanting. Enchanting is safe up to +1, after that in case of failure, the item disappears. <Note>. After combining the pendant's enchant value is reset. Enhanced pendants can't be used for combining. -->
		<set name="icon" val="BranchIcon.Icon.bm_pendant_pve_white" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="chest" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CRYSTAL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pDef">28</stat>
		</stats>
	</item>
	<item id="71704" name="Land Dragon Pendant Lv. 1" type="Armor">
		<!-- A pendant that contains the Land Dragon's energy. Can be enchanted up to +10 with Pendant Polish/ Shining Pendant Polish. Two pendants of the same level can be combined by the Dimensional Merchant to add new effects. The number of effects is increased by the enchanting. Enchanting is safe up to +1, after that in case of failure, the item disappears. <Note>. After combining the pendant's enchant value is reset. Enhanced pendants can't be used for combining. -->
		<set name="icon" val="BranchIcon.Icon.bm_pendant_pvp" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="chest" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CRYSTAL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pDef">28</stat>
		</stats>
	</item>
	<item id="71705" name="Shadow Master" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_thief" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71706" name="Admiral" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_navy" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71707" name="Skull" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_skeleton" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71708" name="Alice" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_alice" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71709" name="Demon" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_devil" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71710" name="Steampunk" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_steampunk" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71711" name="Nymph" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_nymph" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71712" name="Maid" type="Armor">
		<!-- Appearance for the transformation system. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_maid" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="HEAVY" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BRONZE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71713" name="+16 Weapon Coupon (No-grade)" additionalName="Event" type="EtcItem">
		<!-- Can be exchanged for a No-grade weapon +16 in the L-Coin Store. -->
		<set name="icon" val="BranchIcon.Icon.rp_necklace_of_einhasad_i00" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71714" name="+7 Weapon Coupon (C-grade)" additionalName="Event" type="EtcItem">
		<!-- Can be exchanged for a C-grade weapon +7 in the L-Coin Store. -->
		<set name="icon" val="BranchIcon.Icon.rp_ring_of_maphr_i00" />
		<set name="material" val="PAPER" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71715" name="Reinforced Compound Bow Chest" type="EtcItem">
		<!-- Double-click to obtain Compound Bow +16 and Infinite Wooden Quiver. -->
		<set name="icon" val="icon.weapon_composition_bow_i00" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71716" name="Eminence Bow +7" type="EtcItem">
		<!-- Double-click to obtain Eminence Bow +7 and Infinite Steel Quiver. -->
		<set name="icon" val="icon.weapon_eminence_bow_i00" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71717" name="ADEN Gatcha Ticket" additionalName="Event" type="EtcItem">
		<!-- Can be exchanged for 300 L-Coins and a valuable item by any Dimensional Merchant. -->
		<set name="icon" val="icon.bm_jewel_ticket" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71718" name="Transformation Sealbook: Shadow Master" additionalName="Standard - Not Available" type="EtcItem">
		<!-- Double-click to obtain the standard Shadow Master appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_thief" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71719" name="Transformation Sealbook: Shadow Master" additionalName="High-grade - Not Available" type="EtcItem">
		<!-- Double-click to obtain the high-grade Shadow Master appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_thief" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71720" name="Transformation Sealbook: Shadow Master" additionalName="Rare - Not Available" type="EtcItem">
		<!-- Double-click to obtain the rare Shadow Master appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_thief" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71721" name="Transformation Sealbook: Shadow Master" additionalName="Legendary" type="EtcItem">
		<!-- Double-click to obtain the legendary Shadow Master appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_thief" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71722" name="Transformation Sealbook: Shadow Master" additionalName="Mythic" type="EtcItem">
		<!-- Double-click to obtain the mythic Shadow Master appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_thief" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71723" name="Transformation Sealbook: Admiral" additionalName="Standard - Not Available" type="EtcItem">
		<!-- Double-click to obtain the standard Admiral appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_navy" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71724" name="Transformation Sealbook: Admiral" additionalName="High-grade - Not Available" type="EtcItem">
		<!-- Double-click to obtain the high-grade Admiral appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_navy" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71725" name="Transformation Sealbook: Admiral" additionalName="Rare - Not Available" type="EtcItem">
		<!-- Double-click to obtain the rare Admiral appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_navy" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71726" name="Transformation Sealbook: Admiral" additionalName="Legendary" type="EtcItem">
		<!-- Double-click to obtain the legendary Admiral appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_navy" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71727" name="Transformation Sealbook: Admiral" additionalName="Mythic" type="EtcItem">
		<!-- Double-click to obtain the mythic Admiral appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_navy" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71728" name="Transformation Sealbook: Nymph" additionalName="Standard - Not Available" type="EtcItem">
		<!-- Double-click to obtain the standard Nymph appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_nymph" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71729" name="Transformation Sealbook: Nymph" additionalName="High-grade - Not Available" type="EtcItem">
		<!-- Double-click to obtain the high-grade Nymph appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_nymph" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71730" name="Transformation Sealbook: Nymph" additionalName="Rare - Not Available" type="EtcItem">
		<!-- Double-click to obtain the rare Nymph appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_nymph" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71731" name="Transformation Sealbook: Nymph" additionalName="Legendary" type="EtcItem">
		<!-- Double-click to obtain the legendary Nymph appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_nymph" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71732" name="Transformation Sealbook: Nymph" additionalName="Mythic" type="EtcItem">
		<!-- Double-click to obtain the mythic Nymph appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_nymph" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71733" name="Transformation Sealbook: Demon" additionalName="Standard - Not Available" type="EtcItem">
		<!-- Double-click to obtain the standard Demon appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_devil" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71734" name="Transformation Sealbook: Demon" additionalName="High-grade - Not Available" type="EtcItem">
		<!-- Double-click to obtain the high-grade Demon appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_devil" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71735" name="Transformation Sealbook: Demon" additionalName="Rare - Not Available" type="EtcItem">
		<!-- Double-click to obtain the rare Demon appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_devil" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71736" name="Transformation Sealbook: Demon" additionalName="Legendary - Not Available" type="EtcItem">
		<!-- Double-click to obtain the legendary Demon appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_devil" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71737" name="Transformation Sealbook: Demon" additionalName="Mythic" type="EtcItem">
		<!-- Double-click to obtain the mythic Demon appearance. -->
		<set name="icon" val="BranchIcon.Icon.g_bm_costume_devil" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71738" name="Fire Dragon Pendant Chest +6" additionalName="Event" type="EtcItem">
		<!-- Double-click to obtain Fire Dragon Pendant +6. -->
		<set name="icon" val="icon.bm_pendant_pve_box" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71739" name="+8 Weapon Pack (S-grade)" additionalName="Event" type="EtcItem">
		<!-- Double-click to obtain a +8 S-grade weapon. -->
		<set name="icon" val="icon.bm_gold_lure_special_box" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71740" name="+5 Dragon Pendant Chest Lv. 4" additionalName="Event" type="EtcItem">
		<!-- Double-click to obtain +5 Dragon Pendant - Attack/ Magic Power Lv. 4. -->
		<set name="icon" val="icon.bm_pendant_pve_box" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71741" name="Dragon Scroll" additionalName="Event" type="EtcItem">
		<!-- For 20 min., P./ M. Atk. +150, P./ M. Critical Rate +30, Atk. Spd./ Casting Spd. +50, P./ M. Def. +200, Max HP/ MP/ CP +500. Cooldown: 1 h. The effect remains after death. -->
		<set name="icon" val="icon.ev_charge_scroll" />
		<set name="shortcutToggleType" val="1" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="PAPER" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="handler" val="ItemSkills" />
		<set name="immediate_effect" val="true" />
		<skills>
			<skill id="59634" level="1" /> <!-- Dragon Scroll (Event) -->
		</skills>
	</item>
	<item id="71742" name="Dimensional Talisman Lv. 1" type="Armor">
		<!-- P./ M. Def. +25, P./ M. Evasion. +1, Max HP/ MP/ CP +150, Speed +5, Atk. Spd./ Casting Spd. +10, Acquired XP/ SP +5%. Activates Dimensional Border Power Lv. 1 (increases attack power with a certain chance). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="1" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="1" /> <!-- Dimensional Guard -->
			<skill id="35024" level="12" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71743" name="Dimensional Talisman Lv. 2" type="Armor">
		<!-- P./ M. Def. +30, P./ M. Evasion. +2, Max HP/ MP/ CP +200, Speed +7, Atk. Spd./ Casting Spd. +15, Acquired XP/ SP +7%. Activates Dimensional Border Power Lv. 1 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 2 (temporarily increases Vital Point Attack Resistance). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="1" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="2" /> <!-- Dimensional Guard -->
			<skill id="35024" level="13" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71744" name="Dimensional Talisman Lv. 3" type="Armor">
		<!-- P./ M. Def. +35, P./ M. Evasion. +2, Max HP/ MP/ CP +250, Speed +7, Atk. Spd./ Casting Spd. +15, Acquired XP/ SP +10%. Activates Dimensional Border Power Lv. 2 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 3 (temporarily increases Vital Point Attack Resistance). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="2" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="3" /> <!-- Dimensional Guard -->
			<skill id="35024" level="14" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71745" name="Dimensional Talisman Lv. 4" type="Armor">
		<!-- P./ M. Def. +40, P./ M. Evasion. +2, Max HP/ MP/ CP +300, Speed +7, Atk. Spd./ Casting Spd. +15, Acquired XP/ SP +12%. Activates Dimensional Border Power Lv. 3 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 4 (temporarily increases Vital Point Attack Resistance). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="3" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="4" /> <!-- Dimensional Guard -->
			<skill id="35024" level="15" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71746" name="Dimensional Talisman Lv. 5" type="Armor">
		<!-- P./ M. Def. +45, P./ M. Evasion. +3, Max HP/ MP/ CP +400, Speed +8, Atk. Spd./ Casting Spd. +20, Acquired XP/ SP +15%. Activates Dimensional Border Power Lv. 4 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 5 (temporarily increases Vital Point Attack Resistance and decreases Received P./ M. Critical Damage). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="4" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="5" /> <!-- Dimensional Guard -->
			<skill id="35024" level="16" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71747" name="Dimensional Talisman Lv. 6" type="Armor">
		<!-- P./ M. Def. +50, P./ M. Evasion. +3, Max HP/ MP/ CP +500, Speed +8, Atk. Spd./ Casting Spd. +30, Acquired XP/ SP +17%. Activates Dimensional Border Power Lv. 5 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 6 (temporarily increases Vital Point Attack Resistance and decreases Received P./ M. Critical Damage). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="5" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="6" /> <!-- Dimensional Guard -->
			<skill id="35024" level="17" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71748" name="Dimensional Talisman Lv. 7" type="Armor">
		<!-- P./ M. Def. +70, P./ M. Evasion. +4, Max HP/ MP/ CP +1000, Speed +9, Atk. Spd. +40, Casting Spd. +50, Acquired XP/ SP +20%. Activates Dimensional Border Power Lv. 6 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 7 (temporarily increases Vital Point Attack Resistance and decreases Received P./ M. Critical Damage). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="6" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="7" /> <!-- Dimensional Guard -->
			<skill id="35024" level="18" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71749" name="Dimensional Talisman Lv. 8" type="Armor">
		<!-- P./ M. Def. +80, P./ M. Evasion. +4, Max HP/ MP/ CP +1300, Speed +9, Atk. Spd. +40, Casting Spd. +65, Acquired XP/ SP +25%. Activates Dimensional Border Power Lv. 7 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 8 (temporarily increases Vital Point Attack Resistance and M. Damage Resistance, decreases Received P./ M. Critical Damage). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="7" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="8" /> <!-- Dimensional Guard -->
			<skill id="35024" level="19" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71750" name="Dimensional Talisman Lv. 9" type="Armor">
		<!-- P./ M. Def. +90, P./ M. Evasion. +4, Max HP/ MP/ CP +1600, Speed +9, Atk. Spd. +50, Casting Spd. +80, Acquired XP/ SP +30%. Activates Dimensional Border Power Lv. 8 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 9 (temporarily increases Vital Point Attack Resistance and M. Damage Resistance, decreases Received P./ M. Critical Damage). Can be upgraded by Alchemist Veruti in Giran. The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="8" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="9" /> <!-- Dimensional Guard -->
			<skill id="35024" level="20" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71751" name="Dimensional Talisman Lv. 10" type="Armor">
		<!-- P./ M. Def. +100, P./ M. Evasion. +5, Max HP/ MP/ CP +2000, Speed +10, Atk. Spd. +50, Casting Spd. +100, Acquired XP/ SP +40%. Activates Dimensional Border Power Lv. 9 (increases attack power with a certain chance). Allows use of Dimensional Guard Lv. 10 (temporarily increases Vital Point Attack Resistance and M. Damage Resistance, decreases Received P./ M. Critical Damage). Allows use of Dimensional Barrier (for 15 sec. defends from 10 attacks). The effect doesn't stack with that of Talisman of Border, the Dimensional Talisman effect takes precedence. -->
		<set name="icon" val="icon.bm_dimension_tallisman" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="deco1" />
		<skills>
			<skill id="35027" level="9" /> <!-- Dimensional Border Power -->
			<skill id="51299" level="10" /> <!-- Dimensional Guard -->
			<skill id="51298" level="1" /> <!-- Dimensional Warp -->
			<skill id="35024" level="21" /> <!-- Dimensional Talisman -->
		</skills>
	</item>
	<item id="71752" name="Dimensional Spirit" type="EtcItem">
		<!-- Required for creating and upgrading Dimensional Talisman. Take it to Alchemist Veruti in Giran to enchant Dimensional Talisman. Take it to Head Blacksmith Ferris in Aden to transform Talisman of Border to Dimensional Talisman. -->
		<set name="icon" val="icon.bm_dimension_soul" />
		<set name="material" val="PAPER" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71753" name="Talisman of Border Pack" type="EtcItem">
		<!-- Double-click to obtain Talisman of Border Lv. 1. -->
		<set name="icon" val="icon.bm_special_pendant_abradant_box" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71754" name="Good Luck Chest" type="EtcItem">
		<!-- Double-click to obtain Sayha's Blessing. There is also a chance to obtain one of the following items: +16 Weapon Pack (C/ B-grade), Brooch Lv. 3, Wind/ Fire/ Earth/ Water Spirit Evolution Stone, Weapon Pack (C/ B-grade), Armor Pack (B-grade), Scroll: Enchant Weapon (B/ C-grade), Scroll: Enchant Armor (B-grade), Sayha's Cloak Coupon, Agathion Coupon, Eva's Hair Accessory Pack, Radiant Jewelry Box, Talisman of Aden Enchant Pack, Rice Cake of Flaming Fighting Spirit, Dragon Scroll, Sayha's Effect Scroll, Sayha's Storm Lv. 3, Soulshot Ticket. -->
		<set name="icon" val="icon.event_six_party_box_i00" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71755" name="Samurai Long Sword" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_samurai_old_longsword_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="SWORD" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="ORIHARUKON" />
		<set name="weight" val="1380" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="rhand" />
		<stats>
			<stat type="pAtk">156</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="71756" name="Mace of Underworld" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_mace_of_underworld_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="1090" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="rhand" />
		<set name="is_magic_weapon" val="true" />
		<stats>
			<stat type="pAtk">111</stat>
			<stat type="mAtk">111</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">500</stat>
		</stats>
	</item>
	<item id="71757" name="Demon's Staff" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_demons_staff_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="WOOD" />
		<set name="weight" val="990" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_magic_weapon" val="true" />
		<stats>
			<stat type="pAtk">152</stat>
			<stat type="mAtk">134</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="71758" name="Crystal Dagger" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_crystal_dagger_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="DAGGER" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="CRYSTAL" />
		<set name="weight" val="1000" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="rhand" />
		<stats>
			<stat type="pAtk">136</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">12</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">433</stat>
			<stat type="randomDamage">5</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="71759" name="Eminence Bow" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_eminence_bow_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BOW" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="CHRYSOLITE" />
		<set name="weight" val="1720" />
		<set name="soulshots" val="2" />
		<set name="spiritshots" val="1" />
		<set name="mp_consume" val="3" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">323</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">12</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">293</stat>
			<stat type="randomDamage">5</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="71760" name="Orcish Poleaxe" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_orcish_poleaxe_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="POLE" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="BLOOD_STEEL" />
		<set name="weight" val="1950" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">156</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">8</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="71761" name="Katana*Katana" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_dual_sword_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="DUAL" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="2270" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">190</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="71762" name="Yaksa Mace" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_yaksa_mace_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="1640" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="rhand" />
		<stats>
			<stat type="pAtk">156</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">20</stat>
		</stats>
		<skills>
			<skill id="3599" level="1" /> <!-- Polearm Multi-attack -->
		</skills>
	</item>
	<item id="71763" name="Berserker Blade" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_berserker_blade_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="SWORD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="ORIHARUKON" />
		<set name="weight" val="1380" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">190</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="71764" name="Great Pata" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_great_pata_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="DUALFIST" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="CHRYSOLITE" />
		<set name="weight" val="1460" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">190</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">5</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="71765" name="Admiral's Estoc" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_blink_slasher_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1300" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="rhand" />
		<set name="weapon_type" val="RAPIER" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<stats>
			<stat type="pAtk">149</stat>
			<stat type="mAtk">110</stat>
			<stat type="rCrit">10</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">406</stat>
			<stat type="randomDamage">20</stat>
		</stats>
	</item>
	<item id="71766" name="Saber Tooth" additionalName="Sealed" type="Weapon">
		<!-- Special weapon created to celebrate the opening of the Essence server. Cannot be enchanted. -->
		<set name="icon" val="icon.weapon_invincible_blade_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="ANCIENTSWORD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="C" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1530" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">172</stat>
			<stat type="mAtk">91</stat>
			<stat type="rCrit">10</stat>
			<stat type="accCombat">5</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">15</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="71767" name="Samurai Black Long Sword" additionalName="30 days" type="Weapon">
		<!-- Available for 30 days. Cannot be enchanted or endued with special characteristics. -->
		<set name="icon" val="icon.weapon_dual_sword_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="ANCIENTSWORD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="2340" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">155</stat>
			<stat type="mAtk">70</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="71768" name="Black Long Samurai Sword Box" additionalName="30 days" type="EtcItem">
		<!-- Double-click on the item to get Black Samurai Long Sword (30-day). Can be stored in a private warehouse. You cannot exchange, drop or sell Black Samurai Long Sword. -->
		<set name="icon" val="icon.etc_pi_gift_box_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
	</item>
	<item id="71769" name="Weapon Coupon (B-grade)" additionalName="Event" type="EtcItem">
		<!-- Take it to the Dimensional Merchant to exchange for a B-grade weapon. -->
		<set name="icon" val="icon.etc_exchange_ticket_i01" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
	</item>
	<item id="71770" name="Newbie Pack" additionalName="Event" type="EtcItem">
		<!-- Double-click to obtain Samurai Black Long Sword (30 days), Moon Armor set, 1670 Soulshots, 15 Special Hero's Fruits. -->
		<set name="icon" val="icon.etc_pi_gift_box_i04" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
	</item>
	<item id="71771" name="Hero's Pack" additionalName="Event" type="EtcItem">
		<!-- Double-click to obtain 10 Dandy's Home Run Balls, 10 Hero's XP/ SP Scroll, Newbie Amulet (30-day). -->
		<set name="icon" val="icon.etc_pi_gift_box_i00" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
	</item>
	<item id="71772" name="Equipment Package" additionalName="Event" type="EtcItem">
		<!-- Double-click to obtain 2 Weapon Coupons (B-grade), a box with a temporary warrior armor set, temporary rare accessory Lv. 1. -->
		<set name="icon" val="icon.etc_pi_gift_box_i01" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
	</item>
	<item id="71773" name="Newbie Amulet" additionalName="30 days" type="EtcItem">
		<!-- Available for 30 days. Acquired XP/ SP (hunting) +50%. Required level: 1-40. -->
		<set name="icon" val="BranchSys2.lcon.br_life_rune_i00" />
		<set name="material" val="PAPER" />
		<set name="weight" val="50" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="71774" name="Aden Supply Box" type="EtcItem">
		<!-- Double-click to obtain one of the following items: Talisman of Baium, Zaken's Earring, Queen Ant's Ring, Exquisite Jewel Box, Rice Cake of Flaming Fighting Spirit, Dragon Scroll, Sayha's Effect Scroll. -->
		<set name="icon" val="icon.etc_treasure_box_i00" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71775" name="Golden Ticket" additionalName="Event" type="EtcItem">
		<!-- Reward for completing the Golden Quest during the Golden Week event. Can be exchanged for various items by the Dimensional Merchant. -->
		<set name="icon" val="icon.bm_royal_gold_ticket_all" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71776" name="Kamael's Gift" type="EtcItem">
		<!-- Double-click to obtain one of the various items. -->
		<set name="icon" val="icon.npoint_valakas_30day_box" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71777" name="Improved Supply Box" type="EtcItem">
		<!-- Double-click to obtain something valuable. -->
		<set name="icon" val="icon.event_high_oriental_park_i00" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
	</item>
	<item id="71778" name="Sealed Samurai Long Sword Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Samurai Long Sword enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71779" name="Sealed Mace of Underworld Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Mace of Underworld +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71780" name="Sealed Demon's Staff Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Demon's Staff enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71781" name="Sealed Crystal Dagger Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Crystal Dagger enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71782" name="Sealed Eminence Bow Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Eminence Bow enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71783" name="Sealed Orcish Poleaxe Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Orcish Poleaxe enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71784" name="Sealed Katana*Katana Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Katana*Katana enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71785" name="Sealed Yaksa Mace Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Yaksa Mace enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71786" name="Sealed Berserker Blade Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Berserker Blade enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71787" name="Sealed Great Pata Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Great Pata enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71788" name="Sealed Admiral's Estoc Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Admiral's Estoc enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71789" name="Sealed Saber Tooth Pack" additionalName="4" type="EtcItem">
		<!-- Double-click to obtain Sealed Saber Tooth enchanted to +4. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i02" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71790" name="Sealed Samurai Long Sword Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Samurai Long Sword enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71791" name="Sealed Mace of Underworld Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Mace of Underworld enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71792" name="Sealed Demon's Staff Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Demon's Staff enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71793" name="Sealed Crystal Dagger Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Crystal Dagger enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71794" name="Sealed Eminence Bow Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Eminence Bow enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71795" name="Sealed Orcish Poleaxe Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Orcish Poleaxe enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71796" name="Sealed Katana*Katana Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Katana*Katana enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71797" name="Sealed Yaksa Mace Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Yaksa Mace enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71798" name="Sealed Berserker Blade Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Berserker Blade enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="71799" name="Sealed Great Pata Pack" additionalName="7" type="EtcItem">
		<!-- Double-click to obtain Sealed Great Pata enchanted to +7. -->
		<set name="icon" val="BranchIcon.Icon.etc_vip_present_i04" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="FISH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
</list>
