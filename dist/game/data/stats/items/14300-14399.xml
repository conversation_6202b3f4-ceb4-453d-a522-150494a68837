<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="14327" name="<PERSON><PERSON>'s Blade" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_sirr_blade_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="SWORD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="1300" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">251</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14328" name="Sword of Ipos" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_sword_of_ipos_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="SWORD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="BONE" />
		<set name="weight" val="1820" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">305</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14330" name="Behemoth' Tuning Fork" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_tuning_fork_of_behemoth_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="1890" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">305</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14331" name="Naga Storm" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_naga_storm_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="DAGGER" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="ORIHARUKON" />
		<set name="weight" val="930" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">220</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">12</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">433</stat>
			<stat type="randomDamage">5</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="14332" name="Tiphon's Spear" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_tiphon_spear_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="POLE" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="CHRYSOLITE" />
		<set name="weight" val="1820" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">251</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">8</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
		<skills>
			<skill id="3599" level="1" /> <!-- Polearm Multi-attack -->
		</skills>
	</item>
	<item id="14333" name="Shyeed's Bow" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_shyid_bow_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BOW" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="ORIHARUKON" />
		<set name="weight" val="1640" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="4" />
		<set name="spiritshots" val="3" />
		<set name="mp_consume" val="5" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">570</stat>
			<stat type="mAtk">133</stat>
			<stat type="rCrit">12</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">227</stat>
			<stat type="randomDamage">5</stat>
			<stat type="pAtkRange">500</stat>
		</stats>
	</item>
	<item id="14334" name="Sobekk's Hurricane" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_sobekk_hurricane_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="DUALFIST" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="ADAMANTAITE" />
		<set name="weight" val="1330" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">305</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">5</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14335" name="Themis' Tongue" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_tongue_of_themis_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="SWORD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="ADAMANTAITE" />
		<set name="weight" val="820" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<set name="is_magic_weapon" val="true" />
		<stats>
			<stat type="pAtk">202</stat>
			<stat type="mAtk">161</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14336" name="Cabrio's Hand" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_hand_of_cabrio_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="1510" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<set name="is_magic_weapon" val="true" />
		<stats>
			<stat type="pAtk">202</stat>
			<stat type="mAtk">161</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14338" name="Tallum Blade*Damascus" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_dual_sword_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="DUAL" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="FINE_STEEL" />
		<set name="weight" val="1890" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">305</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">8</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14339" name="Éclair Bijou" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_greed_stinger_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="RAPIER" />
		<set name="bodypart" val="rhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1300" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">228</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">10</stat>
			<stat type="accCombat">-1.5</stat>
			<stat type="pAtkSpd">406</stat>
			<stat type="randomDamage">40</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="14341" name="Screaming Vengeance" type="Weapon">
		<!-- For NPC -->
		<set name="icon" val="icon.weapon_soul_shooter_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="CROSSBOW" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1640" />
		<set name="price" val="26968400" />
		<set name="soulshots" val="3" />
		<set name="spiritshots" val="3" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">318</stat>
			<stat type="mAtk">121</stat>
			<stat type="rCrit">10</stat>
			<stat type="accCombat">-1.5</stat>
			<stat type="pAtkSpd">303</stat>
			<stat type="randomDamage">10</stat>
			<stat type="pAtkRange">400</stat>
		</stats>
	</item>
	<item id="14348" name="Majestic Earring" type="Armor">
		<!-- For NPC -->
		<set name="icon" val="icon.accessary_inferno_earing_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="SILVER" />
		<set name="weight" val="150" />
		<set name="price" val="1498000" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="mDef">63</stat>
			<stat type="maxMp">25</stat>
		</stats>
	</item>
	<item id="14349" name="Majestic Ring" type="Armor">
		<!-- For NPC -->
		<set name="icon" val="icon.accessary_inferno_ring_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rfinger;lfinger" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="price" val="999000" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="mDef">42</stat>
			<stat type="maxMp">17</stat>
		</stats>
	</item>
	<item id="14350" name="Majestic Necklace" type="Armor">
		<!-- For NPC -->
		<set name="icon" val="icon.accessary_inferno_necklace_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="neck" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="A" />
		<set name="material" val="SILVER" />
		<set name="weight" val="150" />
		<set name="price" val="1998000" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="mDef">85</stat>
			<stat type="maxMp">33</stat>
		</stats>
	</item>
</list>
