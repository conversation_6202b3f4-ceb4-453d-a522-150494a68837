<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="20900" name="Santa Hat - Not Available" nameRu="Шапка Седобородого Деда Мороза - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- Hat made for a merry Christmas. Occupies 2 Hair Accessory slots. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="hair" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="WOOD" />
		<set name="weight" val="10" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="20160" />
	</item>
	<item id="20901" name="Santa Hat Set - Not Available" nameRu="Комплект Седобородого Деда Мороза - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Hat provided together with Santa's Red Nose. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ExtractableItems" />
		<capsuled_items>
			<item id="20900" min="1" max="1" chance="100" /> <!-- Santa Hat - 14-day -->
			<item id="14611" min="1" max="1" chance="100" /> <!-- Santa's Red Nose -->
		</capsuled_items>
	</item>
</list>
