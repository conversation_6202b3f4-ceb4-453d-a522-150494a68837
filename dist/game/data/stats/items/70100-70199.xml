<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="70100" name="Stockings of Devotion - Not Available" nameRu="Штаны Преданности - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- A 7-day item. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="armor_type" val="MAGIC" />
		<set name="bodypart" val="legs" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CLOTH" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="10080" />
		<stats>
			<stat type="pDef">19</stat>
			<stat type="maxMp">42</stat>
		</stats>
	</item>
	<item id="70101" name="Refined Romantic Chapeau: Gold - Not Available" nameRu="Элегантная Золотая Модная Шляпа - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- A 7-day item. Requires 2 hair accessory slots. Cannot be exchanged, dropped, or sold in a private store. Can be stored in a private warehouse. <Active Skills>. 1. Holiday Hat: resurrects a dead target and restores 50% XP. Consumes 1 Spirit Ore(s). Reuse Time: 30 min. 2 Overwhelming Power of a Holiday Hat: increases Acquired XP/ SP by 5% for 20 minutes. Reuse Time: 60 min. 3. Side Effect: makes character's head bigger for 10 minutes. Reuse Time: 30 min. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="hair" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="WOOD" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="10080" />
		<skills>
			<skill id="39043" level="1" /> <!-- Side Effect -->
			<skill id="39044" level="1" /> <!-- Ability of Resurrection -->
			<skill id="39045" level="1" /> <!-- Holiday Hat -->
		</skills>
	</item>
	<item id="70102" name="Adventurer's Greater Haste Potion" nameRu="Большое Зелье Скор. Атк. Путешественника" type="EtcItem">
		<!-- Increases Atk. Spd. for 20 minutes. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_reagent_green_i00" />
		<set name="shortcutToggleType" val="1" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="etcitem_type" val="POTION" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="LIQUID" />
		<set name="weight" val="5" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_oly_restricted" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="POTION" />
		<set name="for_npc" val="true" />
		<cond addName="1" msgId="113">
			<player flyMounted="false" />
		</cond>
		<skills>
			<skill id="55235" level="1" /> <!-- Adventurer Potion Atk. Spd. lvl 1 -->
		</skills>
	</item>
	<item id="70103" name="Adventurer's Greater Magic Haste Potion" nameRu="Большое Зелье Скор. Маг. Путешественника" type="EtcItem">
		<!-- Increases Casting Spd. for 20 minutes. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_potion_of_acumen2_i00" />
		<set name="shortcutToggleType" val="1" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="etcitem_type" val="POTION" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="LIQUID" />
		<set name="weight" val="5" />
		<set name="is_stackable" val="true" />
		<set name="is_oly_restricted" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="commissionItemType" val="POTION" />
		<set name="for_npc" val="true" />
		<cond addName="1" msgId="113">
			<player flyMounted="false" />
		</cond>
		<skills>
			<skill id="55236" level="1" /> <!-- Adventurer Potion Casting Speed lvl 1 -->
		</skills>
	</item>
	<item id="70104" name="Scroll: Vampiric Rage - Not Available" nameRu="Свиток Гнева Вампира - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Recovers 6% HP from the received physical damage for 20 minutes. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="material" val="PAPER" />
		<set name="weight" val="5" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="SCROLL_OTHER" />
		<skills>
			<skill id="55237" level="1" /> <!-- Vampiric Rage -->
		</skills>
	</item>
	<item id="70105" name="Scroll of Mana - Not Available" nameRu="Свиток Маны - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Increases MP Recovery Rate for 20 minutes. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="material" val="PAPER" />
		<set name="weight" val="5" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="SCROLL_OTHER" />
		<skills>
			<skill id="55241" level="1" /> <!-- Mana Regeneration -->
		</skills>
	</item>
	<item id="70106" name="Rune of Boost - Not Available" nameRu="Руна Улучшения - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A 7-day item. When in inventory, STR +1, INT +1, CON +1, MEN +1, DEX +1, WIT +1. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="immediate_effect" val="true" />
		<set name="etcitem_type" val="ENCHT_ATTR_RUNE" />
		<set name="time" val="10080" />
		<set name="handler" val="ItemSkills" />
		<skills>
			<skill id="55246" level="1" /> <!-- Rune of Boost -->
		</skills>
	</item>
	<item id="70107" name="Scroll: Berserker Spirit - Not Available" nameRu="Свиток Духа Берсерка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- When used, P. Def. -5%, M. Def. -10%, Evasion -2 -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="material" val="PAPER" />
		<set name="weight" val="5" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="commissionItemType" val="SCROLL_OTHER" />
		<skills>
			<skill id="55242" level="1" /> <!-- Scroll: Berserker Spirit -->
		</skills>
	</item>
	<item id="70108" name="Fire Dragon Pendant - Lv. 3 - Not Available" nameRu="Подвеска Дракона Огня - Ур. 3 - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- A 7-day item. Provides the effect of Fire Dragon Pendant enchanted to Lv. +3. Further enchanting or combining is not possible. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="underwear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CRYSTAL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="handler" val="ItemSkills" />
		<set name="time" val="10080" />
		<skills>
			<skill id="55094" level="1" /> <!-- Fire Dragon Pendant - Lv. 3 -->
		</skills>
		<stats>
			<stat type="pDef">28</stat>
		</stats>
	</item>
	<item id="70109" name="Water Dragon Pendant Lv. 3 - Not Available" nameRu="Подвеска Дракона Воды -  Ур. 3 - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- A 7-day item. Provides the effect of Water Dragon Pendant enchanted to Lv. +3. Further enchanting or combining is not possible. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="underwear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CRYSTAL" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="handler" val="ItemSkills" />
		<set name="time" val="10080" />
		<skills>
			<skill id="55104" level="1" /> <!-- Water Dragon Pendant Basic Effect Lv. 3 -->
		</skills>
		<stats>
			<stat type="pDef">28</stat>
		</stats>
	</item>
	<item id="70110" name="Scroll: Dance of the Warrior - Not Available" nameRu="Свиток Танца Воителя - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Provides Dance of the Warrior effect for 10 minutes. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="PAPER" />
		<set name="weight" val="5" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_oly_restricted" val="true" />
		<set name="commissionItemType" val="SCROLL_OTHER" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="handler" val="ItemSkills" />
		<set name="immediate_effect" val="true" />
		<skills>
			<skill id="55243" level="1" /> <!-- Scroll: Dance of the Warrior -->
		</skills>
	</item>
	<item id="70111" name="Scroll: Dance of the Mystic - Not Available" nameRu="Свиток Танца Мистика - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Provides Dance of the Mystic effect for 10 minutes. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="PAPER" />
		<set name="weight" val="5" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_oly_restricted" val="true" />
		<set name="commissionItemType" val="SCROLL_OTHER" />
		<set name="etcitem_type" val="SCROLL" />
		<set name="handler" val="ItemSkills" />
		<set name="immediate_effect" val="true" />
		<skills>
			<skill id="55244" level="1" /> <!-- Scroll: Dance of the Mystic -->
		</skills>
	</item>
	<item id="70112" name="Refined Romantic Chapeau - Special Trait - Not Available" nameRu="Элегантная Модная Шляпа - Особое Свойство - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- A 7-day item. When equipped, increases P. Atk. and M. Atk. Occupies 2 Hair Accessory slots. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="weight" val="10" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="hair" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="WOOD" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="commissionItemType" val="false" />
		<set name="handler" val="ItemSkills" />
		<set name="enchant_enabled" val="true" />
		<set name="commissionItemType" val="HAIR_ACCESSORY" />
		<set name="isAppearanceable" val="true" />
		<set name="time" val="10080" />
		<set name="is_stackable" val="true" />
		<skills>
			<skill id="35011" level="1" /> <!-- Refined Romantic Chapeau lvl 1 - Teleport lv1 -->
			<skill id="55047" level="1" /> <!-- Refined Romantic Chapeau lvl 1 - Blessed Body lv1 -->
			<skill id="55048" level="1" /> <!-- Refined Romantic Chapeau lvl 1 - Wind Walk lv1 -->
			<skill id="55247" level="1" /> <!-- Refined Romantic Chapeau lvl 1 - P. Atk - M. Atk -->
		</skills>
	</item>
	<item id="70113" name="Scroll of Escape: Aden - Not Available" nameRu="Свиток Телепорта - Аден - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A magical scroll that relocates you to the Town of Aden. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="PAPER" />
		<set name="weight" val="30" />
		<set name="price" val="35000" />
		<set name="is_stackable" val="true" />
		<set name="is_oly_restricted" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="SCROLL_OTHER" />
		<cond addName="1" msgId="113">
			<player flyMounted="false" />
		</cond>
		<skills>
			<skill id="2213" level="12" /> <!-- Teleport: Town of Aden -->
		</skills>
	</item>
	<item id="70114" name="Agathion Singer and Dancer - Not Available" nameRu="Браслет Печати Агатиона - Певец и Танцор - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- A 7-day item. Summons Agathion Singer & Dancer when equipped. Cannot be exchanged, dropped, or sold. <Active Skills>. 1. Dance of Fury: increases Atk. Spd. by 5% for 20 minutes. 2. Dance of Concentration: increases Casting Spd. by 5% for 20 minutes. 3. Song of Wind: increases Speed by 20 for 5 minutes. Reuse Time: 10 min. <Note>. Dance of Fury, Dance of Concentration, and Song of Wind effects do not stack. Only one effect can be applied at a time. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="bodypart" val="lbracelet" />
		<set name="is_oly_restricted" val="true" />
		<set name="immediate_effect" val="true" />
		<set name="time" val="10800" />
		<skills>
			<skill id="21233" level="1" /> <!-- Release Agathion's Seal - Singer and Dancer -->
			<skill id="23234" level="1" /> <!-- Singer and Dancer Agathion Cute Trick -->
			<skill id="3267" level="1" type="ON_UNEQUIP" /> <!-- Seal Agathion -->
			<skill id="55270" level="1" /> <!-- Agathion - Dance of Fury -->
			<skill id="55271" level="1" /> <!-- Agathion - Dance of Concentration -->
			<skill id="55272" level="1" /> <!-- Agathion - Song of Wind -->
		</skills>
	</item>
	<item id="70115" name="Fishing Coin - Not Available" nameRu="Рыболовная Монета - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- The coin you found in the whale caught using Golden Bait. Can be exchanged for various items in the Fishermen's Guild. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70116" name="Golden Mermaid's Fancy Fishing Rod" nameRu="Элитная Удочка Золотой Русалки" additionalName="1-day" additionalNameRu="1 д." type="Weapon">
		<!-- A 1-day item. Cannot be exchanged, dropped, or sold in a private store. Can be stored in a warehouse. Bobber usage: 1. When used, Acquired XP/ SP +50% from fishing, enables teleporting to Giran/Gludin Harbor. Reuse Time: 1 min. -->
		<set name="icon" val="icon.bm_gold_fishing_pole" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="FISHINGROD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BONE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="1440" />
		<stats>
			<stat type="pAtk">1</stat>
			<stat type="mAtk">1</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">1</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="70117" name="Golden Mermaid's Fancy Fishing Rod" nameRu="Элитная Удочка Золотой Русалки" additionalName="3-day" additionalNameRu="3 д." type="Weapon">
		<!-- A 3-day item. Cannot be exchanged, dropped, or sold in a private store. Can be stored in a warehouse. Bobber usage: 1. When used, Acquired XP/ SP +50% from fishing, enables teleporting to Giran/Gludin Harbor. Reuse Time: 1 min. -->
		<set name="icon" val="icon.bm_gold_fishing_pole" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="FISHINGROD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BONE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">1</stat>
			<stat type="mAtk">1</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">1</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="70118" name="Golden Mermaid's Fancy Fishing Rod" nameRu="Элитная Удочка Золотой Русалки" additionalName="7-day" additionalNameRu="7 д." type="Weapon">
		<!-- A 7-day item. Cannot be exchanged, dropped, or sold in a private store. Can be stored in a warehouse. Bobber usage: 1. When used, Acquired XP/ SP +50% from fishing, enables teleporting to Giran/Gludin Harbor. Reuse Time: 1 min. -->
		<set name="icon" val="icon.bm_gold_fishing_pole" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="FISHINGROD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BONE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="10080" />
		<stats>
			<stat type="pAtk">1</stat>
			<stat type="mAtk">1</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">1</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="70119" name="Golden Mermaid's Fancy Fishing Rod" nameRu="Элитная Удочка Золотой Русалки" additionalName="30-day" additionalNameRu="30 д." type="Weapon">
		<!-- A 30-day item. Cannot be exchanged, dropped, or sold in a private store. Can be stored in a warehouse. Bobber usage: 1. When used, Acquired XP/ SP +50% from fishing, enables teleporting to Giran/Gludin Harbor. Reuse Time: 1 min. -->
		<set name="icon" val="icon.bm_gold_fishing_pole" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="FISHINGROD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BONE" />
		<set name="weight" val="1000" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="43200" />
		<stats>
			<stat type="pAtk">1</stat>
			<stat type="mAtk">1</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">1</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="70120" name="Golden Mermaid's Fancy Fishing Rod" nameRu="Элитная Удочка Золотой Русалки" type="Weapon">
		<!-- Cannot be dropped. Can be stored in a warehouse. Bobber usage: 1. When used, Acquired XP/ SP +50% from fishing, enables teleporting to Giran/Gludin Harbor. Reuse Time: 1 min. -->
		<set name="icon" val="icon.bm_gold_fishing_pole" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="FISHINGROD" />
		<set name="bodypart" val="lrhand" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="BONE" />
		<set name="weight" val="1000" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="pAtk">1</stat>
			<stat type="mAtk">1</stat>
			<stat type="accCombat">-3.75</stat>
			<stat type="pAtkSpd">325</stat>
			<stat type="randomDamage">1</stat>
			<stat type="pAtkRange">80</stat>
		</stats>
	</item>
	<item id="70121" name="Full Plate Armor Crafting Pack - Not Available" nameRu="Сундук Производства Полного Латного Доспеха - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Full Plate Armor. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70122" name="Full Plate Helmet Crafting Pack - Not Available" nameRu="Сундук Производства Полного Латного Шлема - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Full Plate Helmet. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70123" name="Full Plate Shield Crafting Pack - Not Available" nameRu="Сундук Производства Полного Латного Щита - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Full Plate Shield. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70124" name="Drake Leather Armor Crafting Pack - Not Available" nameRu="Сундук Производства Кожаного Доспеха Дрейка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Drake Leather Armor. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70125" name="Drake Leather Boots Crafting Pack - Not Available" nameRu="Сундук Производства Кожаных Сапог Дрейка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Drake Leather Boots. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70126" name="Divine Tunic Crafting Pack - Not Available" nameRu="Сундук Производства Туники Святости - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Divine Tunic. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70127" name="Divine Stockings Crafting Pack - Not Available" nameRu="Сундук Производства Штанов Святости - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Divine Stockings. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70128" name="Divine Gloves Crafting Pack - Not Available" nameRu="Сундук Производства Перчаток Святости - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Divine Gloves. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70129" name="Doom Shield Crafting Pack - Not Available" nameRu="Сундук Производства Щита Рока - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Doom Shield. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70130" name="Doom Plate Armor Crafting Pack - Not Available" nameRu="Сундук Производства Латного Доспеха Рока - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Doom Plate Armor. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70131" name="Doom Helmet Crafting Pack - Not Available" nameRu="Сундук Производства Шлема Рока - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Doom Helmet. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70132" name="Doom Gloves Crafting Pack - Not Available" nameRu="Сундук Производства Перчаток Рока - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Doom Gloves. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70133" name="Doom Boots Crafting Pack - Not Available" nameRu="Сундук Производства Сапог Рока - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Doom Boots. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70134" name="Blue Wolf Breastplate Crafting Pack - Not Available" nameRu="Сундук Производства Кирасы Синего Волка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Blue Wolf Breastplate. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70135" name="Blue Wolf Gaiters Crafting Pack - Not Available" nameRu="Сундук Производства Набедренников Синего Волка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Blue Wolf Gaiters. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70136" name="Blue Wolf Helmet Crafting Pack - Not Available" nameRu="Сундук Производства Шлема Синего Волка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Blue Wolf Helmet. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70137" name="Blue Wolf Gloves Crafting Pack - Not Available" nameRu="Сундук Производства Перчаток Синего Волка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Blue Wolf Gloves. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70138" name="Blue Wolf Boots Crafting Pack - Not Available" nameRu="Сундук Производства Сапог Синего Волка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Blue Wolf Boots. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70139" name="Doom Leather Armor Crafting Pack - Not Available" nameRu="Сундук Производства Кожаного Доспеха Рока - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Doom Leather Armor. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70140" name="Avadon Robe Crafting Pack - Not Available" nameRu="Сундук Производства Мантии Авадона - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Avadon Robe. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70141" name="Avadon Circlet Crafting Pack - Not Available" nameRu="Сундук Производства Диадемы Авадона - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Avadon Circlet. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70142" name="Avadon Gloves Crafting Pack - Not Available" nameRu="Сундук Производства Перчаток Авадона - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Full Plate Armor. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70143" name="Avadon Boots Crafting Pack - Not Available" nameRu="Сундук Производства Сапог Авадона - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Avadon Boots. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70144" name="Blue Wolf Tunic Crafting Pack - Not Available" nameRu="Сундук Производства Туники Синего Волка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Blue Wolf Tunic. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70145" name="Blue Wolf Stockings Crafting Pack - Not Available" nameRu="Сундук Производства Штанов Синего Волка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Blue Wolf Stockings. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70146" name="Black Ore Necklace Crafting Pack - Not Available" nameRu="Сундук Производства Черного Ожерелья - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Black Ore Necklace. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70147" name="Black Ore Ring Crafting Pack - Not Available" nameRu="Сундук Производства Черного Кольца - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Black Ore Ring. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70148" name="Black Ore Earring Crafting Pack - Not Available" nameRu="Сундук Производства Черной Серьги - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Black Ore Earring. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70149" name="Brigandine Crafting Pack - Not Available" nameRu="Сундук Производства Панцирной Туники - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Brigandine. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70150" name="Brigandine Gaiters Crafting Pack - Not Available" nameRu="Сундук Производства Панцирных Набедренников - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Brigandine Gaiters. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70151" name="Brigandine Helmet Crafting Pack - Not Available" nameRu="Сундук Производства Панцирного Шлема - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Brigandine Helmet. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70152" name="Brigandine Shield Crafting Pack - Not Available" nameRu="Сундук Производства Панцирного Щита - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Brigandine Shield. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70153" name="Manticore Skin Shirt Crafting Pack - Not Available" nameRu="Сундук Производства Рубахи из Шкуры Мантикоры - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Manticore Skin Shirt. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70154" name="Manticore Skin Gaiters Crafting Pack - Not Available" nameRu="Сундук Производства Набедренников из Шкуры Мантикоры - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Manticore Skin Gaiters. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70155" name="Manticore Skin Boots Crafting Pack - Not Available" nameRu="Сундук Производства Сапог из Шкуры Мантикоры - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Manticore Skin Boots. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70156" name="Mithril Tunic Crafting Pack - Not Available" nameRu="Сундук Производства Мифриловой Туники - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Mithril Tunic. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70157" name="Mithril Stockings Crafting Pack - Not Available" nameRu="Сундук Производства Мифриловых Штанов - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Mithril Stockings. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70158" name="Elven Mithril Gloves Crafting Pack - Not Available" nameRu="Сундук Производства Мифриловых Перчаток Эльфов - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain all the ingredients necessary for making Elven Mithril Gloves. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="100" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70159" name="Mana Recovery Potion - Not Available" nameRu="Зелье Восстановления Маны - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Instantly recovers MP without a continuous effect. Cannot be used when MP is full. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="LIQUID" />
		<set name="weight" val="5" />
		<set name="is_stackable" val="true" />
		<set name="immediate_effect" val="true" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="POTION" />
		<cond addName="1" msgId="113">
			<player flyMounted="false" />
		</cond>
		<skills>
			<skill id="26030" level="1" /> <!-- Mana Potion -->
		</skills>
	</item>
	<item id="70160" name="1st Anniversary Coin - Not Available" nameRu="Монета 1-й Годовщины - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A coin commemorating the 1st anniversary. Maybe, if you collect many of such coins, something good will happen?Cannot be exchanged, dropped, or sold in a private store. Can be stored in a private warehouse. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70161" name="My Teleport Pack - Not Available" nameRu="Коробка Свободного Телепорта - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click to obtain 1 My Teleport Book, 3 My Teleports, or 10 My Teleport Scrolls. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70162" name="Valakas Treasure Chest - Not Available" nameRu="Сундук с Сокровищами Валакаса - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Double-click the chest to get one of the treasures, gathered by Fire Dragon Valakas. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70163" name="Energy of Development - Not Available" nameRu="Энергия Развития - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- XP/SP gain +5% for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55273" level="1" /> <!-- Energy of Development -->
		</skills>
	</item>
	<item id="70164" name="Powerful Energy of Development - Not Available" nameRu="Мощная Энергия Развития - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- XP/SP gain +7% for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55273" level="2" /> <!-- Energy of Development -->
		</skills>
	</item>
	<item id="70165" name="Great Energy of Development - Not Available" nameRu="Великая Энергия Развития - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- XP/SP gain +10% for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55273" level="3" /> <!-- Energy of Development -->
		</skills>
	</item>
	<item id="70166" name="Energy of Speed - Not Available" nameRu="Энергия Скорости - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Speed +2 for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55274" level="1" /> <!-- Energy of Speed -->
		</skills>
	</item>
	<item id="70167" name="Powerful Energy of Speed - Not Available" nameRu="Мощная Энергия Скорости - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Speed +3 for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55274" level="2" /> <!-- Energy of Speed -->
		</skills>
	</item>
	<item id="70168" name="Great Energy of Speed - Not Available" nameRu="Великая Энергия Скорости - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- Speed +5 for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55274" level="3" /> <!-- Energy of Speed -->
		</skills>
	</item>
	<item id="70169" name="Energy of Accuracy - Not Available" nameRu="Энергия Точности - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- P. Accuracy +1 for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55275" level="1" /> <!-- Energy of Accuracy -->
		</skills>
	</item>
	<item id="70170" name="Powerful Energy of Accuracy - Not Available" nameRu="Мощная Энергия Точности - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- P. Accuracy +2 for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55275" level="2" /> <!-- Energy of Accuracy -->
		</skills>
	</item>
	<item id="70171" name="Great Energy of Accuracy - Not Available" nameRu="Великая Энергия Точности - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- P. Accuracy +4 for 10 minutes. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="immediate_effect" val="true" />
		<set name="ex_immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="handler" val="ItemSkills" />
		<set name="commissionItemType" val="OTHER_ITEM" />
		<set name="for_npc" val="true" />
		<skills>
			<skill id="55275" level="3" /> <!-- Energy of Accuracy -->
		</skills>
	</item>
	<item id="70172" name="Coin of Tears - Not Available" nameRu="Монета Слез - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A coin saturated with grief. You can find it in the Ruins of Agony once in a while. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70173" name="Decayed Coin - Not Available" nameRu="Обветшалая Монета - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A coin which decayed over time. You can find it in the Ruins of Agony once in a while. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70174" name="Shabby Coin - Not Available" nameRu="Потертая Монета - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A coin worn off due to frequent use. It was lying in the Abandoned Camp. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70175" name="Crude Coin - Not Available" nameRu="Грубая Монета - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- This coin was hastily crafted by an amateurish Orc. You can find it in Orc Barracks once in a while/ -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70176" name="Old Coin - Not Available" nameRu="Старая Монета - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A very old coin. Someone dropped it Cruma Tower long ago. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70177" name="Box of Tears - Not Available" nameRu="Сундук Слез - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A box saturated with grief. You can find it in the Ruins of Agony once in a while. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70178" name="Decayed Box - Not Available" nameRu="Обветшалый Сундук - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A box which decayed over time. You can find it in the Ruins of Agony once in a while. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70179" name="Shabby Box - Not Available" nameRu="Потертый Сундук - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A box from the Abandoned Camp, shabby and very old. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70180" name="Crude Box - Not Available" nameRu="Грубый Сундук - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- This box was hastily crafted by an amateurish Orc. You can find it in Orc Barracks once in a while. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70181" name="Old Box - Not Available" nameRu="Старая Коробка - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="EtcItem">
		<!-- A very old box. Someone dropped it Cruma Tower long ago. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="SKILL_REDUCE_ON_SKILL_SUCCESS" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="70182" name="Devi’s Ring (15-day) - Not Available" nameRu="Кольцо Богини (15 д.) - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- 15-day item. When equipped, increases HP by 20, M. Atk. by 3, P. Atk. by 5. The effect does not stack, if two identical rings are equipped. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rfinger;lfinger" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="21600" />
		<set name="useSkillDisTime" val="1" />
		<skills>
			<skill id="55290" level="1" /> <!-- Devi s Ring -->
		</skills>
		<stats>
			<stat type="mDef">20</stat>
		</stats>
	</item>
	<item id="70183" name="Devi’s Ring - Not Available" nameRu="Кольцо Богини - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- When equipped, increases HP by 20, M. Atk. by 3, P. Atk. by 5. The effect does not stack, if two identical rings are equipped. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rfinger;lfinger" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="mDef">20</stat>
		</stats>
	</item>
	<item id="70184" name="Devi’s Earring (15-day) - Not Available" nameRu="Серьга Богини (15 д.) - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- 15-day item. When equipped, increases MP recovery rate by 0.2, Healing received by 2%. The effect does not stack, if two identical earrings are equipped. MP +10. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="SILVER" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="21600" />
		<stats>
			<stat type="mDef">30</stat>
			<stat type="maxMp">10</stat>
		</stats>
	</item>
	<item id="70185" name="Devi’s Earring - Not Available" nameRu="Серьга Богини - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- When equipped, increases MP recovery rate by +0.2, Healing received by 2%. The effect does not stack, if two identical earrings are equipped. Additionally increases MP by 10. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="SILVER" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="mDef">30</stat>
			<stat type="maxMp">10</stat>
		</stats>
	</item>
	<item id="70186" name="Devi’s Necklace (15-day) - Not Available" nameRu="Ожерелье Богини (15 д.) - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- 15-day item. When equipped, increases MP by 20, M. Def. by 3, P. Def. by 5. Cannot be exchanged, dropped, or sold. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="neck" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="SILVER" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="time" val="21600" />
		<set name="useSkillDisTime" val="1" />
		<skills>
			<skill id="55294" level="1" /> <!-- Devi s Necklace -->
		</skills>
		<stats>
			<stat type="mDef">40</stat>
			<stat type="maxMp">20</stat>
		</stats>
	</item>
	<item id="70187" name="Devi’s Necklace - Not Available" nameRu="Ожерелье Богини - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Armor">
		<!-- When equipped, increases MP by 20, M. Def. by 3, P. Def. by 5. -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="neck" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="SILVER" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<stats>
			<stat type="mDef">40</stat>
			<stat type="maxMp">20</stat>
		</stats>
	</item>
	<item id="70188" name="1 Weapon Slot - Not Available" nameRu="1 Ячейка для Оружия - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Weapon">
		<!-- Test Item -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="bodypart" val="rhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="D" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1280" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">41</stat>
			<stat type="mAtk">43</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="70189" name="2 Weapon Slots - Not Available" nameRu="2 Ячейки для Оружия - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Weapon">
		<!-- Test Item -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="bodypart" val="rhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="D" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1280" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">41</stat>
			<stat type="mAtk">43</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="70190" name="3 Weapon Slots - Not Available" nameRu="3 Ячейки для Оружия - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Weapon">
		<!-- Test Item -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="bodypart" val="rhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="D" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1280" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">41</stat>
			<stat type="mAtk">43</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="70191" name="4 Weapon Slots - Not Available" nameRu="4 Ячейки для Оружия - Недоступно" additionalName="Not Available" additionalNameRu="Недоступно" type="Weapon">
		<!-- Test Item -->
		<set name="icon" val="icon.etc_ertheia2" />
		<set name="default_action" val="EQUIP" />
		<set name="weapon_type" val="BLUNT" />
		<set name="bodypart" val="rhand" />
		<set name="immediate_effect" val="true" />
		<set name="crystal_type" val="D" />
		<set name="material" val="STEEL" />
		<set name="weight" val="1280" />
		<set name="soulshots" val="1" />
		<set name="spiritshots" val="1" />
		<set name="enchant_enabled" val="true" />
		<stats>
			<stat type="pAtk">41</stat>
			<stat type="mAtk">43</stat>
			<stat type="rCrit">6</stat>
			<stat type="accCombat">8</stat>
			<stat type="pAtkSpd">379</stat>
			<stat type="randomDamage">20</stat>
			<stat type="pAtkRange">40</stat>
		</stats>
	</item>
	<item id="70192" name="Earring of Chronicle Heir Lv. 1" nameRu="Серьга Наследника Хроник Ур. 1" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 1%, Max. MP +1%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">2</stat>
		</stats>
	</item>
	<item id="70193" name="Earring of Chronicle Heir Lv. 2" nameRu="Серьга Наследника Хроник Ур. 2" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 2%, Max. MP +2%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">4</stat>
		</stats>
	</item>
	<item id="70194" name="Earring of Chronicle Heir Lv. 3" nameRu="Серьга Наследника Хроник Ур. 3" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 3%, Max. MP +3%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">6</stat>
		</stats>
	</item>
	<item id="70195" name="Earring of Chronicle Heir Lv. 4" nameRu="Серьга Наследника Хроник Ур. 4" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 4%, Max. MP +4%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">8</stat>
		</stats>
	</item>
	<item id="70196" name="Earring of Chronicle Heir Lv. 5" nameRu="Серьга Наследника Хроник Ур. 5" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 5%, Max. MP +5%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">10</stat>
		</stats>
	</item>
	<item id="70197" name="Earring of Chronicle Heir Lv. 6" nameRu="Серьга Наследника Хроник Ур. 6" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 6%, Max. MP +6%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">12</stat>
		</stats>
	</item>
	<item id="70198" name="Earring of Chronicle Heir Lv. 7" nameRu="Серьга Наследника Хроник Ур. 7" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 7%, Max. MP +7%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">14</stat>
		</stats>
	</item>
	<item id="70199" name="Earring of Chronicle Heir Lv. 8" nameRu="Серьга Наследника Хроник Ур. 8" additionalName="Sealed" additionalNameRu="Запечатано" type="Armor">
		<!-- When equipped, increases MP recovery rate by 8%, Max. MP +8%. The effect does not stack if two identical earrings are equipped. Cannot be exchanged, dropped, destroyed, or sold in a private store. Can be shared within account through Dimensional Merchant. -->
		<set name="icon" val="icon.accessory_earring_of_orfen_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="rear;lear" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="GOLD" />
		<set name="weight" val="150" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_destroyable" val="false" />
		<stats>
			<stat type="mDef">16</stat>
		</stats>
	</item>
</list>
