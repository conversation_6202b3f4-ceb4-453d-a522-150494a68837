<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="7485" name="<PERSON><PERSON><PERSON>'s Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon <PERSON><PERSON><PERSON>? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7486" name="<PERSON><PERSON><PERSON>'s Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7487" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7488" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7489" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7490" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7491" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7492" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7493" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7494" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7495" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7496" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7497" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7498" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
	<item id="7499" name="Halisha's Badge" nameRu="Знак Халиши" type="EtcItem">
		<!-- A cursed mark. A certain amount of them is required to summon Halisha? Archon. -->
		<set name="icon" val="icon.etc_eyeball_i00" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_depositable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_questitem" val="true" />
	</item>
</list>
