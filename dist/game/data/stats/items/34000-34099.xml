<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="34070" name="Scroll: Enchant C-grade Weapon" nameRu="Свиток: Модифицировать Оружие Ранга C" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- When enchanted, P. Atk. +4 for C-grade one-handed swords, one-handed blunts, daggers, spears, and other weapons. P. Atk. +5 for two-handed swords, two-handed blunts, dual swords, and two-handed fist weapons. P. Atk. +8 for bows. M. Atk. +3 for all weapons. Starting at +4, P. Atk./ M. Atk. bonus is doubled. Cannot be exchanged, dropped, or sold in a private store. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_scroll_of_enchant_weapon_i02" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="PAPER" />
		<set name="weight" val="120" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
	<item id="34072" name="Scroll: Enchant D-grade Weapon" nameRu="Свиток: Модифицировать Оружие Ранга D" additionalName="Sealed" additionalNameRu="Запечатано" type="EtcItem">
		<!-- When enchanted, P. Atk. +4 for D-grade one-handed swords, one-handed blunts, daggers, spears, and other weapons. P. Atk. +5 for two-handed swords, two-handed blunts, dual swords, and two-handed fist weapons. P. Atk. +8 for bows. M. Atk. +3 for all weapons. Starting at +4, P. Atk./ M. Atk. bonus is doubled. Cannot be exchanged, dropped, or sold in a private store. Can be stored in a private warehouse. -->
		<set name="icon" val="icon.etc_scroll_of_enchant_weapon_i01" />
		<set name="default_action" val="SKILL_REDUCE" />
		<set name="material" val="PAPER" />
		<set name="weight" val="120" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
	</item>
</list>
