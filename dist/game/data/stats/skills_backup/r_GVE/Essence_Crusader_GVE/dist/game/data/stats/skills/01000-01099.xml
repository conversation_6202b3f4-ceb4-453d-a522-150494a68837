<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="1001" toLevel="12" name="Battle Cry" nameRu="Боевой Клич">
		<!-- P. Atk. +$s1. Continuously consumes MP proportionately to your level. -->
		<icon>icon.skill1001</icon>
		<magicLevel>
			<value level="1">1</value>
			<value level="2">14</value>
			<value level="3">25</value>
			<value level="4">35</value>
			<value level="5">40</value>
			<value level="6">48</value>
			<value level="7">56</value>
			<value level="8">60</value>
			<value level="9">66</value>
			<value level="10">72</value>
			<value level="11">78</value>
			<value level="12">78</value>
		</magicLevel>
		<operateType>T</operateType>
		<stayAfterDeath>true</stayAfterDeath> <!-- Custom -->
		<isMagic>1</isMagic>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>NONE</targetType>
		<effects>
			<effect name="ManaDamOverTime">
				<power>1</power>
				<ticks>5</ticks>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">2</value>
					<value level="2">5</value>
					<value level="3">10</value>
					<value level="4">20</value>
					<value level="5">30</value>
					<value level="6">40</value>
					<value level="7">50</value>
					<value level="8">60</value>
					<value level="9">70</value>
					<value level="10">80</value>
					<value level="11">90</value>
					<value level="12">100</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence" fromLevel="5" toLevel="12">
				<amount>
					<value level="5">5</value>
					<value level="6">5</value>
					<value level="7">5</value>
					<value level="8">5</value>
					<value level="9">7</value>
					<value level="10">10</value>
					<value level="11">12</value>
					<value level="12">14</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk" fromLevel="6" toLevel="12">
				<amount>
					<value level="6">5</value>
					<value level="7">5</value>
					<value level="8">5</value>
					<value level="9">7</value>
					<value level="10">10</value>
					<value level="11">12</value>
					<value level="12">14</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence" fromLevel="7" toLevel="12">
				<amount>
					<value level="7">5</value>
					<value level="8">5</value>
					<value level="9">7</value>
					<value level="10">10</value>
					<value level="11">12</value>
					<value level="12">14</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalSkillPower" fromLevel="8" toLevel="12">
				<amount>5</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1002" toLevel="4" name="Chant of Acumen" nameRu="Напев Проницательности">
		<!-- Casting Spd. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1002</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CASTING_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">23</value>
					<value level="3">30</value>
					<value level="4">33</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1003" toLevel="3" name="Eagle Spirit" nameRu="Дух Орла">
		<!-- P. Accuracy +$s1. -->
		<icon>icon.skill1003</icon>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">48</value>
		</magicLevel>
		<operateType>P</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="Accuracy">
				<amount>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1004" toLevel="3" name="Pa'agrio's Wisdom" nameRu="Мудрость Паагрио">
		<!-- M. Skill Cooldown -$s2 for $s1.\n\nConsumes 6 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1004</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">48</value>
			<value level="3">56</value>
		</magicLevel>
		<mpConsume>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<affectObject>FRIEND</affectObject>
		<itemConsumeId>3031</itemConsumeId>
		<itemConsumeCount>6</itemConsumeCount>
		<effects>
			<effect name="Reuse">
				<amount>
					<value level="1">-10</value>
					<value level="2">-12</value>
					<value level="3">-15</value>
				</amount>
				<magicType>1</magicType>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1005" toLevel="3" name="Soul Evasion" nameRu="Духовное Уклонение">
		<!-- P. Evasion +$s1. -->
		<icon>icon.skill1005</icon>
		<magicLevel>
			<value level="1">35</value>
			<value level="2">44</value>
			<value level="3">52</value>
		</magicLevel>
		<operateType>P</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="PhysicalEvasion">
				<amount>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1006" toLevel="4" name="Chant of Empower" nameRu="Напев Воодушевления">
		<!-- M. Atk. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1006</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="MAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1007" toLevel="4" name="Chant of Might" nameRu="Напев Могущества">
		<!-- P. Atk. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1007</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">3</value>
			<value level="2">20</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">80</value>
					<value level="2">150</value>
					<value level="3">230</value>
					<value level="4">250</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1008" toLevel="3" name="Pa'agrio's Glory" nameRu="Слава Паагрио">
		<!-- For $s1, Received M. Skill Critical Damage +$s2.\n\nConsumes 6 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1008</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">48</value>
			<value level="3">56</value>
		</magicLevel>
		<mpConsume>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="DefenceMagicCriticalDamage">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1009" toLevel="4" name="Chant of Shield" nameRu="Напев Щита">
		<!-- P. Def. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1009</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">2</value>
			<value level="2">20</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1010" toLevel="3" name="Spirit Shield" nameRu="Духовный Щит">
		<!-- Claims the power of ancient souls. P. Def. +$s1, M. Def. +$s2. -->
		<icon>icon.skill1010</icon>
		<magicLevel>
			<value level="1">7</value>
			<value level="2">25</value>
			<value level="3">35</value>
		</magicLevel>
		<operateType>P</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">40</value>
					<value level="2">80</value>
					<value level="3">120</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">60</value>
					<value level="2">100</value>
					<value level="3">150</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1011" toLevel="18" name="Heal" nameRu="Лечение">
		<!-- Restores the target's HP with $s1 Power. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1011</icon>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">25</value>
			<value level="2">29</value>
			<value level="3">33</value>
			<value level="4">41</value>
			<value level="5">47</value>
			<value level="6">53</value>
			<value level="7">60</value>
			<value level="8">67</value>
			<value level="9">75</value>
			<value level="10">88</value>
			<value level="11">92</value>
			<value level="12">97</value>
			<value level="13">112</value>
			<value level="14">117</value>
			<value level="15">122</value>
			<value level="16">139</value>
			<value level="17">144</value>
			<value level="18">150</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>5000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">3</value>
			<value level="2">5</value>
			<value level="3">7</value>
			<value level="4">10</value>
			<value level="5">12</value>
			<value level="6">14</value>
			<value level="7">16</value>
			<value level="8">18</value>
			<value level="9">20</value>
			<value level="10">23</value>
			<value level="11">24</value>
			<value level="12">25</value>
			<value level="13">28</value>
			<value level="14">29</value>
			<value level="15">30</value>
			<value level="16">33</value>
			<value level="17">34</value>
			<value level="18">35</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">13</value>
			<value level="3">14</value>
			<value level="4">17</value>
			<value level="5">19</value>
			<value level="6">22</value>
			<value level="7">24</value>
			<value level="8">27</value>
			<value level="9">30</value>
			<value level="10">33</value>
			<value level="11">35</value>
			<value level="12">37</value>
			<value level="13">42</value>
			<value level="14">44</value>
			<value level="15">44</value>
			<value level="16">48</value>
			<value level="17">50</value>
			<value level="18">52</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET_OR_SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">50</value>
					<value level="2">58</value>
					<value level="3">67</value>
					<value level="4">83</value>
					<value level="5">95</value>
					<value level="6">107</value>
					<value level="7">121</value>
					<value level="8">135</value>
					<value level="9">151</value>
					<value level="10">176</value>
					<value level="11">185</value>
					<value level="12">195</value>
					<value level="13">224</value>
					<value level="14">234</value>
					<value level="15">245</value>
					<value level="16">278</value>
					<value level="17">289</value>
					<value level="18">301</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="1012" toLevel="3" name="Cure" nameRu="Исцеление">
		<!-- Cures minor poisoning and bleeding. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1012</icon>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">70</value>
			<value level="2">350</value>
			<value level="3">580</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">7</value>
			<value level="2">35</value>
			<value level="3">58</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">30</value>
			<value level="3">55</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>4000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET_OR_SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelBySlot">
				<dispel>
					<value level="1">POISON,99;BLEEDING,99</value>
					<value level="2">POISON,99;BLEEDING,99;SPEED_DOWN,99;ATTACK_TIME_UP,99</value>
					<value level="3">POISON,99;BLEEDING,99;SPEED_DOWN,99;ROOT_PHYSICALLY,99;ROOT_MAGICALLY,99;SILENCE,99;SILENCE_ALL,99;SILENCE_PHYSICAL,99;FLYING_DAGGER,99;ATTACK_TIME_UP,99</value>
				</dispel>
			</effect>
		</effects>
	</skill>
	<skill id="1013" toLevel="39" name="Mana Effect Boost" nameRu="Увеличение Эффективности Маны">
		<!-- For $s1, Max MP +$s2, MP Recovery Rate +$s3. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1013</icon>
		<abnormalTime>1200</abnormalTime>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">28</value>
			<value level="2">30</value>
			<value level="3">33</value>
			<value level="4">35</value>
			<value level="5">38</value>
			<value level="6">40</value>
			<value level="7">42</value>
			<value level="8">44</value>
			<value level="9">46</value>
			<value level="10">48</value>
			<value level="11">50</value>
			<value level="12">52</value>
			<value level="13">54</value>
			<value level="14">56</value>
			<value level="15">57</value>
			<value level="16">58</value>
			<value level="17">59</value>
			<value level="18">60</value>
			<value level="19">61</value>
			<value level="20">62</value>
			<value level="21">63</value>
			<value level="22">64</value>
			<value level="23">65</value>
			<value level="24">66</value>
			<value level="25">67</value>
			<value level="26">68</value>
			<value level="27">69</value>
			<value level="28">70</value>
			<value level="29">71</value>
			<value level="30">72</value>
			<value level="31">73</value>
			<value level="32">74</value>
			<value level="33">76</value>
			<value level="34">78</value>
			<value level="35">80</value>
			<value level="36">81</value>
			<value level="37">82</value>
			<value level="38">83</value>
			<value level="39">84</value>
		</magicLevel>
		<mpConsume>
			<value level="1">49</value>
			<value level="2">53</value>
			<value level="3">57</value>
			<value level="4">60</value>
			<value level="5">67</value>
			<value level="6">70</value>
			<value level="7">74</value>
			<value level="8">78</value>
			<value level="9">82</value>
			<value level="10">87</value>
			<value level="11">90</value>
			<value level="12">94</value>
			<value level="13">98</value>
			<value level="14">103</value>
			<value level="15">104</value>
			<value level="16">107</value>
			<value level="17">109</value>
			<value level="18">110</value>
			<value level="19">113</value>
			<value level="20">115</value>
			<value level="21">117</value>
			<value level="22">119</value>
			<value level="23">120</value>
			<value level="24">123</value>
			<value level="25">124</value>
			<value level="26">127</value>
			<value level="27">128</value>
			<value level="28">130</value>
			<value level="29">132</value>
			<value level="30">133</value>
			<value level="31">135</value>
			<value level="32">137</value>
			<value level="33">151</value>
			<value level="34">153</value>
			<value level="35">155</value>
			<value level="36">156</value>
			<value level="37">158</value>
			<value level="38">159</value>
			<value level="39">161</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxMp">
				<amount>
					<value level="1">5</value>
					<value level="2">5</value>
					<value level="3">5</value>
					<value level="4">5</value>
					<value level="5">10</value>
					<value level="6">10</value>
					<value level="7">10</value>
					<value level="8">10</value>
					<value level="9">10</value>
					<value level="10">10</value>
					<value level="11">10</value>
					<value level="12">10</value>
					<value level="13">10</value>
					<value level="14">10</value>
					<value level="15">10</value>
					<value level="16">10</value>
					<value level="17">10</value>
					<value level="18">15</value>
					<value level="19">15</value>
					<value level="20">15</value>
					<value level="21">15</value>
					<value level="22">15</value>
					<value level="23">15</value>
					<value level="24">15</value>
					<value level="25">15</value>
					<value level="26">15</value>
					<value level="27">15</value>
					<value level="28">15</value>
					<value level="29">15</value>
					<value level="30">15</value>
					<value level="31">15</value>
					<value level="32">15</value>
					<value level="33">20</value>
					<value level="34">20</value>
					<value level="35">20</value>
					<value level="36">20</value>
					<value level="37">20</value>
					<value level="38">20</value>
					<value level="39">20</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>
					<value level="1">1.5</value>
					<value level="2">1.58</value>
					<value level="3">1.64</value>
					<value level="4">1.72</value>
					<value level="5">1.8</value>
					<value level="6">1.9</value>
					<value level="7">2</value>
					<value level="8">2.1</value>
					<value level="9">2.2</value>
					<value level="10">2.3</value>
					<value level="11">2.4</value>
					<value level="12">2.5</value>
					<value level="13">2.6</value>
					<value level="14">2.7</value>
					<value level="15">2.8</value>
					<value level="16">2.9</value>
					<value level="17">3</value>
					<value level="18">3.2</value>
					<value level="19">3.3</value>
					<value level="20">3.4</value>
					<value level="21">3.5</value>
					<value level="22">3.6</value>
					<value level="23">3.7</value>
					<value level="24">3.8</value>
					<value level="25">3.9</value>
					<value level="26">4</value>
					<value level="27">4.1</value>
					<value level="28">4.2</value>
					<value level="29">4.3</value>
					<value level="30">4.4</value>
					<value level="31">4.5</value>
					<value level="32">4.6</value>
					<value level="33">4.8</value>
					<value level="34">5</value>
					<value level="35">5.2</value>
					<value level="36">5.4</value>
					<value level="37">5.6</value>
					<value level="38">5.8</value>
					<value level="39">6</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1015" toLevel="15" name="Battle Heal" nameRu="Боевое Лечение">
		<!-- Immediately restores the target's HP with $s1 Power. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1015</icon>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">33</value>
			<value level="2">38</value>
			<value level="3">43</value>
			<value level="4">48</value>
			<value level="5">54</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">74</value>
			<value level="9">78</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">98</value>
			<value level="13">111</value>
			<value level="14">115</value>
			<value level="15">120</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">12</value>
			<value level="3">14</value>
			<value level="4">16</value>
			<value level="5">18</value>
			<value level="6">20</value>
			<value level="7">23</value>
			<value level="8">24</value>
			<value level="9">25</value>
			<value level="10">28</value>
			<value level="11">29</value>
			<value level="12">30</value>
			<value level="13">33</value>
			<value level="14">34</value>
			<value level="15">35</value>
		</magicLevel>
		<mpConsume>
			<value level="1">25</value>
			<value level="2">28</value>
			<value level="3">32</value>
			<value level="4">35</value>
			<value level="5">40</value>
			<value level="6">44</value>
			<value level="7">49</value>
			<value level="8">52</value>
			<value level="9">54</value>
			<value level="10">62</value>
			<value level="11">65</value>
			<value level="12">67</value>
			<value level="13">72</value>
			<value level="14">74</value>
			<value level="15">78</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET_OR_SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">83</value>
					<value level="2">95</value>
					<value level="3">107</value>
					<value level="4">121</value>
					<value level="5">135</value>
					<value level="6">151</value>
					<value level="7">176</value>
					<value level="8">185</value>
					<value level="9">195</value>
					<value level="10">224</value>
					<value level="11">234</value>
					<value level="12">245</value>
					<value level="13">278</value>
					<value level="14">289</value>
					<value level="15">301</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="1016" toLevel="9" name="Resurrection" nameRu="Воскрешение">
		<!-- <Skill with fixed cooldown>\n\nResurrects a dead party member. -->
		<icon>icon.skill1016</icon>
		<blockedInOlympiad>true</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>
			<value level="1">121</value>
			<value level="2">196</value>
			<value level="3">290</value>
			<value level="4">374</value>
			<value level="5">460</value>
			<value level="6">502</value>
			<value level="7">542</value>
			<value level="8">595</value>
			<value level="9">624</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>2000</hitTime>
		<coolTime>500</coolTime>
		<isMagic>4</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">48</value>
			<value level="5">56</value>
			<value level="6">60</value>
			<value level="7">64</value>
			<value level="8">70</value>
			<value level="9">74</value>
		</magicLevel>
		<mpConsume>
			<value level="1">59</value>
			<value level="2">88</value>
			<value level="3">122</value>
			<value level="4">152</value>
			<value level="5">180</value>
			<value level="6">195</value>
			<value level="7">207</value>
			<value level="8">228</value>
			<value level="9">239</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<staticReuse>true</staticReuse>
		<targetType>PC_BODY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpResurrection" />
		</conditions>
		<effects>
			<effect name="Resurrection">
				<!-- <power>
					<value level="1">0</value>
					<value level="2">20</value>
					<value level="3">30</value>
					<value level="4">40</value>
					<value level="5">50</value>
					<value level="6">55</value>
					<value level="7">60</value>
					<value level="8">65</value>
					<value level="9">70</value>
				</power> -->
				<power>100</power>
			</effect>
		</effects>
	</skill>
	<skill id="1018" toLevel="3" name="Purify" nameRu="Очищение">
		<!-- Removes immobilizing debuffs from the target. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1018</icon>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">440</value>
			<value level="2">520</value>
			<value level="3">620</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">62</value>
		</magicLevel>
		<mpConsume>
			<value level="1">40</value>
			<value level="2">30</value>
			<value level="3">20</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>4000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET_OR_SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelBySlot">
				<dispel>
					<value level="1">PARALYZE,10;TURN_STONE,10;STUN,10;SLEEP,10;TURN_FLEE,10;IMPRISONMENT,10</value>
					<value level="2">PARALYZE,30;TURN_STONE,30;STUN,30;SLEEP,30;TURN_FLEE,30;IMPRISONMENT,30</value>
					<value level="3">PARALYZE,50;TURN_STONE,50;STUN,50;SLEEP,50;TURN_FLEE,50;IMPRISONMENT,50</value>
				</dispel>
			</effect>
		</effects>
	</skill>
	<skill id="1020" toLevel="30" name="Vitalize" nameRu="Оживление">
		<!-- Restores HP and removes debuffs that decrease P./ M. Atk., P./ M. Def., Atk. Spd., Casting Spd. and Speed. Power $s1. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1020</icon>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">460</value>
			<value level="2">470</value>
			<value level="3">480</value>
			<value level="4">490</value>
			<value level="5">500</value>
			<value level="6">510</value>
			<value level="7">520</value>
			<value level="8">530</value>
			<value level="9">540</value>
			<value level="10">550</value>
			<value level="11">560</value>
			<value level="12">570</value>
			<value level="13">580</value>
			<value level="14">590</value>
			<value level="15">600</value>
			<value level="16">610</value>
			<value level="17">620</value>
			<value level="18">630</value>
			<value level="19">640</value>
			<value level="20">650</value>
			<value level="21">660</value>
			<value level="22">670</value>
			<value level="23">680</value>
			<value level="24">690</value>
			<value level="25">700</value>
			<value level="26">710</value>
			<value level="27">720</value>
			<value level="28">730</value>
			<value level="29">740</value>
			<value level="30">750</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">46</value>
			<value level="2">47</value>
			<value level="3">48</value>
			<value level="4">49</value>
			<value level="5">50</value>
			<value level="6">51</value>
			<value level="7">52</value>
			<value level="8">53</value>
			<value level="9">54</value>
			<value level="10">55</value>
			<value level="11">56</value>
			<value level="12">57</value>
			<value level="13">58</value>
			<value level="14">59</value>
			<value level="15">60</value>
			<value level="16">61</value>
			<value level="17">62</value>
			<value level="18">63</value>
			<value level="19">64</value>
			<value level="20">65</value>
			<value level="21">66</value>
			<value level="22">67</value>
			<value level="23">68</value>
			<value level="24">69</value>
			<value level="25">70</value>
			<value level="26">71</value>
			<value level="27">72</value>
			<value level="28">73</value>
			<value level="29">74</value>
			<value level="30">75</value>
		</magicLevel>
		<mpConsume>
			<value level="1">84</value>
			<value level="2">88</value>
			<value level="3">92</value>
			<value level="4">96</value>
			<value level="5">100</value>
			<value level="6">104</value>
			<value level="7">108</value>
			<value level="8">112</value>
			<value level="9">116</value>
			<value level="10">120</value>
			<value level="11">124</value>
			<value level="12">128</value>
			<value level="13">132</value>
			<value level="14">136</value>
			<value level="15">140</value>
			<value level="16">144</value>
			<value level="17">148</value>
			<value level="18">152</value>
			<value level="19">156</value>
			<value level="20">160</value>
			<value level="21">164</value>
			<value level="22">168</value>
			<value level="23">172</value>
			<value level="24">176</value>
			<value level="25">180</value>
			<value level="26">184</value>
			<value level="27">188</value>
			<value level="28">192</value>
			<value level="29">196</value>
			<value level="30">200</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET_OR_SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">460</value>
					<value level="2">470</value>
					<value level="3">480</value>
					<value level="4">490</value>
					<value level="5">500</value>
					<value level="6">510</value>
					<value level="7">520</value>
					<value level="8">530</value>
					<value level="9">540</value>
					<value level="10">550</value>
					<value level="11">560</value>
					<value level="12">570</value>
					<value level="13">580</value>
					<value level="14">590</value>
					<value level="15">600</value>
					<value level="16">610</value>
					<value level="17">620</value>
					<value level="18">630</value>
					<value level="19">640</value>
					<value level="20">650</value>
					<value level="21">660</value>
					<value level="22">670</value>
					<value level="23">680</value>
					<value level="24">690</value>
					<value level="25">700</value>
					<value level="26">710</value>
					<value level="27">720</value>
					<value level="28">730</value>
					<value level="29">740</value>
					<value level="30">750</value>
				</power>
			</effect>
			<effect name="DispelBySlot">
				<dispel>PA_DOWN,-1;MA_DOWN,-1;PD_DOWN,-1;MD_DOWN,-1;SPEED_DOWN,-1;ELEMENTAL_DUST,-1</dispel>
			</effect>
		</effects>
	</skill>
	<skill id="1027" toLevel="15" name="Group Heal" nameRu="Групповое Лечение">
		<!-- Recovers all party members' HP with $s1 power. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1027</icon>
		<affectRange>1000</affectRange>
		<effectPoint>
			<value level="1">33</value>
			<value level="2">38</value>
			<value level="3">43</value>
			<value level="4">48</value>
			<value level="5">54</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">74</value>
			<value level="9">78</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">98</value>
			<value level="13">111</value>
			<value level="14">115</value>
			<value level="15">120</value>
		</effectPoint>
		<hitTime>7000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">12</value>
			<value level="3">14</value>
			<value level="4">16</value>
			<value level="5">18</value>
			<value level="6">20</value>
			<value level="7">23</value>
			<value level="8">24</value>
			<value level="9">25</value>
			<value level="10">28</value>
			<value level="11">29</value>
			<value level="12">30</value>
			<value level="13">33</value>
			<value level="14">34</value>
			<value level="15">35</value>
		</magicLevel>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">38</value>
			<value level="3">43</value>
			<value level="4">48</value>
			<value level="5">53</value>
			<value level="6">59</value>
			<value level="7">65</value>
			<value level="8">69</value>
			<value level="9">72</value>
			<value level="10">83</value>
			<value level="11">87</value>
			<value level="12">88</value>
			<value level="13">95</value>
			<value level="14">99</value>
			<value level="15">103</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>6000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>PARTY</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">66</value>
					<value level="2">76</value>
					<value level="3">86</value>
					<value level="4">97</value>
					<value level="5">108</value>
					<value level="6">121</value>
					<value level="7">141</value>
					<value level="8">148</value>
					<value level="9">156</value>
					<value level="10">179</value>
					<value level="11">188</value>
					<value level="12">196</value>
					<value level="13">222</value>
					<value level="14">231</value>
					<value level="15">241</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="1028" toLevel="35" name="Might of Heaven" nameRu="Сила Небес">
		<!-- Summons light of heavens. Inflicts magic damage with $s1 power.\nDeals additional damage to demons and undead. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1028</icon>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">-190</value>
			<value level="2">-200</value>
			<value level="3">-210</value>
			<value level="4">-220</value>
			<value level="5">-230</value>
			<value level="6">-240</value>
			<value level="7">-250</value>
			<value level="8">-260</value>
			<value level="9">-270</value>
			<value level="10">-280</value>
			<value level="11">-290</value>
			<value level="12">-300</value>
			<value level="13">-310</value>
			<value level="14">-320</value>
			<value level="15">-330</value>
			<value level="16">-340</value>
			<value level="17">-350</value>
			<value level="18">-360</value>
			<value level="19">-370</value>
			<value level="20">-375</value>
			<value level="21">-380</value>
			<value level="22">-385</value>
			<value level="23">-390</value>
			<value level="24">-395</value>
			<value level="25">-400</value>
			<value level="26">-405</value>
			<value level="27">-410</value>
			<value level="28">-415</value>
			<value level="29">-420</value>
			<value level="30">-425</value>
			<value level="31">-430</value>
			<value level="32">-435</value>
			<value level="33">-440</value>
			<value level="34">-445</value>
			<value level="35">-450</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">40</value>
			<value level="3">42</value>
			<value level="4">44</value>
			<value level="5">46</value>
			<value level="6">48</value>
			<value level="7">50</value>
			<value level="8">52</value>
			<value level="9">54</value>
			<value level="10">56</value>
			<value level="11">58</value>
			<value level="12">60</value>
			<value level="13">62</value>
			<value level="14">64</value>
			<value level="15">66</value>
			<value level="16">68</value>
			<value level="17">70</value>
			<value level="18">72</value>
			<value level="19">74</value>
			<value level="20">75</value>
			<value level="21">76</value>
			<value level="22">77</value>
			<value level="23">78</value>
			<value level="24">79</value>
			<value level="25">80</value>
			<value level="26">81</value>
			<value level="27">82</value>
			<value level="28">83</value>
			<value level="29">84</value>
			<value level="30">85</value>
			<value level="31">86</value>
			<value level="32">87</value>
			<value level="33">88</value>
			<value level="34">89</value>
			<value level="35">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">27</value>
			<value level="2">28</value>
			<value level="3">30</value>
			<value level="4">31</value>
			<value level="5">31</value>
			<value level="6">32</value>
			<value level="7">35</value>
			<value level="8">35</value>
			<value level="9">36</value>
			<value level="10">37</value>
			<value level="11">38</value>
			<value level="12">39</value>
			<value level="13">40</value>
			<value level="14">42</value>
			<value level="15">43</value>
			<value level="16">43</value>
			<value level="17">45</value>
			<value level="18">46</value>
			<value level="19">47</value>
			<value level="20">47</value>
			<value level="21">56</value>
			<value level="22">57</value>
			<value level="23">58</value>
			<value level="24">58</value>
			<value level="25">59</value>
			<value level="26">59</value>
			<value level="27">60</value>
			<value level="28">60</value>
			<value level="29">62</value>
			<value level="30">62</value>
			<value level="31">63</value>
			<value level="32">63</value>
			<value level="33">64</value>
			<value level="34">64</value>
			<value level="35">65</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">66</value>
					<value level="2">68</value>
					<value level="3">70</value>
					<value level="4">72</value>
					<value level="5">74</value>
					<value level="6">76</value>
					<value level="7">78</value>
					<value level="8">80</value>
					<value level="9">82</value>
					<value level="10">84</value>
					<value level="11">86</value>
					<value level="12">90</value>
					<value level="13">92</value>
					<value level="14">94</value>
					<value level="15">96</value>
					<value level="16">99</value>
					<value level="17">102</value>
					<value level="18">106</value>
					<value level="19">109</value>
					<value level="20">111</value>
					<value level="21">113</value>
					<value level="22">114</value>
					<value level="23">116</value>
					<value level="24">118</value>
					<value level="25">119</value>
					<value level="26">121</value>
					<value level="27">123</value>
					<value level="28">125</value>
					<value level="29">126</value>
					<value level="30">128</value>
					<value level="31">131</value>
					<value level="32">133</value>
					<value level="33">136</value>
					<value level="34">138</value>
					<value level="35">140</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="1031" toLevel="10" name="Divine Strike" nameRu="Божественный Удар">
		<!-- Summons holy light.\nInflicts magic damage with $s1 power.\nDeals additional damage to demons and undead. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1031</icon>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-90</value>
			<value level="2">-100</value>
			<value level="3">-110</value>
			<value level="4">-120</value>
			<value level="5">-130</value>
			<value level="6">-140</value>
			<value level="7">-150</value>
			<value level="8">-160</value>
			<value level="9">-170</value>
			<value level="10">-180</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">20</value>
			<value level="3">22</value>
			<value level="4">24</value>
			<value level="5">26</value>
			<value level="6">28</value>
			<value level="7">30</value>
			<value level="8">32</value>
			<value level="9">34</value>
			<value level="10">36</value>
		</magicLevel>
		<mpConsume>
			<value level="1">19</value>
			<value level="2">20</value>
			<value level="3">22</value>
			<value level="4">23</value>
			<value level="5">24</value>
			<value level="6">26</value>
			<value level="7">27</value>
			<value level="8">28</value>
			<value level="9">29</value>
			<value level="10">31</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">30</value>
					<value level="2">32</value>
					<value level="3">35</value>
					<value level="4">38</value>
					<value level="5">41</value>
					<value level="6">44</value>
					<value level="7">47</value>
					<value level="8">50</value>
					<value level="9">54</value>
					<value level="10">58</value>
				</power>
				<raceModifier>1.3</raceModifier>
				<raceList>DEMON,UNDEAD</raceList>
			</effect>
		</effects>
	</skill>
	<skill id="1032" toLevel="3" name="Magic Evasion" nameRu="Уклонение от Магии">
		<!-- Bleed Resistance +$s1. -->
		<icon>icon.skill1032</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">48</value>
			<value level="3">56</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="MagicalEvasion">
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1033" toLevel="3" name="Infection Mitigation" nameRu="Сопротивление Инфекции">
		<!-- Poison Resistance +$s1. -->
		<icon>icon.skill1033</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">35</value>
			<value level="2">40</value>
			<value level="3">44</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="DefenceTrait">
				<POISON>
					<value level="1">30</value>
					<value level="2">40</value>
					<value level="3">50</value>
				</POISON>
			</effect>
		</effects>
	</skill>
	<skill id="1034" toLevel="13" name="Repose" nameRu="Упокоение">
		<!-- Diminishes monster's will to attack. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1034</icon>
		<affectLimit>10-10</affectLimit>
		<affectRange>200</affectRange>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">44</value>
			<value level="2">48</value>
			<value level="3">52</value>
			<value level="4">56</value>
			<value level="5">58</value>
			<value level="6">60</value>
			<value level="7">62</value>
			<value level="8">64</value>
			<value level="9">66</value>
			<value level="10">68</value>
			<value level="11">70</value>
			<value level="12">72</value>
			<value level="13">74</value>
		</magicLevel>
		<mpConsume>
	<value level="1">33</value>
	<value level="2">36</value>
	<value level="3">39</value>
	<value level="4">42</value>
	<value level="5">45</value>
	<value level="6">48</value>
	<value level="7">51</value>
	<value level="8">54</value>
	<value level="9">57</value>
	<value level="10">60</value>
	<value level="11">63</value>
	<value level="12">66</value>
	<value level="13">69</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>DERANGEMENT</trait>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>UNDEAD_REAL_ENEMY</affectObject>
		<effects>
			<effect name="DeleteHate">
				<chance>40</chance>
			</effect>
		</effects>
	</skill>
	<skill id="1035" toLevel="4" name="Mental Shield" nameRu="Ментальный Щит">
		<!-- Sleep/ Silence/ Fear/ Hold Resistance +$s1. -->
		<icon>icon.skill1035</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">25</value>
			<value level="2">40</value>
			<value level="3">48</value>
			<value level="4">56</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="DefenceTrait">
				<HOLD>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">15</value>
					<value level="4">20</value>
				</HOLD>
				<DERANGEMENT>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">15</value>
					<value level="4">20</value>
				</DERANGEMENT>
				<SLEEP>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">15</value>
					<value level="4">20</value>
				</SLEEP>
			</effect>
		</effects>
	</skill>
	<skill id="1036" toLevel="5" name="Magic Barrier" nameRu="Магический Барьер">
		<!-- M. Def. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1036</icon>
		<abnormalLevel>
			<value level="1">2</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MD_UP</abnormalType>
		<effectPoint>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">418</value>
			<value level="5">495</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">4000</value>
			<value level="5">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">44</value>
			<value level="4">52</value>
			<value level="5">56</value>
		</magicLevel>
		<mpConsume>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">39</value>
			<value level="5">48</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">250</value>
					<value level="2">350</value>
					<value level="3">380</value>
					<value level="4">380</value>
					<value level="5">380</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1040" toLevel="10" name="Shield" nameRu="Щит">
		<!-- P. Def. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1040</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="9">9</value>
			<value level="10">10</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PD_UP</abnormalType>
		<castRange>400</castRange>
		<effectPoint>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">121</value>
			<value level="6">243</value>
			<value level="7">418</value>
			<value level="8">121</value>
			<value level="9">243</value>
			<value level="10">418</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">4000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">7</value>
			<value level="2">25</value>
			<value level="3">44</value>
			<value level="4">7</value>
			<value level="5">25</value>
			<value level="6">44</value>
			<value level="7">7</value>
			<value level="8">25</value>
			<value level="9">44</value>
			<value level="9">44</value>
			<value level="10">44</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">10000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1042" toLevel="17" name="Paralysis" nameRu="Паралич">
		<!-- Paralyzes the target for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1042</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<lvlBonusRate>20</lvlBonusRate>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-480</value>
			<value level="2">-520</value>
			<value level="3">-560</value>
			<value level="4">-580</value>
			<value level="5">-600</value>
			<value level="6">-620</value>
			<value level="7">-640</value>
			<value level="8">-660</value>
			<value level="9">-680</value>
			<value level="10">-700</value>
			<value level="11">-720</value>
			<value level="12">-740</value>
			<value level="13">-760</value>
			<value level="14">-780</value>
			<value level="15">-780</value>
			<value level="16">-780</value>
			<value level="17">-780</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">48</value>
			<value level="2">52</value>
			<value level="3">56</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">62</value>
			<value level="7">64</value>
			<value level="8">66</value>
			<value level="9">68</value>
			<value level="10">70</value>
			<value level="11">72</value>
			<value level="12">74</value>
			<value level="13">76</value>
			<value level="14">78</value>
			<value level="15">82</value>
			<value level="16">86</value>
			<value level="17">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">36</value>
			<value level="3">39</value>
			<value level="4">42</value>
			<value level="5">45</value>
			<value level="6">48</value>
			<value level="7">51</value>
			<value level="8">54</value>
			<value level="9">57</value>
			<value level="10">60</value>
			<value level="11">63</value>
			<value level="12">66</value>
			<value level="13">69</value>
			<value level="14">72</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>20000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>PARALYZE</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>35016;35045;18103</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="1043" toLevel="1" name="Mage's Blessing" nameRu="Благословение Мистика">
		<!-- By the holy power M. Atk. +50. -->
		<icon>icon.skill1043</icon>
		<magicLevel>25</magicLevel>
		<operateType>P</operateType>
		<effects>
			<effect name="MAtk">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1044" toLevel="6" name="Regeneration" nameRu="Регенерация">
		<!-- HP Recovery Rate +$s1. -->
		<icon>icon.skill1044</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">35</value>
			<value level="2">48</value>
			<value level="3">56</value>
			<value level="4">35</value>
			<value level="5">48</value>
			<value level="6">56</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="HpRegen">
				<amount>
					<value level="1">10</value>
					<value level="2">15</value>
					<value level="3">20</value>
					<value level="4">10</value>
					<value level="5">15</value>
					<value level="6">20</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1045" toLevel="6" name="Blessed Body" nameRu="Благословение Тела">
		<icon>icon.skill1045</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">10</value>
					<value level="2">15</value>
					<value level="3">20</value>
					<value level="4">25</value>
					<value level="5">30</value>
					<value level="6">35</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1047" toLevel="5" name="Mana Regeneration" nameRu="Регенерация Маны">
		<!-- For $s1, MP Recovery Rate +$s2.\n\nConsumes $s3 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1047</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MP_REGEN_UP</abnormalType>
		<effectPoint>0</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">7</value>
			<value level="2">9</value>
			<value level="3">12</value>
			<value level="4">15</value>
			<value level="5">18</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">48</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">35</value>
			<value level="2">44</value>
			<value level="3">55</value>
			<value level="4">65</value>
			<value level="5">75</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MpRegen">
				<amount>
					<value level="1">1.72</value>
					<value level="2">2.16</value>
					<value level="3">2.74</value>
					<value level="4">3.09</value>
					<value level="5">3.55</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1048" toLevel="6" name="Blessed Soul" nameRu="Благословение Души">
		<icon>icon.skill1048</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="MaxMp">
				<amount>
					<value level="1">10</value>
					<value level="2">15</value>
					<value level="3">20</value>
					<value level="4">25</value>
					<value level="5">30</value>
					<value level="6">35</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1049" toLevel="14" name="Requiem" nameRu="Реквием">
		<!-- For $s1 protects from preemptive attacks from monsters. -->
		<icon>icon.skill1049</icon>
		<abnormalLevel>1</abnormalLevel>
		<shortcutToggleType>2</shortcutToggleType> <!-- Custom -->
		<abnormalTime>30</abnormalTime>
		<abnormalType>TURN_PASSIVE</abnormalType>
		<activateRate>-1</activateRate>
		<!-- <activateRate>35</activateRate> -->
		<!-- <affectLimit>10-10</affectLimit> -->
		<effectPoint> <!-- To make aggro -->
			<value level="1">-436</value>
			<value level="2">-436</value>
			<value level="3">-436</value>
			<value level="4">-439</value>
			<value level="5">-442</value>
			<value level="6">-445</value>
			<value level="7">-448</value>
			<value level="8">-451</value>
			<value level="9">-454</value>
			<value level="10">-457</value>
			<value level="11">-460</value>
			<value level="12">-463</value>
			<value level="13">-466</value>
			<value level="14">-469</value>
		</effectPoint>
		<affectLimit>12-16</affectLimit>
		<!--<affectRange>700</affectRange>-->
		<affectRange> <!-- Custom -->
			<value level="1">500</value>
			<value level="2">700</value>
			<value fromLevel="3" toLevel="14">900</value>
		</affectRange>
		<basicProperty>MAGIC</basicProperty>
		<hitTime>7000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">44</value>
			<value level="3">48</value>
			<value level="4">52</value>
			<value level="5">56</value>
			<value level="6">58</value>
			<value level="7">60</value>
			<value level="8">62</value>
			<value level="9">64</value>
			<value level="10">66</value>
			<value level="11">68</value>
			<value level="12">70</value>
			<value level="13">72</value>
			<value level="14">74</value>
		</magicLevel>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">36</value>
			<value level="3">39</value>
			<value level="4">42</value>
			<value level="5">45</value>
			<value level="6">48</value>
			<value level="7">51</value>
			<value level="8">54</value>
			<value level="9">57</value>
			<value level="10">60</value>
			<value level="11">63</value>
			<value level="12">66</value>
			<value level="13">69</value>
			<value level="14">72</value>
		</mpConsume>
		<operateType>A2</operateType>
		<!--<reuseDelay>10000</reuseDelay>-->
		<reuseDelay>6000</reuseDelay> <!-- Custom -->
		<staticReuse>true</staticReuse>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>DERANGEMENT</trait>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<!-- <affectObject>UNDEAD_REAL_ENEMY</affectObject> -->
		<!-- <effects>
			<effect name="Passive" />
		</effects> -->
	</skill>
	<skill id="1050" toLevel="2" name="Return" nameRu="Возвращение">
		<!-- Teleports to the nearest village. Cannot be used in special places such as the GM Consultation Service. -->
		<icon>Icon.skill1050</icon>
		<blockedInOlympiad>true</blockedInOlympiad>
		<hitTime>
			<value level="1">8000</value>
			<value level="2">1000</value>
		</hitTime>
		<isMagic>4</isMagic>
		<isTeleport>true</isTeleport>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">56</value>
		</magicLevel>
		<mpConsume>
			<value level="1">105</value>
			<value level="2">153</value>
		</mpConsume>
		<itemConsumeId>
			<value level="1">0</value>
			<value level="2">3031</value>
		</itemConsumeId>
		<itemConsumeCount>
			<value level="1">0</value>
			<value level="2">30</value>
		</itemConsumeCount>
		<operateType>A1</operateType>
		<reuseDelay>75000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCanEscape" />
		</conditions>
		<effects>
			<effect name="Escape">
				<escapeType>TOWN</escapeType>
			</effect>
		</effects>
	</skill>
	<skill id="1056" toLevel="12" name="Cancel" nameRu="Отмена">
		<!-- With a certain chance, cancels one or several of the target's buffs. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1056</icon>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-455</value>
			<value level="2">-493</value>
			<value level="3">-530</value>
			<value level="4">-547</value>
			<value level="5">-564</value>
			<value level="6">-580</value>
			<value level="7">-595</value>
			<value level="8">-609</value>
			<value level="9">-622</value>
			<value level="10">-633</value>
			<value level="11">-644</value>
			<value level="12">-653</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>6000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">48</value>
			<value level="2">52</value>
			<value level="3">56</value>
			<value level="4">58</value>
			<value level="5">60</value>
			<value level="6">62</value>
			<value level="7">64</value>
			<value level="8">66</value>
			<value level="9">68</value>
			<value level="10">70</value>
			<value level="11">72</value>
			<value level="12">74</value>
		</magicLevel>
		<mpConsume>
			<value level="1">44</value>
			<value level="2">48</value>
			<value level="3">52</value>
			<value level="4">54</value>
			<value level="5">55</value>
			<value level="6">58</value>
			<value level="7">60</value>
			<value level="8">62</value>
			<value level="9">64</value>
			<value level="10">65</value>
			<value level="11">67</value>
			<value level="12">69</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelByCategory">
				<slot>BUFF</slot>
				<rate>25</rate>
				<max>5</max>
			</effect>
		</effects>
	</skill>
	<skill id="1059" toLevel="10" name="Empower" nameRu="Воодушевление">
		<!-- M. Atk. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1059</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">1</value>
			<value level="5">2</value>
			<value level="6">3</value>
			<value level="7">1</value>
			<value level="8">2</value>
			<value level="9">3</value>
			<value level="9">3</value>
			<value level="10">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<effectPoint>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">243</value>
			<value level="6">418</value>
			<value level="7">495</value>
			<value level="8">243</value>
			<value level="9">418</value>
			<value level="10">495</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">4000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">25</value>
			<value level="2">44</value>
			<value level="3">52</value>
			<value level="4">25</value>
			<value level="5">44</value>
			<value level="6">52</value>
			<value level="7">25</value>
			<value level="8">44</value>
			<value level="9">52</value>
			<value level="9">56</value>
			<value level="10">60</value>
		</magicLevel>
		<mpConsume>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">23</value>
			<value level="6">39</value>
			<value level="7">48</value>
			<value level="8">23</value>
			<value level="9">39</value>
			<value level="10">48</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">10000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
					<value level="5">330</value>
					<value level="6">330</value>
					<value level="7">330</value>
					<value level="8">330</value>
					<value level="9">330</value>
					<value level="9">330</value>
					<value level="10">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1062" toLevel="3" name="Berserker Spirit" nameRu="Дух Берсерка">
		<!-- Max HP $s1\nMax MP $s1\nP. Atk. $s2\nM. Atk. $s3\nAtk. Spd. $s4\nCasting Spd. $s5\nSpeed $s6 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1062</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>BERSERKER</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">20</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">8</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1064" toLevel="22" name="Silence" nameRu="Безмолвие">
		<!-- Blocks enemy's magic skills for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1064</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>SILENCE</abnormalType>
		<abnormalVisualEffect>SILENCE</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-379</value>
			<value level="2">-418</value>
			<value level="3">-457</value>
			<value level="4">-495</value>
			<value level="5">-532</value>
			<value level="6">-549</value>
			<value level="7">-566</value>
			<value level="8">-582</value>
			<value level="9">-597</value>
			<value level="10">-611</value>
			<value level="11">-624</value>
			<value level="12">-635</value>
			<value level="13">-646</value>
			<value level="14">-655</value>
			<value level="15">-659</value>
			<value level="16">-663</value>
			<value level="17">-672</value>
			<value level="18">-681</value>
			<value level="19">-690</value>
			<value level="20">-699</value>
			<value level="21">-706</value>
			<value level="22">-715</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">44</value>
			<value level="3">48</value>
			<value level="4">52</value>
			<value level="5">56</value>
			<value level="6">58</value>
			<value level="7">60</value>
			<value level="8">62</value>
			<value level="9">64</value>
			<value level="10">66</value>
			<value level="11">68</value>
			<value level="12">70</value>
			<value level="13">72</value>
			<value level="14">74</value>
			<value level="15">76</value>
			<value level="16">78</value>
			<value level="17">80</value>
			<value level="18">82</value>
			<value level="19">84</value>
			<value level="20">86</value>
			<value level="21">88</value>
			<value level="22">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">35</value>
			<value level="2">39</value>
			<value level="3">44</value>
			<value level="4">48</value>
			<value level="5">52</value>
			<value level="6">54</value>
			<value level="7">55</value>
			<value level="8">58</value>
			<value level="9">60</value>
			<value level="10">62</value>
			<value level="11">64</value>
			<value level="12">65</value>
			<value level="13">67</value>
			<value level="14">69</value>
			<value level="15">75</value>
			<value level="16">77</value>
			<value level="17">79</value>
			<value level="18">80</value>
			<value level="19">82</value>
			<value level="20">83</value>
			<value level="21">85</value>
			<value level="22">86</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>20000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>DERANGEMENT</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Mute" />
		</effects>
	</skill>
	<skill id="1068" toLevel="10" name="Might" nameRu="Могущество">
		<!-- P. Atk. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1068</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="9">9</value>
			<value level="9">9</value> <!-- FIXME: Auto generated with parser -->
			<value level="10">9</value> <!-- FIXME: Auto generated with parser -->
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<castRange>400</castRange>
		<effectPoint>
	<value level="1">0</value>
	<value level="2">0</value>
	<value level="3">0</value>
	<value level="4">0</value>
	<value level="5">121</value>
	<value level="6">204</value>
	<value level="7">379</value>
	<value level="8">121</value>
	<value level="9">204</value>
	<value level="10">379</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">4000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">7</value>
			<value level="2">20</value>
			<value level="3">40</value>
			<value level="4">7</value>
			<value level="5">60</value>
			<value level="6">40</value>
			<value level="7">7</value>
			<value level="8">60</value>
			<value level="9">40</value>
			<value level="9">40</value> <!-- FIXME: Auto generated with parser -->
			<value level="10">40</value> <!-- FIXME: Auto generated with parser -->
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>
	<value level="1">10000</value>
	<value level="2">10000</value>
	<value level="3">10000</value>
	<value level="4">10000</value>
	<value level="5">2000</value>
	<value level="6">2000</value>
	<value level="7">2000</value>
	<value level="8">2000</value>
	<value level="9">2000</value>
	<value level="10">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">80</value>
					<value level="2">150</value>
					<value level="3">230</value>
					<value level="4">250</value>
					<value level="4">250</value> <!-- FIXME: Auto generated with parser -->
					<value level="5">250</value> <!-- FIXME: Auto generated with parser -->
					<value level="6">250</value> <!-- FIXME: Auto generated with parser -->
					<value level="7">250</value> <!-- FIXME: Auto generated with parser -->
					<value level="8">250</value> <!-- FIXME: Auto generated with parser -->
					<value level="9">250</value> <!-- FIXME: Auto generated with parser -->
					<value level="10">250</value> <!-- FIXME: Auto generated with parser -->
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1069" toLevel="60" name="Sleep" nameRu="Усыпление">
		<!-- Inflicts sleep on the target for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1069</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>SLEEP</abnormalType>
		<abnormalVisualEffect>SLEEP</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-200</value>
			<value level="2">-220</value>
			<value level="3">-240</value>
			<value level="4">-260</value>
			<value level="5">-280</value>
			<value level="6">-300</value>
			<value level="7">-320</value>
			<value level="8">-340</value>
			<value level="9">-360</value>
			<value level="10">-380</value>
			<value level="11">-410</value>
			<value level="12">-420</value>
			<value level="13">-430</value>
			<value level="14">-440</value>
			<value level="15">-450</value>
			<value level="16">-460</value>
			<value level="17">-470</value>
			<value level="18">-480</value>
			<value level="19">-490</value>
			<value level="20">-500</value>
			<value level="21">-510</value>
			<value level="22">-520</value>
			<value level="23">-530</value>
			<value level="24">-540</value>
			<value level="25">-550</value>
			<value level="26">-560</value>
			<value level="27">-570</value>
			<value level="28">-580</value>
			<value level="29">-590</value>
			<value level="30">-600</value>
			<value level="31">-610</value>
			<value level="32">-620</value>
			<value level="33">-630</value>
			<value level="34">-640</value>
			<value level="35">-650</value>
			<value level="36">-660</value>
			<value level="37">-670</value>
			<value level="38">-680</value>
			<value level="39">-690</value>
			<value level="40">-700</value>
			<value level="41">-710</value>
			<value level="42">-720</value>
			<value level="43">-730</value>
			<value level="44">-740</value>
			<value level="45">-750</value>
			<value level="46">-760</value>
			<value level="47">-770</value>
			<value level="48">-780</value>
			<value level="49">-790</value>
			<value level="50">-800</value>
			<value level="51">-810</value>
			<value level="52">-820</value>
			<value level="53">-830</value>
			<value level="54">-840</value>
			<value level="55">-850</value>
			<value level="56">-860</value>
			<value level="57">-870</value>
			<value level="58">-880</value>
			<value level="59">-890</value>
			<value level="60">-900</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">22</value>
			<value level="3">24</value>
			<value level="4">26</value>
			<value level="5">28</value>
			<value level="6">30</value>
			<value level="7">33</value>
			<value level="8">34</value>
			<value level="9">35</value>
			<value level="10">38</value>
			<value level="11">41</value>
			<value level="12">42</value>
			<value level="13">43</value>
			<value level="14">44</value>
			<value level="15">45</value>
			<value level="16">46</value>
			<value level="17">47</value>
			<value level="18">48</value>
			<value level="19">49</value>
			<value level="20">50</value>
			<value level="21">51</value>
			<value level="22">52</value>
			<value level="23">53</value>
			<value level="24">54</value>
			<value level="25">55</value>
			<value level="26">56</value>
			<value level="27">57</value>
			<value level="28">58</value>
			<value level="29">59</value>
			<value level="30">60</value>
			<value level="31">61</value>
			<value level="32">62</value>
			<value level="33">63</value>
			<value level="34">64</value>
			<value level="35">65</value>
			<value level="36">66</value>
			<value level="37">67</value>
			<value level="38">68</value>
			<value level="39">69</value>
			<value level="40">70</value>
			<value level="41">71</value>
			<value level="42">72</value>
			<value level="43">73</value>
			<value level="44">74</value>
			<value level="45">75</value>
			<value level="46">76</value>
			<value level="47">77</value>
			<value level="48">78</value>
			<value level="49">79</value>
			<value level="50">80</value>
			<value level="51">81</value>
			<value level="52">82</value>
			<value level="53">83</value>
			<value level="54">84</value>
			<value level="55">85</value>
			<value level="56">86</value>
			<value level="57">87</value>
			<value level="58">88</value>
			<value level="59">89</value>
			<value level="60">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">22</value>
			<value level="3">23</value>
			<value level="4">24</value>
			<value level="5">25</value>
			<value level="6">26</value>
			<value level="7">27</value>
			<value level="8">28</value>
			<value level="9">29</value>
			<value level="10">30</value>
			<value level="11">31</value>
			<value level="12">32</value>
			<value level="13">33</value>
			<value level="14">34</value>
			<value level="15">35</value>
			<value level="16">36</value>
			<value level="17">37</value>
			<value level="18">38</value>
			<value level="19">39</value>
			<value level="20">40</value>
			<value level="21">41</value>
			<value level="22">42</value>
			<value level="23">43</value>
			<value level="24">44</value>
			<value level="25">45</value>
			<value level="26">46</value>
			<value level="27">47</value>
			<value level="28">48</value>
			<value level="29">49</value>
			<value level="30">50</value>
			<value level="31">51</value>
			<value level="32">52</value>
			<value level="33">53</value>
			<value level="34">54</value>
			<value level="35">55</value>
			<value level="36">56</value>
			<value level="37">57</value>
			<value level="38">58</value>
			<value level="39">59</value>
			<value level="40">60</value>
			<value level="41">61</value>
			<value level="42">62</value>
			<value level="43">63</value>
			<value level="44">64</value>
			<value level="45">65</value>
			<value level="46">66</value>
			<value level="47">67</value>
			<value level="48">68</value>
			<value level="49">69</value>
			<value level="50">70</value>
			<value level="51">71</value>
			<value level="52">72</value>
			<value level="53">73</value>
			<value level="54">74</value>
			<value level="55">75</value>
			<value level="56">76</value>
			<value level="57">77</value>
			<value level="58">78</value>
			<value level="59">79</value>
			<value level="60">80</value>
		</mpConsume>
		<operateType>A2</operateType>
		<removedOnDamage>true</removedOnDamage>
		<reuseDelay>20000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>SLEEP</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>35016;35045;18103</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="1072" toLevel="14" name="Sleeping Cloud" nameRu="Усыпляющее Облако">
		<!-- For $s1, casts Sleep on the target and nearby enemies. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1072</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>SLEEP</abnormalType>
		<abnormalVisualEffect>SLEEP</abnormalVisualEffect>
		<activateRate>70</activateRate>
		<affectLimit>9-10</affectLimit>
		<affectRange>200</affectRange>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-418</value>
			<value level="2">-532</value>
			<value level="3">-582</value>
			<value level="4">-611</value>
			<value level="5">-635</value>
			<value level="6">-659</value>
			<value level="7">-666</value>
			<value level="8">-673</value>
			<value level="9">-680</value>
			<value level="10">-687</value>
			<value level="11">-694</value>
			<value level="12">-701</value>
			<value level="13">-708</value>
			<value level="14">-715</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>
			<value level="1">44</value>
			<value level="2">56</value>
			<value level="3">62</value>
			<value level="4">66</value>
			<value level="5">70</value>
			<value level="6">76</value>
			<value level="7">77</value>
			<value level="8">78</value>
			<value level="9">79</value>
			<value level="10">80</value>
			<value level="11">81</value>
			<value level="12">82</value>
			<value level="13">83</value>
			<value level="14">84</value>
		</magicLevel>
		<mpConsume>
			<value level="1">59</value>
			<value level="2">77</value>
			<value level="3">87</value>
			<value level="4">93</value>
			<value level="5">98</value>
			<value level="6">107</value>
			<value level="7">109</value>
			<value level="8">111</value>
			<value level="9">113</value>
			<value level="10">115</value>
			<value level="11">117</value>
			<value level="12">119</value>
			<value level="13">121</value>
			<value level="14">123</value>
		</mpConsume>
		<operateType>A2</operateType>
		<removedOnDamage>true</removedOnDamage>
		<reuseDelay>60000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>SLEEP</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>35016;35045;18103</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="1073" toLevel="2" name="Increased Power of Magic" nameRu="Увеличение Эффективности Магии">
		<!-- M. Skill Power +$s1, M. Skill MP Consumption -$s2. -->
		<icon>icon.skill1073</icon>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">52</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="MagicalSkillPower">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicMpCost">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="1075" toLevel="15" name="Peace" nameRu="Примирение">
		<!-- Puts target's mind at peace, decreasing their aggression. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1075</icon>
		<castRange>600</castRange>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>
	<value level="1">30</value>
	<value level="2">35</value>
	<value level="3">39</value>
	<value level="4">44</value>
	<value level="5">48</value>
	<value level="6">52</value>
	<value level="7">54</value>
	<value level="8">55</value>
	<value level="9">58</value>
	<value level="10">60</value>
	<value level="11">62</value>
	<value level="12">64</value>
	<value level="13">65</value>
	<value level="14">67</value>
	<value level="15">69</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">35</value>
			<value level="2">40</value>
			<value level="3">44</value>
			<value level="4">48</value>
			<value level="5">52</value>
			<value level="6">56</value>
			<value level="7">58</value>
			<value level="8">60</value>
			<value level="9">62</value>
			<value level="10">64</value>
			<value level="11">66</value>
			<value level="12">68</value>
			<value level="13">70</value>
			<value level="14">72</value>
			<value level="15">74</value>
		</magicLevel>
		<trait>DERANGEMENT</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DeleteHate">
				<chance>80</chance>
			</effect>
		</effects>
	</skill>
	<skill id="1077" toLevel="10" name="Focus" nameRu="Фокусировка">
		<!-- P. Critical Rate $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1077</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="9">9</value>
			<value level="10">10</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<effectPoint>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">243</value>
			<value level="6">418</value>
			<value level="7">495</value>
			<value level="8">243</value>
			<value level="9">418</value>
			<value level="10">495</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">4000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">23</value>
			<value level="6">39</value>
			<value level="7">48</value>
			<value level="8">23</value>
			<value level="9">39</value>
			<value level="10">48</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">10000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">25</value>
			<value level="2">44</value>
			<value level="3">52</value>
			<value level="4">25</value>
			<value level="5">44</value>
			<value level="6">52</value>
			<value level="7">25</value>
			<value level="8">44</value>
			<value level="9">52</value>
			<value level="10">52</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">20</value>
					<value level="2">30</value>
					<value level="3">40</value>
					<value level="4">42</value>
					<value level="5">42</value>
					<value level="6">42</value>
					<value level="7">42</value>
					<value level="8">42</value>
					<value level="9">42</value>
					<value level="10">42</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1078" toLevel="18" name="Concentration" nameRu="Концентрация">
		<!-- For $s1, Casting Interruption Rate -$s2%%. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1078</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="9">9</value>
			<value level="10">10</value>
			<value level="11">11</value>
			<value level="12">12</value>
			<value level="13">13</value>
			<value level="14">14</value>
			<value level="15">15</value>
			<value level="16">16</value>
			<value level="17">17</value>
			<value level="18">18</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CANCEL_PROB_DOWN</abnormalType>
		<effectPoint>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">0</value>
			<value level="6">0</value>
			<value level="7">204</value>
			<value level="8">285</value>
			<value level="9">418</value>
			<value level="10">495</value>
			<value level="11">566</value>
			<value level="12">624</value>
			<value level="13">204</value>
			<value level="14">285</value>
			<value level="15">418</value>
			<value level="16">495</value>
			<value level="17">566</value>
			<value level="18">624</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">1000</value>
			<value level="6">1000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
			<value level="11">4000</value>
			<value level="12">4000</value>
			<value level="13">4000</value>
			<value level="14">4000</value>
			<value level="15">4000</value>
			<value level="16">4000</value>
			<value level="17">4000</value>
			<value level="18">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<operateType>A2</operateType>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">10000</value>
			<value level="5">10000</value>
			<value level="6">10000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
			<value level="14">2000</value>
			<value level="15">2000</value>
			<value level="16">2000</value>
			<value level="17">2000</value>
			<value level="18">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">44</value>
			<value level="4">52</value>
			<value level="5">60</value>
			<value level="6">68</value>
			<value level="7">20</value>
			<value level="8">30</value>
			<value level="9">44</value>
			<value level="10">52</value>
			<value level="11">60</value>
			<value level="12">68</value>
			<value level="13">20</value>
			<value level="14">30</value>
			<value level="15">44</value>
			<value level="16">52</value>
			<value level="17">60</value>
			<value level="18">68</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="ReduceCancel">
				<amount>
					<value level="1">-18</value>
					<value level="2">-25</value>
					<value level="3">-36</value>
					<value level="4">-42</value>
					<value level="5">-48</value>
					<value level="6">-53</value>
					<value level="7">-18</value>
					<value level="8">-25</value>
					<value level="9">-36</value>
					<value level="10">-42</value>
					<value level="11">-48</value>
					<value level="12">-53</value>
					<value level="13">-18</value>
					<value level="14">-25</value>
					<value level="15">-36</value>
					<value level="16">-42</value>
					<value level="17">-48</value>
					<value level="18">-53</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1085" toLevel="10" name="Acumen" nameRu="Проницательность">
		<!-- Casting Spd. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1085</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
			<value level="7">7</value>
			<value level="8">8</value>
			<value level="9">9</value>
			<value level="10">10</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CASTING_TIME_DOWN</abnormalType>
		<effectPoint>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">204</value>
			<value level="6">331</value>
			<value level="7">457</value>
			<value level="8">204</value>
			<value level="9">331</value>
			<value level="10">457</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">4000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">0</value>
			<value level="5">20</value>
			<value level="6">30</value>
			<value level="7">44</value>
			<value level="8">20</value>
			<value level="9">30</value>
			<value level="10">44</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">10000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">35</value>
			<value level="3">48</value>
			<value level="4">20</value>
			<value level="5">35</value>
			<value level="6">48</value>
			<value level="7">20</value>
			<value level="8">35</value>
			<value level="9">48</value>
			<value level="10">48</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">23</value>
					<value level="3">30</value>
					<value level="4">33</value>
					<value level="5">33</value>
					<value level="6">33</value>
					<value level="7">33</value>
					<value level="8">33</value>
					<value level="9">33</value>
					<value level="10">33</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1086" toLevel="5" name="Haste" nameRu="Ускорение">
		<!-- Atk. Spd. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1086</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<effectPoint>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">418</value>
			<value level="5">495</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">4000</value>
			<value level="5">4000</value>
		</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">0</value>
			<value level="4">39</value>
			<value level="5">48</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
		</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">44</value>
			<value level="2">52</value>
			<value level="3">44</value>
			<value level="4">52</value>
			<value level="5">60</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">33</value>
					<value level="3">35</value>
					<value level="4">35</value>
					<value level="5">35</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1087" toLevel="3" name="Agility" nameRu="Проворство">
		<icon>icon.skill1087</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="PhysicalEvasion">
				<amount>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="1090" toLevel="10" name="Life Drain" nameRu="Кража Жизни">
		<!-- <Drain Skill>\n\nDeals magic damage to the enemy with $s1 power. Absorbs $s2 of the inflicted damage as HP. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1090</icon>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-25</value>
			<value level="2">-45</value>
			<value level="3">-60</value>
			<value level="4">-80</value>
			<value level="5">-100</value>
			<value level="6">-120</value>
			<value level="7">-140</value>
			<value level="8">-160</value>
			<value level="9">-180</value>
			<value level="10">-200</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">1</value>
			<value level="2">5</value>
			<value level="3">8</value>
			<value level="4">12</value>
			<value level="5">16</value>
			<value level="6">20</value>
			<value level="7">24</value>
			<value level="8">28</value>
			<value level="9">32</value>
			<value level="10">36</value>
		</magicLevel>
		<mpConsume>
			<value level="1">8</value>
			<value level="2">10</value>
			<value level="3">13</value>
			<value level="4">15</value>
			<value level="5">18</value>
			<value level="6">20</value>
			<value level="7">23</value>
			<value level="8">26</value>
			<value level="9">28</value>
			<value level="10">31</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="HpDrain">
				<power>
					<value level="1">15</value>
					<value level="2">18</value>
					<value level="3">21</value>
					<value level="4">24</value>
					<value level="5">27</value>
					<value level="6">30</value>
					<value level="7">36</value>
					<value level="8">42</value>
					<value level="9">48</value>
					<value level="10">54</value>
				</power>
				<percentage>80</percentage>
			</effect>
		</effects>
	</skill>
	<skill id="1092" toLevel="21" name="Fear" nameRu="Страх">
		<!-- Inflicts Fear on the enemy forcing them to flee for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1092</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>TURN_FLEE</abnormalType>
		<abnormalVisualEffect>TURN_FLEE</abnormalVisualEffect>
		<activateRate>40</activateRate>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-162</value>
			<value level="2">-204</value>
			<value level="3">-243</value>
			<value level="4">-285</value>
			<value level="5">-331</value>
			<value level="6">-379</value>
			<value level="7">-418</value>
			<value level="8">-457</value>
			<value level="9">-495</value>
			<value level="10">-532</value>
			<value level="11">-549</value>
			<value level="12">-566</value>
			<value level="13">-582</value>
			<value level="14">-597</value>
			<value level="15">-611</value>
			<value level="16">-624</value>
			<value level="17">-635</value>
			<value level="18">-646</value>
			<value level="19">-655</value>
			<value level="20">-659</value>
			<value level="21">-662</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">20</value>
			<value level="3">25</value>
			<value level="4">30</value>
			<value level="5">35</value>
			<value level="6">40</value>
			<value level="7">44</value>
			<value level="8">48</value>
			<value level="9">52</value>
			<value level="10">56</value>
			<value level="11">58</value>
			<value level="12">60</value>
			<value level="13">62</value>
			<value level="14">64</value>
			<value level="15">66</value>
			<value level="16">68</value>
			<value level="17">70</value>
			<value level="18">72</value>
			<value level="19">74</value>
			<value level="20">76</value>
			<value level="21">78</value>
		</magicLevel>
		<mpConsume>
			<value level="1">15</value>
			<value level="2">20</value>
			<value level="3">23</value>
			<value level="4">27</value>
			<value level="5">30</value>
			<value level="6">35</value>
			<value level="7">39</value>
			<value level="8">44</value>
			<value level="9">48</value>
			<value level="10">52</value>
			<value level="11">54</value>
			<value level="12">55</value>
			<value level="13">58</value>
			<value level="14">60</value>
			<value level="15">62</value>
			<value level="16">64</value>
			<value level="17">65</value>
			<value level="18">67</value>
			<value level="19">69</value>
			<value level="20">75</value>
			<value level="21">79</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>20000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>DERANGEMENT</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockControl" />
			<effect name="Fear" />
		</effects>
	</skill>
	<skill id="1095" toLevel="5" name="Venom" nameRu="Отрава">
		<!-- For $s1, poisons the target, causing loss of $s2 HP per sec. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1095</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">5</value>
			<value level="5">6</value>
		</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<activateRate>70</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-61</value>
			<value level="2">-81</value>
			<value level="3">-102</value>
			<value level="4">-190</value>
			<value level="5">-248</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">7</value>
			<value level="2">14</value>
			<value level="3">20</value>
			<value level="4">40</value>
			<value level="5">52</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">15</value>
			<value level="3">20</value>
			<value level="4">35</value>
			<value level="5">48</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>POISON</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DamOverTime">
				<power>
					<value level="1">11</value>
					<value level="2">16</value>
					<value level="3">24</value>
					<value level="4">41</value>
					<value level="5">50</value>
				</power>
				<ticks>5</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="1096" toLevel="18" name="Seal of Chaos" nameRu="Печать Хаоса">
		<!-- Nearby enemies' P. Accuracy -$s2 for $s1 -->
		<icon>icon.skill1096</icon>
		<abnormalLevel>
			<value level="1">2</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">3</value>
			<value level="5">3</value>
			<value level="6">3</value>
			<value level="7">3</value>
			<value level="8">3</value>
			<value level="9">3</value>
			<value level="10">3</value>
			<value level="11">3</value>
			<value level="12">3</value>
			<value level="13">3</value>
			<value level="14">3</value>
			<value level="15">3</value>
			<value level="16">3</value>
			<value level="17">3</value>
			<value level="18">3</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">35</value>
			<value level="3">40</value>
			<value level="4">44</value>
			<value level="5">48</value>
			<value level="6">52</value>
			<value level="7">56</value>
			<value level="8">58</value>
			<value level="9">60</value>
			<value level="10">62</value>
			<value level="11">64</value>
			<value level="12">66</value>
			<value level="13">68</value>
			<value level="14">70</value>
			<value level="15">72</value>
			<value level="16">74</value>
			<value level="17">76</value>
			<value level="18">78</value>
		</magicLevel>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>33</chance>
				<targetType>TARGET</targetType>
				<isCritical>false</isCritical>
				<allowWeapons>ALL</allowWeapons>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<skillId>45465</skillId> <!-- Seal of Chaos -->
				<skillLevel>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
					<value level="4">4</value>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
					<value level="11">11</value>
					<value level="12">12</value>
					<value level="13">13</value>
					<value level="14">14</value>
					<value level="15">15</value>
					<value level="16">16</value>
					<value level="17">17</value>
					<value level="18">18</value>
				</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="1097" toLevel="25" name="Dreaming Spirit" nameRu="Сонный Дух">
		<!-- Inflicts sleep on the target for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill1097</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>SLEEP</abnormalType>
		<abnormalVisualEffect>SLEEP</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<basicProperty>MAGIC</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-121</value>
			<value level="2">-162</value>
			<value level="3">-204</value>
			<value level="4">-243</value>
			<value level="5">-285</value>
			<value level="6">-331</value>
			<value level="7">-379</value>
			<value level="8">-418</value>
			<value level="9">-457</value>
			<value level="10">-495</value>
			<value level="11">-532</value>
			<value level="12">-549</value>
			<value level="13">-566</value>
			<value level="14">-582</value>
			<value level="15">-597</value>
			<value level="16">-611</value>
			<value level="17">-624</value>
			<value level="18">-635</value>
			<value level="19">-646</value>
			<value level="20">-655</value>
			<value level="21">-659</value>
			<value level="22">-663</value>
			<value level="23">-667</value>
			<value level="24">-671</value>
			<value level="25">-675</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>
			<value level="1">7</value>
			<value level="2">14</value>
			<value level="3">20</value>
			<value level="4">25</value>
			<value level="5">30</value>
			<value level="6">35</value>
			<value level="7">40</value>
			<value level="8">44</value>
			<value level="9">48</value>
			<value level="10">52</value>
			<value level="11">56</value>
			<value level="12">58</value>
			<value level="13">60</value>
			<value level="14">62</value>
			<value level="15">64</value>
			<value level="16">66</value>
			<value level="17">68</value>
			<value level="18">70</value>
			<value level="19">72</value>
			<value level="20">74</value>
			<value level="21">76</value>
			<value level="22">78</value>
			<value level="23">80</value>
			<value level="24">82</value>
			<value level="25">84</value>
		</magicLevel>
		<mpConsume>
			<value level="1">10</value>
			<value level="2">15</value>
			<value level="3">20</value>
			<value level="4">23</value>
			<value level="5">27</value>
			<value level="6">30</value>
			<value level="7">35</value>
			<value level="8">39</value>
			<value level="9">44</value>
			<value level="10">48</value>
			<value level="11">52</value>
			<value level="12">54</value>
			<value level="13">55</value>
			<value level="14">58</value>
			<value level="15">60</value>
			<value level="16">62</value>
			<value level="17">64</value>
			<value level="18">65</value>
			<value level="19">67</value>
			<value level="20">69</value>
			<value level="21">75</value>
			<value level="22">79</value>
			<value level="23">84</value>
			<value level="24">88</value>
			<value level="25">92</value>
		</mpConsume>
		<operateType>A2</operateType>
		<removedOnDamage>true</removedOnDamage>
		<reuseDelay>20000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>SLEEP</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>35016;35045;18103</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="1099" toLevel="23" name="Seal of Slow" nameRu="Печать Замедления">
		<!-- Nearby enemies' Speed -$s2 for $s1 -->
		<icon>icon.skill1099</icon>
		<abnormalLevel>
			<value level="1">2</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">3</value>
			<value level="5">3</value>
			<value level="6">3</value>
			<value level="7">3</value>
			<value level="8">3</value>
			<value level="9">3</value>
			<value level="10">3</value>
			<value level="11">3</value>
			<value level="12">3</value>
			<value level="13">3</value>
			<value level="14">3</value>
			<value level="15">3</value>
			<value level="16">3</value>
			<value level="17">3</value>
			<value level="18">3</value>
			<value level="19">3</value>
			<value level="20">3</value>
			<value level="21">3</value>
			<value level="22">3</value>
			<value level="23">3</value>
		</abnormalLevel>
		<magicLevel>
			<value level="1">35</value>
			<value level="2">40</value>
			<value level="3">44</value>
			<value level="4">48</value>
			<value level="5">52</value>
			<value level="6">56</value>
			<value level="7">58</value>
			<value level="8">60</value>
			<value level="9">62</value>
			<value level="10">64</value>
			<value level="11">66</value>
			<value level="12">68</value>
			<value level="13">70</value>
			<value level="14">72</value>
			<value level="15">74</value>
			<value level="16">76</value>
			<value level="17">78</value>
			<value level="18">79</value>
			<value level="19">80</value>
			<value level="20">81</value>
			<value level="21">82</value>
			<value level="22">83</value>
			<value level="23">84</value>
		</magicLevel>
		<operateType>P</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>33</chance>
				<targetType>TARGET</targetType>
				<isCritical>false</isCritical>
				<allowWeapons>ALL</allowWeapons>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<skillId>45467</skillId> <!-- Seal of Slow -->
				<skillLevel>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
					<value level="4">4</value>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
					<value level="11">11</value>
					<value level="12">12</value>
					<value level="13">13</value>
					<value level="14">14</value>
					<value level="15">15</value>
					<value level="16">16</value>
					<value level="17">17</value>
					<value level="18">18</value>
					<value level="19">19</value>
					<value level="20">20</value>
					<value level="21">21</value>
					<value level="22">22</value>
					<value level="23">23</value>
				</skillLevel>
			</effect>
		</effects>
	</skill>
</list>
