<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="10015" toLevel="6" name="Chain Strike" nameRu="Цепной Удар">
		<!-- Pulls a selected target to you and attacks. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill10015</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">6</value>
		</abnormalLevel>
		<abnormalTime>1</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<activateRate>190</activateRate>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">82</value>
			<value level="3">84</value>
			<value level="4">86</value>
			<value level="5">88</value>
			<value level="6">90</value>
		</magicLevel>
		<operateType>A2</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<mpConsume>
			<value level="1">69</value>
			<value level="2">73</value>
			<value level="3">77</value>
			<value level="4">81</value>
			<value level="5">85</value>
			<value level="6">89</value>
		</mpConsume>
		<castRange>700</castRange>
		<hitTime>500</hitTime>
		<coolTime>1000</coolTime>
		<!--<reuseDelay>15000</reuseDelay>-->
		<reuseDelay>8000</reuseDelay> <!-- Custom -->
		<reuseDelayPve>2000</reuseDelayPve> <!-- Custom -->
		<reuseDelayGroup>10015</reuseDelayGroup>
		<effectPoint>
			<value level="1">-4132</value>
			<value level="2">-4198</value>
			<value level="3">-4265</value>
			<value level="4">-4320</value>
			<value level="5">-4400</value>
			<value level="6">-4470</value>
		</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<effectRange>1100</effectRange>
		<trait>PULL</trait>
		<isDebuff>true</isDebuff>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PullBack">
				<speed>600</speed>
				<delay>600</delay>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>35016</allowedSkills>
			</effect>
			<effect name="GetAgro" />
		</effects>
	</skill>
	<skill id="10094" toLevel="5" name="Mass Lightning Strike" nameRu="Массовый Удар Молнии">
		<!-- Strikes the target and nearby enemies with a lightning bolt that inflicts damage with $s1 power. Paralyzes enemies for $s2.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skll10094</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<activateRate>40</activateRate>
		<affectLimit>10-10</affectLimit>
		<affectRange>150</affectRange>
		<basicProperty>PHYSICAL</basicProperty>
		<icon>icon.skll10094</icon>
		<lvlBonusRate>20</lvlBonusRate>
		<operateType>A2</operateType>
		<isMagic>0</isMagic>
		<magicLevel>
			<value level="1">80</value>
			<value level="2">81</value>
			<value level="3">82</value>
			<value level="4">83</value>
			<value level="5">84</value>
		</magicLevel>
		<mpConsume>
	<value level="1">94</value>
	<value level="2">94</value>
	<value level="3">96</value>
	<value level="4">97</value>
	<value level="5">99</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<castRange>400</castRange>
		<hitTime>1500</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>60000</reuseDelay>
		<effectPoint>
	<value level="1">-6100</value>
	<value level="2">-6175</value>
	<value level="3">-6250</value>
	<value level="4">-6325</value>
	<value level="5">-6400</value>
		</effectPoint>
		<isDebuff>true</isDebuff>
		<trait>PARALYZE</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1997</value>
					<value level="2">2096</value>
					<value level="3">2179</value>
					<value level="4">2266</value>
					<value level="5">2356</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>35016;35045;18103</allowedSkills>
			</effect>
		</effects>
	</skill>
</list>
