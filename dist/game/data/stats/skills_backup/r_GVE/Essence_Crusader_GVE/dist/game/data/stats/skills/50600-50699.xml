<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="50600" toLevel="1" name="Demonic Pistols">
		<!-- Pistols imbued with demonic power. M. Atk. in range combat (100%). -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="50601" toLevel="15" name="Rabbit Doll's Sorcery Lv. 1">
		<!-- PvE damage $s1 P. Atk. $s2 M. Atk. $s2 P. Def. $s3 M. Def. $s3 Acquired XP/ SP $s4 MEN $s5 -->
		<icon>
			<value level="1">icon.ev_pink_rabbit_00</value>
			<value level="2">icon.ev_pink_rabbit_00</value>
			<value level="3">icon.ev_pink_rabbit_00</value>
			<value level="4">icon.ev_pink_rabbit_00</value>
			<value level="5">icon.ev_pink_rabbit_00</value>
			<value level="6">icon.ev_pink_rabbit_00</value>
			<value level="7">icon.ev_pink_rabbit_00</value>
			<value level="8">icon.ev_pink_rabbit_00</value>
			<value level="9">icon.ev_pink_rabbit_00</value>
			<value level="10">icon.ev_pink_rabbit_00</value>
			<value level="11">icon.ev_pink_rabbit_01</value>
			<value level="12">icon.ev_pink_rabbit_01</value>
			<value level="13">icon.ev_pink_rabbit_01</value>
			<value level="14">icon.ev_pink_rabbit_01</value>
			<value level="15">icon.ev_pink_rabbit_01</value>
		</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="PveMagicalSkillDamageBonus">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">2</value>
					<value level="11">3</value>
					<value level="12">3</value>
					<value level="13">3</value>
					<value level="14">4</value>
					<value level="15">5</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalAttackDamageBonus">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">2</value>
					<value level="11">3</value>
					<value level="12">3</value>
					<value level="13">3</value>
					<value level="14">4</value>
					<value level="15">5</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PvePhysicalSkillDamageBonus">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">2</value>
					<value level="11">3</value>
					<value level="12">3</value>
					<value level="13">3</value>
					<value level="14">4</value>
					<value level="15">5</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">5</value>
					<value level="7">10</value>
					<value level="8">15</value>
					<value level="9">20</value>
					<value level="10">25</value>
					<value level="11">30</value>
					<value level="12">35</value>
					<value level="13">40</value>
					<value level="14">45</value>
					<value level="15">50</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">5</value>
					<value level="7">10</value>
					<value level="8">15</value>
					<value level="9">20</value>
					<value level="10">25</value>
					<value level="11">30</value>
					<value level="12">35</value>
					<value level="13">40</value>
					<value level="14">45</value>
					<value level="15">50</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">10</value>
					<value level="7">20</value>
					<value level="8">30</value>
					<value level="9">40</value>
					<value level="10">50</value>
					<value level="11">60</value>
					<value level="12">70</value>
					<value level="13">80</value>
					<value level="14">90</value>
					<value level="15">100</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">10</value>
					<value level="7">20</value>
					<value level="8">30</value>
					<value level="9">40</value>
					<value level="10">50</value>
					<value level="11">60</value>
					<value level="12">70</value>
					<value level="13">80</value>
					<value level="14">90</value>
					<value level="15">100</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ExpModify">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
					<value level="11">6</value>
					<value level="12">7</value>
					<value level="13">8</value>
					<value level="14">9</value>
					<value level="15">10</value>
				</amount>
			</effect>
			<effect name="SpModify">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
					<value level="11">6</value>
					<value level="12">7</value>
					<value level="13">8</value>
					<value level="14">9</value>
					<value level="15">10</value>
				</amount>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="1" toLevel="5">0</value>
					<value fromLevel="6" toLevel="15">1</value>
				</amount>
				<stat>MEN</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="1" toLevel="7">0</value>
					<value fromLevel="8" toLevel="15">1</value>
				</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="1" toLevel="9">0</value>
					<value fromLevel="10" toLevel="15">1</value>
				</amount>
				<stat>WIT</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="1" toLevel="11">0</value>
					<value fromLevel="12" toLevel="15">1</value>
				</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="1" toLevel="13">0</value>
					<value fromLevel="14" toLevel="15">1</value>
				</amount>
				<stat>INT</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="1" toLevel="14">0</value>
					<value level="15">1</value>
				</amount>
				<stat>STR</stat>
			</effect>
			<effect name="DefenceCriticalRate">
				<amount>
					<value fromLevel="1" toLevel="12">0</value>
					<value level="13">-1</value>
					<value level="14">-3</value>
					<value level="15">-5</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceSkillCriticalDamage">
				<amount>
					<value fromLevel="1" toLevel="12">0</value>
					<value level="13">-1</value>
					<value level="14">-3</value>
					<value level="15">-5</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>2</chance>
				<targetType>SELF</targetType>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50602</skillId> <!-- Fast Moving -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50602" toLevel="1" name="Fast Moving">
		<!-- Speed +5 -->
		<icon>icon.ev_pink_rabbit_01</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<isMagic>5</isMagic>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>5</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="50603" toLevel="1" name="Prestigious Rune of Infinite Grace">
		<!-- When in inventory, recovers Sayha's Grace points. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="50604" toLevel="1" name="Galaxia's Ancient Sword">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50606</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50606</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50606</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50605" toLevel="10" name="+1 Galaxia's Ancient Sword">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50606</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50606</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50606</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50607</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50607</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50607</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>DEX</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>CON</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50606" toLevel="22" name="Galaxia's Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">103</value>
					<value level="2">105</value>
					<value level="3">107</value>
					<value level="4">109</value>
					<value level="5">112</value>
					<value level="6">116</value>
					<value level="7">120</value>
					<value level="8">124</value>
					<value level="9">128</value>
					<value level="10">132</value>
					<value level="11">136</value>
					<value level="12">61</value>
					<value level="13">63</value>
					<value level="14">64</value>
					<value level="15">65</value>
					<value level="16">67</value>
					<value level="17">69</value>
					<value level="18">72</value>
					<value level="19">74</value>
					<value level="20">76</value>
					<value level="21">79</value>
					<value level="22">81</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50607" toLevel="20" name="Galaxia's Enhanced Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack" fromLevel="5" toLevel="10">
				<power>
					<value level="5">174</value>
					<value level="6">180</value>
					<value level="7">186</value>
					<value level="8">192</value>
					<value level="9">198</value>
					<value level="10">204</value>
				</power>
			</effect>
			<effect name="MagicalAttack" fromLevel="15" toLevel="20">
				<power>
					<value level="15">104</value>
					<value level="16">108</value>
					<value level="17">111</value>
					<value level="18">115</value>
					<value level="19">118</value>
					<value level="20">122</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50608" toLevel="1" name="Phiriel's Rapier">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50610</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50610</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50610</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50609" toLevel="10" name="+1 Phiriel's Rapier">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50610</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50610</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50610</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50611</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50611</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50611</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>STR</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>INT</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50610" toLevel="22" name="Phiriel's Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">103</value>
					<value level="2">105</value>
					<value level="3">107</value>
					<value level="4">109</value>
					<value level="5">112</value>
					<value level="6">116</value>
					<value level="7">120</value>
					<value level="8">124</value>
					<value level="9">128</value>
					<value level="10">132</value>
					<value level="11">136</value>
					<value level="12">61</value>
					<value level="13">63</value>
					<value level="14">64</value>
					<value level="15">65</value>
					<value level="16">67</value>
					<value level="17">69</value>
					<value level="18">72</value>
					<value level="19">74</value>
					<value level="20">76</value>
					<value level="21">79</value>
					<value level="22">81</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50611" toLevel="20" name="Phiriel's Enhanced Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack" fromLevel="5" toLevel="10">
				<power>
					<value level="5">174</value>
					<value level="6">180</value>
					<value level="7">186</value>
					<value level="8">192</value>
					<value level="9">198</value>
					<value level="10">204</value>
				</power>
			</effect>
			<effect name="MagicalAttack" fromLevel="15" toLevel="20">
				<power>
					<value level="15">104</value>
					<value level="16">108</value>
					<value level="17">111</value>
					<value level="18">115</value>
					<value level="19">118</value>
					<value level="20">122</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50612" toLevel="1" name="Anais' Fists">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50614</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50614</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50614</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50613" toLevel="10" name="+1 Anais' Fists">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50614</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50614</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50614</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50615</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50615</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50615</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>STR</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>CON</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50614" toLevel="22" name="Anais' Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">103</value>
					<value level="2">105</value>
					<value level="3">107</value>
					<value level="4">109</value>
					<value level="5">112</value>
					<value level="6">116</value>
					<value level="7">120</value>
					<value level="8">124</value>
					<value level="9">128</value>
					<value level="10">132</value>
					<value level="11">136</value>
					<value level="12">61</value>
					<value level="13">63</value>
					<value level="14">64</value>
					<value level="15">65</value>
					<value level="16">67</value>
					<value level="17">69</value>
					<value level="18">72</value>
					<value level="19">74</value>
					<value level="20">76</value>
					<value level="21">79</value>
					<value level="22">81</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50615" toLevel="20" name="Anais' Enhanced Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack" fromLevel="5" toLevel="10">
				<power>
					<value level="5">174</value>
					<value level="6">180</value>
					<value level="7">186</value>
					<value level="8">192</value>
					<value level="9">198</value>
					<value level="10">204</value>
				</power>
			</effect>
			<effect name="MagicalAttack" fromLevel="15" toLevel="20">
				<power>
					<value level="15">104</value>
					<value level="16">108</value>
					<value level="17">111</value>
					<value level="18">115</value>
					<value level="19">118</value>
					<value level="20">122</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50616" toLevel="1" name="Juriel's Dual Swords">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50618</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50618</skillId>
				<skillLevel>1</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>1</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50618</skillId>
				<skillLevel>12</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50617" toLevel="10" name="+1 Juriel's Dual Swords">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<!-- Normal (Auto Attack) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">4</value>
					<value level="7">6</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">12</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50618</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Physical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50618</skillId>
				<skillLevel>
					<value level="1">2</value>
					<value level="2">3</value>
					<value level="3">4</value>
					<value level="4">5</value>
					<value level="5">6</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">9</value>
					<value level="9">10</value>
					<value level="10">11</value>
				</skillLevel>
			</effect>
			<!-- Normal (Magical Skill) -->
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">4</value>
					<value level="6">7</value>
					<value level="7">10</value>
					<value level="8">13</value>
					<value level="9">16</value>
					<value level="10">20</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50618</skillId>
				<skillLevel>
					<value level="1">13</value>
					<value level="2">14</value>
					<value level="3">15</value>
					<value level="4">16</value>
					<value level="5">17</value>
					<value level="6">18</value>
					<value level="7">19</value>
					<value level="8">20</value>
					<value level="9">21</value>
					<value level="10">22</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Auto Attack) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1</value>
					<value level="8">2</value>
					<value level="9">2</value>
					<value level="10">3</value>
				</chance>
				<targetType>TARGET</targetType>
				<allowSkillAttack>false</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50619</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Physical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyPhysicalSkill>true</onlyPhysicalSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50619</skillId>
				<skillLevel>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</skillLevel>
			</effect>
			<!-- Enhanced (Magical Skill) -->
			<effect name="TriggerSkillByAttack" fromLevel="5" toLevel="10">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">2</value>
					<value level="8">3</value>
					<value level="9">4</value>
					<value level="10">5</value>
				</chance>
				<targetType>TARGET</targetType>
				<onlyMagicSkill>true</onlyMagicSkill>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>false</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50619</skillId>
				<skillLevel>
					<value level="5">15</value>
					<value level="6">16</value>
					<value level="7">17</value>
					<value level="8">18</value>
					<value level="9">19</value>
					<value level="10">20</value>
				</skillLevel>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>STR</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
			<effect name="StatUp" fromLevel="5" toLevel="10">
				<stat>CON</stat>
				<amount>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">3</value>
					<value level="8">4</value>
					<value level="9">5</value>
					<value level="10">6</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="50618" toLevel="22" name="Juriel's Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">103</value>
					<value level="2">105</value>
					<value level="3">107</value>
					<value level="4">109</value>
					<value level="5">112</value>
					<value level="6">116</value>
					<value level="7">120</value>
					<value level="8">124</value>
					<value level="9">128</value>
					<value level="10">132</value>
					<value level="11">136</value>
					<value level="12">61</value>
					<value level="13">63</value>
					<value level="14">64</value>
					<value level="15">65</value>
					<value level="16">67</value>
					<value level="17">69</value>
					<value level="18">72</value>
					<value level="19">74</value>
					<value level="20">76</value>
					<value level="21">79</value>
					<value level="22">81</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50619" toLevel="20" name="Juriel's Enhanced Justice">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<isMagic>1</isMagic>
		<effects>
			<effect name="MagicalAttack" fromLevel="5" toLevel="10">
				<power>
					<value level="5">174</value>
					<value level="6">180</value>
					<value level="7">186</value>
					<value level="8">192</value>
					<value level="9">198</value>
					<value level="10">204</value>
				</power>
			</effect>
			<effect name="MagicalAttack" fromLevel="15" toLevel="20">
				<power>
					<value level="15">104</value>
					<value level="16">108</value>
					<value level="17">111</value>
					<value level="18">115</value>
					<value level="19">118</value>
					<value level="20">122</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="50620" toLevel="6" name="Heavenly Cloak">
		<!-- A long-wearing cloak protecting whole body.\n\n<Effect>\nMax HP +$s1\nMax MP +$s1\nBow Resistance +$s2 -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">20</value>
					<value level="2">21</value>
					<value level="3">22</value>
					<value level="4">23</value>
					<value level="5">24</value>
					<value level="6">25</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>
					<value level="1">20</value>
					<value level="2">21</value>
					<value level="3">22</value>
					<value level="4">23</value>
					<value level="5">24</value>
					<value level="6">25</value>
				</amount>
				<mode>PER</mode>
			</effect>
            <effect name="DefenceTrait">
				<BLUNT>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</BLUNT>
				<ANCIENTSWORD>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</ANCIENTSWORD>
				<RAPIER>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</RAPIER>
				<PISTOLS>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</PISTOLS>
				<SWORD>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</SWORD>
				<DUAL>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</DUAL>
				<BOW>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</BOW>
				<POLE>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</POLE>
				<DAGGER>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</DAGGER>
				<DUALFIST>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</DUALFIST>
				<DUALDAGGER>
					<value level="1">15</value>
					<value level="2">16</value>
					<value level="3">17</value>
					<value level="4">18</value>
					<value level="5">19</value>
					<value level="6">20</value>
				</DUALDAGGER>
			</effect>
			<effect name="ExpModify">
				<amount>30</amount>
			</effect>
			<effect name="SpModify">
				<amount>30</amount>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">500</value>
					<value level="2">1000</value>
					<value level="3">1500</value>
					<value level="4">2000</value>
					<value level="5">2500</value>
					<value level="6">3000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">500</value>
					<value level="2">1000</value>
					<value level="3">1500</value>
					<value level="4">2000</value>
					<value level="5">2500</value>
					<value level="6">3000</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalDamage">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="SkillCriticalDamage">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceCriticalRate">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceMagicCriticalRate">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefencePhysicalSkillCriticalRate">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceCriticalDamage">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceMagicCriticalDamage">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceSkillCriticalDamage">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="RealDamageResist">
				<amount>
					<value level="1">5</value>
					<value level="2">6</value>
					<value level="3">7</value>
					<value level="4">8</value>
					<value level="5">9</value>
					<value level="6">10</value>
				</amount>
				<mode>PER</mode>
            </effect>
            <effect name="PhysicalSkillPower" fromLevel="4" toLevel="6">
                <amount>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">3</value>
                </amount>
                <mode>PER</mode>
            </effect>
            <effect name="MagicalSkillPower" fromLevel="4" toLevel="6">
                <amount>
					<value level="4">1</value>
					<value level="5">2</value>
					<value level="6">3</value>
                </amount>
                <mode>PER</mode>
            </effect>
            <effect name="DefenceTrait">
                <DISARM>
					<value level="1">20</value>
					<value level="2">22</value>
					<value level="3">24</value>
					<value level="4">26</value>
					<value level="5">28</value>
					<value level="6">30</value>
                </DISARM>
            </effect>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>5</chance> <!-- ? -->
				<targetType>TARGET</targetType>
				<allowSkillAttack>true</allowSkillAttack>
				<allowNormalAttack>true</allowNormalAttack>
				<allowWeapons>ALL</allowWeapons>
				<skillId>50621</skillId> <!-- Angelic Recovery -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="50621" toLevel="1" name="Angelic Recovery">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<abnormalTime>10</abnormalTime>
		<isTriggeredSkill>true</isTriggeredSkill>
		<reuseDelay>120000</reuseDelay>
		<effects>
		    <effect name="Speed">
		        <amount>33</amount>
		        <mode>DIFF</mode>
            </effect>
			<effect name="HealOverTime">
				<power>92</power>
				<ticks>8</ticks>
			</effect>
			<effect name="ManaHealOverTime">
				<power>23</power>
				<ticks>8</ticks>
			</effect>
		</effects>
	</skill>
</list>
