<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="45101" toLevel="1" name="Final Frenzy" nameRu="Предельное Безумие">
		<!-- Increases P. Atk. The effect is cancelled upon recovering 60%% HP. -->
		<icon>icon.skill0290</icon>
		<operateType>A2</operateType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<magicCriticalRate>5</magicCriticalRate>
		<isTriggeredSkill>true</isTriggeredSkill>
		<irreplacableBuff>true</irreplacableBuff>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<isMagic>4</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="45102" toLevel="1" name="Passive Finality Cancellation" nameRu="Пассивная Отмена Предельности">
		<!-- Final Frenzy/ Final Fortress Cancellation -->
		<operateType>A1</operateType>
	</skill>
	<skill id="45103" toLevel="1" name="Final Fortress" nameRu="Последний Рубеж">
		<!-- Increases P. Def. The effect is cancelled upon recovering 60%% HP. -->
		<icon>icon.skill0291</icon>
		<operateType>A1</operateType>
	</skill>
	<skill id="45104" toLevel="4" name="Acumen" nameRu="Проницательность">
		<!-- Casting Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1085</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CASTING_TIME_DOWN</abnormalType>
		<castRange>-1</castRange>
		<effectPoint>0</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>3</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">35</value>
			<value level="3">48</value>
			<value level="4">48</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">23</value>
					<value level="3">30</value>
					<value level="4">33</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45105" toLevel="4" name="Empower" nameRu="Воодушевление">
		<!-- M. Atk. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1059</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">25</value>
			<value level="2">44</value>
			<value level="3">52</value>
			<value level="4">52</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45106" toLevel="3" name="Wild Magic" nameRu="Дикая Магия">
		<!-- M. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1303</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MAGIC_CRITICAL_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicCriticalRate">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45107" toLevel="3" name="Wind Walk" nameRu="Легкая Походка">
		<!-- Speed $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1204</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>
					<value level="1">20</value>
					<value level="2">33</value>
					<value level="3">33</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45108" toLevel="3" name="Magic Barrier" nameRu="Магический Барьер">
		<!-- M. Def. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1036</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">250</value>
					<value level="2">350</value>
					<value level="3">380</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45109" toLevel="4" name="Focus" nameRu="Фокусировка">
		<!-- P. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1077</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">20</value>
					<value level="2">30</value>
					<value level="3">40</value>
					<value level="4">42</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45110" toLevel="4" name="Death Whisper" nameRu="Шепот Смерти">
		<!-- P. Critical Damage $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1242</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_DMG_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalDamage">
				<amount>
					<value level="1">25</value>
					<value level="2">30</value>
					<value level="3">35</value>
					<value level="4">38</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45111" toLevel="3" name="Haste" nameRu="Ускорение">
		<!-- Atk. Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1086</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">40</value>
			<value level="3">60</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">33</value>
					<value level="3">33</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45112" toLevel="3" name="Berserker Spirit" nameRu="Дух Берсерка">
		<!-- Max HP $s1\nMax MP $s1\nP. Atk. $s2\nM. Atk. $s3\nAtk. Spd. $s4\nCasting Spd. $s5\nSpeed $s6\n\nConsumes $s7 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1062</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>BERSERKER</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">20</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">8</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45113" toLevel="4" name="Clarity" nameRu="Чистота">
		<!-- Song/ Dance/ P. Skill MP Consumption $s1\nM. Skill MP Consumption $s2\n\nConsumes $s3 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1397</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CHEAP_MAGIC</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-4</value>
					<value level="2">-7</value>
					<value level="3">-10</value>
					<value level="4">-11</value>
				</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="45114" toLevel="4" name="Maphr's Focus" nameRu="Фокусировка Мафр">
		<!-- P. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1466</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">20</value>
					<value level="2">30</value>
					<value level="3">40</value>
					<value level="4">42</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45115" toLevel="4" name="Maphr's Death Whisper" nameRu="Шепот Смерти Мафр">
		<!-- P. Critical Damage $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill19004</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_DMG_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalDamage">
				<amount>
					<value level="1">25</value>
					<value level="2">30</value>
					<value level="3">35</value>
					<value level="4">38</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45116" toLevel="3" name="Maphr's Haste" nameRu="Ускорение Мафр">
		<!-- Atk. Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0426</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">40</value>
			<value level="3">60</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">33</value>
					<value level="3">35</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45117" toLevel="3" name="Maphr's Wind Walk" nameRu="Легкая Походка Мафр">
		<!-- Speed $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0225</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>
					<value level="1">20</value>
					<value level="2">33</value>
					<value level="3">35</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45118" toLevel="3" name="Maphr's Magic Barrier" nameRu="Магический Барьер Мафр">
		<!-- M. Def. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0383</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">250</value>
					<value level="2">350</value>
					<value level="3">380</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45119" toLevel="4" name="Maphr's Might" nameRu="Могущество Мафр">
		<!-- P. Atk. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill10252</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">3</value>
			<value level="2">20</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">80</value>
					<value level="2">150</value>
					<value level="3">230</value>
					<value level="4">250</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45120" toLevel="4" name="Maphr's Shield" nameRu="Щит Мафр">
		<!-- P. Def. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill11252</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">2</value>
			<value level="2">20</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45121" toLevel="4" name="Chant of Focus" nameRu="Напев Фокусировки">
		<!-- P. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1308</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">20</value>
					<value level="2">30</value>
					<value level="3">40</value>
					<value level="4">42</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45122" toLevel="4" name="Chant of Death Whisper" nameRu="Напев Шепота Смерти">
		<!-- P. Critical Damage $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1253</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_DMG_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalDamage">
				<amount>
					<value level="1">25</value>
					<value level="2">30</value>
					<value level="3">35</value>
					<value level="4">38</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45123" toLevel="3" name="Chant of Haste" nameRu="Напев Ускорения">
		<!-- Atk. Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1251</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">40</value>
			<value level="3">60</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">33</value>
					<value level="3">35</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45124" toLevel="3" name="Chant of Wind Walk" nameRu="Напев Легкой Походки">
		<!-- Speed $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1535</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>
					<value level="1">20</value>
					<value level="2">33</value>
					<value level="3">35</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45125" toLevel="3" name="Chant of Magic Barrier" nameRu="Напев Магического Барьера">
		<!-- M. Def. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1549</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">250</value>
					<value level="2">350</value>
					<value level="3">380</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45126" toLevel="4" name="Chant of Might" nameRu="Напев Могущества">
		<!-- P. Atk. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1007</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">40</value>
			<value level="3">76</value>
			<value level="4">80</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">80</value>
					<value level="2">150</value>
					<value level="3">230</value>
					<value level="4">250</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45127" toLevel="4" name="Chant of Shield" nameRu="Напев Щита">
		<!-- P. Def. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1009</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">40</value>
			<value level="3">76</value>
			<value level="4">80</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45128" toLevel="3" name="Chant of Berserker Spirit" nameRu="Напев Духа Берсерка">
		<!-- Max HP $s1\nMax MP $s1\nP. Atk. $s2\nM. Atk. $s3\nAtk. Spd. $s4\nCasting Spd. $s5\nSpeed $s6\n\nConsumes $s7 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill19207</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>BERSERKER</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">20</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">8</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45129" toLevel="4" name="Chant of Clarity" nameRu="Напев Чистоты">
		<!-- Song/ Dance/ P. Skill MP Consumption $s1\nM. Skill MP Consumption $s2\n\nConsumes $s3 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1600</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CHEAP_MAGIC</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-4</value>
					<value level="2">-7</value>
					<value level="3">-10</value>
					<value level="4">-11</value>
				</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="45130" toLevel="4" name="Maphr’s Acumen" nameRu="Проницательность Мафр">
		<!-- Casting Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill11184</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CASTING_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">23</value>
					<value level="3">30</value>
					<value level="4">33</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45131" toLevel="4" name="Maphr's Empower" nameRu="Воодушевление Мафр">
		<!-- M. Atk. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill11068</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45132" toLevel="3" name="Maphr's Wild Magic" nameRu="Дикая Магия Мафр">
		<!-- M. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill11151</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MAGIC_CRITICAL_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicCriticalRate">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45133" toLevel="3" name="Maphr's Berserker Spirit" nameRu="Дух Берсерка Мафр">
		<!-- Max HP $s1\nMax MP $s1\nP. Atk. $s2\nM. Atk. $s3\nAtk. Spd. $s4\nCasting Spd. $s5\nSpeed $s6\n\nConsumes $s7 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill19192</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>BERSERKER</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">20</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">8</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45134" toLevel="4" name="Maphr's Clarity" nameRu="Чистота Мафр">
		<!-- Song/ Dance/ P. Skill MP Consumption $s1\nM. Skill MP Consumption $s2\n\nConsumes $s3 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill10256</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CHEAP_MAGIC</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-4</value>
					<value level="2">-7</value>
					<value level="3">-10</value>
					<value level="4">-11</value>
				</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="45135" toLevel="4" name="Chant of Acumen" nameRu="Напев Проницательности">
		<!-- Casting Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1006</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CASTING_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">23</value>
					<value level="3">30</value>
					<value level="4">33</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45136" toLevel="4" name="Chant of Empower" nameRu="Напев Воодушевления">
		<!-- M. Atk. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1002</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45137" toLevel="3" name="Chant of Wild Magic" nameRu="Напев Дикой Магии">
		<!-- M. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1413</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MAGIC_CRITICAL_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicCriticalRate">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45138" toLevel="4" name="Chant of Clarity" nameRu="Напев Чистоты">
		<!-- Song/ Dance/ P. Skill MP Consumption $s1\nM. Skill MP Consumption $s2 -->
		<shortcutToggleType>1</shortcutToggleType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CHEAP_MAGIC</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-4</value>
					<value level="2">-7</value>
					<value level="3">-10</value>
					<value level="4">-11</value>
				</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="45139" toLevel="4" name="Soul Focus" nameRu="Фокусировка Души">
		<!-- P. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill19299</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">20</value>
					<value level="2">30</value>
					<value level="3">40</value>
					<value level="4">42</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45140" toLevel="4" name="Soul Death Whisper" nameRu="Шепот Смерти Души">
		<!-- P. Critical Damage $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1523</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_DMG_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalDamage">
				<amount>
					<value level="1">25</value>
					<value level="2">30</value>
					<value level="3">35</value>
					<value level="4">38</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45141" toLevel="3" name="Soul Haste" nameRu="Ускорение Души">
		<!-- Atk. Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0427</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">40</value>
			<value level="3">60</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">33</value>
					<value level="3">35</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45142" toLevel="3" name="Soul Wind Walk" nameRu="Легкая Походка Души">
		<!-- Speed $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill11823</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>
					<value level="1">20</value>
					<value level="2">33</value>
					<value level="3">35</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45143" toLevel="3" name="Soul Magic Barrier" nameRu="Магический Барьер Души">
		<!-- M. Def. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill11360</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">250</value>
					<value level="2">350</value>
					<value level="3">380</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45144" toLevel="4" name="Soul Might" nameRu="Могущество Души">
		<!-- P. Atk. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill19298</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">3</value>
			<value level="2">20</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">80</value>
					<value level="2">150</value>
					<value level="3">230</value>
					<value level="4">250</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45145" toLevel="4" name="Soul Shield" nameRu="Щит Души">
		<!-- P. Def. $s1 -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill30516</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PD_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">2</value>
			<value level="2">20</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45146" toLevel="4" name="Soul Acumen" nameRu="Проницательность Души">
		<!-- Casting Spd. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1520</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CASTING_TIME_DOWN</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">15</value>
					<value level="2">23</value>
					<value level="3">30</value>
					<value level="4">33</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45147" toLevel="4" name="Soul Empower" nameRu="Воодушевление Души">
		<!-- M. Atk. $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1441</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>
					<value level="1">100</value>
					<value level="2">200</value>
					<value level="3">300</value>
					<value level="4">330</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45148" toLevel="3" name="Soul Wild Magic" nameRu="Дикая Магия Души">
		<!-- M. Critical Rate $s1\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill1479</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>MAGIC_CRITICAL_UP</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicCriticalRate">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45149" toLevel="3" name="Soul Berserker Spirit" nameRu="Дух Берсерка Души">
		<!-- Max HP $s1\nMax MP $s1\nP. Atk. $s2\nM. Atk. $s3\nAtk. Spd. $s4\nCasting Spd. $s5\nSpeed $s6\n\nConsumes $s7 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill30520</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>BERSERKER</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">30</value>
			<value level="2">40</value>
			<value level="3">76</value>
		</magicLevel>
		<itemConsumeCount>3</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>
					<value level="1">-20</value>
					<value level="2">-15</value>
					<value level="3">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">20</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">9</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>
					<value level="1">5</value>
					<value level="2">8</value>
					<value level="3">8</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45150" toLevel="4" name="Soul Clarity" nameRu="Чистота Души">
		<!-- Song/ Dance/ P. Skill MP Consumption $s1\nM. Skill MP Consumption $s2\n\nConsumes $s3 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill11760</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CHEAP_MAGIC</abnormalType>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">76</value>
		</magicLevel>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">3</value>
			<value level="3">3</value>
			<value level="4">5</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<operateType>A2</operateType>
		<reuseDelay>10000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-4</value>
					<value level="2">-7</value>
					<value level="3">-10</value>
					<value level="4">-11</value>
				</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicMpCost">
				<amount>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
					<value level="4">-21</value>
				</amount>
				<mode>PER</mode>
				<magicType>3</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="45151" toLevel="2" name="Collect Light Souls" nameRu="Сбор Сияющих Душ">
		<shortcutToggleType>2</shortcutToggleType>
		<!-- Collects $s1 Light Souls.\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill55474</icon>
		<operateType>A1</operateType>
		<hitTime>1000</hitTime>
		<!-- <reuseDelay>900000</reuseDelay> -->
		<reuseDelay>300000</reuseDelay> <!-- Custom -->
		<mpConsume>50</mpConsume>
		<itemConsumeCount>30</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<!-- <staticReuse>true</staticReuse> -->
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCanAbsorbSoul" />
		</conditions>
		<effects>
			<effect name="FocusSouls">
				<charge>
					<value level="1">70</value>
					<value level="2">100</value>
				</charge>
				<type>LIGHT</type>
			</effect>
		</effects>
	</skill>
	<skill id="45152" toLevel="2" name="Collect Shadow Souls" nameRu="Сбор Сумрачных Душ">
		<shortcutToggleType>2</shortcutToggleType>
		<!-- Collects $s1 Shadow Souls.\n\nConsumes $s2 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill55475</icon>
		<operateType>A1</operateType>
		<hitTime>1000</hitTime>
		<!-- <reuseDelay>900000</reuseDelay> -->
		<reuseDelay>300000</reuseDelay> <!-- Custom -->
		<mpConsume>50</mpConsume>
		<itemConsumeCount>30</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<!-- <staticReuse>true</staticReuse> -->
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCanAbsorbSoul" />
		</conditions>
		<effects>
			<effect name="FocusSouls">
				<charge>
					<value level="1">70</value>
					<value level="2">100</value>
				</charge>
				<type>SHADOW</type>
			</effect>
		</effects>
	</skill>
	<skill id="45153" toLevel="1" name="Shadow Veil" nameRu="Сумрачное Обличье">
		<!-- Doubles the damage inflicted. -->
		<icon>icon.skill19187</icon>
		<operateType>A1</operateType>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<coolTime>0</coolTime>
		<reuseDelay>10000</reuseDelay>
		<effectPoint>-1000</effectPoint>
		<magicLevel>85</magicLevel>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>5286</power> <!-- Like 85 Soul Impulse -->
				<criticalChance>5</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="45154" toLevel="5" name="Death Mark Lv. 1" nameRu="Метка Смерти - Ур. 1">
		<!-- Received P. Critical Damage/ Received M. Skill Critical Damage +$s1 for enemies having a Death Mark. -->
		<icon>icon.skill1435</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
		</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>DEATH_MARK</abnormalType>
		<abnormalVisualEffect>DEATH_MARK</abnormalVisualEffect>
		<operateType>A2</operateType>
		<effectPoint>-655</effectPoint>
		<hitTime>1200</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">15</value>
			<value level="2">32</value>
			<value level="3">56</value>
			<value level="4">78</value>
			<value level="5">85</value>
		</magicLevel>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DefenceCriticalDamage">
				<amount>
					<value level="1">10</value>
					<value level="2">12</value>
					<value level="3">15</value>
					<value level="4">20</value>
					<value level="5">25</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="DefenceMagicCriticalDamage">
				<amount>
					<value level="1">10</value>
					<value level="2">12</value>
					<value level="3">15</value>
					<value level="4">20</value>
					<value level="5">25</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45155" toLevel="12" name="Soul Impulse" nameRu="Импульс Души">
		<!-- Swings a sword to attack the enemy with $s1 power.\nRequires an ancient sword or a two-handed sword.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0497</icon>
		<operateType>A1</operateType>
		<castRange>60</castRange>
		<effectRange>600</effectRange>
		<hitTime>1500</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>1000</reuseDelay>
		<reuseDelayGroup>45155</reuseDelayGroup>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
			<value level="6">-420</value>
			<value level="7">-445</value>
			<value level="8">-470</value>
			<value level="9">-480</value>
			<value level="10">-485</value>
			<value level="11">-490</value>
			<value level="12">-495</value>
		</effectPoint>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">40</value>
			<value level="3">48</value>
			<value level="4">55</value>
			<value level="5">59</value>
			<value level="6">63</value>
			<value level="7">66</value>
			<value level="8">70</value>
			<value level="9">74</value>
			<value level="10">76</value>
			<value level="11">78</value>
			<value level="12">80</value>
		</mpConsume>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
			<value level="9">92</value>
			<value level="10">93</value>
			<value level="11">93</value>
			<value level="12">93</value>
		</magicLevel>
		<nextAction>ATTACK</nextAction>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="Op2hWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>ANCIENTSWORD</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1184</value>
					<value level="2">2076</value>
					<value level="3">2999</value>
					<value level="4">3949</value>
					<value level="5">4432</value>
					<value level="6">5692</value>
					<value level="7">6078</value>
					<value level="8">6466</value>
                    <value level="9">6858</value>
                    <value level="10">7063</value>
                    <value level="11">7274</value>
                    <value level="12">7492</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<criticalChance>15</criticalChance>
				<pDefMod>0.9</pDefMod>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="45156" toLevel="12" name="Ultimate Soul Impulse" nameRu="Совершенный Импульс Души">
		<!-- <Ultimate skill>\n\nAttacks the target from a distance with $s1 power.\nRequires an ancient sword or a two-handed sword.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.wizard_danger_warppow</icon>
		<operateType>A1</operateType>
		<castRange>250</castRange>
		<effectRange>800</effectRange>
		<hitTime>1800</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>1000</reuseDelay>
		<reuseDelayGroup>45155</reuseDelayGroup>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
			<value level="6">-420</value>
			<value level="7">-445</value>
			<value level="8">-470</value>
			<value level="9">-480</value>
			<value level="10">-485</value>
			<value level="11">-490</value>
			<value level="12">-495</value>
		</effectPoint>
		<mpConsume>
			<value level="1">29</value>
			<value level="2">36</value>
			<value level="3">43</value>
			<value level="4">49</value>
			<value level="5">53</value>
			<value level="6">56</value>
			<value level="7">59</value>
			<value level="8">63</value>
			<value level="9">66</value>
			<value level="10">68</value>
			<value level="11">70</value>
			<value level="12">72</value>
		</mpConsume>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
			<value level="9">92</value>
			<value level="10">93</value>
			<value level="11">93</value>
			<value level="12">93</value>
		</magicLevel>
		<nextAction>ATTACK</nextAction>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="Op2hWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>ANCIENTSWORD</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1545</value>
					<value level="2">2708</value>
					<value level="3">3912</value>
					<value level="4">5151</value>
					<value level="5">5781</value>
					<value level="6">7425</value>
					<value level="7">7929</value>
					<value level="8">8434</value>
					<value level="9">8947</value>
					<value level="10">9215</value>
					<value level="11">9491</value>
					<value level="12">9775</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<criticalChance>15</criticalChance>
				<pDefMod>0.75</pDefMod>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="45157" toLevel="6" name="Enuma Elish" nameRu="Энума Элиш">
		<!-- Attacks the enemies at the front with $s1 power.\nRequires an ancient sword or a two-handed sword.\nIgnores Shield Defense.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0526</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>400</affectRange>
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-920</value>
			<value level="2">-1070</value>
			<value level="3">-1145</value>
			<value level="4">-1220</value>
			<value level="5">-1295</value>
			<value level="6">-1370</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<fanRange>0;0;400;90</fanRange>
		<hitTime>1200</hitTime>
		<coolTime>720</coolTime>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">72</value>
			<value level="2">82</value>
			<value level="3">88</value>
			<value level="4">94</value>
			<value level="5">99</value>
			<value level="6">105</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<reuseDelayGroup>45157</reuseDelayGroup>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="Op2hWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>ANCIENTSWORD</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1800</value>
					<value level="2">2520</value>
					<value level="3">3024</value>
					<value level="4">3477</value>
					<value level="5">3825</value>
					<value level="6">4016</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<overHit>true</overHit>
				<criticalChance>10</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="45158" toLevel="6" name="Ultimate Enuma Elish" nameRu="Совершенный Энума Элиш">
		<!-- <Ultimate skill>\n\nAttacks the enemies at the front with $s1 power.\nRequires an ancient sword or a two-handed sword.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill10265</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>300</affectRange>
		<castRange>300</castRange>
		<effectPoint>
			<value level="1">-920</value>
			<value level="2">-1070</value>
			<value level="3">-1145</value>
			<value level="4">-1220</value>
			<value level="5">-1295</value>
			<value level="6">-1370</value>
		</effectPoint>
		<effectRange>500</effectRange>
		<hitTime>1500</hitTime>
		<coolTime>720</coolTime>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
			<value level="5">85</value>
			<value level="6">90</value>
		</magicLevel>
		<mpConsume>
			<value level="1">64</value>
			<value level="2">73</value>
			<value level="3">79</value>
			<value level="4">84</value>
			<value level="5">89</value>
			<value level="6">94</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<reuseDelayGroup>45157</reuseDelayGroup>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="Op2hWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>ANCIENTSWORD</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">2250</value>
					<value level="2">3150</value>
					<value level="3">3780</value>
					<value level="4">4347</value>
					<value level="5">4781</value>
					<value level="6">5020</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.75</pDefMod>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="45159" toLevel="6" name="Rush Impact" nameRu="Стремительный Натиск">
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0793</icon>
		<operateType>A1</operateType>
		<castRange>
			<value level="1">500</value>
			<value level="2">600</value>
			<value fromLevel="3" toLevel="6">700</value>
		</castRange>
		<hitTime>800</hitTime>
		<coolTime>200</coolTime>
		<reuseDelay>1000</reuseDelay>
		<reuseDelayGroup>45159</reuseDelayGroup>
		<effectPoint>
			<value level="1">-600</value>
			<value level="2">-700</value>
			<value level="3">-800</value>
			<value level="4">-850</value>
			<value level="5">-900</value>
			<value level="6">-910</value>
		</effectPoint>
		<mpConsume>
			<value level="1">40</value>
			<value level="2">40</value>
			<value level="3">45</value>
			<value level="4">45</value>
			<value level="5">50</value>
			<value level="6">50</value>
		</mpConsume>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">70</value>
			<value level="3">80</value>
			<value level="4">85</value>
			<value level="5">90</value>
			<value level="6">91</value>
		</magicLevel>
		<basicProperty>PHYSICAL</basicProperty>
		<effectRange>1400</effectRange>
		<affectRange>200</affectRange>
		<fanRange>
			<value level="1">0;0;600;60</value>
			<value level="2">0;0;700;60</value>
			<value level="3">0;0;800;60</value>
			<value level="4">0;0;800;60</value>
			<value level="5">0;0;800;60</value>
			<value level="6">0;0;800;60</value>
		</fanRange>
		<lvlBonusRate>20</lvlBonusRate>
		<nextAction>ATTACK</nextAction>
		<operateType>DA1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>SHOCK</trait>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="OpCheckCastRange">
				<distance>
					<value level="1">150</value>
					<value level="2">120</value>
					<value level="3">100</value>
					<value level="4">100</value>
					<value level="5">100</value>
					<value level="6">100</value>
				</distance>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1700</value>
					<value level="2">2200</value>
					<value level="3">2800</value>
					<value level="4">4200</value>
					<value level="5">5800</value>
					<value level="6">7500</value>
				</power>
				<overHit>true</overHit>
			</effect>
			<effect name="CallSkill">
				<skillId>5600</skillId>
				<skillLevel>1</skillLevel>
				<chance>30</chance>
			</effect>
		</effects>
	</skill>
	<skill id="45160" toLevel="15" name="Soul Slash" nameRu="Рассечение Души">
		<!-- Attacks the target with $s1 power.\nRequires a rapier.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0505</icon>
		<operateType>A1</operateType>
		<castRange>120</castRange>
		<effectRange>500</effectRange>
		<hitTime>1300</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>1000</reuseDelay>
		<effectPoint>
			<value level="1">-120</value>
			<value level="2">-170</value>
			<value level="3">-220</value>
			<value level="4">-270</value>
			<value level="5">-320</value>
			<value level="6">-370</value>
			<value level="7">-395</value>
			<value level="8">-420</value>
			<value level="9">-445</value>
			<value level="10">-470</value>
			<value level="11">-475</value>
			<value level="12">-480</value>
			<value level="13">-485</value>
			<value level="14">-490</value>
			<value level="15">-495</value>
		</effectPoint>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
			<value level="5">60</value>
			<value level="6">70</value>
			<value level="7">75</value>
			<value level="8">80</value>
			<value level="9">85</value>
			<value level="10">90</value>
			<value level="11">91</value>
			<value level="12">92</value>
			<value level="13">93</value>
			<value level="14">93</value>
			<value level="15">93</value>
		</magicLevel>
		<mpConsume>
			<value level="1">18</value>
			<value level="2">25</value>
			<value level="3">33</value>
			<value level="4">40</value>
			<value level="5">48</value>
			<value level="6">55</value>
			<value level="7">59</value>
			<value level="8">63</value>
			<value level="9">66</value>
			<value level="10">70</value>
			<value level="11">72</value>
			<value level="12">74</value>
			<value level="13">76</value>
			<value level="14">78</value>
			<value level="15">80</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>RAPIER</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">310</value>
					<value level="2">430</value>
					<value level="3">981</value>
					<value level="4">1720</value>
					<value level="5">2484</value>
					<value level="6">3271</value>
					<value level="7">3671</value>
					<value level="8">4715</value>
					<value level="9">5035</value>
					<value level="10">5356</value>
					<value level="11">5516</value>
					<value level="12">5681</value>
					<value level="13">5851</value>
					<value level="14">6026</value>
					<value level="15">6206</value>
				</power>
				<overHit>true</overHit>
				<criticalChance>20</criticalChance>
				<pDefMod>0.9</pDefMod>
				<debuffModifier>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">1</value>
					<value level="6">1</value>
					<value level="7">1.3</value>
					<value level="8">1.3</value>
					<value level="9">1.3</value>
					<value level="10">1.3</value>
					<value level="11">1.3</value>
					<value level="12">1.3</value>
					<value level="13">1.3</value>
					<value level="14">1.3</value>
					<value level="15">1.3</value>
				</debuffModifier>
				<debuffType>PARALYZE</debuffType>
			</effect>
		</effects>
	</skill>
	<skill id="45161" toLevel="8" name="Soul Piercing" nameRu="Пробивание Души">
		<!-- Attacks the enemy twice with $s1 power.\nDeals additional magic damage. M. Def. -10%%.\nIn PvE knocks the target back with a certain chance.\nRequires a rapier.\nIgnores Shield Defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0478</icon>
		<castRange>120</castRange>
		<effectRange>400</effectRange>
		<hitTime>1500</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>1000</reuseDelay>
		<reuseDelayGroup>45161</reuseDelayGroup>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
			<value level="6">-420</value>
			<value level="7">-445</value>
			<value level="8">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">40</value>
			<value level="3">48</value>
			<value level="4">55</value>
			<value level="5">59</value>
			<value level="6">63</value>
			<value level="7">66</value>
			<value level="8">70</value>
		</mpConsume>
		<lvlBonusRate>20</lvlBonusRate>
		<activateRate>100</activateRate>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>RAPIER</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">511</value>
					<value level="2">886</value>
					<value level="3">1273</value>
					<value level="4">1673</value>
					<value level="5">1876</value>
					<value level="6">2475</value>
					<value level="7">2643</value>
					<value level="8">2812</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<criticalChance>15</criticalChance>
				<overHit>true</overHit>
			</effect>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">511</value>
					<value level="2">886</value>
					<value level="3">1273</value>
					<value level="4">1673</value>
					<value level="5">1876</value>
					<value level="6">2475</value>
					<value level="7">2643</value>
					<value level="8">2812</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<criticalChance>15</criticalChance>
				<overHit>true</overHit>
			</effect>
			<effect name="CallSkill" fromLevel="4" toLevel="8">
				<chance>50</chance>
				<skillId>45182</skillId> <!-- Soul' thrust -->
				<skillLevel>
					<value level="4">4</value>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
				</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="45162" toLevel="8" name="Ultimate Soul Piercing" nameRu="Совершенное Пробивание Души">
		<!-- <Ultimate skill>\n\nAttacks the enemy thrice with $s1 power.\nDeals additional magic damage. M. Def. -10%%.\nIn PvE knocks the target back with a certain chance.\nRequires a rapier.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0504</icon>
		<castRange>120</castRange>
		<effectRange>400</effectRange>
		<hitTime>2000</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>1000</reuseDelay>
		<reuseDelayGroup>45161</reuseDelayGroup>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
			<value level="6">-420</value>
			<value level="7">-445</value>
			<value level="8">-470</value>
		</effectPoint>
		<mpConsume>
			<value level="1">29</value>
			<value level="2">36</value>
			<value level="3">43</value>
			<value level="4">49</value>
			<value level="5">53</value>
			<value level="6">56</value>
			<value level="7">59</value>
			<value level="8">63</value>
		</mpConsume>
		<lvlBonusRate>20</lvlBonusRate>
		<activateRate>100</activateRate>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>RAPIER</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">510</value>
					<value level="2">886</value>
					<value level="3">1273</value>
					<value level="4">1672</value>
					<value level="5">1876</value>
					<value level="6">2475</value>
					<value level="7">2643</value>
					<value level="8">2812</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.75</pDefMod>
				<criticalChance>15</criticalChance>
				<overHit>true</overHit>
			</effect>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">510</value>
					<value level="2">886</value>
					<value level="3">1273</value>
					<value level="4">1672</value>
					<value level="5">1876</value>
					<value level="6">2475</value>
					<value level="7">2643</value>
					<value level="8">2812</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.75</pDefMod>
				<criticalChance>15</criticalChance>
				<overHit>true</overHit>
			</effect>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">510</value>
					<value level="2">886</value>
					<value level="3">1273</value>
					<value level="4">1672</value>
					<value level="5">1876</value>
					<value level="6">2475</value>
					<value level="7">2643</value>
					<value level="8">2812</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.75</pDefMod>
				<criticalChance>15</criticalChance>
				<overHit>true</overHit>
			</effect>
			<effect name="CallSkill">
				<chance>50</chance>
				<skillId>45182</skillId> <!-- Soul' thrust -->
				<skillLevel>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
					<value level="4">4</value>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
				</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="45163" toLevel="12" name="Soul Spark" nameRu="Вспышка Души">
		<!-- Deals magic damage to the target with $s1 power.\nRequires a rapier. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill11361</icon>
		<operateType>A1</operateType>
		<isMagic>1</isMagic>
		<castRange>700</castRange>
		<effectRange>1400</effectRange>
		<hitTime>1500</hitTime>
		<!--<reuseDelay>1000</reuseDelay>-->
		<reuseDelay>1000</reuseDelay> <!-- Custom -->
		<reuseDelayPve>1000</reuseDelayPve> <!-- Custom -->
		<reuseDelayGroup>45163</reuseDelayGroup>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
			<value level="9">92</value>
			<value level="10">93</value>
			<value level="11">93</value>
			<value level="12">93</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-500</value>
			<value level="2">-600</value>
			<value level="3">-650</value>
			<value level="4">-800</value>
			<value level="5">-850</value>
			<value level="6">-900</value>
			<value level="7">-950</value>
			<value level="8">-1000</value>
			<value level="9">-1020</value>
			<value level="10">-1030</value>
			<value level="11">-1040</value>
			<value level="12">-1050</value>
		</effectPoint>
		<mpConsume>
			<value level="1">24</value>
			<value level="2">28</value>
			<value level="3">32</value>
			<value level="4">36</value>
			<value level="5">40</value>
			<value level="6">44</value>
			<value level="7">48</value>
			<value level="8">52</value>
			<value level="9">56</value>
			<value level="10">58</value>
			<value level="11">60</value>
			<value level="12">62</value>
		</mpConsume>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>RAPIER</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">88</value>
					<value level="2">99</value>
					<value level="3">115</value>
					<value level="4">132</value>
					<value level="5">143</value>
					<value level="6">154</value>
					<value level="7">165</value>
					<value level="8">181</value>
					<value level="9">195</value>
					<value level="10">203</value>
					<value level="11">211</value>
					<value level="12">220</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="45164" toLevel="12" name="Ultimate Soul Spark" nameRu="Совершенная Вспышка Души">
		<!-- <Ultimate skill>\n\nDeals two magic strikes to the target with $s1 power.\nRequires a rapier. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill14216</icon>
		<operateType>A1</operateType>
		<isMagic>1</isMagic>
		<castRange>700</castRange>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<!--<reuseDelay>1000</reuseDelay>-->
		<reuseDelay>1000</reuseDelay> <!-- Custom -->
		<reuseDelayPve>1000</reuseDelayPve> <!-- Custom -->
		<reuseDelayGroup>45163</reuseDelayGroup>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
			<value level="9">92</value>
			<value level="10">93</value>
			<value level="11">93</value>
			<value level="12">93</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-500</value>
			<value level="2">-600</value>
			<value level="3">-650</value>
			<value level="4">-800</value>
			<value level="5">-850</value>
			<value level="6">-900</value>
			<value level="7">-950</value>
			<value level="8">-1000</value>
			<value level="9">-1020</value>
			<value level="10">-1030</value>
			<value level="11">-1040</value>
			<value level="12">-1050</value>
		</effectPoint>
		<mpConsume>
			<value level="1">20</value>
			<value level="2">24</value>
			<value level="3">28</value>
			<value level="4">32</value>
			<value level="5">36</value>
			<value level="6">40</value>
			<value level="7">44</value>
			<value level="8">48</value>
			<value level="9">52</value>
			<value level="10">54</value>
			<value level="11">56</value>
			<value level="12">58</value>
		</mpConsume>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>RAPIER</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">88</value>
					<value level="2">99</value>
					<value level="3">115</value>
					<value level="4">132</value>
					<value level="5">143</value>
					<value level="6">154</value>
					<value level="7">165</value>
					<value level="8">181</value>
					<value level="9">195</value>
					<value level="10">203</value>
					<value level="11">211</value>
					<value level="12">220</value>
				</power>
			</effect>
			<effect name="MagicalAttack">
				<power>
					<value level="1">88</value>
					<value level="2">99</value>
					<value level="3">115</value>
					<value level="4">132</value>
					<value level="5">143</value>
					<value level="6">154</value>
					<value level="7">165</value>
					<value level="8">181</value>
					<value level="9">195</value>
					<value level="10">203</value>
					<value level="11">211</value>
					<value level="12">220</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="45165" toLevel="10" name="Soul Strike" nameRu="Удар Душ">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill1528</icon>
		<operateType>A1</operateType>
		<isMagic>1</isMagic>
		<castRange>700</castRange>
		<reuseDelay>10000</reuseDelay>
		<effectPoint>
			<value level="1">-300</value>
			<value level="2">-400</value>
			<value level="3">-500</value>
			<value level="4">-600</value>
			<value level="5">-650</value>
			<value level="6">-800</value>
			<value level="7">-850</value>
			<value level="8">-900</value>
			<value level="9">-950</value>
			<value level="10">-1000</value>
		</effectPoint>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">55</value>
					<value level="2">60</value>
					<value level="3">65</value>
					<value level="4">70</value>
					<value level="5">75</value>
					<value level="6">80</value>
					<value level="7">85</value>
					<value level="8">90</value>
					<value level="9">95</value>
					<value level="10">100</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="45166" toLevel="10" name="Soul Strike" nameRu="Удар Душ">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill1528</icon>
		<operateType>A1</operateType>
		<isMagic>1</isMagic>
		<castRange>700</castRange>
		<reuseDelay>10000</reuseDelay>
		<effectPoint>
			<value level="1">-300</value>
			<value level="2">-400</value>
			<value level="3">-500</value>
			<value level="4">-600</value>
			<value level="5">-650</value>
			<value level="6">-800</value>
			<value level="7">-850</value>
			<value level="8">-900</value>
			<value level="9">-950</value>
			<value level="10">-1000</value>
		</effectPoint>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">65</value>
					<value level="2">70</value>
					<value level="3">75</value>
					<value level="4">80</value>
					<value level="5">85</value>
					<value level="6">90</value>
					<value level="7">95</value>
					<value level="8">100</value>
					<value level="9">105</value>
					<value level="10">110</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="45167" toLevel="10" name="Soul Strike" nameRu="Удар Душ">
		<!-- Inflicts magic damage on the enemy with $s1 power. -->
		<icon>icon.skill1528</icon>
		<operateType>A1</operateType>
		<isMagic>1</isMagic>
		<castRange>700</castRange>
		<reuseDelay>10000</reuseDelay>
		<effectPoint>
			<value level="1">-300</value>
			<value level="2">-400</value>
			<value level="3">-500</value>
			<value level="4">-600</value>
			<value level="5">-650</value>
			<value level="6">-800</value>
			<value level="7">-850</value>
			<value level="8">-900</value>
			<value level="9">-950</value>
			<value level="10">-1000</value>
		</effectPoint>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">70</value>
					<value level="2">80</value>
					<value level="3">90</value>
					<value level="4">100</value>
					<value level="5">110</value>
					<value level="6">120</value>
					<value level="7">130</value>
					<value level="8">140</value>
					<value level="9">150</value>
					<value level="10">160</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="45168" toLevel="15" name="Twin Shot" nameRu="Сдвоенный Выстрел">
		<!-- Shoots 2 arrows to attack the enemy with $s1 power.\nRequires a bow.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0487</icon>
		<operateType>A1</operateType>
		<castRange>700</castRange>
		<effectRange>1400</effectRange>
		<hitTime>3000</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>
			<value level="1">2000</value>
			<value level="2">2000</value>
			<value level="3">2000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">1500</value>
			<value level="9">1500</value>
			<value level="10">1500</value>
			<value level="11">1500</value>
			<value level="12">1500</value>
			<value level="13">1500</value>
			<value level="14">1500</value>
			<value level="15">1500</value>
		</reuseDelay>
		<reuseDelayGroup>45168</reuseDelayGroup>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
			<value level="5">60</value>
			<value level="6">70</value>
			<value level="7">75</value>
			<value level="8">80</value>
			<value level="9">85</value>
			<value level="10">90</value>
			<value level="11">91</value>
			<value level="12">92</value>
			<value level="13">93</value>
			<value level="14">93</value>
			<value level="15">93</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-120</value>
			<value level="2">-170</value>
			<value level="3">-220</value>
			<value level="4">-270</value>
			<value level="5">-320</value>
			<value level="6">-370</value>
			<value level="7">-395</value>
			<value level="8">-420</value>
			<value level="9">-445</value>
			<value level="10">-470</value>
			<value level="11">-495</value>
			<value level="12">-520</value>
			<value level="13">-545</value>
			<value level="14">-570</value>
			<value level="15">-595</value>
		</effectPoint>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">29</value>
			<value level="3">38</value>
			<value level="4">46</value>
			<value level="5">55</value>
			<value level="6">63</value>
			<value level="7">67</value>
			<value level="8">72</value>
			<value level="9">76</value>
			<value level="10">80</value>
			<value level="11">82</value>
			<value level="12">84</value>
			<value level="13">86</value>
			<value level="14">88</value>
			<value level="15">90</value>
		</mpConsume>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">347</value>
					<value level="2">501</value>
					<value level="3">1101</value>
					<value level="4">1778</value>
					<value level="5">2482</value>
					<value level="6">3209</value>
					<value level="7">3581</value>
					<value level="8">3886</value>
					<value level="9">4193</value>
					<value level="10">4506</value>
					<value level="11">4641</value>
					<value level="12">4780</value>
					<value level="13">4923</value>
					<value level="14">5070</value>
					<value level="15">5222</value>
				</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">347</value>
					<value level="2">501</value>
					<value level="3">1101</value>
					<value level="4">1778</value>
					<value level="5">2482</value>
					<value level="6">3209</value>
					<value level="7">3581</value>
					<value level="8">3886</value>
					<value level="9">4193</value>
					<value level="10">4506</value>
					<value level="11">4641</value>
					<value level="12">4780</value>
					<value level="13">4923</value>
					<value level="14">5070</value>
					<value level="15">5222</value>
				</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="45169" toLevel="15" name="Ultimate Twin Shot" nameRu="Совершенный Сдвоенный Выстрел">
		<!-- <Ultimate skill>\n\nShoots 2 arrows to attack the enemy with $s1 power.\nRequires a bow.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0507</icon>
		<operateType>A1</operateType>
		<castRange>700</castRange>
		<effectRange>1400</effectRange>
		<hitTime>2700</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>
			<value level="1">2000</value>
			<value level="2">2000</value>
			<value level="3">2000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">1500</value>
			<value level="9">1500</value>
			<value level="10">1500</value>
			<value level="11">1500</value>
			<value level="12">1500</value>
			<value level="13">1500</value>
			<value level="14">1500</value>
			<value level="15">1500</value>
		</reuseDelay>
		<reuseDelayGroup>45168</reuseDelayGroup>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
			<value level="5">60</value>
			<value level="6">70</value>
			<value level="7">75</value>
			<value level="8">80</value>
			<value level="9">85</value>
			<value level="10">90</value>
			<value level="11">91</value>
			<value level="12">92</value>
			<value level="13">93</value>
			<value level="14">93</value>
			<value level="15">93</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-120</value>
			<value level="2">-170</value>
			<value level="3">-220</value>
			<value level="4">-270</value>
			<value level="5">-320</value>
			<value level="6">-370</value>
			<value level="7">-395</value>
			<value level="8">-420</value>
			<value level="9">-445</value>
			<value level="10">-470</value>
			<value level="11">-495</value>
			<value level="12">-520</value>
			<value level="13">-545</value>
			<value level="14">-570</value>
			<value level="15">-595</value>
		</effectPoint>
		<mpConsume>
			<value level="1">19</value>
			<value level="2">27</value>
			<value level="3">34</value>
			<value level="4">42</value>
			<value level="5">50</value>
			<value level="6">57</value>
			<value level="7">61</value>
			<value level="8">65</value>
			<value level="9">69</value>
			<value level="10">73</value>
			<value level="11">73</value>
			<value level="12">75</value>
			<value level="13">77</value>
			<value level="14">79</value>
			<value level="15">81</value>
		</mpConsume>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">416</value>
					<value level="2">601</value>
					<value level="3">1321</value>
					<value level="4">2133</value>
					<value level="5">2978</value>
					<value level="6">3851</value>
					<value level="7">4297</value>
					<value level="8">4664</value>
					<value level="9">5033</value>
					<value level="10">5407</value>
					<value level="11">5569</value>
					<value level="12">5736</value>
					<value level="13">5908</value>
					<value level="14">6085</value>
					<value level="15">6267</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.95</pDefMod>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">416</value>
					<value level="2">601</value>
					<value level="3">1321</value>
					<value level="4">2133</value>
					<value level="5">2978</value>
					<value level="6">3851</value>
					<value level="7">4297</value>
					<value level="8">4664</value>
					<value level="9">5033</value>
					<value level="10">5407</value>
					<value level="11">5569</value>
					<value level="12">5736</value>
					<value level="13">5908</value>
					<value level="14">6085</value>
					<value level="15">6267</value>
				</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.95</pDefMod>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="45170" toLevel="8" name="Multishot" nameRu="Мультивыстрел">
		<!-- Attacks enemies at the front with $s1 power.\nRequires a bow.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill10260</icon>
		<operateType>A1</operateType>
		<affectLimit>8-0-1</affectLimit>
		<affectRange>900</affectRange>
		<affectHeight>-200;200</affectHeight>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;60</fanRange>
		<magicCriticalRate>5</magicCriticalRate>
		<castRange>700</castRange>
		<hitTime>3200</hitTime>
		<coolTime>1000</coolTime>
		<!-- <reuseDelay>10000</reuseDelay> -->
		<reuseDelay>5000</reuseDelay> <!-- Custom -->
		<reuseDelayGroup>45170</reuseDelayGroup>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">74</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-620</value>
			<value level="2">-770</value>
			<value level="3">-920</value>
			<value level="4">-1070</value>
			<value level="5">-1145</value>
			<value level="6">-1220</value>
			<value level="7">-1295</value>
			<value level="8">-1370</value>
		</effectPoint>
		<mpConsume>
			<value level="1">75</value>
			<value level="2">93</value>
			<value level="3">111</value>
			<value level="4">129</value>
			<value level="5">138</value>
			<value level="6">147</value>
			<value level="7">156</value>
			<value level="8">165</value>
		</mpConsume>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">340</value>
					<value level="2">544</value>
					<value level="3">816</value>
					<value level="4">1142</value>
					<value level="5">1370</value>
					<value level="6">1507</value>
					<value level="7">1583</value>
					<value level="8">1662</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="45171" toLevel="8" name="Ultimate Multishot" nameRu="Совершенный Мультивыстрел">
		<!-- <Ultimate skill>\n\nAttacks enemies at the front with $s1 power.\nRequires a bow.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill10953</icon>
		<operateType>A1</operateType>
		<affectLimit>8-0-1</affectLimit>
		<affectRange>900</affectRange>
		<affectHeight>-200;200</affectHeight>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;60</fanRange>
		<magicCriticalRate>5</magicCriticalRate>
		<castRange>700</castRange>
		<hitTime>2800</hitTime>
		<coolTime>1000</coolTime>
		<reuseDelay>5000</reuseDelay>
		<reuseDelayGroup>45170</reuseDelayGroup>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">74</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-620</value>
			<value level="2">-770</value>
			<value level="3">-920</value>
			<value level="4">-1070</value>
			<value level="5">-1145</value>
			<value level="6">-1220</value>
			<value level="7">-1295</value>
			<value level="8">-1370</value>
		</effectPoint>
		<mpConsume>
			<value level="1">67</value>
			<value level="2">83</value>
			<value level="3">99</value>
			<value level="4">116</value>
			<value level="5">124</value>
			<value level="6">132</value>
			<value level="7">140</value>
			<value level="8">148</value>
		</mpConsume>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">408</value>
					<value level="2">652</value>
					<value level="3">979</value>
					<value level="4">1370</value>
					<value level="5">1645</value>
					<value level="6">1809</value>
					<value level="7">1900</value>
					<value level="8">1995</value>
				</power>
				<pDefMod>0.8</pDefMod>
				<criticalChance>15</criticalChance>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="45172" toLevel="10" name="Sharp Aiming" nameRu="Стремительное Прицеливание">
		<!-- For $s1, P. Accuracy +$s2, P. Atk. +$s3, Atk. Spd. +$s4, P. Skill Power +$s5 when using a bow. Decreases target's Bow Resistance after an attack.\nRequires a bow. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill10763</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>REAL_TARGET_BUFF</abnormalType>
		<operateType>A2</operateType>
		<hitTime>2500</hitTime>
		<reuseDelay>10000</reuseDelay>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">62</value>
			<value level="3">64</value>
			<value level="4">66</value>
			<value level="5">68</value>
			<value level="6">70</value>
			<value level="7">72</value>
			<value level="8">74</value>
			<value level="9">80</value>
			<value level="10">85</value>
		</mpConsume>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<itemConsumeId>3031</itemConsumeId>
		<itemConsumeCount>
			<value level="1">2</value>
			<value level="2">2</value>
			<value level="3">2</value>
			<value level="4">2</value>
			<value level="5">2</value>
			<value level="6">3</value>
			<value level="7">3</value>
			<value level="8">3</value>
			<value level="9">4</value>
			<value level="10">5</value>
		</itemConsumeCount>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="Accuracy">
				<amount>
					<value level="1">3</value>
					<value level="2">4</value>
					<value level="3">5</value>
					<value level="4">6</value>
					<value level="5">7</value>
					<value level="6">7</value>
					<value level="7">8</value>
					<value level="8">8</value>
					<value level="9">10</value>
					<value level="10">10</value>
				</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
			<effect name="PAtk">
				<amount>
					<value level="1">140</value>
					<value level="2">150</value>
					<value level="3">160</value>
					<value level="4">170</value>
					<value level="5">180</value>
					<value level="6">190</value>
					<value level="7">200</value>
					<value level="8">220</value>
					<value level="9">250</value>
					<value level="10">300</value>
				</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">30</value>
					<value level="2">30</value>
					<value level="3">30</value>
					<value level="4">30</value>
					<value level="5">30</value>
					<value level="6">40</value>
					<value level="7">40</value>
					<value level="8">40</value>
					<value level="9">50</value>
					<value level="10">50</value>
				</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
			<effect name="PhysicalSkillPower">
				<amount>
					<value level="1">5</value>
					<value level="2">5</value>
					<value level="3">5</value>
					<value level="4">5</value>
					<value level="5">5</value>
					<value level="6">10</value>
					<value level="7">10</value>
					<value level="8">10</value>
					<value level="9">15</value>
					<value level="10">15</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="TriggerSkillByAttack">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>30</chance>
				<allowNormalAttack>true</allowNormalAttack>
				<allowSkillAttack>true</allowSkillAttack>
				<targetType>ENEMY</targetType>
				<isCritical>false</isCritical>
				<allowWeapons>BOW</allowWeapons>
				<skillId>45173</skillId> <!-- Aim Guidance -->
				<skillLevel>
					<value level="1">1</value>
					<value level="2">1</value>
					<value level="3">1</value>
					<value level="4">1</value>
					<value level="5">1</value>
					<value level="6">2</value>
					<value level="7">2</value>
					<value level="8">2</value>
					<value level="9">3</value>
					<value level="10">3</value>
				</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="45173" toLevel="3" name="Aim Guidance" nameRu="Наведение Прицела">
		<!-- Bow Resistance -$s1. -->
		<icon>icon.skill0506</icon>
		<operateType>A2</operateType>
		<isDebuff>true</isDebuff>
		<castRange>700</castRange>
		<effectRange>1400</effectRange>
		<hitTime>1000</hitTime>
		<reuseDelay>10000</reuseDelay>
		<isDebuff>true</isDebuff>
		<activateRate>-1</activateRate>
		<abnormalType>REAL_TARGET_DEBUFF</abnormalType>
		<abnormalVisualEffect>REAL_TARGET</abnormalVisualEffect>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<activateRate>-1</activateRate>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-500</value>
			<value level="3">-600</value>
		</effectPoint>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DefenceTrait">
				<BOW>
					<value level="1">-10</value>
					<value level="2">-15</value>
					<value level="3">-20</value>
				</BOW>
			</effect>
		</effects>
	</skill>
	<skill id="45174" toLevel="100" name="Light Soul" nameRu="Сияющая Душа">
		<!-- Collects $s1 Light Soul(s). -->
		<icon>
			<value level="1">icon.skill55463_001</value>
			<value level="2">icon.skill55463_002</value>
			<value level="3">icon.skill55463_003</value>
			<value level="4">icon.skill55463_004</value>
			<value level="5">icon.skill55463_005</value>
			<value level="6">icon.skill55463_006</value>
			<value level="7">icon.skill55463_007</value>
			<value level="8">icon.skill55463_008</value>
			<value level="9">icon.skill55463_009</value>
			<value level="10">icon.skill55463_010</value>
			<value level="11">icon.skill55463_011</value>
			<value level="12">icon.skill55463_012</value>
			<value level="13">icon.skill55463_013</value>
			<value level="14">icon.skill55463_014</value>
			<value level="15">icon.skill55463_015</value>
			<value level="16">icon.skill55463_016</value>
			<value level="17">icon.skill55463_017</value>
			<value level="18">icon.skill55463_018</value>
			<value level="19">icon.skill55463_019</value>
			<value level="20">icon.skill55463_020</value>
			<value level="21">icon.skill55463_021</value>
			<value level="22">icon.skill55463_022</value>
			<value level="23">icon.skill55463_023</value>
			<value level="24">icon.skill55463_024</value>
			<value level="25">icon.skill55463_025</value>
			<value level="26">icon.skill55463_026</value>
			<value level="27">icon.skill55463_027</value>
			<value level="28">icon.skill55463_028</value>
			<value level="29">icon.skill55463_029</value>
			<value level="30">icon.skill55463_030</value>
			<value level="31">icon.skill55463_031</value>
			<value level="32">icon.skill55463_032</value>
			<value level="33">icon.skill55463_033</value>
			<value level="34">icon.skill55463_034</value>
			<value level="35">icon.skill55463_035</value>
			<value level="36">icon.skill55463_036</value>
			<value level="37">icon.skill55463_037</value>
			<value level="38">icon.skill55463_038</value>
			<value level="39">icon.skill55463_039</value>
			<value level="40">icon.skill55463_040</value>
			<value level="41">icon.skill55463_041</value>
			<value level="42">icon.skill55463_042</value>
			<value level="43">icon.skill55463_043</value>
			<value level="44">icon.skill55463_044</value>
			<value level="45">icon.skill55463_045</value>
			<value level="46">icon.skill55463_046</value>
			<value level="47">icon.skill55463_047</value>
			<value level="48">icon.skill55463_048</value>
			<value level="49">icon.skill55463_049</value>
			<value level="50">icon.skill55463_050</value>
			<value level="51">icon.skill55463_051</value>
			<value level="52">icon.skill55463_052</value>
			<value level="53">icon.skill55463_053</value>
			<value level="54">icon.skill55463_054</value>
			<value level="55">icon.skill55463_055</value>
			<value level="56">icon.skill55463_056</value>
			<value level="57">icon.skill55463_057</value>
			<value level="58">icon.skill55463_058</value>
			<value level="59">icon.skill55463_059</value>
			<value level="60">icon.skill55463_060</value>
			<value level="61">icon.skill55463_061</value>
			<value level="62">icon.skill55463_062</value>
			<value level="63">icon.skill55463_063</value>
			<value level="64">icon.skill55463_064</value>
			<value level="65">icon.skill55463_065</value>
			<value level="66">icon.skill55463_066</value>
			<value level="67">icon.skill55463_067</value>
			<value level="68">icon.skill55463_068</value>
			<value level="69">icon.skill55463_069</value>
			<value level="70">icon.skill55463_070</value>
			<value level="71">icon.skill55463_071</value>
			<value level="72">icon.skill55463_072</value>
			<value level="73">icon.skill55463_073</value>
			<value level="74">icon.skill55463_074</value>
			<value level="75">icon.skill55463_075</value>
			<value level="76">icon.skill55463_076</value>
			<value level="77">icon.skill55463_077</value>
			<value level="78">icon.skill55463_078</value>
			<value level="79">icon.skill55463_079</value>
			<value level="80">icon.skill55463_080</value>
			<value level="81">icon.skill55463_081</value>
			<value level="82">icon.skill55463_082</value>
			<value level="83">icon.skill55463_083</value>
			<value level="84">icon.skill55463_084</value>
			<value level="85">icon.skill55463_085</value>
			<value level="86">icon.skill55463_086</value>
			<value level="87">icon.skill55463_087</value>
			<value level="88">icon.skill55463_088</value>
			<value level="89">icon.skill55463_089</value>
			<value level="90">icon.skill55463_090</value>
			<value level="91">icon.skill55463_091</value>
			<value level="92">icon.skill55463_092</value>
			<value level="93">icon.skill55463_093</value>
			<value level="94">icon.skill55463_094</value>
			<value level="95">icon.skill55463_095</value>
			<value level="96">icon.skill55463_096</value>
			<value level="97">icon.skill55463_097</value>
			<value level="98">icon.skill55463_098</value>
			<value level="99">icon.skill55463_099</value>
			<value level="100">icon.skill55463_100</value>
		</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="45175" toLevel="100" name="Shadow Soul" nameRu="Сумрачная Душа">
		<!-- Collects $s1 Shadow Soul(s). -->
		<icon>
			<value level="1">icon.skill55464_001</value>
			<value level="2">icon.skill55464_002</value>
			<value level="3">icon.skill55464_003</value>
			<value level="4">icon.skill55464_004</value>
			<value level="5">icon.skill55464_005</value>
			<value level="6">icon.skill55464_006</value>
			<value level="7">icon.skill55464_007</value>
			<value level="8">icon.skill55464_008</value>
			<value level="9">icon.skill55464_009</value>
			<value level="10">icon.skill55464_010</value>
			<value level="11">icon.skill55464_011</value>
			<value level="12">icon.skill55464_012</value>
			<value level="13">icon.skill55464_013</value>
			<value level="14">icon.skill55464_014</value>
			<value level="15">icon.skill55464_015</value>
			<value level="16">icon.skill55464_016</value>
			<value level="17">icon.skill55464_017</value>
			<value level="18">icon.skill55464_018</value>
			<value level="19">icon.skill55464_019</value>
			<value level="20">icon.skill55464_020</value>
			<value level="21">icon.skill55464_021</value>
			<value level="22">icon.skill55464_022</value>
			<value level="23">icon.skill55464_023</value>
			<value level="24">icon.skill55464_024</value>
			<value level="25">icon.skill55464_025</value>
			<value level="26">icon.skill55464_026</value>
			<value level="27">icon.skill55464_027</value>
			<value level="28">icon.skill55464_028</value>
			<value level="29">icon.skill55464_029</value>
			<value level="30">icon.skill55464_030</value>
			<value level="31">icon.skill55464_031</value>
			<value level="32">icon.skill55464_032</value>
			<value level="33">icon.skill55464_033</value>
			<value level="34">icon.skill55464_034</value>
			<value level="35">icon.skill55464_035</value>
			<value level="36">icon.skill55464_036</value>
			<value level="37">icon.skill55464_037</value>
			<value level="38">icon.skill55464_038</value>
			<value level="39">icon.skill55464_039</value>
			<value level="40">icon.skill55464_040</value>
			<value level="41">icon.skill55464_041</value>
			<value level="42">icon.skill55464_042</value>
			<value level="43">icon.skill55464_043</value>
			<value level="44">icon.skill55464_044</value>
			<value level="45">icon.skill55464_045</value>
			<value level="46">icon.skill55464_046</value>
			<value level="47">icon.skill55464_047</value>
			<value level="48">icon.skill55464_048</value>
			<value level="49">icon.skill55464_049</value>
			<value level="50">icon.skill55464_050</value>
			<value level="51">icon.skill55464_051</value>
			<value level="52">icon.skill55464_052</value>
			<value level="53">icon.skill55464_053</value>
			<value level="54">icon.skill55464_054</value>
			<value level="55">icon.skill55464_055</value>
			<value level="56">icon.skill55464_056</value>
			<value level="57">icon.skill55464_057</value>
			<value level="58">icon.skill55464_058</value>
			<value level="59">icon.skill55464_059</value>
			<value level="60">icon.skill55464_060</value>
			<value level="61">icon.skill55464_061</value>
			<value level="62">icon.skill55464_062</value>
			<value level="63">icon.skill55464_063</value>
			<value level="64">icon.skill55464_064</value>
			<value level="65">icon.skill55464_065</value>
			<value level="66">icon.skill55464_066</value>
			<value level="67">icon.skill55464_067</value>
			<value level="68">icon.skill55464_068</value>
			<value level="69">icon.skill55464_069</value>
			<value level="70">icon.skill55464_070</value>
			<value level="71">icon.skill55464_071</value>
			<value level="72">icon.skill55464_072</value>
			<value level="73">icon.skill55464_073</value>
			<value level="74">icon.skill55464_074</value>
			<value level="75">icon.skill55464_075</value>
			<value level="76">icon.skill55464_076</value>
			<value level="77">icon.skill55464_077</value>
			<value level="78">icon.skill55464_078</value>
			<value level="79">icon.skill55464_079</value>
			<value level="80">icon.skill55464_080</value>
			<value level="81">icon.skill55464_081</value>
			<value level="82">icon.skill55464_082</value>
			<value level="83">icon.skill55464_083</value>
			<value level="84">icon.skill55464_084</value>
			<value level="85">icon.skill55464_085</value>
			<value level="86">icon.skill55464_086</value>
			<value level="87">icon.skill55464_087</value>
			<value level="88">icon.skill55464_088</value>
			<value level="89">icon.skill55464_089</value>
			<value level="90">icon.skill55464_090</value>
			<value level="91">icon.skill55464_091</value>
			<value level="92">icon.skill55464_092</value>
			<value level="93">icon.skill55464_093</value>
			<value level="94">icon.skill55464_094</value>
			<value level="95">icon.skill55464_095</value>
			<value level="96">icon.skill55464_096</value>
			<value level="97">icon.skill55464_097</value>
			<value level="98">icon.skill55464_098</value>
			<value level="99">icon.skill55464_099</value>
			<value level="100">icon.skill55464_100</value>
		</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="45176" toLevel="2" name="HP Recovery" nameRu="Восстановление HP">
		<!-- Constantly recovers a certain amount of HP.\nThe amount of HP recovered and its recovery rate are increased with the skill level.\n\nConsumes $s1 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0211</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<hitTime>500</hitTime>
		<reuseDelay>10000</reuseDelay>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>LIFE_FORCE_KAMAEL</abnormalType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">10</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<effects>
			<effect name="HealOverTime">
				<power>
					<value level="1">15</value> <!-- 50 -->
					<value level="2">46</value> <!-- 520 -->
				</power>
				<ticks>
					<value level="1">5</value>
					<value level="2">17</value>
				</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="45177" toLevel="2" name="MP Recovery" nameRu="Восстановление MP">
		<!-- Constantly recovers a certain amount of MP.\nThe amount of MP recovered and its recovery rate are increased with the skill level.\n\nConsumes $s1 Spirit Ore. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0213</icon>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<hitTime>500</hitTime>
		<reuseDelay>10000</reuseDelay>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
		</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>FORCE_MEDITATION</abnormalType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<itemConsumeCount>
			<value level="1">3</value>
			<value level="2">10</value>
		</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<effects>
			<effect name="ManaHealOverTime">
				<power>
					<value level="1">9</value> <!-- 30 -->
					<value level="2">11.5</value> <!-- 130, retail: 110 -->
				</power>
				<ticks>
					<value level="1">5</value>
					<value level="2">17</value>
				</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="45178" toLevel="2" name="Light Master" nameRu="Владение Сиянием">
		<!-- Allows to absorb the power of light.\n\nCollect 100 Light Souls to use Light Veil.\n\n<Light Veil>\nP. Atk. +130, Atk. Spd. +30, Casting Spd. +40, Speed +10, P./ M. Def. +5%%,\nHas a certain chance to recover MP after an attack. -->
		<icon>icon.skill_jinkama_white1</icon>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">60</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="SoulEating">
				<maxSouls>
					<value level="1">100</value>
					<value level="2">100</value>
				</maxSouls>
				<expNeeded>
					<value level="1">294</value>
					<value level="2">2160</value>
				</expNeeded>
				<chance>
					<value level="1">30</value>
					<value level="2">50</value>
				</chance>
				<type>LIGHT</type>
			</effect>
			<effect name="TriggerSkillByAttack">
				<skillId>45180</skillId> <!-- Light Soul Absorption -->
				<skillLevel>1</skillLevel>
				<allowWeapons>ALL</allowWeapons>
				<attackerType>Creature</attackerType>
				
				
				<allowNormalAttack>false</allowNormalAttack>
				<allowSkillAttack>true</allowSkillAttack>
				<targetType>SELF</targetType>
				<minDamage>1</minDamage>
				<chance>30</chance>
			</effect>
		</effects>
	</skill>
	<skill id="45179" toLevel="2" name="Shadow Master" nameRu="Владение Сумраком">
		<!-- Allows to absorb the power of darkness.\n\nCollect 100 Shadow Souls to use Shadow Veil.\n\n<Shadow Veil>\nP. Atk. +280, Atk. Spd. +80, Casting Spd. +120, Speed +15.\nHas a certain chance to deliver a powerful blow. -->
		<icon>icon.skill_jinkama_black1</icon>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">60</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="SoulEating">
				<maxSouls>
					<value level="1">100</value>
					<value level="2">100</value>
				</maxSouls>
				<expNeeded>
					<value level="1">294</value>
					<value level="2">2160</value>
				</expNeeded>
				<chance>
					<value level="1">20</value>
					<value level="2">30</value>
				</chance>
				<type>SHADOW</type>
			</effect>
			<effect name="TriggerSkillByAttack">
				<skillId>45181</skillId> <!-- Shadow Soul Absorption -->
				<skillLevel>1</skillLevel>
				<allowWeapons>ALL</allowWeapons>
				<attackerType>Creature</attackerType>
				<allowNormalAttack>false</allowNormalAttack>
				<allowSkillAttack>true</allowSkillAttack>
				<targetType>SELF</targetType>
				<minDamage>1</minDamage>
				<chance>20</chance>
			</effect>
		</effects>
	</skill>
	<skill id="45180" toLevel="4" name="Light Soul Absorption" nameRu="Активация Поглощения Сияющей Души">
		<!-- Activates after an attack. -->
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>20</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="FocusSouls">
				<charge>1</charge>
				<type>LIGHT</type>
			</effect>
		</effects>
	</skill>
	<skill id="45181" toLevel="4" name="Shadow Soul Absorption" nameRu="Активация Поглощения Сумрачной Души">
		<!-- Activates after an attack. -->
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>20</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>0</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="FocusSouls">
				<charge>1</charge>
				<type>SHADOW</type>
			</effect>
		</effects>
	</skill>
	<skill id="45182" toLevel="8" name="Soul Piercing" nameRu="Пробивание Души">
		<!-- M. Def. -$s1. -->
		<icon>icon.skill_crofin_stun</icon>
		<operateType>A2</operateType>
		<isMagic>1</isMagic>
		<isDebuff>true</isDebuff>
		<castRange>700</castRange>
		<hitTime>1500</hitTime>
		<reuseDelay>8000</reuseDelay>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
			<value level="6">-420</value>
			<value level="7">-445</value>
			<value level="8">-470</value>
		</effectPoint>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>KNOCKDOWN</abnormalType>
		<trait>KNOCKDOWN</trait>
		<activateRate>100</activateRate>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
			<value level="6">80</value>
			<value level="7">85</value>
			<value level="8">90</value>
		</magicLevel>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">70</value>
					<value level="2">75</value>
					<value level="3">80</value>
					<value level="4">85</value>
					<value level="5">90</value>
					<value level="6">95</value>
					<value level="7">100</value>
					<value level="8">105</value>
				</power>
			</effect>
			<effect name="MagicalDefence">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="KnockBack">
				<distance>50</distance>
				<speed>400</speed>
				<knockDown>false</knockDown>
				<pveOnly>true</pveOnly>
			</effect>	
		</effects>
	</skill>
	<skill id="45183" toLevel="1" name="Soul Chase" nameRu="Погоня Души">
		<!-- Charges to the target, if the distance between the target and the character is more than $s1. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0479</icon>
		<castRange>400</castRange>
		<effectRange>600</effectRange>
		<magicLevel>40</magicLevel>
		<hitTime>800</hitTime>
		<coolTime>200</coolTime>
		<reuseDelay>3000</reuseDelay>
		<effectPoint>-300</effectPoint>
		<mpConsume>35</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>DA1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCheckCastRange">
				<distance>100</distance>
			</condition>
		</conditions>
	</skill>
	<skill id="45184" toLevel="3" name="Potion Mastery" nameRu="Владение Зельями">
		<!-- HP Recovery Potions' Effect +$s1. -->
		<icon>icon.skill19225</icon>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="AdditionalPotionHpPer">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="45185" toLevel="1" name="Fatal Slasher" nameRu="Фатальное Рассечение">
		<!-- When attacking, has a chance to deal an additional blow to the target. -->
		<icon>icon.skill19435</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="TriggerSkillByAttack">
				<skillId>45186</skillId>
				<skillLevel>1</skillLevel>
				<chance>30</chance>
				<allowNormalAttack>true</allowNormalAttack>
				<allowSkillAttack>true</allowSkillAttack>
				<allowWeapons>ALL</allowWeapons>
				<targetType>TARGET</targetType>
			</effect>
		</effects>
	</skill>
	<skill id="45186" toLevel="1" name="Fatal Slasher" nameRu="Фатальное Рассечение">
		<!-- 1.5 times increase of the attack damage. -->
		<icon>icon.skill19435</icon>
		<reuseDelay>1000</reuseDelay>
		<magicLevel>75</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<reuseDelay>10000</reuseDelay>
		<staticReuse>true</staticReuse>
		<effects>
			<effect name="PhysicalAttack">
				<power>1345</power>
			</effect>
		</effects>
	</skill>
	<skill id="45187" toLevel="5" name="Guard Crush" nameRu="Сокрушение Защиты">
		<!-- Attacks the target with $s1 power.\nRequires a sword, a blunt weapon, or dual swords.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0496</icon>
		<operateType>A1</operateType>
		<castRange>40</castRange>
		<effectRange>400</effectRange>
		<hitTime>1200</hitTime>
		<coolTime>500</coolTime>
		<reuseDelay>1000</reuseDelay>
		<magicLevel>
			<value level="1">40</value>
			<value level="2">50</value>
			<value level="3">60</value>
			<value level="4">70</value>
			<value level="5">75</value>
		</magicLevel>
		<effectPoint>
			<value level="1">-220</value>
			<value level="2">-270</value>
			<value level="3">-320</value>
			<value level="4">-370</value>
			<value level="5">-395</value>
		</effectPoint>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">40</value>
			<value level="3">48</value>
			<value level="4">55</value>
			<value level="5">59</value>
		</mpConsume>
		<affectLimit>10-10</affectLimit>
		<affectRange>150</affectRange>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>DUAL</item>
					<item>SWORD</item>
					<item>BLUNT</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">811</value>
					<value level="2">1455</value>
					<value level="3">2123</value>
					<value level="4">2810</value>
					<value level="5">3160</value>
				</power>
				<criticalChance>15</criticalChance>
				<overHit>true</overHit>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.9</pDefMod>
			</effect>
		</effects>
	</skill>
	<skill id="45188" toLevel="1" name="Evolution Defense" nameRu="Защита Профессии">
		<!-- P. Def. +$s1, M. Def. +$s2.\n\n<Stats depends on the character level>\nRequired level: 76.\nP. Def. +$s3, M. Def. +$s4 with each level. -->
		<icon>icon.skill19131</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="EffectFlag">
				<flag>EVOLUTION_DEFENSE</flag>
			</effect>
		</effects>
	</skill>
	<skill id="45189" toLevel="1" name="Evolution Power" nameRu="Мощь Профессии">
		<!-- P. Atk. +$s1, M. Atk. +$s2.\n\n<Stats depends on the character level>\nRequired level: 80.\nP. Atk. +$s3, M. Atk. +$s4 with each level. -->
		<icon>icon.skill19132</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="EffectFlag">
				<flag>EVOLUTION_POWER</flag>
			</effect>
		</effects>
	</skill>
	<skill id="45190" toLevel="3" name="Detection" nameRu="Обнаружение">
		<!-- Allows to find invisible enemies nearby. The target can't become invisible and cancel this effect for 2 sec.\nRequires a weapon. -->
		<icon>icon.skill19203</icon>
		<affectLimit>6-12</affectLimit>
		<affectRange>
			<value level="1">200</value>
			<value level="2">400</value>
			<value level="3">450</value>
		</affectRange>
		<!--<effectPoint>
			<value level="1">-760</value>
			<value level="2">-850</value>
			<value level="3">-880</value>
		</effectPoint>-->
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<operateType>A2</operateType>
		<abnormalTime>2</abnormalTime>
		<activateRate>-1</activateRate>
		<isDebuff>true</isDebuff>
		<hitTime>1200</hitTime>
		<coolTime>300</coolTime>
		<reuseDelay>30000</reuseDelay>
		<mpConsume>70</mpConsume>
		<itemConsumeId>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">3031</value>
		</itemConsumeId>
		<itemConsumeCount>
			<value level="1">0</value>
			<value level="2">0</value>
			<value level="3">1</value>
		</itemConsumeCount>
		<effects>
			<effect name="Detection" />
			<effect name="BlockAbnormalSlot">
				<slot>HIDE</slot>
			</effect>
		</effects>
	</skill>
	<skill id="45191" toLevel="3" name="STR Increase Bonus" nameRu="Бонус Увеличения СИЛ">
		<!-- <STR increase effect Lv. 1>\n\nP. Atk. +20 -->
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="PAtk" fromLevel="1" toLevel="3">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="SkillCriticalDamage" fromLevel="2" toLevel="3">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalSkillPower" fromLevel="3" toLevel="3">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45192" toLevel="3" name="INT Increase Bonus" nameRu="Бонус Увеличения ИНТ">
		<!-- <INT increase effect Lv. 1>\n\nM. Atk. +20 -->
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="MAtk" fromLevel="1" toLevel="3">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicCriticalDamage" fromLevel="2" toLevel="3">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalSkillPower" fromLevel="3" toLevel="3">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45193" toLevel="3" name="DEX Increase Bonus" nameRu="Бонус Увеличения ЛВК">
		<!-- <DEX increase effect Lv. 1\n\nP. Critical Rate +50 -->
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="CriticalRate" fromLevel="1" toLevel="3">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Reuse" fromLevel="2" toLevel="3">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="SkillEvasion" fromLevel="3" toLevel="3">
				<amount>20</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="45194" toLevel="3" name="WIT Increase Bonus" nameRu="Бонус Увеличения МДР">
		<!-- <WIT increase effect Lv. 1>\n\nM. Skill Critical Rate +50. -->
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="MagicCriticalRate" fromLevel="1" toLevel="3">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="Reuse" fromLevel="2" toLevel="3">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="SkillEvasion" fromLevel="3" toLevel="3">
				<amount>20</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="45195" toLevel="3" name="CON Increase Bonus" nameRu="Бонус Увеличения ВЫН">
		<!-- <CON increase effect Lv. 1>\n\nP. Def. +200 -->
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="PhysicalDefence" fromLevel="1" toLevel="3">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxHp" fromLevel="2" toLevel="3">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="HpRegen" fromLevel="3" toLevel="3">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45196" toLevel="3" name="MEN Increase Bonus" nameRu="Бонус Увеличения ДУХ">
		<!-- <MEN increase effect Lv. 1>\n\nM. Def. +200 -->
		<magicLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="MagicalDefence" fromLevel="1" toLevel="3">
				<amount>200</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MaxMp" fromLevel="2" toLevel="3">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MpRegen" fromLevel="3" toLevel="3">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="45197" toLevel="2" name="Grand Master's Transcendent Blessing" nameRu="Невероятное Благословение Великого Мастера">
		<!-- Grand Master's power has allowed you to push the limits of your class and now you can use the main skill with increased stats. -->
		<operateType>A2</operateType>
		<abnormalTime>600</abnormalTime>
		<abnormalType>TRANSCENDENT_BUFF</abnormalType>
		<abnormalVisualEffect>U_ER_WI_WINDHIDE_AVE</abnormalVisualEffect>
		<effects>
			<effect name="EffectFlag">
				<flag>TRANSCENDENT_SKILLS</flag>
			</effect>
			<effect name="AdditionalPotionHp" fromLevel="1" toLevel="1">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MpVampiricAttack" fromLevel="1" toLevel="1">
				<amount>3</amount>
				<chance>20</chance>
			</effect>
		</effects>
	</skill>
	<skill id="45198" toLevel="2" name="Grand Master's Transcendent Blessing" nameRu="Невероятное Благословение Великого Мастера">
		<!-- Grand Master's power has allowed you to push the limits of your class and now you can use the main skill with increased stats.\n\n<Notice>\nWhen blessed you can't drain light/ shadow souls. -->
		<operateType>A2</operateType>
		<abnormalTime>600</abnormalTime>
		<abnormalType>TRANSCENDENT_BUFF</abnormalType>
		<abnormalVisualEffect>KAMAEL_BLACK_TRANSFORM_2;BLACK_TRANS_DECO_AVE;LONG_RAPIER_BLACK_AVE</abnormalVisualEffect>
		<effects>
			<effect name="EffectFlag">
				<flag>TRANSCENDENT_SKILLS</flag>
			</effect>
			<effect name="AdditionalPotionHp" fromLevel="1" toLevel="1">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MpVampiricAttack" fromLevel="1" toLevel="1">
				<amount>3</amount>
				<chance>20</chance>
			</effect>
		</effects>
	</skill>
	<skill id="45199" toLevel="24" name="Transcendent Power Strike" nameRu="Невероятный Мощный Удар">
		<!-- Delivers a powerful blow that strikes the target with $s1 power.\n\nRequires a sword/ blunt weapon/ spear/ dual swords.\nOver-hit.\nThe skill can be used only in the transcendent instance zones. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<effectPoint>
	<value level="1">-35</value>
	<value level="2">-40</value>
	<value level="3">-45</value>
	<value level="4">-60</value>
	<value level="5">-65</value>
	<value level="6">-70</value>
	<value level="7">-85</value>
	<value level="8">-90</value>
	<value level="9">-95</value>
	<value level="10">-110</value>
	<value level="11">-115</value>
	<value level="12">-120</value>
	<value level="13">-130</value>
	<value level="14">-135</value>
	<value level="15">-140</value>
	<value level="16">-150</value>
	<value level="17">-155</value>
	<value level="18">-160</value>
	<value level="19">-170</value>
	<value level="20">-175</value>
	<value level="21">-180</value>
	<value level="22">-190</value>
	<value level="23">-195</value>
	<value level="24">-200</value>
		</effectPoint>
		<mpConsume>
	<value level="1">5</value>
	<value level="2">6</value>
	<value level="3">6</value>
	<value level="4">9</value>
	<value level="5">9</value>
	<value level="6">9</value>
	<value level="7">11</value>
	<value level="8">12</value>
	<value level="9">13</value>
	<value level="10">15</value>
	<value level="11">16</value>
	<value level="12">17</value>
	<value level="13">18</value>
	<value level="14">18</value>
	<value level="15">19</value>
	<value level="16">20</value>
	<value level="17">21</value>
	<value level="18">22</value>
	<value level="19">23</value>
	<value level="20">24</value>
	<value level="21">25</value>
	<value level="22">26</value>
	<value level="23">27</value>
	<value level="24">27</value>
		</mpConsume>
		<coolTime>500</coolTime>
	</skill>
</list>
