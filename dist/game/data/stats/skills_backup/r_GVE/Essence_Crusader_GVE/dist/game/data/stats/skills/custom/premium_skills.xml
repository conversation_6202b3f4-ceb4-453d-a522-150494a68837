<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/skills.xsd">
	<!-- Premium Status Active Effect -->
	<skill id="90043" toLevel="1" name="Premium Status Active">
		<icon>icon.skill0000</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<abnormalType>PREMIUM_STATUS</abnormalType>
		<abnormalVisualEffect>LEGEND_DECO_HERO</abnormalVisualEffect>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<blockedInOlympiad>false</blockedInOlympiad>
		<basicProperty>NONE</basicProperty>
	</skill>

	<!-- Shared Premium Benefits Effect -->
	<skill id="90044" toLevel="1" name="Shared Premium Benefits">
		<icon>icon.skill11560</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>-1</abnormalTime>
		<abnormalType>SHARED_PREMIUM</abnormalType>
		<abnormalVisualEffect>PARTY_RECALL</abnormalVisualEffect>
		<operateType>A2</operateType>
		<isMagic>4</isMagic>
		<magicLevel>1</magicLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<isTriggeredSkill>true</isTriggeredSkill>
		<canBeDispelled>false</canBeDispelled>
		<stayAfterDeath>true</stayAfterDeath>
		<blockedInOlympiad>false</blockedInOlympiad>
		<basicProperty>NONE</basicProperty>
	</skill>
</list>
