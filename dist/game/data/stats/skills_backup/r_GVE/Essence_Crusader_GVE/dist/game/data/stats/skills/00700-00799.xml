<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="700" toLevel="1" name="Divine Healer Group Heal">
		<!-- Restores party members' HP and faction members within range. Power 400. Additionally restores 27 HP per sec. for 15 sec. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>5</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<activateRate>0</activateRate>
		<affectRange>1000</affectRange>
		<affectLimit>15</affectLimit>
		<effectPoint>661</effectPoint>
		<hitTime>7000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<mpConsume>300</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>8000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>PARTY_OR_FACTION</affectScope>
		<affectObject>FACTION</affectObject>
		<effects>
			<effect name="HealOverTime">
				<power>27</power>
				<ticks>1</ticks>
			</effect>
			<effect name="Heal">
				<power>400</power>
			</effect>
		</effects>
	</skill>
	<skill id="701" toLevel="1" name="Divine Healer Resurrection">
		<!-- Resurrects a dead party member. Additionally restores about 70%% XP. -->
		<icon>icon.skill_transform_etc</icon>
		<castRange>400</castRange>
		<effectPoint>661</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>6000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<mpConsume>252</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>PC_BODY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpResurrection" />
		</conditions>
		<effects>
			<effect name="Resurrection">
				<power>70</power>
			</effect>
		</effects>
	</skill>
	<skill id="702" toLevel="1" name="Divine Healer Cleanse">
		<!-- Cancel all the debuffs of the target. -->
		<icon>icon.skill_transform_etc</icon>
		<castRange>600</castRange>
		<effectPoint>676</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<mpConsume>59</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>8000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelByCategory">
				<slot>DEBUFF</slot>
				<rate>100</rate>
				<max>10</max>
			</effect>
		</effects>
	</skill>
	<skill id="703" toLevel="1" name="Sacrifice Healer">
		<!-- Regenerate party member's HP and MP and faction members within range by sacrificing yourself. Usable only when MP is under 10 percent. -->
		<icon>icon.skill_transform_etc</icon>
		<affectRange>300</affectRange>
		<affectLimit>10</affectLimit>
		<effectPoint>676</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>1800000</reuseDelay>
		<staticReuse>true</staticReuse>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>PARTY_OR_FACTION</affectScope>
		<affectObject>FACTION</affectObject>
		<conditions>
			<condition name="RemainMpPer">
				<amount>10</amount>
				<percentType>LESS</percentType>
			</condition>
		</conditions>
		<effects>
			<effect name="HealPercent">
				<power>100</power>
			</effect>
			<effect name="ManaHealPercent">
				<power>100</power>
			</effect>
		</effects>
		<selfEffects>
			<effect name="CallSkill">
				<skillId>5602</skillId> <!-- Transform Sacrifice -->
				<skillLevel>1</skillLevel>
			</effect>
		</selfEffects>
	</skill>
	<skill id="704" toLevel="1" name="Divine Enchanter Water Spirit">
		<!-- For 2 min., the targeted party member's M. Skill Critical Rate +2, MP +20%%, P. Atk. +10%%, P. Def. +20%%, Atk. Spd. +20%%, M. Atk. +20%%, M. Def. +20%%, Casting Spd. +20%%, Debuff Resistance +10%%. Speed -20%%, Skill MP consumption -5%%. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>4000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="PAtk">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicMpCost">
				<amount>-5</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="705" toLevel="1" name="Divine Enchanter Fire Spirit">
		<!-- For 2 min., the targeted party member's Max MP +20%%, HP Recovery Rate +20%%, M. Skill Critical Rate +2%%, P. Critical Damage +20%%, P. Atk. +10%%, P. Def. +20%%, Atk. Spd. +20%%, M. Atk. +20%%, M. Def. +20%%, Casting Spd. +20%%, Debuff Resistance +10%%. Speed -20%%. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>4000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="PAtk">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="706" toLevel="1" name="Divine Enchanter Wind Spirit">
		<!-- For 2 min., the targeted party member's Max HP +20%%, P. Critical Rate +20%%, M. Skill Critical Damage +20%%, P. Atk. +10%%, P. Def. +20%%, Atk. Spd. +20%%, M. Atk. +20%%, M. Def. +20%%, Casting Spd. +20%%, Debuff Resistance +10%%. Speed -20%%. With a certain chance, absorbs 5%% of the inflicted damage as HP. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>4000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="MaxHp">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="VampiricAttack">
				<amount>5</amount>
				<chance>80</chance>
			</effect>
			<effect name="PAtk">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="CriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="707" toLevel="1" name="Divine Enchanter Hero Spirit">
		<!-- For 2 min., the targeted party member's Max MP +20%%, M. Skill Critical Rate +2, P. Critical Damage +20%%, P. Atk. +10%%, P. Def. +20%%, Atk. Spd. +20%%, M. Atk. +20%%, M. Def. +20%%, Casting Spd. +20%%, Debuff Resistance +10%%. Speed -20%%. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>4000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="MaxHp">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PAtk">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="708" toLevel="1" name="Divine Enchanter Mass Binding">
		<!-- Momentarily apply Hold to surrounding enemies. Additional Hold is not available while effects last. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ROOT_MAGICALLY</abnormalType>
		<abnormalVisualEffect>ROOT</abnormalVisualEffect>
		<activateRate>40</activateRate>
		<affectLimit>10-10</affectLimit>
		<affectRange>200</affectRange>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>-676</effectPoint>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>80</magicLevel>
		<mpConsume>109</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>HOLD</trait>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="Root" />
			<effect name="DefenceTrait">
				<HOLD>100</HOLD>
			</effect>
		</effects>
	</skill>
	<skill id="709" toLevel="1" name="Sacrifice Enchanter">
		<!-- Sacrifices the character to increase party’s stats. Can be used only when MP < 10%%. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>TRANSFORM_SCRIFICE</abnormalType>
		<affectRange>300</affectRange>
		<effectPoint>1</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>1800000</reuseDelay>
		<staticReuse>true</staticReuse>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>PARTY</affectScope>
		<affectObject>FRIEND</affectObject>
		<conditions>
			<condition name="RemainMpPer">
				<amount>10</amount>
				<percentType>LESS</percentType>
			</condition>
		</conditions>
		<effects>
			<effect name="PAtk">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
		<selfEffects>
			<effect name="CallSkill">
				<skillId>5602</skillId> <!-- Transform Sacrifice -->
				<skillLevel>1</skillLevel>
			</effect>
		</selfEffects>
	</skill>
	<skill id="710" toLevel="1" name="Divine Summoner Summon Divine Beast">
		<!-- Summons a Holy Wild Beast. Requires 2 A-grade Crystals. -->
		<icon>icon.skill_transform_etc</icon>
		<hitTime>15000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>2</itemConsumeCount>
		<itemConsumeId>1461</itemConsumeId> <!-- Crystal (A-Grade) -->
		<magicLevel>80</magicLevel>
		<mpConsume>145</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="CanSummon" />
		</conditions>
		<effects>
			<effect name="Summon">
				<npcId>14870</npcId> <!-- Divine Beast -->
				<consumeItemId>1461</consumeItemId> <!-- Crystal (A-grade) -->
				<consumeItemCount>1</consumeItemCount>
				<expMultiplier>0.7</expMultiplier>
			</effect>
		</effects>
	</skill>
	<skill id="711" toLevel="1" name="Divine Summoner Transfer Pain">
		<!-- Transfers some of the player's damage to the summoner. Consumes MP continuously. -->
		<icon>icon.skill_transform_etc</icon>
		<magicLevel>80</magicLevel>
		<operateType>T</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>NONE</targetType>
		<effects>
			<effect name="MpConsumePerLevel">
				<power>0.4</power>
				<ticks>5</ticks>
			</effect>
			<effect name="TransferDamageToSummon">
				<amount>50</amount>
			</effect>
		</effects>
	</skill>
	<skill id="712" toLevel="1" name="Divine Summoner Final Servitor">
		<!-- Temporarily causes the summoner to be possessed with the spirit of an ancient hero. Consumes 20 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>300</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<itemConsumeCount>20</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicLevel>80</magicLevel>
		<mpConsume>72</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>60000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SUMMON</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MaxHp">
				<amount>20</amount>
				<mode>PER</mode>
				<heal>true</heal>
			</effect>
			<effect name="PAtk">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-20</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="CriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="713" toLevel="1" name="Divine Summoner Servitor Hill">
		<!-- Restores the servitor's HP by 991 power. -->
		<icon>icon.skill_transform_etc</icon>
		<castRange>600</castRange>
		<effectPoint>1</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<mpConsume>127</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SUMMON</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Heal">
				<power>991</power>
			</effect>
		</effects>
	</skill>
	<skill id="714" toLevel="1" name="Sacrifice Summoner">
		<!-- Sacrifices the character to increase party’s P. Critical. Can be used only when MP < 10%%. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>TRANSFORM_SCRIFICE</abnormalType>
		<affectRange>300</affectRange>
		<effectPoint>1</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>1800000</reuseDelay>
		<staticReuse>true</staticReuse>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>PARTY</affectScope>
		<affectObject>FRIEND</affectObject>
		<conditions>
			<condition name="RemainMpPer">
				<amount>10</amount>
				<percentType>LESS</percentType>
			</condition>
		</conditions>
		<effects>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
		</effects>
		<selfEffects>
			<effect name="CallSkill">
				<skillId>5602</skillId> <!-- Transform Sacrifice -->
				<skillLevel>1</skillLevel>
			</effect>
		</selfEffects>
	</skill>
	<skill id="715" toLevel="4" name="Zaken Energy Drain">
		<!-- Exhausts enemy's HP/ MP and absorbs some of HP. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>100</affectRange>
		<castRange>600</castRange>
		<coolTime>2000</coolTime>
		<effectPoint>
	<value level="1">-307</value>
	<value level="2">-323</value>
	<value level="3">-335</value>
	<value level="4">-342</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
	<value level="1">60</value>
	<value level="2">65</value>
	<value level="3">69</value>
	<value level="4">72</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>8000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="HpDrain">
				<power>
					<value level="1">121</value>
					<value level="2">133</value>
					<value level="3">145</value>
					<value level="4">160</value>
				</power>
				<percentage>80</percentage>
			</effect>
			<effect name="Mp">
				<amount>
					<value level="1">-274</value>
					<value level="2">-348</value>
					<value level="3">-422</value>
					<value level="4">-496</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="716" toLevel="4" name="Zaken Hold">
		<!-- Momentarily inflicts Hold on the enemy. Additional Hold is not available while effects last. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ROOT_MAGICALLY</abnormalType>
		<abnormalVisualEffect>ROOT</abnormalVisualEffect>
		<activateRate>95</activateRate>
		<basicProperty>MAGIC</basicProperty>
		<castRange>800</castRange>
		<coolTime>2000</coolTime>
		<effectPoint>
	<value level="1">-307</value>
	<value level="2">-323</value>
	<value level="3">-335</value>
	<value level="4">-342</value>
		</effectPoint>
		<effectRange>1300</effectRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
	<value level="1">60</value>
	<value level="2">65</value>
	<value level="3">69</value>
	<value level="4">72</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>HOLD</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Root" />
		</effects>
	</skill>
	<skill id="717" toLevel="4" name="Zaken Concentrated Attack">
		<!-- Focuses on one specific place and attacks with $s1 power. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>50</castRange>
		<coolTime>900</coolTime>
		<effectPoint>
	<value level="1">-307</value>
	<value level="2">-323</value>
	<value level="3">-335</value>
	<value level="4">-342</value>
		</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>2100</hitTime>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
	<value level="1">60</value>
	<value level="2">65</value>
	<value level="3">69</value>
	<value level="4">72</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">2751</value>
					<value level="2">3252</value>
					<value level="3">3752</value>
					<value level="4">4224</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="718" toLevel="4" name="Zaken Dancing Sword">
		<!-- Uses dancing swords to attack all at once. Power $s1 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>100</affectRange>
		<coolTime>1500</coolTime>
		<effectPoint>
	<value level="1">-307</value>
	<value level="2">-323</value>
	<value level="3">-335</value>
	<value level="4">-342</value>
		</effectPoint>
		<hitTime>1500</hitTime>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
	<value level="1">60</value>
	<value level="2">65</value>
	<value level="3">69</value>
	<value level="4">72</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>15000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1651</value>
					<value level="2">1951</value>
					<value level="3">2251</value>
					<value level="4">2534</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="719" toLevel="1" name="Zaken Vampiric Rage">
		<!-- Temporarily with a certain chance, absorbs some of the inflicted damage as HP. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>VAMPIRIC_ATTACK</abnormalType>
		<castRange>400</castRange>
		<effectPoint>604</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>65</magicLevel>
		<mpConsume>60</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="VampiricAttack">
				<amount>15</amount>
				<chance>80</chance>
			</effect>
		</effects>
	</skill>
	<skill id="720" toLevel="2" name="Anakim Holy Light Burst">
		<!-- Concentrates to deal heavy damage. Power $s1 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
	<value level="1">-335</value>
	<value level="2">-342</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>1080</hitTime>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>
	<value level="1">69</value>
	<value level="2">72</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">3189</value>
					<value level="2">3590</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="721" toLevel="2" name="Anakim's Energy Attack">
		<!-- Concentrates energy and fires it. Power $s1 -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>200</affectRange>
		<castRange>700</castRange>
		<effectPoint>
	<value level="1">-335</value>
	<value level="2">-342</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>
	<value level="1">69</value>
	<value level="2">72</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">186</value>
					<value level="2">197</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="722" toLevel="2" name="Anakim Divine Beam">
		<!-- Deals damage with a beam of light. Power $s1 -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>200</affectRange>
		<castRange>700</castRange>
		<effectPoint>
	<value level="1">-335</value>
	<value level="2">-342</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>
	<value level="1">69</value>
	<value level="2">72</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">186</value>
					<value level="2">197</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="723" toLevel="1" name="Anakim Sunshine">
		<!-- Instantly recovers a party member's HP. Power 1340. -->
		<icon>icon.skill_transform_etc</icon>
		<affectRange>1000</affectRange>
		<effectPoint>789</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>75</magicLevel>
		<mpConsume>25</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<hitCancelTime>0.2</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>PARTY</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="Heal">
				<power>1340</power>
			</effect>
		</effects>
	</skill>
	<skill id="724" toLevel="1" name="Anakim Cleanse">
		<!-- Cancel all of your debuffs. -->
		<icon>icon.skill_transform_etc</icon>
		<effectPoint>659</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>75</magicLevel>
		<mpConsume>25</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>8000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelByCategory">
				<slot>DEBUFF</slot>
				<rate>100</rate>
				<max>10</max>
			</effect>
		</effects>
	</skill>
	<skill id="725" toLevel="2" name="Venom Power Smash">
		<!-- Unleashes a powerful strike. Power $s1 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">2626</value>
					<value level="2">2957</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="726" toLevel="2" name="Venom Sonic Storm">
		<!-- Attacks the surrounding area by detonating the Momentum. Power $s1 -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>150</affectRange>
		<castRange>500</castRange>
		<effectPoint>-342</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>1900</hitTime>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1576</value>
					<value level="2">1774</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="727" toLevel="1" name="Venom Disillusion">
		<!-- Temporarily P. Atk. +8%%. -->
		<icon>icon.skill_transform_s_attack</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<effectPoint>138</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>75</magicLevel>
		<mpConsume>35</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="728" toLevel="1" name="Gordon Beast Attack">
		<!-- Attacks the target with 2957 power. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>2957</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="729" toLevel="1" name="Gordon Sword Stab">
		<!-- Attacks the enemy in the front with 2957 power. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>150</affectRange>
		<castRange>500</castRange>
		<effectPoint>-342</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>1900</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>1774</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="730" toLevel="1" name="Gordon Press">
		<!-- Instantly nearby enemies' Speed/ Atk. Spd./ Casting Spd. -23%%. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ATTACK_TIME_UP</abnormalType>
		<trait>SUPPRESSION</trait>
		<activateRate>60</activateRate>
		<affectLimit>10-10</affectLimit>
		<affectRange>200</affectRange>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>-1</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>80</magicLevel>
		<mpConsume>109</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="Speed">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="731" toLevel="1" name="Ranku Dark Explosion">
		<!-- Attack by detonating the devil's energy. Power 162. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>300</affectRange>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>3300</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<mpConsume>112</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>15000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>162</power>
			</effect>
		</effects>
	</skill>
	<skill id="732" toLevel="1" name="Ranku Stun Attack">
		<!-- Attacks the enemy with 1 power, stunning them. Over-hit. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>9</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>80</magicLevel>
		<mpConsume>19</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A2</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>SHOCK</trait>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>1479</power>
				<overHit>true</overHit>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>35016;35045;18103</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="733" toLevel="1" name="Kechi Double Cutter">
		<!-- Crosses swords and strikes at the enemy with 2957 power. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>2957</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="734" toLevel="1" name="Kechi Air Blade">
		<!-- Unleases the sword energy to attack the enemy at a distance with 1812 power. Critical. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>600</castRange>
		<effectPoint>-342</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1900</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>56</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>1812</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="735" toLevel="1" name="Devil Spinning Weapon">
		<!-- Attack surrounding enemies by swinging the weapon. Power 162. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectRange>200</affectRange>
		<effectPoint>-342</effectPoint>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>3300</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>80</magicLevel>
		<mpConsume>109</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>11000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>162</power>
			</effect>
		</effects>
	</skill>
	<skill id="736" toLevel="1" name="Devil Seed">
		<!-- Momentarily decreases HP. Serious damage will be caused by the fully grown seed if not canceled. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>DARK_SEED</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>100</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<coolTime>800</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>1400</effectRange>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>3300</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>30</lvlBonusRate>
		<magicLevel>80</magicLevel>
		<mpConsume>82</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>BLEED</trait>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DamOverTime">
				<power>351</power>
				<ticks>5</ticks>
			</effect>
		</effects>
		<endEffects>
			<effect name="CallSkill">
				<skillId>5248</skillId> <!-- Seed Explosion -->
				<skillLevel>9</skillLevel>
			</effect>
		</endEffects>
	</skill>
	<skill id="737" toLevel="1" name="Devil Ultimate Defense">
		<!-- Momentarily and drastically increases resistance to attacks that decreases P. Def./ M. Def. or cancels buffs. The player cannot move while the effect lasts. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PD_UP_SPECIAL</abnormalType>
		<effectPoint>100</effectPoint>
		<hitTime>1000</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>10</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>900000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockMove" />
			<effect name="MagicalDefence">
				<amount>1350</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>1800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="738" toLevel="3" name="Heretic Heal">
		<!-- Momentarily recovers the target's HP. Power $s1 -->
		<icon>icon.skill_transform_etc</icon>
		<castRange>600</castRange>
		<effectPoint>575</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>5000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>60</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">892</value>
					<value level="2">926</value>
					<value level="3">956</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="739" toLevel="3" name="Heretic Battle Heal">
		<!-- Immediately restores the target's HP with $s1 Power. -->
		<icon>icon.skill_transform_etc</icon>
		<castRange>600</castRange>
		<effectPoint>
	<value level="1">595</value>
	<value level="2">617</value>
	<value level="3">637</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">170</value>
	<value level="2">178</value>
	<value level="3">183</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">743</value>
					<value level="2">772</value>
					<value level="3">796</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="740" toLevel="3" name="Heretic Resurrection">
		<!-- Resurrects a dead party member restoring them $s1 of the lost XP. -->
		<icon>icon.skill_transform_etc</icon>
		<castRange>400</castRange>
		<effectPoint>
	<value level="1">595</value>
	<value level="2">617</value>
	<value level="3">637</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>4</isMagic>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">228</value>
	<value level="2">237</value>
	<value level="3">244</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<coolTime>500</coolTime>
		<staticReuse>true</staticReuse>
		<targetType>PC_BODY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpResurrection" />
		</conditions>
		<effects>
			<effect name="Resurrection">
				<power>
					<value level="1">50</value>
					<value level="2">55</value>
					<value level="3">60</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="741" toLevel="3" name="Heretic Heal Side Effect">
		<!-- Cause side effects of the heal. Momentarily make the enemy addicted. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>8</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<activateRate>70</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
	<value level="1">-323</value>
	<value level="2">-331</value>
	<value level="3">-337</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">65</value>
	<value level="2">68</value>
	<value level="3">70</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>POISON</trait>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DamOverTime">
				<power>52</power>
				<ticks>5</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="742" toLevel="3" name="Veil Master Bursting Flame">
		<!-- Attacks the enemy by unleashing dark flames. Power $s1 -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>600</castRange>
		<effectPoint>-92</effectPoint>
		<effectRange>1100</effectRange>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>1200</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">85</value>
	<value level="2">88</value>
	<value level="3">92</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttackRange">
				<power>
					<value level="1">112</value>
					<value level="2">117</value>
					<value level="3">122</value>
				</power>
				<shieldDefPercent>40</shieldDefPercent>
			</effect>
		</effects>
	</skill>
	<skill id="743" toLevel="3" name="Veil Master Dark Explosion">
		<!-- Detonates the soul to attack surrounding enemies. Power $s1 -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>200</affectRange>
		<castRange>500</castRange>
		<effectPoint>
	<value level="1">-323</value>
	<value level="2">-331</value>
	<value level="3">-337</value>
		</effectPoint>
		<effectRange>1000</effectRange>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>1800</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">85</value>
	<value level="2">88</value>
	<value level="3">92</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>4000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">56</value>
					<value level="2">59</value>
					<value level="3">61</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="744" toLevel="3" name="Veil Master: Dark Flare">
		<!-- Attacks a large number of enemies by creating huge explosion. Power $s1 -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>150</affectRange>
		<effectPoint>-275</effectPoint>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>1800</hitTime>
		<hitTime>1800</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">85</value>
	<value level="2">88</value>
	<value level="3">92</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">112</value>
					<value level="2">117</value>
					<value level="3">122</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="745" toLevel="3" name="Veil Master Dark Cure">
		<!-- Cancel all the debuffs of the target. -->
		<icon>icon.skill_transform_etc</icon>
		<castRange>600</castRange>
		<effectPoint>
	<value level="1">635</value>
	<value level="2">650</value>
	<value level="3">662</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">27</value>
	<value level="2">28</value>
	<value level="3">29</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>8000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelByCategory">
				<slot>DEBUFF</slot>
				<rate>100</rate>
				<max>10</max>
			</effect>
		</effects>
	</skill>
	<skill id="746" toLevel="3" name="Saber Tooth Tiger: Bite">
		<!-- Bites ferociously. Power $s1 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>
	<value level="1">-648</value>
	<value level="2">-660</value>
	<value level="3">-671</value>
		</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>88</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1789</value>
					<value level="2">1955</value>
					<value level="3">2117</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="747" toLevel="3" name="Saber Tooth Tiger Fear">
		<!-- Attacks surrounding enemies forcing them to flee. Power $s1 Over-hit. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>TURN_FLEE</abnormalType>
		<abnormalVisualEffect>TURN_FLEE</abnormalVisualEffect>
		<activateRate>60</activateRate>
		<affectLimit>6-12</affectLimit>
		<affectRange>150</affectRange>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>
	<value level="1">-323</value>
	<value level="2">-331</value>
	<value level="3">-337</value>
		</effectPoint>
		<hitTime>2000</hitTime>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">150</value>
	<value level="2">156</value>
	<value level="3">160</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>15000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<isDebuff>true</isDebuff>
		<trait>DERANGEMENT</trait>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">895</value>
					<value level="2">978</value>
					<value level="3">1059</value>
				</power>
				<overHit>true</overHit>
			</effect>
			<effect name="BlockControl" />
			<effect name="Fear" />
		</effects>
	</skill>
	<skill id="748" toLevel="1" name="Saber Tooth Tiger Sprint">
		<!-- Temporarily Speed +33. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<effectPoint>495</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>70</magicLevel>
		<mpConsume>24</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>3000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>33</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="749" toLevel="3" name="Oel Mahum Stun Attack">
		<!-- Deals damage and shock simultaneously. Power $s1 Momentarily inflicts stun on the enemy. Over-hit. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>9</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
	<value level="1">-323</value>
	<value level="2">-331</value>
	<value level="3">-337</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">68</value>
	<value level="2">71</value>
	<value level="3">73</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A2</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<isDebuff>true</isDebuff>
		<trait>SHOCK</trait>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">895</value>
					<value level="2">978</value>
					<value level="3">1059</value>
				</power>
				<overHit>true</overHit>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>35016;35045;18103</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="750" toLevel="1" name="Oel Mahum Ultimate Defense">
		<!-- Momentarily and drastically increases resistance to attacks that decreases P. Def./ M. Def. or cancels buffs. The player cannot move while the effect lasts. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PD_UP_SPECIAL</abnormalType>
		<effectPoint>100</effectPoint>
		<hitTime>1000</hitTime>
		<magicLevel>70</magicLevel>
		<mpConsume>10</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>900000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockMove" />
			<effect name="MagicalDefence">
				<amount>1350</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>1800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="751" toLevel="3" name="Oel Mahum Arm Flourish">
		<!-- Flourishes weapons to attack surrounding enemies. Power $s1 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>80</affectRange>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
	<value level="1">-323</value>
	<value level="2">-331</value>
	<value level="3">-337</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<fanRange>0;0;80;150</fanRange>
		<hitTime>1080</hitTime>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">71</value>
	<value level="2">74</value>
	<value level="3">77</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>4000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1073</value>
					<value level="2">1173</value>
					<value level="3">1271</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="752" toLevel="3" name="Doll Blader Sting">
		<!-- Strikes the enemy making them bleed. Over-hit. -->
		<icon>icon.skill_transform_debuff</icon>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>20</abnormalTime>
		<subordinationAbnormalType>BLEEDING</subordinationAbnormalType>
		<abnormalType>BLEEDING</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
	<value level="1">-323</value>
	<value level="2">-331</value>
	<value level="3">-337</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">71</value>
	<value level="2">74</value>
	<value level="3">77</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A2</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<isDebuff>true</isDebuff>
		<trait>BLEED</trait>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">1193</value>
					<value level="2">1303</value>
					<value level="3">1412</value>
				</power>
				<overHit>true</overHit>
			</effect>
			<effect name="DamOverTime">
				<power>134</power>
				<ticks>5</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="753" toLevel="3" name="Doll Blader Throwing Knife">
		<!-- Throws a dagger to attack an enemy in the distance. Power $s1 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<castRange>700</castRange>
		<coolTime>800</coolTime>
		<effectPoint>
	<value level="1">-323</value>
	<value level="2">-331</value>
	<value level="3">-337</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>3200</hitTime>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
	<value level="1">142</value>
	<value level="2">148</value>
	<value level="3">153</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>6000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">4769</value>
					<value level="2">5211</value>
					<value level="3">5645</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="754" toLevel="1" name="Doll Blader Clairvoyance">
		<!-- Temporarily P. Critical Rate +30%%. -->
		<icon>icon.skill_transform_buff</icon>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<castRange>400</castRange>
		<effectPoint>635</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>70</magicLevel>
		<mpConsume>65</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>2000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="762" toLevel="1" name="Insane Crusher" nameRu="Безумный Сокрушитель">
		<!-- Casts a dark curse and unleashes a powerful strike with 8409 power dealing damage to nearby enemies.\nCancels at least one of enemy's buffs, and temporarily decreases their Max CP significantly. Decreases Debuff Resistance and Received Healing.\nRequires a sword or a blunt weapon.\nIgnores Shield Defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0762</icon>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>TOUCH_OF_DEATH</abnormalType>
		<abnormalVisualEffect>REVENGE_AURA</abnormalVisualEffect>
		<activateRate>90</activateRate>
		<affectLimit>5-12</affectLimit>
		<affectRange>300</affectRange>
		<basicProperty>PHYSICAL</basicProperty>
		<coolTime>700</coolTime>
		<effectPoint>-6100</effectPoint>
		<hitTime>1300</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<magicLevel>80</magicLevel>
		<mpConsume>94</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>60000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>DEATH</trait>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>BLUNT</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>8409</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<criticalChance>50</criticalChance>
				<overHit>true</overHit>
			</effect>
			<effect name="DispelByCategory">
				<slot>BUFF</slot>
				<rate>25</rate>
				<max>3</max>
			</effect>
			<effect name="MaxCp">
				<amount>-90</amount>
				<mode>PER</mode>
				<heal>true</heal>
			</effect>
			<effect name="HealEffect">
				<amount>-30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>30</amount>
				<slot>DEBUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="767" toLevel="10" name="Bow Mastery" nameRu="Владение Луком">
		<!-- P. Atk. +$s1, when using a bow. -->
		<icon>icon.skill0767</icon>
		<magicLevel>81</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">95</value>
					<value level="2">160</value>
					<value level="3">300</value>
					<value level="4">435</value>
					<value level="5">720</value>
					<value level="6">970</value>
					<value level="7">1100</value>
					<value level="8">1180</value>
					<value level="9">1260</value>
					<value level="10">1340</value>
				</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>
					<value level="1">30</value>
					<value level="2">30</value>
					<value level="3">100</value>
					<value level="4">100</value>
					<value level="5">140</value>
					<value level="6">140</value>
					<value level="7">140</value>
					<value level="8">180</value>
					<value level="9">200</value>
					<value level="10">200</value>
				</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
			<effect name="PhysicalAttackRange">
				<amount>200</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
		</effects>
	</skill>
	<skill id="768" toLevel="1" name="Exciting Adventure" nameRu="Увлекательное Приключение">
		<!-- For 30 sec., Speed +10, P. Evasion +10, Vital Point Attack Rate +15%%, P. Skill Evasion +40%%, Buff Cancel Resistance +90%%. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0768</icon>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>AVOID_UP_SPECIAL</abnormalType>
		<abnormalVisualEffect>ULTIMATE_DEFENCE</abnormalVisualEffect>
		<effectPoint>0</effectPoint>
		<hitTime>1000</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>36</mpConsume>
		<operateType>A2</operateType>
		<!-- <reuseDelay>900000</reuseDelay> -->
		<reuseDelay>300000</reuseDelay> <!-- Custom -->
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>40</amount>
			</effect>
			<effect name="Speed">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="FatalBlowRate">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-90</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="769" toLevel="1" name="Wind Riding" nameRu="Оседлать Ветер">
		<!-- Storms the battleground riding the wind. For 30 sec., Speed +30, P. Evasion +15, Vital Point Attack Rate +10%%, P. Skill Evasion +60%%, Buff Cancel Resistance +80%%. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0769</icon>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>AVOID_UP_SPECIAL</abnormalType>
		<abnormalVisualEffect>ULTIMATE_DEFENCE</abnormalVisualEffect>
		<effectPoint>0</effectPoint>
		<hitTime>1000</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>36</mpConsume>
		<operateType>A2</operateType>
		<!-- <reuseDelay>900000</reuseDelay> -->
		<reuseDelay>300000</reuseDelay> <!-- Custom -->
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>60</amount>
			</effect>
			<effect name="Speed">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>15</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="FatalBlowRate">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="770" toLevel="1" name="Ghost Walking" nameRu="Походка Призрака">
		<!-- Infiltrates the battleground like a ghost. For 30 sec., Speed +20, P. Evasion +10, Vital Point Attack Rate +20%%, P. Skill Evasion +50%%, Buff Cancel Resistance +80%%. -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0770</icon>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>AVOID_UP_SPECIAL</abnormalType>
		<abnormalVisualEffect>ULTIMATE_DEFENCE</abnormalVisualEffect>
		<effectPoint>0</effectPoint>
		<hitTime>1000</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>36</mpConsume>
		<operateType>A2</operateType>
		<!-- <reuseDelay>900000</reuseDelay> -->
		<reuseDelay>300000</reuseDelay> <!-- Custom -->
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>50</amount>
			</effect>
			<effect name="Speed">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="FatalBlowRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="771" toLevel="1" name="Flame Hawk" nameRu="Пламенный Ястреб">
		<!-- Fires an arrow imbued with the phoenix energy, attacking the target and enemies at the front with 8049 power. For 10 sec., inflicts 250 additional damage per sec.\nIgnores Shield Defense.\nIgnores 10%% of the target's defense.\nRequires a bow.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0771</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>900</affectRange>
		<castRange>700</castRange>
		<effectPoint>-1220</effectPoint>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;40</fanRange>
		<hitTime>3500</hitTime>
		<coolTime>1000</coolTime>
		<magicLevel>80</magicLevel>
		<mpConsume>147</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>8049</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.9</pDefMod>
			</effect>
			<effect name="CallSkill">
				<skillId>23298</skillId> <!-- Flame Hawk -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="772" toLevel="1" name="Arrow Rain" nameRu="Дождь Стрел">
		<!-- Causes arrows to rain down from the sky, attacking the target and nearby enemies with 7043 power. For 10 sec., inflicts 250 additional damage per sec.\nIgnores Shield Defense.\nIgnores 10%% of the target's defense.\nRequires a bow.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0772</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>200</affectRange>
		<castRange>700</castRange>
		<coolTime>1000</coolTime>
		<effectPoint>-1220</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>147</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>RANGE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>7043</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.9</pDefMod>
			</effect>
			<effect name="CallSkill">
				<skillId>23299</skillId> <!-- Arrow Rain -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="773" toLevel="1" name="Ghost Piercing" nameRu="Призрачное Пробивание">
		<!-- Shoots ghost arrows, attacking the target and enemies in the front with 8452 power. For 10 sec., inflicts 300 additional damage per sec.\nIgnores Shield Defense.\nIgnores 5%% of the target's defense.\nRequires a bow.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0773</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>900</affectRange>
		<castRange>700</castRange>
		<effectPoint>-1220</effectPoint>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;100</fanRange>
		<hitTime>3000</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>147</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SQUARE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>8452</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
			<effect name="CallSkill">
				<skillId>23300</skillId> <!-- Ghost Piercing -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
		<coolTime>1000</coolTime>
	</skill>
	<skill id="777" toLevel="20" name="Demolition Impact" nameRu="Удар Низвержения">
		<!-- Unleashes a destructive shockwave at the target with $s1 power.\nRequires a sword or a blunt weapon.\nIgnores Shield Defense.\nIgnores $s2 of the target's defense.\nCritical.\nOver-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0777</icon>
		<castRange>40</castRange>
		<effectRange>200</effectRange>
		<hitTime>1000</hitTime>
		<effectPoint>
			<value level="1">-400</value>
			<value level="2">-405</value>
			<value level="3">-410</value>
			<value level="4">-415</value>
			<value level="5">-420</value>
			<value level="6">-425</value>
			<value level="7">-430</value>
			<value level="8">-435</value>
			<value level="9">-440</value>
			<value level="10">-445</value>
			<value level="11">-450</value>
			<value level="12">-455</value>
			<value level="13">-460</value>
			<value level="14">-465</value>
			<value level="15">-470</value>
			<value level="16">-475</value>
			<value level="17">-480</value>
			<value level="18">-485</value>
			<value level="19">-490</value>
			<value level="20">-495</value>
		</effectPoint>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">77</value>
			<value level="3">78</value>
			<value level="4">79</value>
			<value level="5">80</value>
			<value level="6">81</value>
			<value level="7">82</value>
			<value level="8">83</value>
			<value level="9">84</value>
			<value level="10">85</value>
			<value level="11">86</value>
			<value level="12">87</value>
			<value level="13">88</value>
			<value level="14">89</value>
			<value level="15">90</value>
			<value level="16">91</value>
			<value level="17">92</value>
			<value level="18">93</value>
			<value level="19">93</value>
			<value level="20">93</value>
		</magicLevel>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">60</value>
			<value level="3">61</value>
			<value level="4">62</value>
			<value level="5">63</value>
			<value level="6">63</value>
			<value level="7">64</value>
			<value level="8">65</value>
			<value level="9">66</value>
			<value level="10">66</value>
			<value level="11">67</value>
			<value level="12">68</value>
			<value level="13">69</value>
			<value level="14">69</value>
			<value level="15">70</value>
			<value level="16">72</value>
			<value level="17">74</value>
			<value level="18">76</value>
			<value level="19">78</value>
			<value level="20">80</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<nextAction>ATTACK</nextAction>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>BLUNT</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">4907</value>
					<value level="2">4976</value>
					<value level="3">5046</value>
					<value level="4">5117</value>
					<value level="5">5186</value>
					<value level="6">5256</value>
					<value level="7">5327</value>
					<value level="8">5397</value>
					<value level="9">5468</value>
					<value level="10">5538</value>
					<value level="11">5608</value>
					<value level="12">5679</value>
					<value level="13">5749</value>
					<value level="14">5821</value>
					<value level="15">5891</value>
					<value level="16">6067</value>
					<value level="17">6249</value>
					<value level="18">6436</value>
					<value level="19">6629</value>
					<value level="20">6827</value>
				</power>
				<criticalChance>10</criticalChance>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<pDefMod>0.9</pDefMod>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
		</effects>
		<coolTime>500</coolTime>
	</skill>
	<skill id="790" toLevel="3" name="Critical Shot" nameRu="Критический Выстрел">
		<!-- P. Critical Rate +$s1 when using a bow. -->
		<icon>icon.skill0790</icon>
		<magicLevel>
			<value level="1">28</value>
			<value level="2">40</value>
			<value level="3">49</value>
		</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
				<mode>DIFF</mode>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
		</effects>
	</skill>
	<skill id="791" toLevel="11" name="Lightning" nameRu="Разряд Молнии">
		<!-- Strikes the target with lightning paralyzing them for $s1 -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0791</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<trait>PARALYZE</trait>
		<activateRate>50</activateRate>
		<castRange>700</castRange>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>
			<value level="1">-760</value>
			<value level="2">-780</value>
			<value level="3">-800</value>
			<value level="4">-820</value>
			<value level="5">-840</value>
			<value level="6">-860</value>
			<value level="7">-870</value>
			<value level="8">-880</value>
			<value level="9">-890</value>
			<value level="10">-900</value>
			<value level="11">-910</value>
		</effectPoint>
		<hitTime>1000</hitTime>
		<coolTime>500</coolTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">78</value>
			<value level="3">80</value>
			<value level="4">82</value>
			<value level="5">84</value>
			<value level="6">86</value>
			<value level="7">87</value>
			<value level="8">88</value>
			<value level="9">89</value>
			<value level="10">90</value>
			<value level="11">91</value>
		</magicLevel>
		<mpConsume>
			<value level="1">30</value>
			<value level="2">35</value>
			<value level="3">40</value>
			<value level="4">45</value>
			<value level="5">50</value>
			<value level="6">55</value>
			<value level="7">60</value>
			<value level="8">65</value>
			<value level="9">70</value>
			<value level="10">75</value>
			<value level="11">85</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>20000</reuseDelay>
		<reuseDelayGroup>791</reuseDelayGroup>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockActions" />
		</effects>
	</skill>
	<skill id="792" toLevel="5" name="Soul Stigma" nameRu="Стигма Души">
		<!-- Marks the target. For $s1, P. Critical Damage/ M. Skill Critical Damage -$s2. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0792</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>STIGMA_A</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<activateRate>70</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">-460</value>
			<value level="2">-470</value>
			<value level="3">-480</value>
			<value level="4">-490</value>
			<value level="5">-500</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1500</hitTime>
		<coolTime>300</coolTime>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">78</value>
			<value level="3">80</value>
			<value level="4">82</value>
			<value level="5">84</value>
		</magicLevel>
		<mpConsume>
			<value level="1">40</value>
			<value level="2">45</value>
			<value level="3">50</value>
			<value level="4">55</value>
			<value level="5">60</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>15000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<isDebuff>true</isDebuff>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicCriticalDamage">
				<amount>
					<value level="1">-20</value>
					<value level="2">-25</value>
					<value level="3">-30</value>
					<value level="4">-35</value>
					<value level="5">-40</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>
					<value level="1">-20</value>
					<value level="2">-25</value>
					<value level="3">-30</value>
					<value level="4">-35</value>
					<value level="5">-40</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="794" toLevel="3" name="Enhanced Critical Shot" nameRu="Усиление Критического Выстрела">
		<!-- P. Critical Rate +$s1. -->
		<icon>icon.skill0794</icon>
		<operateType>P</operateType> <!-- confirm -->
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
			</effect>
		</effects>
	</skill>
	<skill id="795" toLevel="1" name="Divine Knight Brandish">
		<!-- Attacks the enemies in the front with 2322 power. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>200</affectRange>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>400</effectRange>
		<fanRange>0;0;200;180</fanRange>
		<hitTime>830</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>2322</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="796" toLevel="1" name="Divine Knight Explosion Attack">
		<!-- Detonates the floor by striking it hard. Power 1900. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>200</affectRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>830</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>1900</power>
			</effect>
		</effects>
	</skill>
	<skill id="797" toLevel="1" name="Divine Rogue Piercing Attack">
		<!-- Attacks all surrounding enemies with 2111 power. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<affectLimit>5-12</affectLimit>
		<affectRange>250</affectRange>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<fanRange>0;0;250;60</fanRange>
		<hitTime>830</hitTime>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SQUARE</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>2111</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="798" toLevel="1" name="Divine Warrior Crippling Attack">
		<!-- Poisons the enemy with an unexpected poison attack. -->
		<icon>icon.skill_transform_s_attack</icon>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>20</abnormalTime>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>-340</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicLevel>80</magicLevel>
		<mpConsume>80</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A2</operateType>
		<reuseDelay>3000</reuseDelay>
		<magicCriticalRate>5</magicCriticalRate>
		<trait>POISON</trait>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>1689</power>
			</effect>
			<effect name="DamOverTime">
				<power>69</power>
				<ticks>5</ticks>
			</effect>
		</effects>
	</skill>
</list>
