<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="6000" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6001" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6002" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6003" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6004" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6005" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6006" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6007" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6008" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6009" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6010" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6011" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6012" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6013" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6014" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6015" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6016" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6017" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6018" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6019" toLevel="1" name="Searching Master" nameRu="Поиск Хозяина">
		<!-- Directly attacks a pet's master or summoner. -->
		<icon>icon.skill6019</icon>
		<magicLevel>52</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6020" toLevel="1" name="Soul Retain" nameRu="Спасение Души">
		<!-- none -->
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>10800</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<activateRate>0</activateRate>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>85</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>1000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>6</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>-25</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>-25</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>-25</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6021" toLevel="1" name="Soul Retain" nameRu="Спасение Души">
		<!-- none -->
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>10800</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<activateRate>0</activateRate>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>85</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>1000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>9</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>-50</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>-50</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>-50</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6022" toLevel="1" name="Soul Retain" nameRu="Спасение Души">
		<!-- none -->
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>10800</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<activateRate>0</activateRate>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>85</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>1000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>12</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>-75</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>-75</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>-75</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6023" toLevel="1" name="Soul Retain" nameRu="Спасение Души">
		<!-- none -->
		<abnormalLevel>5</abnormalLevel>
		<abnormalTime>10800</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<activateRate>0</activateRate>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>85</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>1000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MAtk">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>-94</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>-94</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>-94</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6025" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6026" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6027" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6028" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6029" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6030" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6031" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6032" toLevel="1" name="not_used" nameRu="Неиспользуемый">
		<!-- none -->
		<magicLevel>83</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6033" toLevel="1" name="Treasure Seeker's Betrayal" nameRu="Клеймо Расхитителя Сокровищ">
		<!-- Carrying this mark of betrayal makes one the target of nearby monsters. -->
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>STIGMA_A</abnormalType>
		<castRange>1500</castRange>
		<effectPoint>-671</effectPoint>
		<effectRange>2000</effectRange>
		<isDebuff>true</isDebuff>
		<magicLevel>76</magicLevel>
		<mpConsume>42</mpConsume>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6034" toLevel="1" name="Freezing" nameRu="Заморозка">
		<!-- Impossible to move until the match starts. -->
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>INVINCIBILITY</abnormalType>
		<castRange>900</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>3500</hitTime>
		<magicLevel>85</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>6000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DamageBlock">
				<type>BLOCK_HP</type>
			</effect>
			<effect name="DamageBlock">
				<type>BLOCK_MP</type>
			</effect>
			<effect name="EffectFlag">
				<flag>DEBUFF_BLOCK</flag>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-100</amount>
				<slot>ALL</slot>
			</effect>
			<effect name="BlockMove"/>
			<effect name="BlockActions">
				<allowedSkills>35016</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="6037" toLevel="1" name="Soul Unleash" nameRu="Высвобождение Души">
		<!-- none -->
		<magicLevel>80</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6038" toLevel="1" name="Movement Block" nameRu="Блокировка Движения">
		<!-- You are under suspicion of using an illegal program. Chatting has been blocked. -->
		<icon>icon.skill6038</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>BOT_PENALTY</abnormalType>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>-1</magicLevel>
		<operateType>A2</operateType>
		<stayAfterDeath>True</stayAfterDeath>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockChat"/>
		</effects>
	</skill>
	<skill id="6039" toLevel="1" name="Movement Block" nameRu="Блокировка Движения">
		<!-- You are under suspicion of using this program illegally. Party participation has been blocked. -->
		<icon>icon.skill6038</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3600</abnormalTime>
		<abnormalType>BOT_PENALTY</abnormalType>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>-1</magicLevel>
		<operateType>A2</operateType>
		<stayAfterDeath>True</stayAfterDeath>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockParty"/>
		</effects>
	</skill>
	<skill id="6040" toLevel="1" name="Movement Block" nameRu="Блокировка Движения">
		<!-- You are under suspicion of using this program illegally. You can be attacked. -->
		<icon>icon.skill6038</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>300</abnormalTime>
		<abnormalType>BOT_PENALTY</abnormalType>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>-1</magicLevel>
		<operateType>A2</operateType>
		<stayAfterDeath>True</stayAfterDeath>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Flag"/>
		</effects>
	</skill>
	<skill id="6041" toLevel="1" name="Phoenix Rush" nameRu="Бросок Феникса">
		<!-- Charges the enemy and inflicts damage. -->
		<icon>icon.skill6041</icon>
		<castRange>400</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-679</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>800</hitTime>
		<magicLevel>81</magicLevel>
		<nextAction>ATTACK</nextAction>
		<operateType>DA1</operateType>
		<reuseDelay>30000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<conditions>
			<condition name="OpCheckCastRange">
				<distance>200</distance>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<power>2372</power>
			</effect>
		</effects>
	</skill>
	<skill id="6042" toLevel="1" name="Phoenix Cleanse" nameRu="Искупление Феникса">
		<!-- Cancels the target's de-buff. -->
		<icon>icon.skill6042</icon>
		<castRange>600</castRange>
		<effectPoint>679</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>81</magicLevel>
		<mpConsume>59</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>180000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelByCategory">
				<slot>DEBUFF</slot>
				<rate>100</rate>
				<max>5</max>
			</effect>
		</effects>
	</skill>
	<skill id="6043" toLevel="1" name="Phoenix Flame Feather" nameRu="Пылающее Перо Феникса">
		<!-- Spreads defensive ring-shaped flames. Power 70. -->
		<icon>icon.skill6043</icon>
		<affectLimit>6-12</affectLimit>
		<affectRange>200</affectRange>
		<effectPoint>-340</effectPoint>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>81</magicLevel>
		<mpConsume>109</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>15000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>70</power>
			</effect>
		</effects>
	</skill>
	<skill id="6044" toLevel="1" name="Phoenix Flame Beak" nameRu="Пылающий Клюв Феникса">
		<!-- Attacks the enemy with a powerful beak. Power 2156. -->
		<icon>icon.skill6044</icon>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>-343</effectPoint>
		<effectRange>400</effectRange>
		<attributeType>FIRE</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>1080</hitTime>
		<magicLevel>81</magicLevel>
		<mpConsume>80</mpConsume>
		<nextAction>ATTACK</nextAction>
		<operateType>A1</operateType>
		<reuseDelay>13000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>2156</power>
			</effect>
		</effects>
	</skill>
	<skill id="6045" toLevel="1" name="Presentation - Fortune Bug" nameRu="Жук Удачи">
		<!-- none -->
		<hitTime>2000</hitTime>
		<magicLevel>85</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6046" toLevel="14" name="Piercing Attack" nameRu="Проникающая Атака">
		<!-- A powerful stab pierces the target. -->
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-136</value>
			<value level="2">-202</value>
			<value level="3">-283</value>
			<value level="4">-377</value>
			<value level="5">-475</value>
			<value level="6">-564</value>
			<value level="7">-633</value>
			<value level="8">-644</value>
			<value level="9">-653</value>
			<value level="10">-660</value>
			<value level="11">-664</value>
			<value level="12">-667</value>
			<value level="13">-671</value>
			<value level="14">-674</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1000</hitTime>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">72</value>
			<value level="9">74</value>
			<value level="10">76</value>
			<value level="11">77</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">13</value>
			<value level="2">20</value>
			<value level="3">28</value>
			<value level="4">38</value>
			<value level="5">50</value>
			<value level="6">61</value>
			<value level="7">71</value>
			<value level="8">73</value>
			<value level="9">75</value>
			<value level="10">77</value>
			<value level="11">78</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">136</value>
					<value level="2">313</value>
					<value level="3">655</value>
					<value level="4">1248</value>
					<value level="5">2163</value>
					<value level="6">3407</value>
					<value level="7">4877</value>
					<value level="8">5179</value>
					<value level="9">5478</value>
					<value level="10">5772</value>
					<value level="11">5917</value>
					<value level="12">6059</value>
					<value level="13">6198</value>
					<value level="14">6334</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="6047" toLevel="14" name="Whirlwind" nameRu="Вихревой Поток">
		<!-- Caster spins rapidly, targeting nearby enemies. -->
		<affectLimit>5-12</affectLimit>
		<affectRange>200</affectRange>
		<effectPoint>
			<value level="1">-273</value>
			<value level="2">-405</value>
			<value level="3">-568</value>
			<value level="4">-756</value>
			<value level="5">-951</value>
			<value level="6">-1130</value>
			<value level="7">-1268</value>
			<value level="8">-1289</value>
			<value level="9">-1307</value>
			<value level="10">-1322</value>
			<value level="11">-1330</value>
			<value level="12">-1336</value>
			<value level="13">-1343</value>
			<value level="14">-1349</value>
		</effectPoint>
		<hitTime>2000</hitTime>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">72</value>
			<value level="9">74</value>
			<value level="10">76</value>
			<value level="11">77</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">25</value>
			<value level="2">40</value>
			<value level="3">56</value>
			<value level="4">76</value>
			<value level="5">99</value>
			<value level="6">121</value>
			<value level="7">142</value>
			<value level="8">146</value>
			<value level="9">150</value>
			<value level="10">153</value>
			<value level="11">155</value>
			<value level="12">156</value>
			<value level="13">158</value>
			<value level="14">159</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>POINT_BLANK</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">91</value>
					<value level="2">209</value>
					<value level="3">437</value>
					<value level="4">832</value>
					<value level="5">1442</value>
					<value level="6">2271</value>
					<value level="7">3251</value>
					<value level="8">3452</value>
					<value level="9">3652</value>
					<value level="10">3848</value>
					<value level="11">3944</value>
					<value level="12">4039</value>
					<value level="13">4132</value>
					<value level="14">4223</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="6048" toLevel="14" name="Spear Smash" nameRu="Сокрушающий Удар Копьем">
		<!-- Flourishes a lance and frontally attacks enemies. -->
		<affectLimit>5-15</affectLimit>
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-273</value>
			<value level="2">-405</value>
			<value level="3">-568</value>
			<value level="4">-756</value>
			<value level="5">-951</value>
			<value level="6">-1130</value>
			<value level="7">-1268</value>
			<value level="8">-1289</value>
			<value level="9">-1307</value>
			<value level="10">-1322</value>
			<value level="11">-1330</value>
			<value level="12">-1336</value>
			<value level="13">-1343</value>
			<value level="14">-1349</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<fanRange>0;0;300;90</fanRange>
		<hitTime>2000</hitTime>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">72</value>
			<value level="9">74</value>
			<value level="10">76</value>
			<value level="11">77</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">25</value>
			<value level="2">40</value>
			<value level="3">56</value>
			<value level="4">76</value>
			<value level="5">99</value>
			<value level="6">121</value>
			<value level="7">142</value>
			<value level="8">146</value>
			<value level="9">150</value>
			<value level="10">153</value>
			<value level="11">155</value>
			<value level="12">156</value>
			<value level="13">158</value>
			<value level="14">159</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>2000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>FAN</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">91</value>
					<value level="2">209</value>
					<value level="3">437</value>
					<value level="4">832</value>
					<value level="5">1442</value>
					<value level="6">2271</value>
					<value level="7">3251</value>
					<value level="8">3452</value>
					<value level="9">3652</value>
					<value level="10">3848</value>
					<value level="11">3944</value>
					<value level="12">4039</value>
					<value level="13">4132</value>
					<value level="14">4223</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="6049" toLevel="4" name="Battle Cry" nameRu="Боевой Клич">
		<!-- Utters a battle cry that increases one's own P. Atk., P./ M. Def. -->
		<icon>icon.action117</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<coolTime>500</coolTime>
		<effectPoint>100</effectPoint>
		<hitTime>1000</hitTime>
		<magicLevel>
			<value level="1">50</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">45</value>
			<value level="2">65</value>
			<value level="3">69</value>
			<value level="4">72</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>6000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">20</value>
					<value level="4">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">20</value>
					<value level="4">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">20</value>
					<value level="4">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6050" toLevel="14" name="Power Smash" nameRu="Мощный Сокрушающий Удар">
		<!-- Powerfully strikes a target. -->
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-136</value>
			<value level="2">-202</value>
			<value level="3">-283</value>
			<value level="4">-377</value>
			<value level="5">-475</value>
			<value level="6">-564</value>
			<value level="7">-633</value>
			<value level="8">-644</value>
			<value level="9">-653</value>
			<value level="10">-660</value>
			<value level="11">-664</value>
			<value level="12">-667</value>
			<value level="13">-671</value>
			<value level="14">-674</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1000</hitTime>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">72</value>
			<value level="9">74</value>
			<value level="10">76</value>
			<value level="11">77</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">13</value>
			<value level="2">20</value>
			<value level="3">28</value>
			<value level="4">38</value>
			<value level="5">50</value>
			<value level="6">61</value>
			<value level="7">71</value>
			<value level="8">73</value>
			<value level="9">75</value>
			<value level="10">77</value>
			<value level="11">78</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>1000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">136</value>
					<value level="2">313</value>
					<value level="3">655</value>
					<value level="4">1248</value>
					<value level="5">2163</value>
					<value level="6">3407</value>
					<value level="7">4877</value>
					<value level="8">5179</value>
					<value level="9">5478</value>
					<value level="10">5772</value>
					<value level="11">5917</value>
					<value level="12">6059</value>
					<value level="13">6198</value>
					<value level="14">6334</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="6051" toLevel="14" name="Energy Burst" nameRu="Шаровая Молния">
		<!-- Fires energy gathered in one's body at the enemies to the front. -->
		<affectLimit>5-15</affectLimit>
		<effectPoint>
			<value level="1">-273</value>
			<value level="2">-405</value>
			<value level="3">-568</value>
			<value level="4">-756</value>
			<value level="5">-951</value>
			<value level="6">-1130</value>
			<value level="7">-1268</value>
			<value level="8">-1289</value>
			<value level="9">-1307</value>
			<value level="10">-1322</value>
			<value level="11">-1330</value>
			<value level="12">-1336</value>
			<value level="13">-1343</value>
			<value level="14">-1349</value>
		</effectPoint>
		<fanRange>0;180;600;400</fanRange>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">72</value>
			<value level="9">74</value>
			<value level="10">76</value>
			<value level="11">77</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">24</value>
			<value level="2">39</value>
			<value level="3">53</value>
			<value level="4">70</value>
			<value level="5">90</value>
			<value level="6">110</value>
			<value level="7">130</value>
			<value level="8">133</value>
			<value level="9">137</value>
			<value level="10">139</value>
			<value level="11">140</value>
			<value level="12">143</value>
			<value level="13">144</value>
			<value level="14">145</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SQUARE_PB</affectScope>
		<affectObject>NOT_FRIEND</affectObject>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">17</value>
					<value level="2">25</value>
					<value level="3">37</value>
					<value level="4">51</value>
					<value level="5">67</value>
					<value level="6">84</value>
					<value level="7">101</value>
					<value level="8">104</value>
					<value level="9">107</value>
					<value level="10">110</value>
					<value level="11">111</value>
					<value level="12">113</value>
					<value level="13">114</value>
					<value level="14">115</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="6052" toLevel="14" name="Shock Wave" nameRu="Шоковая Волна">
		<!-- Fires energy gathered in one's body at the enemies to the front. -->
		<castRange>900</castRange>
		<effectPoint>
			<value level="1">-273</value>
			<value level="2">-405</value>
			<value level="3">-568</value>
			<value level="4">-756</value>
			<value level="5">-951</value>
			<value level="6">-1130</value>
			<value level="7">-1268</value>
			<value level="8">-1289</value>
			<value level="9">-1307</value>
			<value level="10">-1322</value>
			<value level="11">-1330</value>
			<value level="12">-1336</value>
			<value level="13">-1343</value>
			<value level="14">-1349</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">72</value>
			<value level="9">74</value>
			<value level="10">76</value>
			<value level="11">77</value>
			<value level="12">78</value>
			<value level="13">79</value>
			<value level="14">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">24</value>
			<value level="2">39</value>
			<value level="3">53</value>
			<value level="4">70</value>
			<value level="5">90</value>
			<value level="6">110</value>
			<value level="7">130</value>
			<value level="8">133</value>
			<value level="9">137</value>
			<value level="10">139</value>
			<value level="11">140</value>
			<value level="12">143</value>
			<value level="13">144</value>
			<value level="14">145</value>
		</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>5000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="MagicalAttack">
				<power>
					<value level="1">17</value>
					<value level="2">25</value>
					<value level="3">37</value>
					<value level="4">51</value>
					<value level="5">67</value>
					<value level="6">84</value>
					<value level="7">101</value>
					<value level="8">104</value>
					<value level="9">107</value>
					<value level="10">110</value>
					<value level="11">111</value>
					<value level="12">113</value>
					<value level="13">114</value>
					<value level="14">115</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="6053" toLevel="4" name="Howl" nameRu="Вой">
		<!-- The pet momentarily amplifies its abilities with a loud roar. Increases P./ M. Atk. and P./ M. Def. -->
		<icon>icon.action117</icon>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
		</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<coolTime>500</coolTime>
		<effectPoint>100</effectPoint>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<magicLevel>
			<value level="1">50</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">90</value>
			<value level="2">130</value>
			<value level="3">137</value>
			<value level="4">145</value>
		</mpConsume>
		<operateType>A2</operateType>
		<reuseDelay>6000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">20</value>
					<value level="4">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MAtk">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">20</value>
					<value level="4">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">20</value>
					<value level="4">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">5</value>
					<value level="2">10</value>
					<value level="3">20</value>
					<value level="4">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6054" toLevel="1" name="Switch State" nameRu="Смена Режима Питомца">
		<!-- The pet's condition changes. -->
		<magicLevel>85</magicLevel>
		<operateType>T</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>SELF</targetType>
	</skill>
	<skill id="6055" toLevel="1" name="Movement Block" nameRu="Блокировка Движения">
		<!-- You are under suspicion of using an illegal program. Your actions have been restricted. -->
		<icon>icon.skill6038</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>7200</abnormalTime>
		<abnormalType>BOT_PENALTY</abnormalType>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>-1</magicLevel>
		<operateType>A2</operateType>
		<stayAfterDeath>True</stayAfterDeath>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6056" toLevel="1" name="Movement Block" nameRu="Блокировка Движения">
		<!-- You are under suspicion of using an illegal program. Your actions have been restricted. -->
		<icon>icon.skill6038</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10800</abnormalTime>
		<abnormalType>BOT_PENALTY</abnormalType>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>-1</magicLevel>
		<operateType>A2</operateType>
		<stayAfterDeath>True</stayAfterDeath>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6057" toLevel="1" name="Movement Block" nameRu="Блокировка Движения">
		<!-- You are under suspicion of using this program illegally. you're no longer permitted to move. -->
		<icon>icon.skill6038</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>7200</abnormalTime>
		<abnormalType>BOT_PENALTY</abnormalType>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>-1</magicLevel>
		<operateType>A2</operateType>
		<stayAfterDeath>True</stayAfterDeath>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>-100</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6058" toLevel="1" name="Movement Block" nameRu="Блокировка Движения">
		<!-- You are under suspicion of using this program illegally. you're no longer permitted to move. -->
		<icon>icon.skill6038</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>7200</abnormalTime>
		<abnormalType>BOT_PENALTY</abnormalType>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>-1</magicLevel>
		<operateType>A2</operateType>
		<stayAfterDeath>True</stayAfterDeath>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<hitCancelTime>0</hitCancelTime>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Speed">
				<amount>-100</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6059" toLevel="1" name="Counter Critical" nameRu="Ответный Крит. Удар">
		<!-- P. Critical Damage +100%%. -->
		<icon>icon.skill1542</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>8</abnormalTime>
		<abnormalType>COUNTER_CRITICAL_TRIGGER</abnormalType>
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>80</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>15000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<isMagic>1</isMagic>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="CriticalDamage">
				<amount>100</amount>
				<mode>PER</mode>
				<!-- <weaponType>
					<item>SWORD</item>
					<item>BLUNT</item>
					<item>DAGGER</item>
					<item>DUALDAGGER</item>
					<item>DUAL</item>
					<item>DUALFIST</item>
					<item>POLE</item>
					<item>ANCIENTSWORD</item>
					<item>RAPIER</item>
				</weaponType> -->
			</effect>
		</effects>
	</skill>
	<skill id="6060" toLevel="1" name="Great Fury" nameRu="Неукротимая Ярость">
		<!-- Atk. Spd. +30%%, P. Critical Damage +1000. -->
		<icon>icon.skill1543</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>8</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN_SPECIAL</abnormalType>
		<affectRange>1000</affectRange>
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>80</magicLevel>
		<operateType>A2</operateType>
		<reuseDelay>15000</reuseDelay>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<affectObject>FRIEND</affectObject>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>1000</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6061" toLevel="1" name="Decrease Weight" nameRu="Легкость">
		<!-- Overweight limit +9000. Increases the amount of items that can be carried in the inventory. -->
		<icon>icon.skill1257</icon>
		<magicLevel>52</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6062" toLevel="1" name="Raid Boss (Lv. 99)" nameRu="Рейдовый Босс - Уровень 99">
		<!-- An evil undead version of the elder of twin knights who were famous warriors in Gracia before the fall. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6063" toLevel="1" name="Raid Boss (Lv. 99)" nameRu="Рейдовый Босс - Уровень 99">
		<!-- An evil undead version of the younger of twin knights who were famous warriors in Gracia before the fall. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6064" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- An undead version of a famous healer in Gracia before the fall. It served Ekimus and later aspired to rule the Hall of Erosion. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6065" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- An undead version of an infamous serial killer infamous throughout Gracia before the fall. He attempted to preserve his life by threatening Cohemenes, but was deceived and became an undead slave. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6066" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- Steals souls in order to secrete matter from the Seed of Infinity. These Soul Devourers eventually lose all capacity for thought, driven only by their unholy appetite. They particularly favor souls that suffered a horrific and agonizing death. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6067" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- An undead version of the royal astrologer in Gracia before the fall. While alive, his intentionally false prophecy opened the kingdom to the influence of Ekimus and the Seed of Infinity. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6068" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- An undead version of a former Gracian high priest. Obsessed with attaining eternal life, he played a pivotal role in bringing the Seed of Infinity to the capital. He styled himself the 1st priest of Ekimus. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6069" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- An exceptionally efficient pre-fall Gracian provincial official infamous for his brutal interrogation methods. He was transformed into undead by the Seed of Infinity. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6070" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- In his bid for power, he committed atrocities in Gracia rarely seen before the fall. Because of his thirst for violence and bloodshed, he was transformed into undead by the Seed of Infinity. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6071" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This undead monster was once a powerful warlord on the frontier of pre-fall Gracia. He believed that the enemy's power was contained in their blood, and so he killed countless prisoners in order to absorb their strength. He was transformed into undead by the Seed of Infinity. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6072" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This undead monster was once of the most powerful warlords in Gracia. He gained popularity among the fallen nobles for mercilessly carrying out his missions, whatever the cost. He was also a secret worshipper of the Goddess of Death. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6073" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This undead monster was created from the rage and pain of prisoners tortured and killed in ancient Gracia. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6074" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This undead monster was created to protect a sacred burial ground for nobles in ancient Gracia. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6075" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This undead monster was created to prevent the corpses left on the battlegrounds of ancient Gracia from being raised. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6076" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This undead monster fed on the souls of ancient Gracians. It awoke from its long slumber though the magic of Ekimus. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6077" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This undead monster fed on the souls of ancient Gracians. It awoke from its long slumber though the magic of Ekimus. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6078" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This ancient undead monster awoke through the M. Atk. of the Seed of Infinity. Once it was a powerful Gracian warrior, but now it lacks any vestige of its former self. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6079" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This ancient undead monster awoke through the M. Atk. of the Seed of Infinity. Once it was a powerful Gracian warrior, but now it lacks any vestige of its former self. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6080" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This ancient undead monster awoke through the M. Atk. of the Seed of Infinity. Once it was a powerful Gracian warrior, but now it lacks any vestige of its former self. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6081" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This ancient undead monster awoke through the M. Atk. of the Seed of Infinity. Once it was a powerful Gracian warrior, but now it lacks any vestige of its former self. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6082" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This ancient undead monster awoke through the M. Atk. of the Seed of Infinity. Once it was a powerful Gracian warrior, but now it lacks any vestige of its former self. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6083" toLevel="1" name="Raid Boss (Lv. 81)" nameRu="Рейдовый Босс - Уровень 81">
		<!-- This ancient undead monster awoke through the M. Atk. of the Seed of Infinity. Once it was a powerful Gracian warrior, but now it lacks any vestige of its former self. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6084" toLevel="1" name="Raid Boss (Lv. 80)" nameRu="Рейдовый Босс - Уровень 80">
		<!-- Lord of ancient Hwuh whose hand was imbued with Demon power in return for not taking action while Hwuh was being conquered by the Seed of Infinity. He now has the power to control Aurabirds and has become a Master Rider. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6085" toLevel="1" name="Raid Boss (Lv. 80)" nameRu="Рейдовый Босс - Уровень 80">
		<!-- This ill-tempered king of the birds in Gracia has become even more violent after losing its sky nest to the Storm Dragon Lindvior. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6086" toLevel="1" name="Raid Boss (Lv. 80)" nameRu="Рейдовый Босс - Уровень 80">
		<!-- One of the ladies-in-waiting of the Banshee Queen, she became close to Ekimus and eventually was transformed into a Banshee Queen herself. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6087" toLevel="1" name="Raid Boss (Lv. 80)" nameRu="Рейдовый Босс - Уровень 80">
		<!-- This Drake originally appeared with the Seed of Destruction. Though one of the children of Skellus, the Dragon of Darkness , it does not serve Tiat. Instead, it watches Tiat's actions for Skellus. -->
		<icon>icon.skillraid</icon>
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6088" toLevel="1" name="Block Buff" nameRu="Неснимаемый Эффект Блока">
		<!-- none -->
		<magicLevel>1</magicLevel>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
	</skill>
	<skill id="6089" toLevel="1" name="Attack Motion" nameRu="Атакующий Жест">
		<!-- none -->
		<coolTime>500</coolTime>
		<hitTime>3500</hitTime>
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<effectRange>1100</effectRange>
		<castRange>600</castRange>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6090" toLevel="1" name="Lightning Strike" nameRu="Удар Молнии">
		<!-- Paralyzed. -->
		<icon>icon.skill0279</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<effectPoint>1</effectPoint>
		<isDebuff>true</isDebuff>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>58</magicLevel>
		<nextAction>ATTACK</nextAction>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<trait>PARALYZE</trait>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockActions"/>
		</effects>
	</skill>
	<skill id="6091" toLevel="1" name="Anchor" nameRu="Якорь">
		<!-- Paralyzed. -->
		<icon>icon.skill1170</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<effectPoint>-850</effectPoint>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>44</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<trait>PARALYZE</trait>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockActions"/>
		</effects>
	</skill>
	<skill id="6092" toLevel="1" name="Lightning" nameRu="Разряд Молнии">
		<!-- Paralyzed. -->
		<icon>icon.skill0791</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<effectPoint>1</effectPoint>
		<isDebuff>true</isDebuff>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicLevel>81</magicLevel>
		<operateType>A2</operateType>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<trait>PARALYZE</trait>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="BlockActions"/>
		</effects>
	</skill>
	<skill id="6093" toLevel="1" name="Panther Hide" nameRu="Покров Пантеры">
		<!-- Allows one to stay hidden. Speed -10%%. The effect is cancelled if any action other than movement is initiated. -->
		<icon>icon.skill0922</icon>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>HIDE</abnormalType>
		<abnormalVisualEffect>STEALTH</abnormalVisualEffect>
		<effectPoint>679</effectPoint>
		<magicLevel>78</magicLevel>
		<mpConsume>42</mpConsume>
		<operateType>A2</operateType>
		<removedOnAnyActionExceptMove>true</removedOnAnyActionExceptMove>
		<removedOnDamage>true</removedOnDamage>
		<reuseDelay>180000</reuseDelay>
		<staticReuse>true</staticReuse>
		<basicProperty>NONE</basicProperty>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="Hide"/>
			<effect name="Speed">
				<amount>-10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6094" toLevel="11" name="Panther Cancel" nameRu="Натиск Пантеры">
		<!-- Cancels one or several of the target's buffs. -->
		<icon>icon.skill6094</icon>
		<castRange>40</castRange>
		<effectPoint>-475</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1500</value>
			<value level="5">1000</value>
			<value level="6">1500</value>
			<value level="7">1000</value>
			<value level="8">1500</value>
			<value level="9">1500</value>
			<value level="10">1500</value>
			<value level="11">1500</value>
		</hitTime>
		<magicLevel>
			<value level="1">68</value>
			<value level="2">72</value>
			<value level="3">76</value>
			<value level="4">78</value>
			<value level="5">79</value>
			<value level="6">80</value>
			<value level="7">81</value>
			<value level="8">82</value>
			<value level="9">83</value>
			<value level="10">84</value>
			<value level="11">85</value>
		</magicLevel>
		<mpConsume>42</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>
			<value level="1">30000</value>
			<value level="2">30000</value>
			<value level="3">30000</value>
			<value level="4">60000</value>
			<value level="5">30000</value>
			<value level="6">60000</value>
			<value level="7">30000</value>
			<value level="8">60000</value>
			<value level="9">60000</value>
			<value level="10">60000</value>
			<value level="11">60000</value>
		</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="DispelByCategory">
				<slot>BUFF</slot>
				<rate>25</rate>
				<max>2</max>
			</effect>
		</effects>
	</skill>
	<skill id="6095" toLevel="15" name="Panther's Dark Claw" nameRu="Темный Коготь Пантеры">
		<!-- Attacks the enemy with $s1 power. -->
		<icon>icon.skill1445</icon>
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-420</value>
			<value level="2">-510</value>
			<value level="3">-600</value>
			<value level="4">-640</value>
			<value level="5">-680</value>
			<value level="6">-720</value>
			<value level="7">-760</value>
			<value level="8">-780</value>
			<value level="9">-790</value>
			<value level="10">-800</value>
			<value level="11">-810</value>
			<value level="12">-820</value>
			<value level="13">-830</value>
			<value level="14">-840</value>
			<value level="15">-850</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<hitTime>1000</hitTime>
		<magicLevel>
			<value level="1">42</value>
			<value level="2">51</value>
			<value level="3">60</value>
			<value level="4">64</value>
			<value level="5">68</value>
			<value level="6">72</value>
			<value level="7">76</value>
			<value level="8">78</value>
			<value level="9">79</value>
			<value level="10">80</value>
			<value level="11">81</value>
			<value level="12">82</value>
			<value level="13">83</value>
			<value level="14">84</value>
			<value level="15">85</value>
		</magicLevel>
		<mpConsume>42</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>10000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY_ONLY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="PhysicalAttack">
				<power>
					<value level="1">469</value>
					<value level="2">758</value>
					<value level="3">1136</value>
					<value level="4">1327</value>
					<value level="5">1525</value>
					<value level="6">1727</value>
					<value level="7">1925</value>
					<value level="8">2020</value>
					<value level="9">2067</value>
					<value level="10">2112</value>
					<value level="11">2156</value>
					<value level="12">2200</value>
					<value level="13">2241</value>
					<value level="14">2282</value>
					<value level="15">2320</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="6096" toLevel="13" name="Panther's Fatal Claw" nameRu="Смертоносный Коготь Пантеры">
		<!-- Panther's P. Atk. is supplemented with $s1 Power added to P. Atk., in a deadly attack. -->
		<icon>icon.skill6096</icon>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>-475</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">1000</value>
			<value level="6">1080</value>
			<value level="7">1000</value>
			<value level="8">1080</value>
			<value level="9">1000</value>
			<value level="10">1080</value>
			<value level="11">1080</value>
			<value level="12">1080</value>
			<value level="13">1080</value>
		</hitTime>
		<magicLevel>
			<value level="1">60</value>
			<value level="2">64</value>
			<value level="3">68</value>
			<value level="4">72</value>
			<value level="5">76</value>
			<value level="6">78</value>
			<value level="7">79</value>
			<value level="8">80</value>
			<value level="9">81</value>
			<value level="10">82</value>
			<value level="11">83</value>
			<value level="12">84</value>
			<value level="13">85</value>
		</magicLevel>
		<mpConsume>42</mpConsume>
		<operateType>A1</operateType>
		<reuseDelay>10000</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">3608</value>
					<value level="2">4180</value>
					<value level="3">4775</value>
					<value level="4">5380</value>
					<value level="5">5973</value>
					<value level="6">6260</value>
					<value level="7">6399</value>
					<value level="8">6535</value>
					<value level="9">6668</value>
					<value level="10">6798</value>
					<value level="11">6923</value>
					<value level="12">7044</value>
					<value level="13">7160</value>
				</power>
				<chanceBoost>200</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="6097" toLevel="1" name="Scissors" nameRu="Ножницы">
		<!-- Scissors. -->
		<icon>icon.xmas_gawi_i00</icon>
		<effectPoint>-1</effectPoint>
		<hitTime>2000</hitTime>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>1500</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<effectRange>500</effectRange>
		<castRange>400</castRange>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6098" toLevel="1" name="Rock" nameRu="Камень">
		<!-- Rock. -->
		<icon>icon.xmas_bawi_i00</icon>
		<effectPoint>-1</effectPoint>
		<hitTime>2000</hitTime>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>1500</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<effectRange>500</effectRange>
		<castRange>400</castRange>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
	<skill id="6099" toLevel="1" name="Paper" nameRu="Бумага">
		<!-- Paper. -->
		<icon>icon.xmas_bo_i00</icon>
		<effectPoint>-1</effectPoint>
		<hitTime>2000</hitTime>
		<isMagic>0</isMagic> <!-- Auto generated with parser -->
		<magicLevel>1</magicLevel>
		<operateType>A1</operateType>
		<reuseDelay>1500</reuseDelay>
		<magicCriticalRate>-5</magicCriticalRate>
		<hitCancelTime>0</hitCancelTime>
		<effectRange>500</effectRange>
		<castRange>400</castRange>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
	</skill>
</list>
