<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="shuttle" maxOccurs="unbounded" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="doors">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="door" maxOccurs="unbounded" minOccurs="0">
											<xs:complexType>
												<xs:simpleContent>
													<xs:extension base="xs:string">
														<xs:attribute type="xs:integer" name="id" use="required" />
													</xs:extension>
												</xs:simpleContent>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="stops">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="stop" maxOccurs="unbounded" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="dimension" maxOccurs="unbounded" minOccurs="0">
														<xs:complexType>
															<xs:simpleContent>
																<xs:extension base="xs:string">
																	<xs:attribute type="xs:integer" name="x" use="required" />
																	<xs:attribute type="xs:integer" name="y" use="required" />
																	<xs:attribute type="xs:integer" name="z" use="required" />
																</xs:extension>
															</xs:simpleContent>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
												<xs:attribute type="xs:byte" name="id" use="required" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="routes">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="route" maxOccurs="unbounded" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="loc">
														<xs:complexType>
															<xs:simpleContent>
																<xs:extension base="xs:string">
																	<xs:attribute type="xs:integer" name="x" use="required" />
																	<xs:attribute type="xs:integer" name="y" use="required" />
																	<xs:attribute type="xs:integer" name="z" use="required" />
																	<xs:attribute type="xs:integer" name="heading" use="required" />
																</xs:extension>
															</xs:simpleContent>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
												<xs:attribute type="xs:byte" name="id" use="required" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute type="xs:byte" name="id" use="required" />
						<xs:attribute type="xs:string" name="name" use="required" />
						<xs:attribute type="xs:integer" name="x" use="required" />
						<xs:attribute type="xs:integer" name="y" use="required" />
						<xs:attribute type="xs:integer" name="z" use="required" />
						<xs:attribute type="xs:integer" name="heading" use="required" />
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>