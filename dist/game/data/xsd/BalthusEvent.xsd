<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence minOccurs="1" maxOccurs="1">
			    <xs:element name="rewards" minOccurs="1" maxOccurs="1">
			        <xs:complexType>
				        <xs:sequence minOccurs="1" maxOccurs="1">
				            <xs:element name="reward" minOccurs="0" maxOccurs="unbounded">
                                <xs:complexType>
                                    <xs:sequence minOccurs="1" maxOccurs="1">
                                        <xs:element name="state" minOccurs="0" maxOccurs="unbounded">
                                             <xs:complexType>
                                                <xs:attribute name="id" type="xs:positiveInteger" use="required" />
                                                <xs:attribute name="token" type="xs:positiveInteger" use="optional" />
                                                <xs:attribute name="chance" type="xs:double" use="optional" />
                                             </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="id" type="xs:positiveInteger" use="required" />
                                    <xs:attribute name="count" type="xs:positiveInteger" use="optional" />
                                </xs:complexType>
                            </xs:element>
					    </xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute type="xs:string" name="enabled" />
			<xs:attribute type="xs:int" name="minimumLevel" />
		</xs:complexType>
	</xs:element>
</xs:schema>