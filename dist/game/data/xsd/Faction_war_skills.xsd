<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified">

    <xs:element name="list">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="skill" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:positiveInteger" use="required"/>
                        <xs:attribute name="level" type="xs:positiveInteger" use="required"/>
                        <xs:attribute name="minTrust" type="xs:nonNegativeInteger" use="required"/>
                        <xs:attribute name="cost" type="xs:nonNegativeInteger" use="required"/>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="enabled" type="xs:boolean" use="optional" default="true"/>
        </xs:complexType>
    </xs:element>

</xs:schema>