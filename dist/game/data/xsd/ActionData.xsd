<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="action" maxOccurs="unbounded" minOccurs="0">
					<xs:complexType>
						<xs:attribute type="xs:nonNegativeInteger" name="id" use="required" />
						<xs:attribute type="xs:string" name="handler" use="required" />
						<xs:attribute type="xs:int" name="option" use="optional" />
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>