<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <!-- Define the main element "list" -->
    <xs:element name="list">
        <xs:complexType>
            <xs:sequence>
                <!-- Define the "artifact" element -->
                <xs:element name="artifact" maxOccurs="unbounded" minOccurs="0">
                    <xs:complexType>
                        <xs:sequence>
                            <!-- Define "spawn_groups" -->
                            <xs:element name="spawn_groups" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="group" maxOccurs="unbounded" minOccurs="0">
                                            <xs:complexType>
                                                <xs:attribute name="type" type="xs:string" use="required"/>
                                                <xs:attribute name="name" type="xs:string" use="required"/>
                                                <xs:attribute name="leaderNpcId" type="xs:nonNegativeInteger" use="required"/>
                                                <xs:attribute name="guardNpcId" type="xs:nonNegativeInteger" use="required"/>
                                                <xs:attribute name="guardCount" type="xs:nonNegativeInteger" use="required"/>
                                                <xs:attribute name="respawnLeader" type="xs:string" use="required"/>
                                                <xs:attribute name="respawnGuard" type="xs:string" use="required"/>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>

                            <!-- Define "teleport_locations" -->
                            <xs:element name="teleport_locations" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="location" maxOccurs="unbounded" minOccurs="0">
                                            <xs:complexType>
                                                <xs:attribute name="x" type="xs:int" use="required"/>
                                                <xs:attribute name="y" type="xs:int" use="required"/>
                                                <xs:attribute name="z" type="xs:int" use="required"/>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>

                            <!-- Define "regions" -->
                            <xs:element name="regions" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="stat" maxOccurs="unbounded" minOccurs="0">
                                            <xs:complexType>
                                                <xs:attribute name="name" type="xs:string" use="required"/>
                                                <xs:attribute name="val" type="xs:string" use="required"/>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="name" type="xs:string" use="required"/>
                                </xs:complexType>
                            </xs:element>

                            <!-- Define "rewardSkills" -->
                            <xs:element name="rewardSkills" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="skill" maxOccurs="unbounded" minOccurs="0">
                                            <xs:complexType>
                                                <xs:attribute name="id" type="xs:nonNegativeInteger" use="required"/>
                                                <xs:attribute name="level" type="xs:nonNegativeInteger" use="required"/>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>

                            <!-- Define "rewardItems" -->
                            <xs:element name="rewardItems" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="item" maxOccurs="unbounded" minOccurs="0">
                                            <xs:complexType>
                                                <xs:attribute name="itemId" type="xs:nonNegativeInteger" use="required"/>
                                                <xs:attribute name="count" type="xs:long" use="required"/>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>

                            <!-- Define "params" -->
                            <xs:element name="params" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="param" maxOccurs="unbounded" minOccurs="0">
                                            <xs:complexType>
                                                <xs:attribute name="name" type="xs:string" use="required"/>
                                                <xs:attribute name="value" type="xs:string" use="required"/>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:nonNegativeInteger" use="required"/>
                        <xs:attribute name="npcId" type="xs:nonNegativeInteger" use="required"/>
                        <xs:attribute name="protectTime" type="xs:nonNegativeInteger" use="required"/>
                        <xs:attribute name="stringName" type="xs:string" use="required"/>
                        <xs:attribute name="region" type="xs:string" use="required"/>
                        <xs:attribute name="loc" type="xs:string" use="required"/>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>