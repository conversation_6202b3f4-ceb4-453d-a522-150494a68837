<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence minOccurs="1">
				<xs:element name="zone" maxOccurs="10000">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="teleport" minOccurs="1" maxOccurs="100">
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute type="xs:integer" name="x" use="required" />
											<xs:attribute type="xs:integer" name="y" use="required" />
											<xs:attribute type="xs:integer" name="z" use="required" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute type="xs:nonNegativeInteger" name="zoneId" use="required" />
						<xs:attribute type="xs:nonNegativeInteger" name="minLevel" use="required" />
						<xs:attribute type="xs:nonNegativeInteger" name="maxLevel" use="required" />
						<xs:attribute type="xs:nonNegativeInteger" name="maxPlayerCount" use="optional" />
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>