<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="raid" maxOccurs="unbounded" minOccurs="0">
					<xs:complexType>
						<xs:simpleContent>
							<xs:extension base="xs:string">
								<xs:attribute type="xs:int" name="id" use="required"/>
								<xs:attribute type="xs:int" name="x" use="required"/>
								<xs:attribute type="xs:int" name="y" use="required"/>
								<xs:attribute type="xs:int" name="z" use="required"/>
								<xs:attribute type="xs:boolean" name="enabled" use="optional"/>
							</xs:extension>
						</xs:simpleContent>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>