<html><title>Spawn NPC menu</title><body>
<center>
<table width=260>
<tr>
<td width=40><button value="Main" action="bypass -h admin_admin" width=40 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td width=180><center><button value="Spawn Mobs" action="bypass -h admin_show_spawns" width=100 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
<td width=40><button value="Back" action="bypass -h admin_show_spawns" width=40 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table><br>
Spawn NPC / Mobb by ID: <font color="LEVEL">[*]</font> Normal or Once<br1>
<table width=260 bgcolor="666666">
<tr>
<td width=80>Id/Name|qty:</td>
<td width=60><edit var="id" width=55 height=15></td>
<td width=15><edit var="qty" width=20 height=15></td>
<td width=50><button value="Spawn" action="bypass -h admin_spawn_monster $id $qty" width=50 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td width=30><button value="x1" action="bypass -h admin_spawn_once $id $qty" width=30 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table><br><br>
Show NPCs whose name start with:<br1>
<table width=260>
<tr>
<td><button value="A" action="bypass -h admin_npc_index A" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="B" action="bypass -h admin_npc_index B" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="C" action="bypass -h admin_npc_index C" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="D" action="bypass -h admin_npc_index D" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="E" action="bypass -h admin_npc_index E" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="F" action="bypass -h admin_npc_index F" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="G" action="bypass -h admin_npc_index G" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="H" action="bypass -h admin_npc_index H" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="I" action="bypass -h admin_npc_index I" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="J" action="bypass -h admin_npc_index J" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="K" action="bypass -h admin_npc_index K" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="L" action="bypass -h admin_npc_index L" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="M" action="bypass -h admin_npc_index M" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="N" action="bypass -h admin_npc_index N" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="O" action="bypass -h admin_npc_index O" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="P" action="bypass -h admin_npc_index P" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Q" action="bypass -h admin_npc_index Q" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="R" action="bypass -h admin_npc_index R" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="S" action="bypass -h admin_npc_index S" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="T" action="bypass -h admin_npc_index T" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="U" action="bypass -h admin_npc_index U" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="V" action="bypass -h admin_npc_index V" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="W" action="bypass -h admin_npc_index W" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="X" action="bypass -h admin_npc_index X" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Y" action="bypass -h admin_npc_index Y" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Z" action="bypass -h admin_npc_index Z" width=40 height=25 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table><br>
</center>
[*]: Names are case insensitive, but you must use underscores instead of spaces.<br>You can leave qty field empty if you don't need more than one mob.
</body></html>