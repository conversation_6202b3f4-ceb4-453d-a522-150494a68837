<html><title>Help</title><body><font color="LEVEL">
<table width=270><tr>
<td width=90><a action="bypass -h admin_html help/main_menu.htm">Prev:Intro</a></td>
<td width=90><a action="bypass -h admin_html admhelp.htm">Up:Index</a></td>
<td width=90><a action="bypass -h admin_html help/main_menu3.htm">Next:Player</a></td>
</tr></table></font><br>
<font color="LEVEL">1-Main Panel Buttons</font><br>
The most useful commands have been organized here.<br>
<font color="LEVEL">1-1 The stuff you will use most of the time</font><br>
<font color="LEVEL">Item:</font> takes item id and optionally an item amount from quickbox to create such an item in your inventory. Shows specific dialog if nothing was specified.<br>
<font color="LEVEL">Teleport:</font> read coordinates from quickbox. Shows teleport and moves menu if None specified.<br>
<font color="LEVEL">Spawn:</font> takes npc_id or name (spaces must be changed to underscores); optionally spawn count and respawn time. Shows specific dialog if None.<br>
<font color="LEVEL">List Spawns:</font> takes npc_id or name (spaces need to be replaced by underscores). Show results as system messages.<br>
<font color="LEVEL">Announce:</font> takes text to announce from quickbox; shows announce menu if no text was specified.<br>
<font color="LEVEL">Set:</font> requires "parameter=value" in the quickbox. Shows settings dialog if nothing valid was specified.<br>
<font color="LEVEL">GM Chat:</font> broadcast quickbox contents for GMs only. Admin menu will popup after broadcast.<br>
<font color="LEVEL">List+Go:</font> Like Google's Lucky button, will teleport you to the first result of a List Spawn command.<br>
<font color="LEVEL">Go To:</font> reads player name from quickbox. Shows characters management screen if None specified.<br>
<font color="LEVEL">Recall:</font> brings a player to your position, if a player name was specified in the quickbox<br>
<font color="LEVEL">Karma:</font> set target's karma points to a value specified in the quickbox (if target is a player, naturally).<br>
<font color="LEVEL">No Karma:</font> works over your target just like the Karma button, but ignores QB values setting karma to zero.
</body></html>