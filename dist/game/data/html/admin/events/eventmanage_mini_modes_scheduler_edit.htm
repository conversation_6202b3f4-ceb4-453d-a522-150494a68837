<html><title>%title%</title><body>
<center>
<table width=270 border=0 bgcolor="363636"><tr><td><button value="Main Events" action="bypass admin_event_manage edit_events_menu edit_events_menu" width=90 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td><td><button value="Mini Events" action="bypass admin_event_manage mini_menu" width=90 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td><td width=120><button value="Config" action="bypass admin_event_manage globalconfig_menu Core 1" width=90 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr></table>

<br>
<font color=9f9f9f>%modeName% mode running times:</font>
<br1>
<table width=240>

<tr>
<td align=center width=220><font color=ac9775>Hours:</font>  <font color=9f9f9f>%from% - %to%</font></td>
</tr>

</table>

<br1>

<font color=606060>(%future%)</font>
<br1>

<table width=160>

<tr>
<td width=60><edit var="hour" width=60 height=15></td>
<td width=50><button value="From" action="bypass admin_event_manage mini_edit_modes_scheduler_set_from $hour" width=50 height=20 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
<td width=50><button value="To" action="bypass admin_event_manage mini_edit_modes_scheduler_set_to $hour" width=50 height=20 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
</tr>

</table> 
<br1>
<table width=220>

<tr>
<td witth=60><font color=ac9775>Days:</font></td>>
<td align=right width=90><combobox width=90 height=17 var=day list=AllDays;Monday;Tuesday;Wednesday;Thursday;Friday;Saturday;Sunday></td>
<td align=left width=50><button value="Add" action="bypass admin_event_manage mini_edit_modes_scheduler_addday $day" width=50 height=20 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
</tr>

</table> 

<br>

%days%

<br>
<img src="L2UI_CH3.herotower_deco" width=256 height=32>

<br>
<button value="Remove" action="bypass admin_event_manage mini_edit_modes_scheduler_removetime" width=75 height=20 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
<button value="Back" action="bypass admin_event_manage mini_edit_modes_scheduler_menu" width=75 height=20 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">

</center>
</body></html>