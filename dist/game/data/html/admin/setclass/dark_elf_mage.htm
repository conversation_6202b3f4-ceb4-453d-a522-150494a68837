<html><body>
<title>//setclass  Menu</title>
<table width="260"><tr>
<td width="40"><center><button value="Back to Main Menu" action="bypass -h admin_admin" width="292" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr></table>
<table width="260" border="0" bgcolor="000000">
<tr>
<td><button value="Human F." action="bypass -h admin_html setclass/human_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Human M." action="bypass -h admin_html setclass/human_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Elf F." action="bypass -h admin_html setclass/elf_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Elf M." action="bypass -h admin_html setclass/elf_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Dark Elf F." action="bypass -h admin_html setclass/dark_elf_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Dark Elf M." action="bypass -h admin_html setclass/dark_elf_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Orc F." action="bypass -h admin_html setclass/orc_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Orc M." action="bypass -h admin_html setclass/orc_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Dwarf" action="bypass -h admin_html setclass/dwarf.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Kamael" action="bypass -h admin_html setclass/kamael.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="D. Knight" action="bypass -h admin_html setclass/death_knight.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Sylph" action="bypass -h admin_html setclass/sylph.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<table width="302" border="0" bgcolor="999999">
<tr>
<td><center><button value="Mystic" action="bypass -h admin_setclass 38" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr>
</table>
<table width="302" border="0" bgcolor="666666">
<tr>
<td><center><button value="Wizard" action="bypass -h admin_setclass 39" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr>
</table>
<table width="302" border="0" bgcolor="0066FF">
<tr>
<td><button value="SpellHowler" action="bypass -h admin_setclass 40" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Phantom Summoner" action="bypass -h admin_setclass 41" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Storm Screamer" action="bypass -h admin_setclass 110" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Spectral Master" action="bypass -h admin_setclass 111" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<table width="302" border="0" bgcolor="666666">
<tr>
<td><center><button value="Shillien Oracle" action="bypass -h admin_setclass 42" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr>
</table>
<table width="302" border="0" bgcolor="0066FF">
<tr>
<td><button value="Shillien Elder" action="bypass -h admin_setclass 43" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Shillien Saint" action="bypass -h admin_setclass 112" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
</body></html>