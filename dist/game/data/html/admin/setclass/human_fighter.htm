<html><body>
<title>//setclass  Menu</title>
<table width="260"><tr>
<td width="40"><center><button value="Back to Main Menu" action="bypass -h admin_admin" width="292" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr></table>
<table width="260" border="0" bgcolor="000000">
<tr>
<td><button value="Human F." action="bypass -h admin_html setclass/human_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Human M." action="bypass -h admin_html setclass/human_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Elf F." action="bypass -h admin_html setclass/elf_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Elf M." action="bypass -h admin_html setclass/elf_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Dark Elf F." action="bypass -h admin_html setclass/dark_elf_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Dark Elf M." action="bypass -h admin_html setclass/dark_elf_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Orc F." action="bypass -h admin_html setclass/orc_fighter.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Orc M." action="bypass -h admin_html setclass/orc_mage.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Dwarf" action="bypass -h admin_html setclass/dwarf.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Kamael" action="bypass -h admin_html setclass/kamael.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="D. Knight" action="bypass -h admin_html setclass/death_knight.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Sylph" action="bypass -h admin_html setclass/sylph.htm" width="70" height="15" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<table width="302" border="0" bgcolor="999999">
<tr>
<td><center><button value="Fighter" action="bypass -h admin_setclass 0" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr>
</table>
<table width="302" border="0" bgcolor="666666">
<tr>
<td><center><button value="Warrior" action="bypass -h admin_setclass 1" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr>
</table>
<table width="302" border="0" bgcolor="0066FF">
<tr>
<td><button value="Gladiator" action="bypass -h admin_setclass 2" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Warlord" action="bypass -h admin_setclass 3" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Duelist" action="bypass -h admin_setclass 88" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Dreadnought" action="bypass -h admin_setclass 89" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<table width="302" border="0" bgcolor="666666">
<tr>
<td><center><button value="Knight" action="bypass -h admin_setclass 4" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr>
</table>
<table width="302" border="0" bgcolor="0066FF">
<tr>
<td><button value="Paladin" action="bypass -h admin_setclass 5" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Dark Avenger" action="bypass -h admin_setclass 6" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Phoenix Knight" action="bypass -h admin_setclass 90" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Hell Knight" action="bypass -h admin_setclass 91" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<table width="302" border="0" bgcolor="666666">
<tr>
<td><center><button value="Rogue" action="bypass -h admin_setclass 7" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></center></td>
</tr>
</table>
<table width="302" border="0" bgcolor="0066FF">
<tr>
<td><button value="Treasure Hunter" action="bypass -h admin_setclass 8" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Hawkeye" action="bypass -h admin_setclass 9" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Adventurer" action="bypass -h admin_setclass 93" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Sagittarius" action="bypass -h admin_setclass 92" width="130" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
</body></html>