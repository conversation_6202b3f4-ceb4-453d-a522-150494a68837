<html><title>Transformation</title><body>
<center>
<table width=270>
<tr>
<td width=45><button value="Main" action="bypass -h admin_admin" width=45 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td width=180><center>Transformation Menu</center></td>
<td width=45><button value="Back" action="bypass -h admin_admin3" width=45 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<br>
<table width="270">
<tr>
<td><button value="Page 1" action="bypass -h admin_html transform.htm" width="50" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Page 2" action="bypass -h admin_html transform2.htm" width="50" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Page 3" action="bypass -h admin_html transform3.htm" width="50" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Page 4" action="bypass -h admin_html transform4.htm" width="50" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Page 5" action="bypass -h admin_html transform5.htm" width="50" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<br>
<table width="270">
<tr>
<td>Values:</td>
<td><edit var="qbox" width="200" height="15" /></td>
</tr>
</table>
<br>
<table width="270">
<tr>
<td><button value="Send" action="bypass -h admin_transform_menu $qbox" width="40" height="21" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF" /></td>
<td><button value="Help" action="bypass -h admin_html help/transform.htm" width="40" height="21" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF" /></td>
<td><button value="Untransform" action="bypass -h admin_untransform" width="90" height="21" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF" /></td>
</tr>
</table>
<br>
Addition of class horses and Cool Lu.<br1>
<table width=260>
<tr>
<td><button value="Knight" action="bypass -h admin_transform_menu 129 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Fighter" action="bypass -h admin_transform_menu 130 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Ranger" action="bypass -h admin_transform_menu 131 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Archer" action="bypass -h admin_transform_menu 132 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Wizard" action="bypass -h admin_transform_menu 133 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Summon" action="bypass -h admin_transform_menu 134 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Master" action="bypass -h admin_transform_menu 135 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Pastor" action="bypass -h admin_transform_menu 136 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
<tr>
<td><button value="Kukaru" action="bypass -h admin_transform_menu 137 $qbox" width=65 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td></td>
<td></td>
<td></td>
</tr>
</table>
</center>
</body></html>