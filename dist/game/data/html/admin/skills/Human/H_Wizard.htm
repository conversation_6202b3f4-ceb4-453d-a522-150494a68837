<html><body><table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td><td width=180>
<center>Human Wizard Skill Menu</center>
</td><td width=40>
<button value="Back" action="bypass -h admin_html skills/Human/human.htm" width=40 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="b5ba6b"><table width=260>
<tr><td>Anti Magic (max 12)</td><td><button value="Add" action="bypass -h admin_add_skill 146 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Quick Recovery (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 164 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Fast HP Recovery (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 212 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Boost Mana (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 213 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Fast Spell Casting (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 228 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Fast Mana Recovery (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 229 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Robe Mastery (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 235 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Weapon Mastery (max 9)</td><td><button value="Add" action="bypass -h admin_add_skill 249 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Higher Mana Gain (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 285 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Sleep (max 9)</td><td><button value="Add" action="bypass -h admin_add_skill 1069 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Concentration (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 1078 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Surrender To Fire (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1083 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Summon Kat the Cat (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1111 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Servitor Recharge (max 6)</td><td><button value="Add" action="bypass -h admin_add_skill 1126 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Servitor Heal (max 12)</td><td><button value="Add" action="bypass -h admin_add_skill 1127 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Fast Servitor (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1144 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Vampiric Touch (max 6)</td><td><button value="Add" action="bypass -h admin_add_skill 1147 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Corpse Life Drain (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 1151 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Body To Mind (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1157 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Curse: Weakness (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 1164 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Poisonous Cloud (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 1167 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Curse: Poison (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1168 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Aura Burn (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1172 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Flame Strike (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1181 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Ice Bolt (max 6)</td><td><button value="Add" action="bypass -h admin_add_skill 1184 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Blaze (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1220 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Curse Chaos (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1222 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Summon Mew the Cat (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1225 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Energy Bolt (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1274 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
</table></center></font>
</body></html>