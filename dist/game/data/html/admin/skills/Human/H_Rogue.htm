<html><body><table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td><td width=180>
<center>Rogue Skill Menu</center>
</td><td width=40>
<button value="Back" action="bypass -h admin_html skills/Human/human.htm" width=40 height=21 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="deba73"><table width=260>
<tr><td>Dash (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 4 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Mortal Blow (max 24)</td><td><button value="Add" action="bypass -h admin_add_skill 16 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Unlock (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 27 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Power Shot (max 24)</td><td><button value="Add" action="bypass -h admin_add_skill 56 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Bleed (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 96 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Rapid Shot (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 99 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Stun Shot (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 101 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Ultimate Evasion (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 111 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Long Shot (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 113 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Critical Chance (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 137 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Vital Force (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 148 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Boost Attack Speed (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 168 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Quick Step (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 169 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Esprit (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 171 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Acrobat (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 173 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Critical Power (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 193 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Boost Breath (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 195 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Boost Evasion (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 198 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Bow Mastery (max 15)</td><td><button value="Add" action="bypass -h admin_add_skill 208 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Dagger Mastery (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 209 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Acrobatic Move (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 225 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Light Armor Mastery (max 10)</td><td><button value="Add" action="bypass -h admin_add_skill 233 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Accuracy (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 256 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Vicious Stance (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 312 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
</table></center></font>
</body></html>