<html noscrollbar>
	<body>
		<center>
			<br>
			<table border="0" cellpadding="0" cellspacing="0" width="97%" height="480" background="L2UI_CH3.refinewnd_back_Pattern">
				<tr>
					<td valign="top" align="center">
						<table border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td width="256" height="185" background="L2UI_CT1.OlympiadWnd_DF_GrandTexture"></td>
							</tr>
						</table>
						Add announcement:<br1>
						<table width=600 height=210 cellspacing=5 background="L2UI_CT1.Windows.Windows_DF_TooltipBG">
							<tr>
								<td width="200">Type</td>
								<td width="420"><combobox width=200 height=17 var="typeBox" list="NORMAL;CRITICAL;AUTO_NORMAL;AUTO_CRITICAL"></td>
							</tr>
							<tr>
								<td width="200">Initial Delay in sec</td>
								<td width="420"><edit var="autoann_initdelay" width=200 height=12 type=number></td>
							</tr>
							<tr>
								<td width="200">Delay in sec</td>
								<td width="420"><edit var="autoann_delay" width=200 height=12 type=number></td>
							</tr>
							<tr>
								<td width="200">Repeat (0 for infinite):</td>
								<td width="420"><edit var="autoann_repeat" width=200 height=12 type=number></td>
							</tr>
							<tr>
								<td width="200">Text:</td>
								<td width="420"><multiedit var="autoann_memo" width=200 height=100></td>
							</tr>
						</table>
						<center>
							<table>
								<tr>
									<td><button value="Add" action="bypass -h admin_announces add $typeBox $autoann_initdelay $autoann_delay $autoann_repeat $autoann_memo" width="70" height="21" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
									<td><button value="Back" action="bypass -h admin_announces list" width="70" height="21" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
								</tr>
							</table>
						</center>
					</td>
				</tr>
			</table>
		</center>
	</body>
</html>