# ---------------------------------------------------------------------------
# Login Server Settings
# ---------------------------------------------------------------------------
# This is the server configuration file. Here you can set up the connection information for your server.
# This was written with the assumption that you are behind a router.
# Dumbed Down Definitions...
# LAN (LOCAL area network) - typically consists of computers connected to the same router as you.
# WAN (WIDE area network) - typically consists of computers OUTSIDE of your router (ie. the internet).
# x.x.x.x - Format of an IP address. Do not include the x'es into settings. Must be real numbers.

# ---------------------------------------------------------------------------
# Networking
# ---------------------------------------------------------------------------

# Bind ip of the LoginServer, use 0.0.0.0 to bind on all available IPs
# WARNING: <u><b><font color="red">Please don't change default IPs here if you don't know what are you doing!</font></b></u>
# WARNING: <u><b><font color="red">External/Internal IPs are now inside "ipconfig.xml" file.</font></b></u>
# Default: 0.0.0.0
LoginserverHostname = 0.0.0.0

# Default: 2106
LoginserverPort = 2108

# The address on which login will listen for GameServers, use * to bind on all available IPs
# WARNING: <u><b><font color="red">Please don't change default IPs here if you don't know what are you doing!</font></b></u>
# WARNING: <u><b><font color="red">External/Internal IPs are now inside "ipconfig.xml" file.</font></b></u>
# Default: 127.0.0.1
LoginHostname = 127.0.0.1

# The port on which login will listen for GameServers
# Default: 9014
LoginPort = 9014


# ---------------------------------------------------------------------------
# Database
# ---------------------------------------------------------------------------

# Specify the appropriate driver and url for your database.
# Default: org.mariadb.jdbc.Driver
Driver = org.mariadb.jdbc.Driver

# Database URL
# Default: **********************************************************************************************
# URL = ********************************************************************************************
# URL = *****************************************************************************************
URL = **************************************************************************************************

# Database user info (default is "root" but it's not recommended)
Login = root

# Database connection password
Password = 123456

# Default: 5
MaximumDbConnections = 5


# ---------------------------------------------------------------------------
# Automatic Database Backup Settings
# ---------------------------------------------------------------------------
# Generate database backups when server restarts or shuts down. 
BackupDatabase = False

# Path to MySQL bin folder. Only necessary on Windows.
MySqlBinLocation = C:/xampp/mysql/bin/

# Path where MySQL backups are stored.
BackupPath = ../backup/

# Maximum number of days that backups will be kept.
# Old backups will be deleted.
# Set to 0 to disable.
BackupDays = 30


# ---------------------------------------------------------------------------
# Security
# ---------------------------------------------------------------------------

# How many times you can provide an invalid account/pass before the IP gets banned.
# Default: 5
LoginTryBeforeBan = 5

# Time you won't be able to login back again after LoginTryBeforeBan tries to login.
# Default: 900 (15 minutes)
LoginBlockAfterBan = 900

# If set to True any GameServer can register on your login's free slots
# Default: True
AcceptNewGameServer = True

# Flood Protection. All values are in milliseconds.
# Default: True
EnableFloodProtection = True

# Default: 15
FastConnectionLimit = 15

# Default: 700
NormalConnectionTime = 700

# Default: 350
FastConnectionTime = 350

# Default: 50
MaxConnectionPerIP = 50


# ---------------------------------------------------------------------------
# Command Line Login Method
# ---------------------------------------------------------------------------
# Start client with "account=YourAccount password=YourPassword" parameters.
# You need to have enabled ExternalLogin and CmdLineLogin in your l2.ini file.

# Default: False
EnableCmdLineLogin = False

# Default: False
OnlyCmdLineLogin = False


# ---------------------------------------------------------------------------
# Misc Login Settings
# ---------------------------------------------------------------------------

# If False, the license (after the login) will not be shown.
# Default: True
ShowLicence = True

# Displays PI Agreement
# Default: False
ShowPIAgreement = False

# Default: True
AutoCreateAccounts = True

# Datapack root directory.
# Defaults to current directory from which the server is started.
DatapackRoot = .


# ---------------------------------------------------------------------------
# Scheduled Login Restart
# ---------------------------------------------------------------------------

# Enable disable scheduled login restart.
# Default: False
LoginRestartSchedule = False
# Time in hours.
# Default: 24
LoginRestartTime = 24
